import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { apiRequest } from "@/lib/queryClient";
import { User, Shop, Branch } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";

interface AuthContextType {
  user: User | null;
  token: string | null;
  userShops: Shop[];
  userBranches: Branch[];
  isAuthenticated: boolean;
  isLoading: boolean;
  hasShops: boolean;
  hasBranches: boolean;
  tokenChecked: boolean; // Add tokenChecked to track if token validation is complete
  userPermissions: string[];
  hasPermission: (permission: string) => boolean;
  login: (username: string, password: string) => Promise<void>;
  sendOtp: (identifier: string) => Promise<{ message: string; otp?: string; username?: string }>;
  verifyOtp: (identifier: string, otp: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  forgotPassword: (email: string) => Promise<{ message: string; otp?: string; username?: string }>;
  resetPassword: (data: { username: string; otp: string; password: string; confirmPassword: string }) => Promise<void>;
}

// No demo user or token needed as we're using API-based authentication

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [userShops, setUserShops] = useState<Shop[]>([]);
  const [userBranches, setUserBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true); // Start with loading true
  const [tokenChecked, setTokenChecked] = useState<boolean>(false); // Track if token check is complete
  const [userPermissions, setUserPermissions] = useState<string[]>([]);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Computed properties
  const hasShops = userShops.length > 0;
  const hasBranches = userBranches.length > 0;

  useEffect(() => {
    // Check for saved token in localStorage
    const savedToken = localStorage.getItem("auth_token");
    if (savedToken) {
      setToken(savedToken);
      fetchUser(savedToken);
    } else {
      setIsLoading(false);
      setTokenChecked(true); // Mark token check as complete even if no token found
    }
  }, []);

  const fetchUser = async (authToken: string) => {
    try {
      // Use apiRequest instead of fetch
      const userResponse = await apiRequest("GET", "/api/users/me");

      if (userResponse.ok) {
        const userData = await userResponse.json();
        setUser(userData);

        // Fetch user's permissions if they have a role
        if (userData.roleId) {
          try {
            const roleResponse = await apiRequest("GET", `/api/roles/${userData.roleId}`);

            if (roleResponse.ok) {
              const roleData = await roleResponse.json();
              setUserPermissions(roleData.permissions || []);
            }
          } catch (permissionsError) {
            console.error("Error fetching user permissions:", permissionsError);
            setUserPermissions([]);
          }
        } else {
          setUserPermissions([]);
        }

        // Fetch user's shops
        try {
          const shopsResponse = await apiRequest("GET", "/api/shops");

          if (shopsResponse.ok) {
            const shopsData = await shopsResponse.json();
            setUserShops(shopsData || []);

            // If we have shops, fetch branches for the first shop
            if (shopsData && shopsData.length > 0) {
              try {
                const branchesResponse = await apiRequest("GET", `/api/shops/${shopsData[0].id}/branches`);

                if (branchesResponse.ok) {
                  const branchesData = await branchesResponse.json();
                  setUserBranches(branchesData || []);
                }
              } catch (branchesError) {
                console.error("Error fetching user branches:", branchesError);
                setUserBranches([]);
              }
            }
          }
        } catch (shopsError) {
          console.error("Error fetching user shops:", shopsError);
          setUserShops([]);
        }
      } else {
        // Token is invalid, clear it
        console.error("Invalid token detected during user fetch");
        setToken(null);
        localStorage.removeItem("auth_token");
        setUserPermissions([]);
      }
    } catch (error) {
      console.error("Error fetching user:", error);
      setToken(null);
      localStorage.removeItem("auth_token");
      setUserPermissions([]);
    } finally {
      setIsLoading(false);
      setTokenChecked(true); // Mark token check as complete
    }
  };

  // Check if user has a specific permission
  const hasPermission = (permission: string) => {
    return userPermissions.includes(permission);
  };

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      setTokenChecked(false); // Reset token check status during login

      // Real implementation
      const response = await apiRequest("POST", "/api/auth/login", { username, password });
      const data = await response.json();

      setUser(data.user);
      setToken(data.token);
      setUserShops(data.shops || []);
      setUserBranches(data.branches || []);
      localStorage.setItem("auth_token", data.token);

      // Fetch user's role permissions if they have a role
      if (data.user.roleId) {
        try {
          const roleResponse = await apiRequest("GET", `/api/roles/${data.user.roleId}`);

          if (roleResponse.ok) {
            const roleData = await roleResponse.json();
            setUserPermissions(roleData.permissions || []);
          }
        } catch (permissionsError) {
          console.error("Error fetching user permissions:", permissionsError);
          setUserPermissions([]);
        }
      } else {
        setUserPermissions([]);
      }

      setTokenChecked(true); // Mark token as checked after successful login

      toast({
        title: "Login successful",
        description: `Welcome back, ${data.user.name}!`,
      });

      // Redirect to shop selection if user has shops
      if (data.shops && data.shops.length > 0) {
        setLocation("/select-shop");
      } else {
        setLocation("/create-shop");
      }
    } catch (error) {
      console.error("Login error:", error);
      setTokenChecked(true); // Mark token check as complete even on error
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: any) => {
    try {
      setIsLoading(true);

      // Real implementation
      const response = await apiRequest("POST", "/api/auth/register", userData);
      const data = await response.json();

      toast({
        title: "Registration successful",
        description: "You can now log in with your credentials",
      });

      return data;
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Real implementation
    setUser(null);
    setToken(null);
    setUserShops([]);
    setUserBranches([]);
    setUserPermissions([]);
    localStorage.removeItem("auth_token");
    localStorage.removeItem("current_shop");
    localStorage.removeItem("current_branch");
    setTokenChecked(true); // Ensure token check is marked as complete

    toast({
      title: "Logged out",
      description: "You have been logged out successfully",
    });

    // Redirect to landing page
    setLocation("/");
  };

  const forgotPassword = async (email: string) => {
    try {
      setIsLoading(true);

      // Real implementation
      const response = await apiRequest("POST", "/api/auth/forgot-password", { email });
      const data = await response.json();

      toast({
        title: "Password recovery",
        description: data.message,
      });

      return data;
    } catch (error) {
      console.error("Forgot password error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (data: { username: string; otp: string; password: string; confirmPassword: string }) => {
    try {
      setIsLoading(true);

      // Real implementation
      const response = await apiRequest("POST", "/api/auth/reset-password", data);
      const responseData = await response.json();

      toast({
        title: "Password reset",
        description: "Your password has been reset successfully",
      });

      return responseData;
    } catch (error) {
      console.error("Reset password error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // OTP Login functions
  const sendOtp = async (identifier: string) => {
    try {
      setIsLoading(true);

      // Real implementation
      const response = await apiRequest("POST", "/api/auth/send-otp", { identifier });
      const data = await response.json();

      toast({
        title: "OTP Sent",
        description: data.message,
      });

      return data;
    } catch (error) {
      console.error("Send OTP error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (identifier: string, otp: string) => {
    try {
      setIsLoading(true);
      setTokenChecked(false); // Reset token check status during OTP verification

      // Real implementation
      const response = await apiRequest("POST", "/api/auth/verify-otp", { identifier, otp });
      const data = await response.json();

      setUser(data.user);
      setToken(data.token);
      setUserShops(data.shops || []);
      setUserBranches(data.branches || []);
      localStorage.setItem("auth_token", data.token);

      // Fetch user's role permissions if they have a role
      if (data.user.roleId) {
        try {
          const roleResponse = await apiRequest("GET", `/api/roles/${data.user.roleId}`);

          if (roleResponse.ok) {
            const roleData = await roleResponse.json();
            setUserPermissions(roleData.permissions || []);
          }
        } catch (permissionsError) {
          console.error("Error fetching user permissions:", permissionsError);
          setUserPermissions([]);
        }
      } else {
        setUserPermissions([]);
      }

      setTokenChecked(true); // Mark token as checked after successful login

      toast({
        title: "Login successful",
        description: `Welcome back, ${data.user.name}!`,
      });

      // Redirect to shop selection if user has shops
      if (data.shops && data.shops.length > 0) {
        setLocation("/select-shop");
      } else {
        setLocation("/create-shop");
      }
    } catch (error) {
      console.error("Verify OTP error:", error);
      setTokenChecked(true); // Mark token check as complete even on error
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update user information
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      // Update the user state with the new data
      setUser({
        ...user,
        ...userData
      });
    }
  };

  const value = {
    user,
    token,
    userShops,
    userBranches,
    isAuthenticated: !!token, // Authenticated if token exists
    isLoading,
    hasShops: userShops.length > 0,
    hasBranches: userBranches.length > 0,
    tokenChecked, // Add tokenChecked to the context
    userPermissions,
    hasPermission,
    login,
    sendOtp,
    verifyOtp,
    register,
    logout,
    updateUser,
    forgotPassword,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
