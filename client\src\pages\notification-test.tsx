import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useNotifications } from "@/hooks/use-notifications";
import { NotificationHelpers } from "@/lib/notification-helpers";
import { useToast } from "@/hooks/use-toast";
import { Bell, Package, Megaphone, Settings, Send, TestTube } from "lucide-react";

export default function NotificationTestPage() {
  const { toast } = useToast();
  const { isCreatingNotification } = useNotifications();
  
  const [orderForm, setOrderForm] = useState({
    orderId: 1,
    orderNumber: 'ORD-001',
    customerName: '<PERSON>'
  });

  const [stockForm, setStockForm] = useState({
    productName: 'Coffee Beans',
    currentStock: 5,
    minStock: 10
  });

  const [marketingForm, setMarketingForm] = useState({
    title: 'Special Offer',
    message: 'Get 20% off on all beverages this weekend!'
  });

  const [systemForm, setSystemForm] = useState({
    title: 'System Maintenance',
    message: 'Scheduled maintenance will occur tonight from 2 AM to 4 AM',
    priority: 'high' as 'low' | 'normal' | 'high' | 'urgent'
  });

  const handleOrderNotification = async () => {
    try {
      await NotificationHelpers.createOrderNotification(
        orderForm.orderId,
        orderForm.orderNumber,
        orderForm.customerName
      );
      toast({
        title: "Order notification sent",
        description: "Order notification has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create order notification.",
        variant: "destructive",
      });
    }
  };

  const handleStockNotification = async () => {
    try {
      await NotificationHelpers.createStockAlert(
        stockForm.productName,
        stockForm.currentStock,
        stockForm.minStock
      );
      toast({
        title: "Stock alert sent",
        description: "Stock alert notification has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create stock alert.",
        variant: "destructive",
      });
    }
  };

  const handleMarketingNotification = async () => {
    try {
      await NotificationHelpers.createMarketingNotification(
        marketingForm.title,
        marketingForm.message
      );
      toast({
        title: "Marketing notification sent",
        description: "Marketing notification has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create marketing notification.",
        variant: "destructive",
      });
    }
  };

  const handleSystemNotification = async () => {
    try {
      await NotificationHelpers.createSystemNotification(
        systemForm.title,
        systemForm.message,
        systemForm.priority
      );
      toast({
        title: "System notification sent",
        description: "System notification has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create system notification.",
        variant: "destructive",
      });
    }
  };

  const handleQuickTests = async () => {
    const tests = [
      () => NotificationHelpers.createOrderNotification(123, 'ORD-123', 'Alice Smith'),
      () => NotificationHelpers.createStockAlert('Milk', 2, 5),
      () => NotificationHelpers.createMarketingNotification('Flash Sale', '50% off all pastries for the next 2 hours!'),
      () => NotificationHelpers.createSystemNotification('Database Update', 'Database has been updated with new features', 'normal'),
    ];

    for (const test of tests) {
      try {
        await test();
        await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between notifications
      } catch (error) {
        console.error('Test failed:', error);
      }
    }

    toast({
      title: "Quick tests completed",
      description: "All test notifications have been sent.",
    });
  };

  return (
    <div className="container mx-auto max-w-6xl py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <TestTube className="h-8 w-8 text-blue-600" />
              Notification Test Center
            </h1>
            <p className="text-muted-foreground">
              Test all notification types and functionality
            </p>
          </div>
          <Button onClick={handleQuickTests} disabled={isCreatingNotification}>
            <Send className="h-4 w-4 mr-2" />
            Run Quick Tests
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Order Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-blue-600" />
                Order Notifications
              </CardTitle>
              <CardDescription>
                Test order-related notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="orderId">Order ID</Label>
                <Input
                  id="orderId"
                  type="number"
                  value={orderForm.orderId}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, orderId: parseInt(e.target.value) || 0 }))}
                />
              </div>
              <div>
                <Label htmlFor="orderNumber">Order Number</Label>
                <Input
                  id="orderNumber"
                  value={orderForm.orderNumber}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, orderNumber: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="customerName">Customer Name</Label>
                <Input
                  id="customerName"
                  value={orderForm.customerName}
                  onChange={(e) => setOrderForm(prev => ({ ...prev, customerName: e.target.value }))}
                />
              </div>
              <Button onClick={handleOrderNotification} disabled={isCreatingNotification} className="w-full">
                <Send className="h-4 w-4 mr-2" />
                Send Order Notification
              </Button>
            </CardContent>
          </Card>

          {/* Stock Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-orange-600" />
                Stock Alerts
              </CardTitle>
              <CardDescription>
                Test inventory-related notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="productName">Product Name</Label>
                <Input
                  id="productName"
                  value={stockForm.productName}
                  onChange={(e) => setStockForm(prev => ({ ...prev, productName: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="currentStock">Current Stock</Label>
                <Input
                  id="currentStock"
                  type="number"
                  value={stockForm.currentStock}
                  onChange={(e) => setStockForm(prev => ({ ...prev, currentStock: parseInt(e.target.value) || 0 }))}
                />
              </div>
              <div>
                <Label htmlFor="minStock">Minimum Stock</Label>
                <Input
                  id="minStock"
                  type="number"
                  value={stockForm.minStock}
                  onChange={(e) => setStockForm(prev => ({ ...prev, minStock: parseInt(e.target.value) || 0 }))}
                />
              </div>
              <Button onClick={handleStockNotification} disabled={isCreatingNotification} className="w-full">
                <Send className="h-4 w-4 mr-2" />
                Send Stock Alert
              </Button>
            </CardContent>
          </Card>

          {/* Marketing Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Megaphone className="h-5 w-5 text-green-600" />
                Marketing Messages
              </CardTitle>
              <CardDescription>
                Test promotional and marketing notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="marketingTitle">Title</Label>
                <Input
                  id="marketingTitle"
                  value={marketingForm.title}
                  onChange={(e) => setMarketingForm(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="marketingMessage">Message</Label>
                <Textarea
                  id="marketingMessage"
                  value={marketingForm.message}
                  onChange={(e) => setMarketingForm(prev => ({ ...prev, message: e.target.value }))}
                  rows={3}
                />
              </div>
              <Button onClick={handleMarketingNotification} disabled={isCreatingNotification} className="w-full">
                <Send className="h-4 w-4 mr-2" />
                Send Marketing Notification
              </Button>
            </CardContent>
          </Card>

          {/* System Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-gray-600" />
                System Notifications
              </CardTitle>
              <CardDescription>
                Test system and administrative notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="systemTitle">Title</Label>
                <Input
                  id="systemTitle"
                  value={systemForm.title}
                  onChange={(e) => setSystemForm(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="systemMessage">Message</Label>
                <Textarea
                  id="systemMessage"
                  value={systemForm.message}
                  onChange={(e) => setSystemForm(prev => ({ ...prev, message: e.target.value }))}
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={systemForm.priority} onValueChange={(value: any) => setSystemForm(prev => ({ ...prev, priority: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={handleSystemNotification} disabled={isCreatingNotification} className="w-full">
                <Send className="h-4 w-4 mr-2" />
                Send System Notification
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
  );
}
