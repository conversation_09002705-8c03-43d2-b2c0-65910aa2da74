import { apiRequest } from "@/lib/queryClient";

export interface CreateNotificationData {
  title: string;
  message: string;
  type: 'order' | 'stock' | 'marketing' | 'system';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  data?: Record<string, any>;
}

export class NotificationHelpers {
  
  /**
   * Create an order notification when a new order is placed
   */
  static async createOrderNotification(orderId: number, orderNumber: string, customerName: string) {
    try {
      const response = await apiRequest("POST", "/api/notifications/order", {
        orderId,
        orderNumber,
        customerName
      });
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create order notification:', error);
    }
  }

  /**
   * Create a stock alert notification when inventory is low
   */
  static async createStockAlert(productName: string, currentStock: number, minStock: number) {
    try {
      const response = await apiRequest("POST", "/api/notifications/stock", {
        productName,
        currentStock,
        minStock
      });
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create stock alert:', error);
    }
  }

  /**
   * Create a marketing notification for promotions or announcements
   */
  static async createMarketingNotification(title: string, message: string) {
    try {
      const response = await apiRequest("POST", "/api/notifications/marketing", {
        title,
        message
      });
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create marketing notification:', error);
    }
  }

  /**
   * Create a system notification for important updates
   */
  static async createSystemNotification(title: string, message: string, priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal') {
    try {
      const response = await apiRequest("POST", "/api/notifications/system", {
        title,
        message,
        priority
      });
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create system notification:', error);
    }
  }

  /**
   * Create a custom notification for specific users
   */
  static async createCustomNotification(userIds: number[], notificationData: CreateNotificationData) {
    try {
      const response = await apiRequest("POST", "/api/notifications/users", {
        userIds,
        ...notificationData
      });
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create custom notification:', error);
    }
  }

  /**
   * Create a shop-wide notification
   */
  static async createShopNotification(notificationData: CreateNotificationData) {
    try {
      const response = await apiRequest("POST", "/api/notifications/shop", notificationData);
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create shop notification:', error);
    }
  }

  /**
   * Create a branch-specific notification
   */
  static async createBranchNotification(notificationData: CreateNotificationData) {
    try {
      const response = await apiRequest("POST", "/api/notifications/branch", notificationData);
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create branch notification:', error);
    }
  }

  /**
   * Helper to check if stock is low and create alert if needed
   */
  static async checkAndCreateStockAlert(productName: string, currentStock: number, minStock: number) {
    if (currentStock <= minStock) {
      await this.createStockAlert(productName, currentStock, minStock);
    }
  }

  /**
   * Helper to create order status change notification
   */
  static async createOrderStatusNotification(orderNumber: string, oldStatus: string, newStatus: string, customerName?: string) {
    const statusMessages = {
      'pending': 'Order is being prepared',
      'preparing': 'Order is being prepared',
      'ready': 'Order is ready for pickup/delivery',
      'completed': 'Order has been completed',
      'cancelled': 'Order has been cancelled'
    };

    const title = `Order ${orderNumber} Status Update`;
    const message = `Order ${orderNumber}${customerName ? ` for ${customerName}` : ''} status changed from ${oldStatus} to ${newStatus}. ${statusMessages[newStatus as keyof typeof statusMessages] || ''}`;

    try {
      const response = await apiRequest("POST", "/api/notifications/order", {
        orderId: 0, // This would need to be passed in
        orderNumber,
        customerName: customerName || 'Customer'
      });
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to create order status notification:', error);
    }
  }

  /**
   * Helper to create payment received notification
   */
  static async createPaymentNotification(orderNumber: string, amount: number, paymentMethod: string) {
    const title = 'Payment Received';
    const message = `Payment of $${amount.toFixed(2)} received for order ${orderNumber} via ${paymentMethod}`;

    try {
      await this.createSystemNotification(title, message, 'normal');
    } catch (error) {
      console.error('Failed to create payment notification:', error);
    }
  }

  /**
   * Helper to create refund notification
   */
  static async createRefundNotification(orderNumber: string, amount: number, reason: string) {
    const title = 'Refund Processed';
    const message = `Refund of $${amount.toFixed(2)} processed for order ${orderNumber}. Reason: ${reason}`;

    try {
      await this.createSystemNotification(title, message, 'high');
    } catch (error) {
      console.error('Failed to create refund notification:', error);
    }
  }

  /**
   * Helper to create new user registration notification
   */
  static async createUserRegistrationNotification(userName: string, userRole: string) {
    const title = 'New User Registered';
    const message = `${userName} has registered as a ${userRole}`;

    try {
      await this.createSystemNotification(title, message, 'normal');
    } catch (error) {
      console.error('Failed to create user registration notification:', error);
    }
  }

  /**
   * Helper to create table status change notification
   */
  static async createTableStatusNotification(tableNumber: string, oldStatus: string, newStatus: string) {
    const title = 'Table Status Update';
    const message = `Table ${tableNumber} status changed from ${oldStatus} to ${newStatus}`;

    try {
      await this.createSystemNotification(title, message, 'low');
    } catch (error) {
      console.error('Failed to create table status notification:', error);
    }
  }

  /**
   * Helper to create daily sales summary notification
   */
  static async createDailySalesNotification(totalSales: number, orderCount: number, date: string) {
    const title = 'Daily Sales Summary';
    const message = `Sales for ${date}: $${totalSales.toFixed(2)} from ${orderCount} orders`;

    try {
      await this.createSystemNotification(title, message, 'normal');
    } catch (error) {
      console.error('Failed to create daily sales notification:', error);
    }
  }

  /**
   * Helper to create maintenance notification
   */
  static async createMaintenanceNotification(title: string, message: string, scheduledTime?: string) {
    const fullMessage = scheduledTime 
      ? `${message} Scheduled for: ${scheduledTime}`
      : message;

    try {
      await this.createSystemNotification(title, fullMessage, 'high');
    } catch (error) {
      console.error('Failed to create maintenance notification:', error);
    }
  }

  /**
   * Helper to create promotion notification
   */
  static async createPromotionNotification(promotionTitle: string, description: string, validUntil?: string) {
    const message = validUntil 
      ? `${description} Valid until: ${validUntil}`
      : description;

    try {
      await this.createMarketingNotification(promotionTitle, message);
    } catch (error) {
      console.error('Failed to create promotion notification:', error);
    }
  }
}
