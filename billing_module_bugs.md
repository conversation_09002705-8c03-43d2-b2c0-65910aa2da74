# Billing Module - Bug Report

## Task 1: POS Issues

### Status:
Open

### Description:
The POS page offers **four order types**. However, the following issues have been identified:

1. **Order Type Mismatch**  
   When selecting **"Eat and Pay"**, the system stores the incorrect order type in the `types_order` table.

2. **Payment Method Not Saved Correctly**  
   After completing an order (Save and Print), even if a specific payment method is selected (e.g., UPI, Card), the system always defaults to **Cash** in the reports module.

### Affected Modules:
- `types_order` table
- Sales Reports → Payment Method display

### Required Fixes:
- Ensure the selected order type (e.g., "Eat and Pay") is accurately saved in the `types_order` table.
- Ensure the selected payment method during order completion is correctly stored and reflected in the reports module.

