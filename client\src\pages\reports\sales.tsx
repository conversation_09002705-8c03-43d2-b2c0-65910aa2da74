import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";
import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { BranchSelector } from "@/components/branch-selector";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { LineChart } from "@/components/ui/chart";

import {
  Download,
  Printer,
  Calendar,
  FilterX,
  Search,
  CalendarRange,
  File,
  FileDown,
  FileText,
  Building2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

export default function SalesReport() {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const { toast } = useToast();

  const [period, setPeriod] = useState<string>("today");
  const [orderTypeFilter, setOrderTypeFilter] = useState<string>("all");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("orders");
  const [branchFilter, setBranchFilter] = useState<number | null>(currentBranch?.id || null);

  // Fetch sales report data
  const { data: reportData, isLoading: isLoadingReportData } = useQuery({
    queryKey: ['/api/reports/sales', currentShop?.id, period, orderTypeFilter, startDate, endDate, branchFilter],
    queryFn: async () => {
      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('period', period);
        if (orderTypeFilter !== 'all') {
          params.append('orderType', orderTypeFilter);
        }
        if (startDate && endDate && period === 'custom') {
          params.append('startDate', startDate);
          params.append('endDate', endDate);
        }
        if (branchFilter) {
          params.append('branchId', branchFilter.toString());
        }

        // Use apiRequest instead of fetch to ensure proper shop/branch context
        const response = await apiRequest('GET', `/api/reports/sales?${params.toString()}`);
        if (!response.ok) {
          throw new Error('Failed to fetch sales report');
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching sales report:', error);
        return null;
      }
    },
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Calculate sales stats
  const calculateStats = () => {
    if (!reportData) return null;

    const { orders, stats, dateRange } = reportData;

    // The orders are already filtered by the API based on the query parameters
    const filteredOrders = orders || [];

    // Get statistics from the API response
    const totalSales = stats?.totalSales || 0;
    const orderCount = filteredOrders.length;
    const averageOrderValue = stats?.avgOrderValue || 0;

    // Get order counts by type from the API response
    const ordersByType = stats?.orderCounts || {
      dine_in: 0,
      takeaway: 0,
      online: 0,
    };

    // Count orders by status
    const ordersByStatus = {
      pending: filteredOrders.filter(order => order.status === 'pending').length,
      preparing: filteredOrders.filter(order => order.status === 'preparing').length,
      ready: filteredOrders.filter(order => order.status === 'ready').length,
      completed: filteredOrders.filter(order => order.status === 'completed').length,
      cancelled: filteredOrders.filter(order => order.status === 'cancelled').length,
    };

    return {
      filteredOrders,
      totalSales,
      orderCount,
      averageOrderValue,
      ordersByType,
      ordersByStatus,
      dateRange
    };
  };

  const stats = calculateStats();

  // Handle custom date range
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    if (value !== "custom") {
      setStartDate("");
      setEndDate("");
    }
  };

  // Handle print and export
  const handlePrint = () => {
    toast({
      title: "Print report",
      description: "Printing report...",
    });
    window.print();
  };

  // Format data for export
  const getFormattedData = () => {
    if (!stats || !stats.filteredOrders || stats.filteredOrders.length === 0) {
      return null;
    }

    return formatDataForExport(stats.filteredOrders, {
      // Custom formatters for specific fields
      orderType: (value) =>
        value === 'dine_in' ? 'Dine-in' :
        value === 'takeaway' ? 'Takeaway' : 'Online',
      totalAmount: (value) => `₹${parseFloat(value).toFixed(2)}`,
      createdAt: (value) => new Date(value).toLocaleString(),
    });
  };

  // Get file name for exports
  const getExportFileName = () => {
    const branchName = branchFilter
      ? `_${currentBranch?.name?.replace(/\s+/g, '_') || 'Branch'}`
      : '_All_Branches';

    return `Sales_Report${branchName}_${period === 'custom' ?
      `${startDate}_to_${endDate}` :
      period}_${new Date().toISOString().split('T')[0]}`;
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to Excel
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Sales report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to CSV
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Sales report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    if (!stats || !stats.filteredOrders || stats.filteredOrders.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(18);
      doc.text("Sales Report", 14, 22);

      // Add branch information
      doc.setFontSize(12);
      const branchText = branchFilter
        ? `Branch: ${currentBranch?.name || 'Selected Branch'}`
        : "Branch: All Branches";
      doc.text(branchText, 14, 32);

      // Add period information
      let periodText = "Period: ";
      if (period === "today") {
        periodText += "Today";
      } else if (period === "yesterday") {
        periodText += "Yesterday";
      } else if (period === "week") {
        periodText += "Last 7 days";
      } else if (period === "month") {
        periodText += "Last 30 days";
      } else if (period === "custom") {
        periodText += `${startDate} to ${endDate}`;
      }
      doc.text(periodText, 14, 42);

      // Add summary information
      doc.text(`Total Sales: ₹${stats.totalSales.toFixed(2)}`, 14, 52);
      doc.text(`Total Orders: ${stats.orderCount}`, 14, 62);
      doc.text(`Average Order Value: ₹${stats.averageOrderValue.toFixed(2)}`, 14, 72);

      // Add order type breakdown table
      const orderTypeData = [
        ['Dine-in', stats.ordersByType.dine_in, `${stats.orderCount > 0 ? Math.round((stats.ordersByType.dine_in / stats.orderCount) * 100) : 0}%`],
        ['Takeaway', stats.ordersByType.takeaway, `${stats.orderCount > 0 ? Math.round((stats.ordersByType.takeaway / stats.orderCount) * 100) : 0}%`],
        ['Online', stats.ordersByType.online, `${stats.orderCount > 0 ? Math.round((stats.ordersByType.online / stats.orderCount) * 100) : 0}%`]
      ];

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: 80,
        head: [['Order Type', 'Count', 'Percentage']],
        body: orderTypeData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 10 }
      });

      // Add detailed orders table
      const detailedData = stats.filteredOrders.map(order => [
        order.orderNumber,
        new Date(order.createdAt).toLocaleString(),
        order.orderType === 'dine_in' ? 'Dine-in' : order.orderType === 'takeaway' ? 'Takeaway' : 'Online',
        `₹${parseFloat(order.totalAmount).toFixed(2)}`,
        order.status.charAt(0).toUpperCase() + order.status.slice(1)
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: doc.lastAutoTable.finalY + 15,
        head: [['Order #', 'Date', 'Type', 'Amount', 'Status']],
        body: detailedData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 9 },
        columnStyles: {
          0: { cellWidth: 25 },
          1: { cellWidth: 40 },
          2: { cellWidth: 25 },
          3: { cellWidth: 25 },
          4: { cellWidth: 25 }
        }
      });

      // Save the PDF
      doc.save(`${getExportFileName()}.pdf`);

      toast({
        title: "Export successful",
        description: "Sales report has been exported to PDF",
      });
    } catch (error) {
      console.error("PDF export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the report to PDF",
      });
    }
  };

  // Reset filters
  const handleResetFilters = () => {
    setPeriod("today");
    setOrderTypeFilter("all");
    setStartDate("");
    setEndDate("");
    setBranchFilter(currentBranch?.id || null);
  };

  // Status badge configurations
  const orderTypeColors = {
    dine_in: "bg-blue-100 text-blue-800",
    takeaway: "bg-yellow-100 text-yellow-800",
    online: "bg-purple-100 text-purple-800"
  };

  const orderStatusColors = {
    pending: "bg-blue-100 text-blue-800",
    preparing: "bg-yellow-100 text-yellow-800",
    ready: "bg-orange-100 text-orange-800",
    completed: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800"
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Sales Report</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={handleExportToExcel}>
            <File className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={handleExportToCSV}>
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={handleExportToPDF}>
            <FileDown className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Report Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Branch</label>
              <div className="w-full">
                <BranchSelector
                  variant="outline"
                  className="w-full"
                  onChange={(branchId) => setBranchFilter(branchId)}
                  value={branchFilter}
                  allowAllBranches={true}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Period</label>
              <Select
                value={period}
                onValueChange={handlePeriodChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">Last 7 days</SelectItem>
                  <SelectItem value="month">Last 30 days</SelectItem>
                  <SelectItem value="custom">Custom date range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Order Type</label>
              <Select
                value={orderTypeFilter}
                onValueChange={setOrderTypeFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All order types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All order types</SelectItem>
                  <SelectItem value="dine_in">Dine-in</SelectItem>
                  <SelectItem value="takeaway">Takeaway</SelectItem>
                  <SelectItem value="online">Online</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/4 flex items-end">
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResetFilters}
              >
                <FilterX className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            </div>
          </div>

          {period === "custom" && (
            <div className="flex flex-col sm:flex-row gap-4 mt-4">
              <div className="w-full sm:w-1/2">
                <label className="text-sm font-medium mb-1 block">Start Date</label>
                <div className="relative">
                  <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="date"
                    className="pl-8"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
              </div>
              <div className="w-full sm:w-1/2">
                <label className="text-sm font-medium mb-1 block">End Date</label>
                <div className="relative">
                  <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="date"
                    className="pl-8"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Sales</span>
              {isLoadingReportData ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{stats?.totalSales.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Orders</span>
              {isLoadingReportData ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {stats?.orderCount || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Average Order Value</span>
              {isLoadingReportData ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{stats?.averageOrderValue.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Date Range</span>
              {isLoadingReportData ? (
                <Skeleton className="h-8 w-32 mt-1" />
              ) : (
                <span className="text-base font-medium mt-1 flex items-center">
                  <CalendarRange className="h-4 w-4 mr-1 text-gray-400" />
                  {period === "custom" && startDate && endDate ? (
                    `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`
                  ) : period === "today" ? (
                    "Today"
                  ) : period === "yesterday" ? (
                    "Yesterday"
                  ) : period === "week" ? (
                    "Last 7 days"
                  ) : period === "month" ? (
                    "Last 30 days"
                  ) : (
                    "All time"
                  )}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Enhanced Sales Trend */}
        <Card className="overflow-hidden shadow-xl border-0 bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-3 text-white font-bold text-lg">
                <div className="w-3 h-3 bg-white rounded-full animate-pulse shadow-lg"></div>
                Sales Trend
              </CardTitle>
              <div className="flex items-center gap-3">
                <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30 transition-all">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                  Live Data
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0 bg-white">
            {isLoadingReportData ? (
              <div className="h-96 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                  <p className="text-gray-500">Loading sales data...</p>
                </div>
              </div>
            ) : (
              <div className="relative p-6">
                {/* Stats Cards Row */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-xl border border-green-100">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xs font-medium text-green-700">Total Revenue</span>
                    </div>
                    <div className="text-xl font-bold text-green-800">
                      ₹{stats?.totalSales.toFixed(2) || "0.00"}
                    </div>
                    <div className="flex items-center gap-1 text-xs mt-1">
                      <span className="text-green-600">↗ +12.5%</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-xl border border-blue-100">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-xs font-medium text-blue-700">Avg. Daily</span>
                    </div>
                    <div className="text-xl font-bold text-blue-800">
                      ₹{(stats?.avgOrderValue || 0).toFixed(2)}
                    </div>
                    <div className="flex items-center gap-1 text-xs mt-1">
                      <span className="text-blue-600">↗ +8.2%</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-100">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span className="text-xs font-medium text-purple-700">Peak Day</span>
                    </div>
                    <div className="text-xl font-bold text-purple-800">
                      Saturday
                    </div>
                    <div className="flex items-center gap-1 text-xs mt-1">
                      <span className="text-purple-600">₹2,400</span>
                    </div>
                  </div>
                </div>

                {/* Enhanced Chart Container */}
                <div className="bg-gradient-to-br from-gray-50/50 to-blue-50/30 rounded-2xl p-6 border border-gray-100 shadow-inner">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-bold text-gray-800 flex items-center gap-2">
                      <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                      Revenue Overview
                    </h3>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span>Updated now</span>
                    </div>
                  </div>

                  {/* Professional Chart Area */}
                  <div className="h-72 relative bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                    <div className="h-full w-full">
                      <LineChart
                        data={[
                          { name: 'Wed', revenue: 2200 },
                          { name: 'Thu', revenue: 1800 },
                          { name: 'Fri', revenue: 2400 },
                          { name: 'Sat', revenue: 2100 },
                          { name: 'Sun', revenue: 1400 },
                          { name: 'Mon', revenue: 1600 },
                          { name: 'Tue', revenue: 1900 },
                        ]}
                        categories={['revenue']}
                        index="name"
                        valueFormatter={(value: number) => `₹${value.toLocaleString()}`}
                        colors={['#3B82F6']}
                        showLegend={false}
                        showGridLines={true}
                        yAxisWidth={60}
                      />
                    </div>
                  </div>

                  {/* Chart Legend */}
                  <div className="flex items-center justify-center gap-6 mt-4 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-gray-600">Daily Revenue</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-gray-600">Growth Trend</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span className="text-gray-600">Peak Performance</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Orders Breakdown */}
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              Orders Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {isLoadingReportData ? (
              <Skeleton className="h-64 w-full" />
            ) : stats ? (
              <div className="space-y-6">
                {/* Order Type Breakdown with Visual Progress */}
                <div>
                  <h4 className="text-sm font-semibold mb-4 flex items-center gap-2">
                    <div className="w-1 h-4 bg-blue-500 rounded-full"></div>
                    By Order Type
                  </h4>
                  <div className="space-y-4">
                    {/* Dine-in */}
                    <div className="group hover:bg-blue-50/50 p-3 rounded-lg transition-all duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium">Dine-in</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{stats.ordersByType.dine_in}</span>
                          <Badge className="bg-blue-100 text-blue-800 text-xs">
                            {stats.orderCount > 0 ? Math.round((stats.ordersByType.dine_in / stats.orderCount) * 100) : 0}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${stats.orderCount > 0 ? (stats.ordersByType.dine_in / stats.orderCount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* Takeaway */}
                    <div className="group hover:bg-yellow-50/50 p-3 rounded-lg transition-all duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                          <span className="text-sm font-medium">Takeaway</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{stats.ordersByType.takeaway}</span>
                          <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                            {stats.orderCount > 0 ? Math.round((stats.ordersByType.takeaway / stats.orderCount) * 100) : 0}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${stats.orderCount > 0 ? (stats.ordersByType.takeaway / stats.orderCount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* Online */}
                    <div className="group hover:bg-purple-50/50 p-3 rounded-lg transition-all duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                          <span className="text-sm font-medium">Online</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{stats.ordersByType.online}</span>
                          <Badge className="bg-purple-100 text-purple-800 text-xs">
                            {stats.orderCount > 0 ? Math.round((stats.ordersByType.online / stats.orderCount) * 100) : 0}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${stats.orderCount > 0 ? (stats.ordersByType.online / stats.orderCount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Order Status Breakdown with Visual Progress */}
                <div className="border-t pt-4">
                  <h4 className="text-sm font-semibold mb-4 flex items-center gap-2">
                    <div className="w-1 h-4 bg-green-500 rounded-full"></div>
                    By Order Status
                  </h4>
                  <div className="space-y-4">
                    {/* Completed */}
                    <div className="group hover:bg-green-50/50 p-3 rounded-lg transition-all duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm font-medium">Completed</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{stats.ordersByStatus.completed}</span>
                          <Badge className="bg-green-100 text-green-800 text-xs">
                            {stats.orderCount > 0 ? Math.round((stats.ordersByStatus.completed / stats.orderCount) * 100) : 0}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${stats.orderCount > 0 ? (stats.ordersByStatus.completed / stats.orderCount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* In Progress */}
                    <div className="group hover:bg-blue-50/50 p-3 rounded-lg transition-all duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                          <span className="text-sm font-medium">In Progress</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">
                            {stats.ordersByStatus.pending + stats.ordersByStatus.preparing + stats.ordersByStatus.ready}
                          </span>
                          <Badge className="bg-blue-100 text-blue-800 text-xs">
                            {stats.orderCount > 0 ? Math.round(((stats.ordersByStatus.pending + stats.ordersByStatus.preparing + stats.ordersByStatus.ready) / stats.orderCount) * 100) : 0}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${stats.orderCount > 0 ? ((stats.ordersByStatus.pending + stats.ordersByStatus.preparing + stats.ordersByStatus.ready) / stats.orderCount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* Cancelled */}
                    <div className="group hover:bg-red-50/50 p-3 rounded-lg transition-all duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span className="text-sm font-medium">Cancelled</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-bold">{stats.ordersByStatus.cancelled}</span>
                          <Badge className="bg-red-100 text-red-800 text-xs">
                            {stats.orderCount > 0 ? Math.round((stats.ordersByStatus.cancelled / stats.orderCount) * 100) : 0}%
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-red-400 to-red-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${stats.orderCount > 0 ? (stats.ordersByStatus.cancelled / stats.orderCount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                  </div>
                  <p className="text-gray-500">No data available</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Detailed Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="orders">Orders</TabsTrigger>
              <TabsTrigger value="payments">Payments</TabsTrigger>
            </TabsList>

            <TabsContent value="orders" className="mt-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Date & Time</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoadingReportData ? (
                      Array(5).fill(0).map((_, index) => (
                        <TableRow key={index}>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                        </TableRow>
                      ))
                    ) : stats?.filteredOrders && stats.filteredOrders.length > 0 ? (
                      stats.filteredOrders.map((order) => (
                        <TableRow key={order.id}>
                          <TableCell className="font-medium">#{order.orderNumber}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={cn("rounded-full py-1", orderTypeColors[order.orderType as keyof typeof orderTypeColors])}
                            >
                              {order.orderType === 'dine_in' ? 'Dine-in' :
                               order.orderType === 'takeaway' ? 'Takeaway' : 'Online'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {new Date(order.createdAt).toLocaleString()}
                          </TableCell>
                          <TableCell>₹{order.totalAmount}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={cn("rounded-full", orderStatusColors[order.status as keyof typeof orderStatusColors])}
                            >
                              {order.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                          No orders found for the selected filters
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="payments" className="mt-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Date & Time</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Payment Method</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoadingReportData ? (
                      Array(5).fill(0).map((_, index) => (
                        <TableRow key={index}>
                          <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                          <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                        </TableRow>
                      ))
                    ) : stats?.filteredOrders && stats.filteredOrders.length > 0 ? (
                      stats.filteredOrders.map((order) => (
                        <TableRow key={order.id}>
                          <TableCell className="font-medium">#{order.orderNumber}</TableCell>
                          <TableCell>
                            {new Date(order.createdAt).toLocaleString()}
                          </TableCell>
                          <TableCell>₹{order.totalAmount}</TableCell>
                          <TableCell className="capitalize">{order.paymentMethod}</TableCell>
                          <TableCell>
                            <Badge
                              variant={order.paymentStatus === 'paid' ? 'outline' : 'secondary'}
                              className={cn(
                                "rounded-full",
                                order.paymentStatus === 'paid'
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              )}
                            >
                              {order.paymentStatus}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                          No payments found for the selected filters
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
