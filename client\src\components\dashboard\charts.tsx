import { useState, useEffect, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart";
import { Skeleton } from "@/components/ui/skeleton";
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useSocketEvent } from "@/context/socket-context";
import { DashboardEvents } from "@/lib/socket";

interface RevenueChartProps {
  data?: any[];
  isLoading?: boolean;
  refreshInterval?: number;
}

export function RevenueChart({
  data,
  isLoading = false,
  refreshInterval = 30000
}: RevenueChartProps) {
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch revenue data from API
  const {
    data: revenueData,
    isLoading: isLoadingRevenue,
    error: revenueError,
    refetch: refetchRevenue
  } = useQuery({
    queryKey: ['/api/dashboard/revenue', currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop,
    refetchInterval: refreshInterval,
    retry: 1,
    onError: (error) => {
      console.error('Revenue chart query error:', error);
    },
    onSuccess: (data) => {
      console.log('Revenue chart query success:', data);
    }
  });

  // Set up socket event listener for real-time updates
  useSocketEvent(DashboardEvents.REVENUE_DATA_UPDATED, (newData) => {
    console.log('Received real-time revenue update:', newData);
    refetchRevenue();
  });

  // Debug logging
  useEffect(() => {
    console.log('=== RevenueChart Debug ===');
    console.log('revenueData:', revenueData);
    console.log('isLoadingRevenue:', isLoadingRevenue);
    console.log('token exists:', !!token);
    console.log('currentShop:', currentShop);
    console.log('query enabled:', !!token && !!currentShop);
    console.log('========================');
  }, [revenueData, isLoadingRevenue, token, currentShop]);

  // Handle manual refresh
  const handleRefresh = () => {
    if (!isRefreshing) {
      setIsRefreshing(true);
      refetchRevenue().then(() => {
        setLastUpdated(new Date());
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      });
    }
  };

  // Update last updated time when data changes
  useEffect(() => {
    if (revenueData) {
      setLastUpdated(new Date());
    }
  }, [revenueData]);

  // Format the last updated time
  const formatLastUpdated = () => {
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) {
      return `${diffSec}s ago`;
    } else if (diffSec < 3600) {
      return `${Math.floor(diffSec / 60)}m ago`;
    } else {
      return lastUpdated.toLocaleTimeString();
    }
  };

  // Sample data if actual data is not provided
  const chartData = useMemo(() => {
    if (revenueData && Array.isArray(revenueData) && revenueData.length > 0) {
      return revenueData;
    }
    if (data && Array.isArray(data) && data.length > 0) {
      return data;
    }
    // Fallback sample data
    return [
      { name: 'Mon', revenue: 12000 },
      { name: 'Tue', revenue: 18000 },
      { name: 'Wed', revenue: 15000 },
      { name: 'Thu', revenue: 22000 },
      { name: 'Fri', revenue: 24000 },
      { name: 'Sat', revenue: 30000 },
      { name: 'Sun', revenue: 28000 },
    ];
  }, [revenueData, data]);

  if (isLoading || isLoadingRevenue) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Revenue Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <Skeleton className="h-full w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-r-4 border-r-primary/50">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center">
          <CardTitle>Revenue Overview</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "ml-2 text-muted-foreground hover:text-primary",
              isRefreshing && "animate-spin text-primary"
            )}
            onClick={handleRefresh}
            disabled={isRefreshing}
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <span className="text-xs text-muted-foreground ml-2">
            Updated {formatLastUpdated()}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-64 p-2 bg-gradient-to-br from-background to-muted/30 rounded-md shadow-inner mt-3">
          {chartData && chartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsLineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₹${value.toLocaleString()}`} />
                <Tooltip formatter={(value) => [`₹${value.toLocaleString()}`, 'Revenue']} />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="hsl(var(--chart-1))"
                  strokeWidth={2}
                  dot={{ fill: 'hsl(var(--chart-1))' }}
                />
              </RechartsLineChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-4xl mb-2">📊</div>
                <p>No revenue data available</p>
                <p className="text-xs mt-2">
                  Loading: {isLoadingRevenue ? 'Yes' : 'No'} |
                  Data: {revenueData ? `${revenueData.length} items` : 'None'} |
                  Token: {token ? 'Yes' : 'No'} |
                  Shop: {currentShop?.name || 'None'}
                  {revenueError && <><br/>Error: {revenueError.message}</>}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface OrderDistributionProps {
  data?: any[];
  isLoading?: boolean;
  refreshInterval?: number;
}

export function OrderDistribution({
  data,
  isLoading = false,
  refreshInterval = 30000
}: OrderDistributionProps) {
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch order distribution data from API
  const {
    data: distributionData,
    isLoading: isLoadingDistribution,
    error: distributionError,
    refetch: refetchDistribution
  } = useQuery({
    queryKey: ['/api/dashboard/order-distribution', currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop,
    refetchInterval: refreshInterval,
    retry: 1,
    onError: (error) => {
      console.error('Order distribution chart query error:', error);
    },
    onSuccess: (data) => {
      console.log('Order distribution chart query success:', data);
    }
  });

  // Set up socket event listener for real-time updates
  useSocketEvent(DashboardEvents.ORDER_DISTRIBUTION_UPDATED, (newData) => {
    console.log('Received real-time order distribution update:', newData);
    refetchDistribution();
  });

  // Debug logging
  useEffect(() => {
    console.log('=== OrderDistribution Debug ===');
    console.log('distributionData:', distributionData);
    console.log('isLoadingDistribution:', isLoadingDistribution);
    console.log('token exists:', !!token);
    console.log('currentShop:', currentShop);
    console.log('query enabled:', !!token && !!currentShop);
    console.log('==============================');
  }, [distributionData, isLoadingDistribution, token, currentShop]);

  // Handle manual refresh
  const handleRefresh = () => {
    if (!isRefreshing) {
      setIsRefreshing(true);
      refetchDistribution().then(() => {
        setLastUpdated(new Date());
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      });
    }
  };

  // Update last updated time when data changes
  useEffect(() => {
    if (distributionData) {
      setLastUpdated(new Date());
    }
  }, [distributionData]);

  // Format the last updated time
  const formatLastUpdated = () => {
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) {
      return `${diffSec}s ago`;
    } else if (diffSec < 3600) {
      return `${Math.floor(diffSec / 60)}m ago`;
    } else {
      return lastUpdated.toLocaleTimeString();
    }
  };

  // Sample data if actual data is not provided
  const chartData = useMemo(() => {
    if (distributionData && Array.isArray(distributionData) && distributionData.length > 0) {
      return distributionData;
    }
    if (data && Array.isArray(data) && data.length > 0) {
      return data;
    }
    // Fallback sample data
    return [
      { name: 'Dine-in', value: 45 },
      { name: 'Takeaway', value: 30 },
      { name: 'Online', value: 25 },
    ];
  }, [distributionData, data]);

  if (isLoading || isLoadingDistribution) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Order Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <Skeleton className="h-full w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-r-4 border-r-secondary/50">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center">
          <CardTitle>Order Distribution</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "ml-2 text-muted-foreground hover:text-primary",
              isRefreshing && "animate-spin text-primary"
            )}
            onClick={handleRefresh}
            disabled={isRefreshing}
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <span className="text-xs text-muted-foreground ml-2">
            Updated {formatLastUpdated()}
          </span>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-64 p-2 bg-gradient-to-br from-background to-muted/30 rounded-md shadow-inner mt-3">
          {chartData && chartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  nameKey="name"
                >
                  {chartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={`hsl(var(--chart-${(index % 3) + 1}))`}
                    />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
              </RechartsPieChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <div className="text-4xl mb-2">🥧</div>
                <p>No order distribution data available</p>
                <p className="text-xs mt-2">
                  Loading: {isLoadingDistribution ? 'Yes' : 'No'} |
                  Data: {distributionData ? `${distributionData.length} items` : 'None'} |
                  Token: {token ? 'Yes' : 'No'} |
                  Shop: {currentShop?.name || 'None'}
                  {distributionError && <><br/>Error: {distributionError.message}</>}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
