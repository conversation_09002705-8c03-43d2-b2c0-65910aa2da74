import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface SelectedProduct {
  id: number;
  name: string;
  price: number;
  categoryName?: string;
  quantity?: number;
  description?: string;
  barcode?: string;
}

const STORAGE_KEY = 'selectedProducts';

// Helper function to get initial state from localStorage
const getInitialProducts = (): SelectedProduct[] => {
  try {
    const savedProducts = localStorage.getItem(STORAGE_KEY);
    console.log('Checking localStorage for key:', STORAGE_KEY, 'Value:', savedProducts);
    if (savedProducts) {
      const products = JSON.parse(savedProducts);
      console.log('✅ Initial load from localStorage successful:', products);
      return products;
    } else {
      console.log('ℹ️ No saved products found in localStorage');
    }
  } catch (error) {
    console.error('❌ Error loading initial selected products:', error);
    localStorage.removeItem(STORAGE_KEY);
  }
  return [];
};

export function useSelectedProducts() {
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>(getInitialProducts);
  const [hasInitialized, setHasInitialized] = useState(false);
  const { toast } = useToast();

  // Mark as initialized after first render
  useEffect(() => {
    setHasInitialized(true);
  }, []);

  // Save selected products to localStorage whenever they change (but not on initial load)
  useEffect(() => {
    if (hasInitialized) {
      console.log('💾 Saving selected products to localStorage:', selectedProducts);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(selectedProducts));

      // Verify it was saved
      const verification = localStorage.getItem(STORAGE_KEY);
      console.log('✅ Verification - localStorage now contains:', verification);
    } else {
      console.log('⏭️ Skipping save on initial load');
    }
  }, [selectedProducts, hasInitialized]);

  const addProduct = (product: SelectedProduct) => {
    // Check if product is already selected
    const isAlreadySelected = selectedProducts.some(p => p.id === product.id);
    
    if (!isAlreadySelected) {
      setSelectedProducts(prev => [...prev, product]);
      toast({
        title: 'Product Added',
        description: `${product.name} added to selection`,
      });
      return true;
    } else {
      toast({
        title: 'Product Already Selected',
        description: `${product.name} is already in your selection`,
        variant: 'destructive'
      });
      return false;
    }
  };

  const removeProduct = (productId: number) => {
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));
    toast({
      title: 'Product Removed',
      description: 'Product removed from selection',
    });
  };

  const clearAllProducts = () => {
    setSelectedProducts([]);
    toast({
      title: 'Selection Cleared',
      description: 'All products removed from selection',
    });
  };

  const getTotalValue = () => {
    return selectedProducts.reduce((sum, product) => sum + product.price, 0);
  };

  const getTotalCount = () => {
    return selectedProducts.length;
  };

  const isProductSelected = (productId: number) => {
    return selectedProducts.some(p => p.id === productId);
  };

  return {
    selectedProducts,
    addProduct,
    removeProduct,
    clearAllProducts,
    getTotalValue,
    getTotalCount,
    isProductSelected,
  };
}
