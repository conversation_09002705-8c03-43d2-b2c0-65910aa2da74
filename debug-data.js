// Script to debug what data is being returned
import fetch from 'node-fetch';

async function debugData() {
  try {
    // First login to get a token
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);

    if (!loginData.token) {
      console.error('No token received');
      return;
    }

    // Get the first shop and branch
    const shopId = loginData.shops[0]?.id;
    const branchId = loginData.branches[0]?.id;

    console.log(`Using shop ${shopId}, branch ${branchId}`);

    // Call debug endpoints
    console.log('\n=== DEBUGGING STOCK MOVEMENTS ===');
    const movementsDebugResponse = await fetch('http://localhost:5000/api/debug/stock-movements', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${loginData.token}`,
        'x-shop-id': shopId.toString(),
        'x-branch-id': branchId.toString()
      }
    });

    const movementsDebugData = await movementsDebugResponse.json();
    console.log('Stock movements debug data:', JSON.stringify(movementsDebugData, null, 2));

    console.log('\n=== DEBUGGING STOCK TRANSFERS ===');
    const transfersDebugResponse = await fetch('http://localhost:5000/api/debug/stock-transfers', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${loginData.token}`,
        'x-shop-id': shopId.toString(),
        'x-branch-id': branchId.toString()
      }
    });

    const transfersDebugData = await transfersDebugResponse.json();
    console.log('Stock transfers debug data:', JSON.stringify(transfersDebugData, null, 2));

    console.log('\n=== GETTING ACTUAL API DATA ===');
    
    // Get actual movements data
    const movementsResponse = await fetch('http://localhost:5000/api/stock-movements', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${loginData.token}`,
        'x-shop-id': shopId.toString(),
        'x-branch-id': branchId.toString()
      }
    });

    const movementsData = await movementsResponse.json();
    console.log('Stock movements API data:', JSON.stringify(movementsData, null, 2));

    // Get actual transfers data
    const transfersResponse = await fetch('http://localhost:5000/api/stock-transfers', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${loginData.token}`,
        'x-shop-id': shopId.toString(),
        'x-branch-id': branchId.toString()
      }
    });

    const transfersData = await transfersResponse.json();
    console.log('Stock transfers API data:', JSON.stringify(transfersData, null, 2));

  } catch (error) {
    console.error('Error:', error);
  }
}

debugData();
