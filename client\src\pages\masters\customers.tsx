import { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient, invalidateAndRefetch, logQueryCache } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useDebounce } from "@/hooks/use-debounce";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Plus,
  Search,
  Edit,
  Loader2,
  Phone,
  Mail,
  MapPin,
  Trash2,
  ChevronDown,
  ChevronUp,
  Calendar,
  DollarSign,
  Package,
  ArrowUpDown,
  Undo2,
} from "lucide-react";

// Customer form schema
const customerSchema = z.object({
  name: z.string().min(1, "Customer name is required"),
  phone: z.string()
    .length(10, "Phone number must be exactly 10 digits")
    .regex(/^\d{10}$/, "Phone number must contain only digits"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  address: z.string().optional(),
  gstNumber: z.string().optional().or(z.literal("")),
  campaignOptIn: z.boolean().default(true),
});

type CustomerFormValues = z.infer<typeof customerSchema>;

// Define Customer interface
interface Customer {
  id: number;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  gstNumber?: string;
  loyaltyPoints: number;
  loyaltyTier: string;
  lastVisitDate?: string;
  campaignOptIn: boolean;
  shopId: number;
  branchId?: number;
}

// Define Order interface
interface Order {
  id: number;
  orderNumber: string;
  orderType: string;
  status: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  createdAt: string;
  customerId: number;
}

// Define OrdersResponse interface
interface OrdersResponse {
  orders: Order[];
  total: number;
  totalPages: number;
}

export default function Customers() {
  const { token } = useAuth();
  const { toast } = useToast();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // State to force component re-render
  const [refreshKey, setRefreshKey] = useState(0);

  // State for expandable customer cards and order history
  const [expandedCustomerId, setExpandedCustomerId] = useState<number | null>(null);
  const [customerOrdersPage, setCustomerOrdersPage] = useState(1);
  const [customerOrdersPageSize] = useState(5);
  const [sortField, setSortField] = useState<'date' | 'amount'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterType, setFilterType] = useState<'all' | 'recent' | 'highSpending' | 'alphabetical'>('all');
  const [loyaltyPoints, setLoyaltyPoints] = useState<number>(0);
  const [loyaltyOperation, setLoyaltyOperation] = useState<'add' | 'subtract'>('add');

  // State for deletion confirmation and undo functionality
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [lastDeletedCustomer, setLastDeletedCustomer] = useState<Customer | null>(null);

  // Fetch customers
  const { data: customers, isLoading: isLoadingCustomers, refetch: refetchCustomers } = useQuery<Customer[]>({
    queryKey: ['/api/customers', refreshKey], // Add refreshKey to force refetch when it changes
    enabled: !!token,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Force a refetch when component mounts
  useEffect(() => {
    if (token) {
      console.log("Customers component mounted - forcing data refresh");
      // Force a refetch to ensure we have the latest data
      queryClient.removeQueries({ queryKey: ['/api/customers'] });
      refetchCustomers();

      // Log the query cache state for debugging
      logQueryCache();

      // Check for query parameters to auto-open add form
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('action') === 'add') {
        setIsAddDialogOpen(true);
      }
    }
  }, [token, refetchCustomers]);

  // Function to force a refresh of the customers data
  const forceRefresh = () => {
    console.log("Forcing refresh of customers data");
    setRefreshKey(prevKey => prevKey + 1);
    queryClient.removeQueries({ queryKey: ['/api/customers'] });
    setTimeout(() => {
      refetchCustomers();
    }, 100); // Small delay to ensure query is removed before refetching
  };

  // Log when customers data changes
  useEffect(() => {
    console.log("Customers data changed:", customers);
  }, [customers]);

  // Query for customer orders when a customer is expanded
  const {
    data: customerOrders,
    isLoading: isLoadingCustomerOrders
  } = useQuery<OrdersResponse>({
    queryKey: [
      `/api/customers/${expandedCustomerId}/orders`,
      customerOrdersPage,
      customerOrdersPageSize,
      sortField,
      sortDirection
    ],
    enabled: !!token && expandedCustomerId !== null,
  });

  // Apply filters to customers
  const filteredCustomers = customers?.filter((customer: Customer) => {
    // First apply search filter
    if (debouncedSearchTerm) {
      const search = debouncedSearchTerm.toLowerCase();
      const matchesSearch =
        customer.name.toLowerCase().includes(search) ||
        customer.phone.toLowerCase().includes(search) ||
        (customer.email && customer.email.toLowerCase().includes(search));

      if (!matchesSearch) return false;
    }

    // Then apply type filter
    switch (filterType) {
      case 'all':
        return true;
      case 'alphabetical':
        // This is just for filtering, actual sorting happens later
        return true;
      case 'recent':
        // In a real implementation, we would check if the customer has recent orders
        // For now, we'll just return all customers when this filter is selected
        return true;
      case 'highSpending':
        // In a real implementation, we would check if the customer has high-value orders
        // For now, we'll just return all customers when this filter is selected
        return true;
      default:
        return true;
    }
  });

  // Sort customers based on filter type
  const sortedCustomers = filteredCustomers ? [...filteredCustomers] : [];
  if (filterType === 'alphabetical') {
    sortedCustomers.sort((a, b) => a.name.localeCompare(b.name));
  }

  // Add customer form
  const addForm = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      address: "",
      gstNumber: "",
      campaignOptIn: true,
    },
  });

  // Edit customer form (separate instance)
  const editForm = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      address: "",
      gstNumber: "",
      campaignOptIn: true,
    },
  });

  // Reset add form
  const resetAddForm = () => {
    addForm.reset({
      name: "",
      phone: "",
      email: "",
      address: "",
      gstNumber: "",
      campaignOptIn: true,
    });
  };

  // Reset edit form
  const resetEditForm = () => {
    editForm.reset({
      name: "",
      phone: "",
      email: "",
      address: "",
      gstNumber: "",
      campaignOptIn: true,
    });
  };

  // Set form values for editing
  const setFormForEdit = (customer: Customer) => {
    editForm.reset({
      name: customer.name,
      phone: customer.phone,
      email: customer.email || "",
      address: customer.address || "",
      gstNumber: customer.gstNumber || "",
      campaignOptIn: customer.campaignOptIn,
    });
  };

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log("Creating customer with data:", data);
      const response = await apiRequest("POST", "/api/customers", data);
      console.log("Create customer response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Create customer error:", errorText);
        throw new Error(errorText);
      }
      return response;
    },
    onSuccess: async (response) => {
      console.log("Customer created successfully");

      // Get the created customer data
      const createdCustomer = await response.json();
      console.log("Created customer:", createdCustomer);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/customers');

      // Force a refresh of the customers data
      forceRefresh();

      toast({
        title: "Customer created",
        description: "Customer has been created successfully",
      });

      setIsAddDialogOpen(false);
      resetAddForm();
    },
    onError: (error) => {
      console.error("Failed to create customer:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create customer",
      });
    },
  });

  // Update customer mutation
  const updateCustomerMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      console.log(`Updating customer ${id} with data:`, data);
      const response = await apiRequest("PATCH", `/api/customers/${id}`, data);
      console.log("Update customer response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Update customer error:", errorText);
        throw new Error(errorText);
      }
      return response;
    },
    onSuccess: async (response) => {
      console.log("Customer updated successfully");

      // Get the updated customer data
      const updatedCustomer = await response.json();
      console.log("Updated customer:", updatedCustomer);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/customers');

      // Force a refresh of the customers data
      forceRefresh();

      toast({
        title: "Customer updated",
        description: "Customer has been updated successfully",
      });

      setIsEditDialogOpen(false);
      setSelectedCustomer(null);
    },
    onError: (error) => {
      console.error("Failed to update customer:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update customer",
      });
    },
  });

  // Update loyalty points mutation
  const updateLoyaltyPointsMutation = useMutation({
    mutationFn: async ({ id, points, operation }: { id: number; points: number; operation: 'add' | 'subtract' }) => {
      console.log(`Updating loyalty points for customer ${id}: ${operation} ${points} points`);
      const response = await apiRequest("PATCH", `/api/customers/${id}/loyalty`, { points, operation });
      console.log("Update loyalty points response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Update loyalty points error:", errorText);
        throw new Error(errorText);
      }
      return response;
    },
    onSuccess: async (response) => {
      console.log("Loyalty points updated successfully");

      // Get the updated customer data
      const updatedCustomer = await response.json();
      console.log("Updated customer with loyalty points:", updatedCustomer);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/customers');

      // Force a refresh of the customers data
      forceRefresh();

      toast({
        title: "Loyalty points updated",
        description: "Customer loyalty points have been updated successfully",
      });

      // Reset loyalty points input
      setLoyaltyPoints(0);
    },
    onError: (error) => {
      console.error("Failed to update loyalty points:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update loyalty points",
      });
    },
  });



  // Delete customer mutation
  const deleteCustomerMutation = useMutation({
    mutationFn: async (id: number) => {
      console.log(`Deleting customer ${id}`);
      const response = await apiRequest("DELETE", `/api/customers/${id}`);
      console.log("Delete customer response:", response);
      if (!response.ok) {
        const errorData = await response.json();
        console.error("Delete customer error:", errorData);
        throw new Error(errorData.message || "Failed to delete customer");
      }
      return response;
    },
    onSuccess: async (response, id) => {
      console.log("Customer deleted successfully");

      // Get the deleted customer data
      const deletedCustomer = await response.json();
      console.log("Deleted customer:", deletedCustomer);

      // Store the deleted customer for potential undo
      setLastDeletedCustomer(deletedCustomer);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/customers');

      // Force a refresh of the customers data
      forceRefresh();

      // Show toast with undo option
      toast({
        title: "Customer deleted",
        description: "Customer has been deleted successfully",
        action: (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleUndoDelete(deletedCustomer)}
            className="gap-1"
          >
            <Undo2 className="h-3 w-3" />
            Undo
          </Button>
        ),
      });

      setIsDeleteDialogOpen(false);
      setCustomerToDelete(null);
    },
    onError: (error) => {
      console.error("Failed to delete customer:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete customer",
      });
      setIsDeleteDialogOpen(false);
    },
  });

  // Handle undo delete
  const handleUndoDelete = (customer: Customer) => {
    if (!customer) return;

    // Re-create the customer
    createCustomerMutation.mutate({
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address
    });

    setLastDeletedCustomer(null);
  };

  // Form submission handlers
  const handleCreateCustomer = (data: CustomerFormValues) => {
    console.log("Creating customer with data:", data);
    createCustomerMutation.mutate(data);
  };

  const handleUpdateCustomer = (data: CustomerFormValues) => {
    if (!selectedCustomer) return;

    console.log("Updating customer with data:", data);
    updateCustomerMutation.mutate({
      id: selectedCustomer.id,
      data: data
    });
  };

  const handleUpdateLoyaltyPoints = (customerId: number) => {
    if (loyaltyPoints <= 0) {
      toast({
        variant: "destructive",
        title: "Invalid points",
        description: "Please enter a positive number of points",
      });
      return;
    }

    updateLoyaltyPointsMutation.mutate({
      id: customerId,
      points: loyaltyPoints,
      operation: loyaltyOperation
    });
  };

  // Loading state
  const isLoading =
    isLoadingCustomers ||
    isLoadingCustomerOrders ||
    createCustomerMutation.isPending ||
    updateCustomerMutation.isPending ||
    deleteCustomerMutation.isPending ||
    updateLoyaltyPointsMutation.isPending;



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Customers</h1>
        <Sheet open={isAddDialogOpen} onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            // If opening add modal, close edit modal if it's open
            if (open && isEditDialogOpen) {
              setIsEditDialogOpen(false);
              setSelectedCustomer(null);
            }
            // If closing, reset the form
            if (!open) {
              resetAddForm();
            }
          }}>
          <SheetTrigger asChild>
            <Button onClick={() => {
              // Close edit modal if it's open when clicking Add Customer
              if (isEditDialogOpen) {
                setIsEditDialogOpen(false);
                setSelectedCustomer(null);
              }
            }}>
              <Plus className="h-4 w-4 mr-2" />
              Add Customer
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <SheetHeader>
              <SheetTitle>Add New Customer</SheetTitle>
              <SheetDescription>
                Add a new customer to your database
              </SheetDescription>
            </SheetHeader>

            <Form {...addForm}>
              <form onSubmit={addForm.handleSubmit(handleCreateCustomer)} className="space-y-4">
                <FormField
                  control={addForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Customer Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter customer name" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter phone number (10 digits)"
                          {...field}
                          disabled={isLoading}
                          type="tel"
                          maxLength={10}
                          onInput={(e) => {
                            // Allow only numeric input
                            const target = e.target as HTMLInputElement;
                            target.value = target.value.replace(/[^0-9]/g, '');
                            field.onChange(target.value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter email address" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address (Optional)</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Enter address" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="gstNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>GST Number (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter GST number" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="campaignOptIn"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Opt-in for Marketing Campaigns
                        </FormLabel>
                        <FormDescription>
                          Allow sending promotional messages and offers
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <SheetFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false);
                      resetAddForm();
                    }}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {createCustomerMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Customer"
                    )}
                  </Button>
                </SheetFooter>
              </form>
            </Form>
          </SheetContent>
        </Sheet>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Customer List</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative w-full md:max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search customers..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Select
                value={filterType}
                onValueChange={(value: 'all' | 'recent' | 'highSpending' | 'alphabetical') => setFilterType(value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Customers</SelectItem>
                  <SelectItem value="recent">Recent Customers</SelectItem>
                  <SelectItem value="highSpending">High-Spending</SelectItem>
                  <SelectItem value="alphabetical">Alphabetical</SelectItem>
                </SelectContent>
              </Select>

              {filterType !== 'all' && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setFilterType('all')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                </Button>
              )}
            </div>
          </div>

          {/* Customers Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead className="hidden md:table-cell">Email</TableHead>
                  <TableHead className="hidden md:table-cell">GST Number</TableHead>
                  <TableHead className="hidden lg:table-cell">Loyalty</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingCustomers ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell className="hidden lg:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : sortedCustomers && sortedCustomers.length > 0 ? (
                  sortedCustomers.map((customer: Customer) => (
                    <TableRow
                      key={customer.id}
                      className={expandedCustomerId === customer.id ? "bg-muted/50" : ""}
                    >
                      <TableCell
                        className="font-medium cursor-pointer"
                        onClick={() => {
                          if (expandedCustomerId === customer.id) {
                            setExpandedCustomerId(null);
                          } else {
                            setExpandedCustomerId(customer.id);
                            setCustomerOrdersPage(1);
                          }
                        }}
                      >
                        <div className="flex items-center">
                          {customer.name}
                          {expandedCustomerId === customer.id ? (
                            <ChevronUp className="h-4 w-4 ml-2" />
                          ) : (
                            <ChevronDown className="h-4 w-4 ml-2" />
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          {customer.phone}
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {customer.email ? (
                          <div className="flex items-center">
                            <Mail className="h-4 w-4 mr-2 text-gray-500" />
                            {customer.email}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {customer.gstNumber ? (
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2 text-gray-500"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/></svg>
                            <span>{customer.gstNumber}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2 text-gray-500"><path d="M12 2l2.4 7.4H22l-6 4.6 2.3 7-6.3-4.6L5.7 21l2.3-7-6-4.6h7.6L12 2z"/></svg>
                          <div className="flex flex-col">
                            <span className="font-medium">{customer.loyaltyTier.charAt(0).toUpperCase() + customer.loyaltyTier.slice(1)}</span>
                            <span className="text-xs text-gray-500">{customer.loyaltyPoints} points</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedCustomer(customer);
                              setFormForEdit(customer);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setCustomerToDelete(customer);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                      No customers found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the customer{' '}
              <span className="font-semibold">{customerToDelete?.name}</span>.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (customerToDelete) {
                  deleteCustomerMutation.mutate(customerToDelete.id);
                }
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteCustomerMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Customer Loyalty Management */}
      {expandedCustomerId !== null && (
        <Card className="mt-4">
          <CardHeader className="pb-3">
            <CardTitle>Loyalty Management</CardTitle>
            <CardDescription>
              Manage customer loyalty points and tier status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {sortedCustomers && expandedCustomerId && (
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row justify-between gap-4">
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Current Points</span>
                    <span className="text-2xl font-bold">
                      {sortedCustomers.find(c => c.id === expandedCustomerId)?.loyaltyPoints || 0}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Loyalty Tier</span>
                    <span className="text-2xl font-bold capitalize">
                      {sortedCustomers.find(c => c.id === expandedCustomerId)?.loyaltyTier || 'Standard'}
                    </span>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm text-muted-foreground">Last Visit</span>
                    <span className="text-lg">
                      {sortedCustomers.find(c => c.id === expandedCustomerId)?.lastVisitDate
                        ? new Date(sortedCustomers.find(c => c.id === expandedCustomerId)?.lastVisitDate as string).toLocaleDateString()
                        : 'Never'}
                    </span>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="text-sm font-medium mb-2">Update Loyalty Points</h3>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <div className="flex-1">
                      <Input
                        type="number"
                        min="1"
                        placeholder="Enter points"
                        value={loyaltyPoints || ''}
                        onChange={(e) => setLoyaltyPoints(parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <Select
                      value={loyaltyOperation}
                      onValueChange={(value: 'add' | 'subtract') => setLoyaltyOperation(value)}
                    >
                      <SelectTrigger className="w-[120px]">
                        <SelectValue placeholder="Operation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="add">Add</SelectItem>
                        <SelectItem value="subtract">Subtract</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={() => handleUpdateLoyaltyPoints(expandedCustomerId)}
                      disabled={loyaltyPoints <= 0 || updateLoyaltyPointsMutation.isPending}
                    >
                      {updateLoyaltyPointsMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        "Update Points"
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Customer Order History */}
      {expandedCustomerId !== null && (
        <Card className="mt-4">
          <CardHeader className="pb-3">
            <CardTitle className="flex justify-between items-center">
              <span>Order History</span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSortField('date');
                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                  }}
                  className="text-xs h-8 gap-1"
                >
                  Date
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSortField('amount');
                    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                  }}
                  className="text-xs h-8 gap-1"
                >
                  Amount
                  <ArrowUpDown className="h-3 w-3" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingCustomerOrders ? (
              <div className="space-y-3">
                {Array(3).fill(0).map((_, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <div className="mt-2">
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </div>
                ))}
              </div>
            ) : customerOrders && customerOrders.orders.length > 0 ? (
              <div className="space-y-3">
                {customerOrders.orders.map((order) => (
                  <div key={order.id} className="border rounded-md p-4 hover:bg-muted/50 transition-colors">
                    <div className="flex justify-between">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium">
                          ₹{order.totalAmount.toFixed(2)}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 flex justify-between">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          Order #{order.orderNumber}
                        </span>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        order.status === 'completed' ? 'bg-green-100 text-green-800' :
                        order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No order history found for this customer
              </div>
            )}

            {/* Pagination */}
            {customerOrders && customerOrders.totalPages > 1 && (
              <Pagination className="mt-4">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCustomerOrdersPage(prev => Math.max(prev - 1, 1))}
                      className={customerOrdersPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {Array.from({ length: customerOrders.totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCustomerOrdersPage(page)}
                        isActive={page === customerOrdersPage}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCustomerOrdersPage(prev => Math.min(prev + 1, customerOrders.totalPages))}
                      className={customerOrdersPage === customerOrders.totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </CardContent>
        </Card>
      )}

      {/* Edit Customer Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        // If closing edit modal, reset selected customer
        if (!open) {
          setSelectedCustomer(null);
          resetEditForm();
        }
        // If opening edit modal, close add modal if it's open
        if (open && isAddDialogOpen) {
          setIsAddDialogOpen(false);
          resetAddForm();
        }
      }}>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Edit Customer</SheetTitle>
            <SheetDescription>
              Make changes to the customer details
            </SheetDescription>
          </SheetHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdateCustomer)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter customer name" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter phone number (10 digits)"
                        {...field}
                        disabled={isLoading}
                        type="tel"
                        maxLength={10}
                        onInput={(e) => {
                          // Allow only numeric input
                          const target = e.target as HTMLInputElement;
                          target.value = target.value.replace(/[^0-9]/g, '');
                          field.onChange(target.value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter email address" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address (Optional)</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter address" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="gstNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>GST Number (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter GST number" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="campaignOptIn"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        Opt-in for Marketing Campaigns
                      </FormLabel>
                      <FormDescription>
                        Allow sending promotional messages and offers
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <SheetFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setSelectedCustomer(null);
                    resetEditForm();
                  }}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {updateCustomerMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Customer"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
