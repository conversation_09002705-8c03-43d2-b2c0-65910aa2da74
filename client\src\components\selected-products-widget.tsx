import React, { useState } from 'react';
import { ShoppingCart, X, Trash2, Users, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSelectedProducts } from '@/hooks/use-selected-products';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/auth-context';
import { useApp } from '@/context/app-context';
import { apiRequest } from '@/lib/queryClient';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface SelectedProductsWidgetProps {
  className?: string;
  showTitle?: boolean;
  compact?: boolean;
  showTableAssignment?: boolean;
  onTableAssign?: (tableId: number, products: any[]) => void;
}

export function SelectedProductsWidget({
  className = "",
  showTitle = true,
  compact = false,
  showTableAssignment = false,
  onTableAssign
}: SelectedProductsWidgetProps) {
  const {
    selectedProducts,
    removeProduct,
    clearAllProducts,
    getTotalValue,
    getTotalCount
  } = useSelectedProducts();

  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();

  // Fetch tables data
  const { data: tables, isLoading: isLoadingTables, error: tablesError } = useQuery({
    queryKey: ['/api/tables', currentShop?.id, currentBranch?.id],
    queryFn: async () => {
      console.log('🪑 Fetching tables for selected products widget...');
      const response = await apiRequest('GET', '/api/tables');
      if (!response.ok) {
        throw new Error('Failed to fetch tables');
      }
      const tablesData = await response.json();
      console.log('🪑 Tables fetched:', tablesData);
      return tablesData;
    },
    enabled: !!token && !!currentShop && showTableAssignment,
  });

  // Don't render if no products selected
  if (selectedProducts.length === 0) {
    return null;
  }

  const handleTableAssign = (tableId: number) => {
    console.log('🪑 Table assignment triggered:', { tableId, selectedProducts });
    if (onTableAssign) {
      console.log('🪑 Calling onTableAssign callback...');
      onTableAssign(tableId, selectedProducts);
    } else {
      console.log('❌ No onTableAssign callback provided');
    }
  };

  // Get available tables for dropdown
  const availableTables = tables || [];

  // Debug logging - UPDATED VERSION
  console.log('🪑 SelectedProductsWidget state (v2.0):', {
    showTableAssignment,
    isLoadingTables,
    tablesError,
    availableTables: availableTables.length,
    selectedProducts: selectedProducts.length,
    token: !!token,
    currentShop: !!currentShop,
    timestamp: new Date().toLocaleTimeString()
  });

  // Force log when showTableAssignment is true
  if (showTableAssignment) {
    console.log('🎯 TABLE ASSIGNMENT IS ENABLED - Component should show table selection UI');
  }

  if (compact) {
    return (
      <Card className={`${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <ShoppingCart className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">
                {getTotalCount()} products selected
              </span>
              <Badge variant="secondary" className="text-xs">
                ${getTotalValue().toFixed(2)}
              </Badge>
            </div>
            <div className="flex items-center space-x-1">
              {showTableAssignment && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-2 text-green-600 hover:text-green-700"
                    >
                      <Users className="h-3 w-3 mr-1" />
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {isLoadingTables ? (
                      <DropdownMenuItem disabled>
                        Loading tables...
                      </DropdownMenuItem>
                    ) : availableTables.length > 0 ? (
                      availableTables.map((table: any) => (
                        <DropdownMenuItem
                          key={table.id}
                          onClick={() => handleTableAssign(table.id)}
                          className="flex items-center justify-between"
                        >
                          <span>{table.name}</span>
                          <Badge
                            variant={table.status === 'available' ? 'default' : 'secondary'}
                            className="text-xs ml-2"
                          >
                            {table.status}
                          </Badge>
                        </DropdownMenuItem>
                      ))
                    ) : (
                      <DropdownMenuItem disabled>
                        No tables available
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllProducts}
                className="text-red-600 hover:text-red-700 h-8 px-2"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      {showTitle && (
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <ShoppingCart className="mr-2 h-5 w-5" />
              Selected Products
              <Badge variant="secondary" className="ml-2">
                {getTotalCount()}
              </Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllProducts}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="mr-1 h-4 w-4" />
              Clear All
            </Button>
          </CardTitle>
          <CardDescription>
            Products selected from Quick Product Search
          </CardDescription>
        </CardHeader>
      )}
      <CardContent>
        <div className="space-y-3">
          {selectedProducts.map((product, index) => (
            <div
              key={`${product.id}-${index}`}
              className="flex items-center justify-between p-3 border rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-sm">
                    {product.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{product.name}</h4>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span>${product.price.toFixed(2)}</span>
                    {product.categoryName && (
                      <Badge variant="outline" className="text-xs">
                        {product.categoryName}
                      </Badge>
                    )}
                    {product.quantity !== undefined && (
                      <Badge 
                        variant={product.quantity > 0 ? "default" : "destructive"} 
                        className="text-xs"
                      >
                        Stock: {product.quantity}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeProduct(product.id)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Total Products:</span>
            <span className="font-semibold">{getTotalCount()}</span>
          </div>
          <div className="flex justify-between items-center text-sm mt-1">
            <span className="text-gray-600">Total Value:</span>
            <span className="font-semibold text-green-600">
              ${getTotalValue().toFixed(2)}
            </span>
          </div>

          {/* Table Assignment for Full View */}
          {showTableAssignment && (
            <div className="mt-4 pt-4 border-t bg-green-50 rounded-lg p-4 border-2 border-green-300">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <span className="text-sm font-medium text-green-800">🎯 Ready to Create Order!</span>
                  <p className="text-xs text-green-600 mt-1">Select a table to assign these {selectedProducts.length} products</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => {
                      console.log('🧪 TEST BUTTON CLICKED');
                      alert('Test button works! Tables available: ' + availableTables.length);
                    }}
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-200"
                  >
                    🧪 Test
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="default"
                        size="lg"
                        className="bg-green-600 hover:bg-green-700 text-white shadow-lg border-2 border-green-400 animate-pulse"
                        disabled={isLoadingTables}
                      >
                        <Users className="h-5 w-5 mr-2" />
                        {isLoadingTables ? '🔄 Loading...' : '🪑 SELECT TABLE'}
                        <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      {isLoadingTables ? (
                        <DropdownMenuItem disabled>
                          🔄 Loading tables...
                        </DropdownMenuItem>
                      ) : tablesError ? (
                        <DropdownMenuItem disabled>
                          ❌ Error loading tables
                        </DropdownMenuItem>
                      ) : availableTables.length > 0 ? (
                        availableTables.map((table: any) => (
                          <DropdownMenuItem
                            key={table.id}
                            onClick={() => handleTableAssign(table.id)}
                            className="flex items-center justify-between cursor-pointer hover:bg-green-50"
                          >
                            <span className="font-medium">{table.name}</span>
                            <Badge
                              variant={table.status === 'available' ? 'default' : 'secondary'}
                              className="text-xs ml-2"
                            >
                              {table.status}
                            </Badge>
                          </DropdownMenuItem>
                        ))
                      ) : (
                        <DropdownMenuItem disabled>
                          ❌ No tables available
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <div className="mt-3 p-2 bg-white rounded border border-green-200">
                <p className="text-xs text-green-700 font-medium">
                  💡 Click "Select Table" above to assign these {getTotalCount()} products to a table and create an order
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
