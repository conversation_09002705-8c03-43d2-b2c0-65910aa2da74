﻿import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";

import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { BranchSelector } from "@/components/branch-selector";
import { apiRequest } from '@/lib/queryClient';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { cn } from "@/lib/utils";
import { Printer, File, FileText, FileDown, Search, FilterX, AlertTriangle, ChevronsLeft, ChevronsRight } from "lucide-react";

interface InventoryItem {
  id: number;
  name: string;
  sku: string;
  category: string;
  quantity: number;
  price: number;
  categoryId?: number;
  reorderLevel?: number;
  barcode?: string;
  branchId?: number;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface InventoryStats {
  totalProducts: number;
  totalValue: number;
  lowStockCount: number;
  outOfStockCount: number;
  availableCount: number;
}

interface InventoryReport {
  products: InventoryItem[];
  categories: any[];
  pagination: PaginationInfo;
  stats: InventoryStats;
}

interface ColumnConfig {
  header: string;
  cell?: (item: InventoryItem) => React.ReactNode;
}

const columns: Record<string, ColumnConfig> = {
  productName: {
    header: "Product Name",
    cell: (item: InventoryItem) => item.name
  },
  sku: {
    header: "SKU",
    cell: (item: InventoryItem) => item.sku
  },
  category: {
    header: "Category",
    cell: (item: InventoryItem) => item.category
  },
  quantity: {
    header: "Quantity",
    cell: (item: InventoryItem) => item.quantity.toString()
  },
  price: {
    header: "Price",
    cell: (item: InventoryItem) => `${item.price.toFixed(2)}`
  },
  totalValue: {
    header: "Total Value",
    cell: (item: InventoryItem) => `${(item.quantity * item.price).toFixed(2)}`
  },
  stockStatus: {
    header: "Stock Status",
    cell: (item: InventoryItem) => {
      if (item.quantity <= 0) return "Out of Stock";
      if (item.quantity <= 10) return "Low Stock";
      return "In Stock";
    }
  }
};

export default function InventoryReport() {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const { toast } = useToast();

  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [stockFilter, setStockFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [branchFilter, setBranchFilter] = useState<number | null>(currentBranch?.id || null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(25);
  // Fetch products with pagination
  const { data: inventoryReport, isLoading } = useQuery<InventoryReport>({
    queryKey: ['/api/reports/inventory', currentShop?.id, branchFilter, categoryFilter, stockFilter, searchQuery, currentPage, pageSize],
    queryFn: async () => {
      // Token is available from useAuth hook
      if (!token) throw new Error("Not authenticated");

      // Build query parameters
      const params = new URLSearchParams();
      if (branchFilter) params.append('branchId', branchFilter.toString());
      if (categoryFilter !== 'all') params.append('categoryId', categoryFilter);
      if (stockFilter !== 'all') params.append('stockStatus', stockFilter);
      if (searchQuery) params.append('search', searchQuery);
      params.append('page', currentPage.toString());
      params.append('pageSize', pageSize.toString());

      const response = await apiRequest("GET", `/api/reports/inventory?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Failed to fetch inventory data");
      }
      const data = await response.json();
      return data || {
        products: [],
        categories: [],
        pagination: { page: 1, pageSize: 25, totalCount: 0, totalPages: 0, hasNextPage: false, hasPreviousPage: false },
        stats: { totalProducts: 0, lowStockCount: 0, outOfStockCount: 0, totalValue: 0, availableCount: 0 }
      };
    }
  });

  // Fetch categories for filter
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['/api/categories', currentShop?.id],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/categories");
      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }
      const data = await response.json();
      return data || [];
    },
    enabled: !!token,
    retry: 1
  });
  // Loading states are managed by React Query

  // Get stats and products directly from API response
  const stats = inventoryReport?.stats || {
    totalProducts: 0,
    totalValue: 0,
    lowStockCount: 0,
    outOfStockCount: 0,
    availableCount: 0
  };

  const products = inventoryReport?.products || [];
  const pagination = inventoryReport?.pagination || {
    page: 1,
    pageSize: 25,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: string) => {
    setPageSize(parseInt(size));
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Reset filters
  const handleResetFilters = () => {
    setCategoryFilter("all");
    setStockFilter("all");
    setSearchQuery("");
    setBranchFilter(currentBranch?.id || null);
    setCurrentPage(1); // Reset to first page when resetting filters
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [categoryFilter, stockFilter, searchQuery, branchFilter]);

  // Handle print and export
  const handlePrint = () => {
    toast({
      title: "Print report",
      description: "Printing inventory report...",
    });
    window.print();
  };

  // Format data for export
  const getFormattedData = () => {
    if (!products || products.length === 0) {
      return null;
    }

    return formatDataForExport(products, {
      price: (value) => `${parseFloat(value).toFixed(2)}`,
      totalValue: (value, item) => `${(item.quantity * item.price).toFixed(2)}`,
      stockStatus: (_: unknown, item: InventoryItem) => {
        if (item.quantity <= 0) return "Out of Stock";
        if (item.quantity <= (item.reorderLevel || 10)) return "Low Stock";
        return "In Stock";
      }
    });
  };

  // Get file name for exports
  const getExportFileName = () => {
    const branchName = branchFilter
      ? `_${currentBranch?.name?.replace(/\s+/g, '_') || 'Branch'}`
      : '_All_Branches';

    return `Inventory_Report${branchName}_${new Date().toISOString().split('T')[0]}`;
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to Excel
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Inventory report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to CSV
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Inventory report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    if (!products || products.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(18);
      doc.text("Inventory Report", 14, 22);

      // Add branch information
      doc.setFontSize(12);
      const branchText = branchFilter
        ? `Branch: ${currentBranch?.name || 'Selected Branch'}`
        : "Branch: All Branches";
      doc.text(branchText, 14, 32);

      // Add date information
      doc.text(`Date: ${new Date().toLocaleDateString()}`, 14, 42);

      // Add summary information
      doc.text(`Total Products: ${stats.totalProducts}`, 14, 52);
      doc.text(`Total Inventory Value: ${stats.totalValue.toFixed(2)}`, 14, 62);
      doc.text(`Low Stock Items: ${stats.lowStockCount}`, 14, 72);
      doc.text(`Out of Stock Items: ${stats.outOfStockCount}`, 14, 82);

      // Add inventory table
      const tableData = products.map(product => [
        product.name,
        product.sku || 'N/A',
        product.quantity.toString(),
        `${product.price.toFixed(2)}`,
        `${(product.quantity * product.price).toFixed(2)}`,
        (product.quantity || 0) <= 0 ? 'Out of Stock' :
          (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0 ? 'Low Stock' : 'In Stock'
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: 92,
        head: [['Product Name', 'SKU', 'Quantity', 'Unit Price', 'Total Value', 'Status']],
        body: tableData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 9 },
        columnStyles: {
          0: { cellWidth: 50 },
          1: { cellWidth: 25 },
          2: { cellWidth: 20 },
          3: { cellWidth: 25 },
          4: { cellWidth: 30 },
          5: { cellWidth: 25 }
        }
      });

      // Save the PDF
      doc.save(`${getExportFileName()}.pdf`);

      toast({
        title: "Export successful",
        description: "Inventory report has been exported to PDF",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the report to PDF",
      });
    }
  };

  // Stock status badge configurations
  const stockStatusColors = {
    outOfStock: "bg-red-100 text-red-800",
    lowStock: "bg-yellow-100 text-yellow-800",
    inStock: "bg-green-100 text-green-800"
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Inventory Report</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={handleExportToExcel}>
            <File className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={handleExportToCSV}>
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={handleExportToPDF}>
            <FileDown className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Report Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Branch</label>
              <div className="w-full">
                <BranchSelector
                  variant="outline"
                  className="w-full"
                  onChange={(branchId) => setBranchFilter(branchId)}
                  value={branchFilter}
                  allowAllBranches={true}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Category</label>
              <Select
                value={categoryFilter}
                onValueChange={setCategoryFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  {categories && categories.map((category: any) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Stock Status</label>
              <Select
                value={stockFilter}
                onValueChange={setStockFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All stock" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All stock</SelectItem>
                  <SelectItem value="low">Low stock</SelectItem>
                  <SelectItem value="out">Out of stock</SelectItem>
                  <SelectItem value="available">Available</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Search</label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/5 flex items-end">
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResetFilters}
              >
                <FilterX className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Products</span>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {stats?.totalProducts || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Inventory Value</span>
              {isLoading ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {stats?.totalValue.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Low Stock Items</span>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <div className="flex items-center mt-1">
                  <span className="text-2xl font-semibold">
                    {stats?.lowStockCount || 0}
                  </span>
                  {stats && stats.lowStockCount > 0 && (
                    <AlertTriangle className="h-5 w-5 ml-2 text-yellow-500" />
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Out of Stock Items</span>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <div className="flex items-center mt-1">
                  <span className="text-2xl font-semibold">
                    {stats?.outOfStockCount || 0}
                  </span>
                  {stats && stats.outOfStockCount > 0 && (
                    <AlertTriangle className="h-5 w-5 ml-2 text-red-500" />
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stock Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Status Overview</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : stats ? (
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">In Stock</span>
                  <span className="text-sm font-medium">
                    {stats.availableCount} / {stats.totalProducts} ({stats.totalProducts > 0 ? Math.round((stats.availableCount / stats.totalProducts) * 100) : 0}%)
                  </span>
                </div>
                <Progress value={stats.totalProducts > 0 ? (stats.availableCount / stats.totalProducts) * 100 : 0} className="h-2 bg-gray-100" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Low Stock</span>
                  <span className="text-sm font-medium">
                    {stats.lowStockCount} / {stats.totalProducts} ({stats.totalProducts > 0 ? Math.round((stats.lowStockCount / stats.totalProducts) * 100) : 0}%)
                  </span>
                </div>
                <Progress 
                  value={stats.totalProducts > 0 ? (stats.lowStockCount / stats.totalProducts) * 100 : 0} 
                  className="h-2 w-full bg-gray-100" 
                  progressBarClassName="bg-yellow-500" 
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Out of Stock</span>
                  <span className="text-sm font-medium">
                    {stats.outOfStockCount} / {stats.totalProducts} ({stats.totalProducts > 0 ? Math.round((stats.outOfStockCount / stats.totalProducts) * 100) : 0}%)
                  </span>
                </div>
                <Progress 
                  value={stats.totalProducts > 0 ? (stats.outOfStockCount / stats.totalProducts) * 100 : 0} 
                  className="h-2 w-full bg-gray-100" 
                  progressBarClassName="bg-red-500" 
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-32">
              <p className="text-gray-500">No inventory data available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle>Inventory Details</CardTitle>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Items per page:</span>
              <Select
                value={pageSize.toString()}
                onValueChange={handlePageSizeChange}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {pagination.totalCount > 0 && (
              <div className="text-sm text-gray-600">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} items
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product Name</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                    </TableRow>
                  ))
                ) : products && products.length > 0 ? (
                  products.map((product) => {
                    const categoryName = categories?.find((c: any) => c.id === product.categoryId)?.name || 'Uncategorized';
                    const stockStatus = (product.quantity || 0) <= 0 ? 'outOfStock' :
                                       (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0 ? 'lowStock' : 'inStock';
                    const stockLabel = stockStatus === 'outOfStock' ? 'Out of Stock' :
                                      stockStatus === 'lowStock' ? 'Low Stock' : 'In Stock';

                    return (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>{product.sku || 'N/A'}</TableCell>
                        <TableCell>{categoryName}</TableCell>
                        <TableCell>{product.quantity}</TableCell>
                        <TableCell>{product.price.toFixed(2)}</TableCell>
                        <TableCell>{(product.quantity * product.price).toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={cn("rounded-full", stockStatusColors[stockStatus])}
                          >
                            {stockLabel}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-gray-500">
                      No products found for the selected filters
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          {!isLoading && pagination.totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(1)}
                      className={pagination.page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </PaginationLink>
                  </PaginationItem>

                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, pagination.page - 1))}
                      className={pagination.page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={pagination.page === pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(pagination.totalPages, pagination.page + 1))}
                      className={pagination.page === pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(pagination.totalPages)}
                      className={pagination.page === pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </PaginationLink>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

