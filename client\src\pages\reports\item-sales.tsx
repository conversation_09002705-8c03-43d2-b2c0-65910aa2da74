import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";
import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { BranchSelector } from "@/components/branch-selector";
import { apiRequest } from '@/lib/queryClient';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { BarChart } from "@/components/ui/chart";

import {
  Download,
  Printer,
  Calendar,
  FilterX,
  Search,
  CalendarRange,
  File,
  FileDown,
  FileText,
  Building2,
  TrendingUp,
  ShoppingBag,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

export default function ItemSalesReport() {
  const { token } = useAuth();
  const { currentBranch } = useApp();
  const { toast } = useToast();

  const [period, setPeriod] = useState<string>("today");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("quantity");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [branchFilter, setBranchFilter] = useState<number | null>(currentBranch?.id || null);

  // Fetch orders from API
  const { data: orders = [], isLoading: isLoadingOrders } = useQuery({
    queryKey: ['/api/orders'],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/orders");
      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }
      const data = await response.json();
      return data || [];
    },
    enabled: !!token,
    retry: 1
  });

  // Fetch products from API
  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products'],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/products");
      if (!response.ok) {
        throw new Error("Failed to fetch products");
      }
      const data = await response.json();
      return data || [];
    },
    enabled: !!token,
    retry: 1
  });

  // Fetch categories from API
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['/api/categories'],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/categories");
      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }
      const data = await response.json();
      return data || [];
    },
    enabled: !!token,
    retry: 1
  });

  // Calculate item sales stats
  const calculateItemSalesStats = () => {
    // Check if data is available
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      console.log('No orders data available for item sales report');
      return {
        items: [],
        totalQuantity: 0,
        totalSales: 0,
        totalProducts: 0
      };
    }

    if (!products || !Array.isArray(products)) {
      console.log('No products data available for item sales report');
      return {
        items: [],
        totalQuantity: 0,
        totalSales: 0,
        totalProducts: 0
      };
    }

    console.log('Calculating item sales stats with', orders.length, 'orders and', products.length, 'products');

    // Filter orders based on selected period
    let filteredOrders = [...orders];

    // Filter by branch
    if (branchFilter) {
      filteredOrders = filteredOrders.filter(
        (order) => order.branchId === branchFilter
      );
    }

    // Filter by date range
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // End of day

      filteredOrders = filteredOrders.filter((order) => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= start && orderDate <= end;
      });
    } else if (period !== "custom") {
      // Filter by predefined period
      const now = new Date();
      let periodStart = new Date();

      if (period === "today") {
        periodStart.setHours(0, 0, 0, 0);
      } else if (period === "yesterday") {
        periodStart.setDate(periodStart.getDate() - 1);
        periodStart.setHours(0, 0, 0, 0);
      } else if (period === "week") {
        periodStart.setDate(periodStart.getDate() - 7);
      } else if (period === "month") {
        periodStart.setMonth(periodStart.getMonth() - 1);
      }

      filteredOrders = filteredOrders.filter(
        (order) => new Date(order.createdAt) >= periodStart
      );
    }

    console.log('Filtered orders:', filteredOrders.length);

    // Extract all order items
    const allOrderItems: any[] = [];
    filteredOrders.forEach(order => {
      if (order.items && Array.isArray(order.items)) {
        order.items.forEach(item => {
          allOrderItems.push({
            ...item,
            orderDate: order.createdAt,
            orderNumber: order.orderNumber || order.id,
            orderStatus: order.status
          });
        });
      }
    });

    console.log('Extracted order items:', allOrderItems.length);

    // If no order items are available and we're in development, create sample data
    if (allOrderItems.length === 0 && process.env.NODE_ENV !== 'production') {
      console.log('No order items found, creating sample data for demonstration');

      // Create sample data for demonstration
      const sampleProducts = products && products.length > 0 ?
        products.slice(0, 10) :
        Array(10).fill(0).map((_, index) => ({
          id: index + 1,
          name: `Sample Product ${index + 1}`,
          categoryId: index % 3 + 1,
          price: 100 + (index * 10)
        }));

      const itemsByProduct: Record<string, any> = {};

      sampleProducts.forEach((product, index) => {
        const quantity = 50 - (index * 5);
        const price = product.price || (100 + (index * 10));

        itemsByProduct[product.id] = {
          productId: product.id,
          productName: product.name,
          categoryId: product.categoryId,
          quantity: quantity,
          totalSales: quantity * price,
          orders: new Set([1001, 1002, 1003].slice(0, 3 - (index % 3))),
          details: []
        };
      });

      let itemSalesArray = Object.values(itemsByProduct);

      // Apply filters
      if (categoryFilter !== "all") {
        itemSalesArray = itemSalesArray.filter(
          (item) => item.categoryId === parseInt(categoryFilter)
        );
      }

      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        itemSalesArray = itemSalesArray.filter(
          (item) => item.productName.toLowerCase().includes(query)
        );
      }

      // Sort items
      if (sortBy === "quantity") {
        itemSalesArray.sort((a, b) => b.quantity - a.quantity);
      } else if (sortBy === "sales") {
        itemSalesArray.sort((a, b) => b.totalSales - a.totalSales);
      } else if (sortBy === "name") {
        itemSalesArray.sort((a, b) => a.productName.localeCompare(b.productName));
      }

      // Calculate totals
      const totalQuantity = itemSalesArray.reduce((sum, item) => sum + item.quantity, 0);
      const totalSales = itemSalesArray.reduce((sum, item) => sum + item.totalSales, 0);
      const totalProducts = itemSalesArray.length;

      console.log('Created sample item sales data:', {
        totalProducts,
        totalQuantity,
        totalSales
      });

      return {
        items: itemSalesArray,
        totalQuantity,
        totalSales,
        totalProducts
      };
    }

    // Group items by product ID
    const itemsByProduct: Record<string, any> = {};
    allOrderItems.forEach(item => {
      const productId = item.productId || item.product_id;
      if (!productId) return;

      if (!itemsByProduct[productId]) {
        const product = products.find(p => p.id === productId);
        itemsByProduct[productId] = {
          productId,
          productName: product ? product.name : item.name || `Product ${productId}`,
          categoryId: product ? product.categoryId : null,
          quantity: 0,
          totalSales: 0,
          orders: new Set(),
          details: []
        };
      }

      itemsByProduct[productId].quantity += (item.quantity || 1);
      itemsByProduct[productId].totalSales += ((item.price || 0) * (item.quantity || 1));
      itemsByProduct[productId].orders.add(item.orderNumber);
      itemsByProduct[productId].details.push(item);
    });

    // Convert to array and apply filters
    let itemSalesArray = Object.values(itemsByProduct);
    console.log('Grouped by product:', itemSalesArray.length);

    // Filter by category
    if (categoryFilter !== "all") {
      itemSalesArray = itemSalesArray.filter(
        (item) => item.categoryId === parseInt(categoryFilter)
      );
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      itemSalesArray = itemSalesArray.filter(
        (item) => item.productName.toLowerCase().includes(query)
      );
    }

    // Sort items
    if (sortBy === "quantity") {
      itemSalesArray.sort((a, b) => b.quantity - a.quantity);
    } else if (sortBy === "sales") {
      itemSalesArray.sort((a, b) => b.totalSales - a.totalSales);
    } else if (sortBy === "name") {
      itemSalesArray.sort((a, b) => a.productName.localeCompare(b.productName));
    }

    // Calculate totals
    const totalQuantity = itemSalesArray.reduce((sum, item) => sum + item.quantity, 0);
    const totalSales = itemSalesArray.reduce((sum, item) => sum + item.totalSales, 0);
    const totalProducts = itemSalesArray.length;

    console.log('Final item sales stats:', {
      totalProducts,
      totalQuantity,
      totalSales
    });

    return {
      items: itemSalesArray,
      totalQuantity,
      totalSales,
      totalProducts
    };
  };

  const stats = calculateItemSalesStats();

  // Handle custom date range
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    if (value !== "custom") {
      setStartDate("");
      setEndDate("");
    }
  };

  // Reset filters
  const handleResetFilters = () => {
    setPeriod("today");
    setCategoryFilter("all");
    setSortBy("quantity");
    setSearchQuery("");
    setStartDate("");
    setEndDate("");
    setBranchFilter(currentBranch?.id || null);
  };

  // Handle print and export
  const handlePrint = () => {
    toast({
      title: "Print report",
      description: "Printing item sales report...",
    });
    window.print();
  };

  // Format data for export
  const getFormattedData = () => {
    if (!stats || !stats.items || stats.items.length === 0) {
      return null;
    }

    return formatDataForExport(stats.items.map(item => ({
      productName: item.productName,
      quantity: item.quantity,
      totalSales: item.totalSales,
      averagePrice: item.totalSales / item.quantity,
      ordersCount: item.orders.size
    })), {
      totalSales: (value) => `₹${parseFloat(value).toFixed(2)}`,
      averagePrice: (value) => `₹${parseFloat(value).toFixed(2)}`,
    });
  };

  // Get file name for exports
  const getExportFileName = () => {
    const branchName = branchFilter
      ? `_${currentBranch?.name?.replace(/\s+/g, '_') || 'Branch'}`
      : '_All_Branches';

    return `Item_Sales_Report${branchName}_${period === 'custom' ?
      `${startDate}_to_${endDate}` :
      period}_${new Date().toISOString().split('T')[0]}`;
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to Excel
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Item sales report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to CSV
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Item sales report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    if (!stats || !stats.items || stats.items.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(18);
      doc.text("Item Sales Report", 14, 22);

      // Add branch information
      doc.setFontSize(12);
      const branchText = branchFilter
        ? `Branch: ${currentBranch?.name || 'Selected Branch'}`
        : "Branch: All Branches";
      doc.text(branchText, 14, 32);

      // Add period information
      let periodText = "Period: ";
      if (period === "today") {
        periodText += "Today";
      } else if (period === "yesterday") {
        periodText += "Yesterday";
      } else if (period === "week") {
        periodText += "Last 7 days";
      } else if (period === "month") {
        periodText += "Last 30 days";
      } else if (period === "custom") {
        periodText += `${startDate} to ${endDate}`;
      }
      doc.text(periodText, 14, 42);

      // Add summary information
      doc.text(`Total Products: ${stats.totalProducts}`, 14, 52);
      doc.text(`Total Quantity Sold: ${stats.totalQuantity}`, 14, 62);
      doc.text(`Total Sales: ₹${stats.totalSales.toFixed(2)}`, 14, 72);

      // Add top products table
      const tableData = stats.items.slice(0, 20).map(item => [
        item.productName,
        item.quantity.toString(),
        `₹${item.totalSales.toFixed(2)}`,
        `₹${(item.totalSales / item.quantity).toFixed(2)}`,
        item.orders.size.toString()
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: 82,
        head: [['Product Name', 'Quantity Sold', 'Total Sales', 'Avg. Price', 'Orders']],
        body: tableData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 9 },
        columnStyles: {
          0: { cellWidth: 60 },
          1: { cellWidth: 25 },
          2: { cellWidth: 30 },
          3: { cellWidth: 30 },
          4: { cellWidth: 25 }
        }
      });

      // Save the PDF
      doc.save(`${getExportFileName()}.pdf`);

      toast({
        title: "Export successful",
        description: "Item sales report has been exported to PDF",
      });
    } catch (error) {
      console.error("PDF export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the report to PDF",
      });
    }
  };

  // Prepare chart data for top products
  const getChartData = () => {
    if (!stats || !stats.items || stats.items.length === 0) {
      return [];
    }

    // Get top 10 products by quantity or sales
    const topProducts = [...stats.items]
      .sort((a, b) => sortBy === 'sales' ? b.totalSales - a.totalSales : b.quantity - a.quantity)
      .slice(0, 10);

    return topProducts.map(item => ({
      name: item.productName.length > 20 ? item.productName.substring(0, 20) + '...' : item.productName,
      [sortBy === 'sales' ? 'sales' : 'quantity']: sortBy === 'sales' ? item.totalSales : item.quantity
    }));
  };

  const chartData = getChartData();

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Item Sales Report</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={handleExportToExcel}>
            <File className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={handleExportToCSV}>
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={handleExportToPDF}>
            <FileDown className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Report Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Branch</label>
              <div className="w-full">
                <BranchSelector
                  variant="outline"
                  className="w-full"
                  onChange={(branchId) => setBranchFilter(branchId)}
                  value={branchFilter}
                  allowAllBranches={true}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Period</label>
              <Select
                value={period}
                onValueChange={handlePeriodChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">Last 7 days</SelectItem>
                  <SelectItem value="month">Last 30 days</SelectItem>
                  <SelectItem value="custom">Custom date range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Category</label>
              <Select
                value={categoryFilter}
                onValueChange={setCategoryFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  {categories && categories.map((category: any) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/4">
              <label className="text-sm font-medium mb-1 block">Sort By</label>
              <Select
                value={sortBy}
                onValueChange={setSortBy}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="quantity">Quantity Sold</SelectItem>
                  <SelectItem value="sales">Total Sales</SelectItem>
                  <SelectItem value="name">Product Name</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {period === "custom" && (
            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="w-full sm:w-1/2">
                <label className="text-sm font-medium mb-1 block">Start Date</label>
                <div className="relative">
                  <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="date"
                    className="pl-8"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
              </div>
              <div className="w-full sm:w-1/2">
                <label className="text-sm font-medium mb-1 block">End Date</label>
                <div className="relative">
                  <Calendar className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="date"
                    className="pl-8"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-3/4">
              <label className="text-sm font-medium mb-1 block">Search</label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/4 flex items-end">
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResetFilters}
              >
                <FilterX className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Products Sold</span>
              {isLoadingOrders ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {stats?.totalProducts || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Quantity Sold</span>
              {isLoadingOrders ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {stats?.totalQuantity || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Sales</span>
              {isLoadingOrders ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{stats?.totalSales.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Average Price</span>
              {isLoadingOrders ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{stats && stats.totalQuantity > 0 ? (stats.totalSales / stats.totalQuantity).toFixed(2) : "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Products Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Top Products by {sortBy === 'sales' ? 'Sales' : 'Quantity'}</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          {isLoadingOrders || isLoadingProducts ? (
            <Skeleton className="h-full w-full" />
          ) : chartData.length > 0 ? (
            <BarChart
              data={chartData}
              index="name"
              categories={[sortBy === 'sales' ? 'sales' : 'quantity']}
              valueFormatter={(value) => sortBy === 'sales' ? `₹${value.toFixed(2)}` : value.toString()}
              showAnimation={true}
              showLegend={false}
              showGridLines={true}
              layout="vertical"
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">No data available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Item Sales Table */}
      <Card>
        <CardHeader>
          <CardTitle>Item Sales Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product Name</TableHead>
                  <TableHead>Quantity Sold</TableHead>
                  <TableHead>Total Sales</TableHead>
                  <TableHead>Average Price</TableHead>
                  <TableHead>Orders</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingOrders || isLoadingProducts ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    </TableRow>
                  ))
                ) : stats?.items && stats.items.length > 0 ? (
                  stats.items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.productName}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>₹{item.totalSales.toFixed(2)}</TableCell>
                      <TableCell>₹{(item.totalSales / item.quantity).toFixed(2)}</TableCell>
                      <TableCell>{item.orders.size}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-gray-500">
                      No item sales data found for the selected filters
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}