import pg from 'pg';
const { Pool } = pg;

const pool = new Pool({
  connectionString: '****************************************************/nemboobill?sslmode=disable'
});

async function testSubscriptionData() {
  const client = await pool.connect();
  
  try {
    console.log('Testing subscription data...');
    
    // Test 1: Get all subscription plans
    const plans = await client.query('SELECT * FROM subscription_plans ORDER BY price');
    console.log(`\n📋 Subscription Plans (${plans.rows.length}):`);
    plans.rows.forEach(plan => {
      console.log(`  - ${plan.name}: $${plan.price}/${plan.billing_cycle}`);
    });
    
    // Test 2: Get all subscriptions with plan details
    const subscriptions = await client.query(`
      SELECT 
        s.id,
        s.status,
        s.total_amount,
        s.start_date,
        s.end_date,
        p.name as plan_name,
        p.price as plan_price
      FROM subscriptions s
      JOIN subscription_plans p ON s.plan_id = p.id
      ORDER BY s.created_at DESC
      LIMIT 10
    `);
    
    console.log(`\n📊 Recent Subscriptions (showing 10 of ${subscriptions.rows.length}):`);
    subscriptions.rows.forEach(sub => {
      console.log(`  - ID ${sub.id}: ${sub.plan_name} (${sub.status}) - $${sub.total_amount}`);
    });
    
    // Test 3: Get subscription statistics
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total_subscriptions,
        COUNT(*) FILTER (WHERE status = 'active') as active_subscriptions,
        COUNT(*) FILTER (WHERE status = 'expired') as expired_subscriptions,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_subscriptions,
        COUNT(*) FILTER (WHERE status = 'inactive') as inactive_subscriptions,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(SUM(total_amount) FILTER (WHERE status = 'active'), 0) as active_revenue
      FROM subscriptions
    `);
    
    console.log('\n📈 Subscription Statistics:');
    const stat = stats.rows[0];
    console.log(`  Total Subscriptions: ${stat.total_subscriptions}`);
    console.log(`  Active: ${stat.active_subscriptions}`);
    console.log(`  Expired: ${stat.expired_subscriptions}`);
    console.log(`  Cancelled: ${stat.cancelled_subscriptions}`);
    console.log(`  Inactive: ${stat.inactive_subscriptions}`);
    console.log(`  Total Revenue: $${parseFloat(stat.total_revenue).toFixed(2)}`);
    console.log(`  Active Revenue: $${parseFloat(stat.active_revenue).toFixed(2)}`);
    
    // Test 4: Get subscriptions by shop
    const shopSubscriptions = await client.query(`
      SELECT 
        s.shop_id,
        COUNT(*) as subscription_count,
        COUNT(*) FILTER (WHERE s.status = 'active') as active_count,
        COALESCE(SUM(s.total_amount), 0) as shop_revenue
      FROM subscriptions s
      GROUP BY s.shop_id
      ORDER BY subscription_count DESC
      LIMIT 5
    `);
    
    console.log('\n🏪 Subscriptions by Shop:');
    shopSubscriptions.rows.forEach(shop => {
      console.log(`  Shop ${shop.shop_id}: ${shop.subscription_count} subscriptions (${shop.active_count} active) - $${parseFloat(shop.shop_revenue).toFixed(2)}`);
    });
    
    console.log('\n✅ Subscription data test completed successfully!');
    console.log('\nThe subscription APIs should now return this data when called.');
    
  } catch (error) {
    console.error('Error testing subscription data:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

testSubscriptionData();
