import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CreditCard, Banknote, Smartphone, Receipt, Printer, Loader2 } from "lucide-react";

interface PaymentMethod {
  id: number;
  name: string;
  description?: string;
  active: boolean;
  shopId: number;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (paymentDetails: {
    method: string;
    amount: number;
    printReceipt: boolean;
  }) => void;
  totalAmount: number;
  paymentMethods: PaymentMethod[];
  isProcessing: boolean;
}

export function PaymentModal({
  isOpen,
  onClose,
  onSubmit,
  totalAmount,
  paymentMethods,
  isProcessing
}: PaymentModalProps) {
  // Generate a code from payment method name for consistency
  const generateMethodCode = (name: string) => {
    return name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
  };

  // Set default to first payment method if available, otherwise "cash"
  const defaultMethod = paymentMethods.length > 0 ? generateMethodCode(paymentMethods[0].name) : "cash";
  const [selectedMethod, setSelectedMethod] = useState(defaultMethod);
  const [amount, setAmount] = useState(totalAmount.toFixed(2));
  const [printReceipt, setPrintReceipt] = useState(true);
  const [changeAmount, setChangeAmount] = useState(0);

  // Reset form when modal opens or payment methods change
  useEffect(() => {
    if (isOpen) {
      const newDefaultMethod = paymentMethods.length > 0 ? generateMethodCode(paymentMethods[0].name) : "cash";
      setSelectedMethod(newDefaultMethod);
      setAmount(totalAmount.toFixed(2));
      setPrintReceipt(true);
      setChangeAmount(0);
    }
  }, [isOpen, paymentMethods, totalAmount]);

  // Calculate change when amount changes
  const handleAmountChange = (value: string) => {
    console.log('PaymentModal: Amount changed to:', value);
    setAmount(value);
    const amountValue = parseFloat(value) || 0;
    const change = amountValue > totalAmount ? amountValue - totalAmount : 0;
    setChangeAmount(change);
  };

  // Check if payment amount is valid (handles floating point precision)
  const isValidPaymentAmount = () => {
    const amountValue = parseFloat(amount) || 0;
    const isValid = amountValue >= (totalAmount - 0.01);
    console.log('PaymentModal: Validation check:', {
      amount: amount,
      amountValue: amountValue,
      totalAmount: totalAmount,
      isValid: isValid,
      difference: amountValue - totalAmount
    });
    return isValid;
  };

  const handleSubmit = () => {
    console.log('PaymentModal: Submitting payment with method:', selectedMethod);
    console.log('PaymentModal: Available payment methods:', paymentMethods.map(m => ({ id: m.id, name: m.name, code: generateMethodCode(m.name) })));
    onSubmit({
      method: selectedMethod,
      amount: parseFloat(amount),
      printReceipt
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Payment</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Total Amount</Label>
              <div className="text-2xl font-bold">₹{totalAmount.toFixed(2)}</div>
            </div>
            <div>
              <Label>Change</Label>
              <div className="text-2xl font-bold text-green-600">₹{changeAmount.toFixed(2)}</div>
            </div>
          </div>

          <Separator />

          <Tabs defaultValue="single" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="single">Single Payment</TabsTrigger>
              <TabsTrigger value="split">Split Payment</TabsTrigger>
            </TabsList>
            <TabsContent value="single" className="space-y-4">
              <div>
                <Label>Payment Method</Label>
                <RadioGroup
                  value={selectedMethod}
                  onValueChange={(value) => {
                    console.log('PaymentModal: Payment method changed to:', value);
                    setSelectedMethod(value);
                  }}
                  className="grid grid-cols-2 gap-2 mt-2"
                >
                  {paymentMethods.map((method) => {
                    const methodCode = generateMethodCode(method.name);
                    return (
                      <div key={method.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={methodCode} id={`method-${methodCode}`} />
                        <Label htmlFor={`method-${methodCode}`} className="flex items-center">
                          {methodCode === "cash" && <Banknote className="w-4 h-4 mr-2" />}
                          {methodCode === "card" && <CreditCard className="w-4 h-4 mr-2" />}
                          {methodCode === "upi" && <Smartphone className="w-4 h-4 mr-2" />}
                          {method.name}
                        </Label>
                      </div>
                    );
                  })}
                </RadioGroup>
              </div>

              <div>
                <Label htmlFor="amount">Amount Received</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={amount}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  className={`mt-1 ${!isValidPaymentAmount() ? 'border-red-500' : ''}`}
                />
                {!isValidPaymentAmount() && (
                  <p className="text-sm text-red-500 mt-1">
                    Amount must be at least ₹{totalAmount.toFixed(2)}
                  </p>
                )}
              </div>
            </TabsContent>
            <TabsContent value="split" className="space-y-4">
              <div className="text-center text-muted-foreground">
                Split payment functionality will be implemented in a future update.
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="print-receipt"
              checked={printReceipt}
              onChange={(e) => setPrintReceipt(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="print-receipt" className="flex items-center">
              <Printer className="w-4 h-4 mr-2" />
              Print Receipt
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isProcessing || !isValidPaymentAmount()}
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Receipt className="mr-2 h-4 w-4" />
                Complete Payment
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
