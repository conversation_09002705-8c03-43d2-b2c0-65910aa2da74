import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

import {
  Dialog,
  DialogContentRightSlide,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog-right-slide";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowRightLeft, Package } from "lucide-react";

interface TransferStockModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function TransferStockModal({ open, onOpenChange, onSuccess }: TransferStockModalProps) {
  const { token } = useAuth();
  const { currentBranch, currentShop, userBranches } = useApp();
  const { toast } = useToast();

  const [selectedProduct, setSelectedProduct] = useState("");
  const [fromBranch, setFromBranch] = useState(currentBranch?.id?.toString() || "");
  const [toBranch, setToBranch] = useState("");
  const [quantity, setQuantity] = useState("");
  const [reason, setReason] = useState("");
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch products
  const { data: productsData, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products', currentShop?.id, currentBranch?.id],
    enabled: !!token && open,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/products");
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      return response.json();
    }
  });

  // Extract products array from the response
  const products = productsData?.products || [];

  // Fetch branches - use userBranches from context if available, otherwise fetch from API
  const { data: branches, isLoading: isLoadingBranches, error: branchesError } = useQuery({
    queryKey: ['/api/shops', currentShop?.id, 'branches'],
    enabled: !!token && open && !!currentShop,
    queryFn: async () => {
      console.log('Transfer Modal: Fetching branches for shop', currentShop?.id);

      // Use the same endpoint as app context for consistency
      const response = await apiRequest("GET", `/api/shops/${currentShop.id}/branches`);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Transfer Modal: Failed to fetch branches', response.status, errorText);
        throw new Error(`Failed to fetch branches: ${response.status} ${errorText}`);
      }
      const data = await response.json();
      console.log('Transfer Modal: Fetched branches from API', data);
      return data;
    },
    // Use userBranches as initial data if available
    initialData: userBranches && userBranches.length > 0 ? userBranches : undefined,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Use userBranches from context as fallback if query fails
  const effectiveBranches = branches || userBranches || [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedProduct || !fromBranch || !toBranch || !quantity || !reason) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill in all required fields",
      });
      return;
    }

    if (fromBranch === toBranch) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Source and destination branches must be different",
      });
      return;
    }

    const transferQuantity = parseInt(quantity);
    if (isNaN(transferQuantity) || transferQuantity <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please enter a valid quantity",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await apiRequest("POST", "/api/stock-transfers", {
        productId: parseInt(selectedProduct),
        fromBranchId: parseInt(fromBranch),
        toBranchId: parseInt(toBranch),
        quantity: transferQuantity,
        reason,
        notes
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create stock transfer');
      }

      toast({
        title: "Transfer Created",
        description: "Stock transfer has been created successfully",
      });

      // Reset form
      setSelectedProduct("");
      setFromBranch(currentBranch?.id?.toString() || "");
      setToBranch("");
      setQuantity("");
      setReason("");
      setNotes("");
      
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating stock transfer:', error);
      toast({
        variant: "destructive",
        title: "Transfer Failed",
        description: error instanceof Error ? error.message : "Failed to create stock transfer",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProductData = Array.isArray(products) ? products.find((p: any) => p.id === parseInt(selectedProduct)) : null;

  // Debug logging
  console.log('Transfer Modal Debug:', {
    open,
    currentShop: currentShop?.id,
    userBranches: userBranches?.length,
    branches: branches?.length,
    effectiveBranches: effectiveBranches?.length,
    isLoadingBranches,
    branchesError: branchesError?.message,
    branchesType: typeof branches,
    branchesIsArray: Array.isArray(branches),
    effectiveBranchesIsArray: Array.isArray(effectiveBranches)
  });

  // Don't render if no shop is selected
  if (!currentShop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContentRightSlide className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ArrowRightLeft className="h-5 w-5" />
              Transfer Stock
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 text-center">
            <p className="text-gray-500">Please select a shop first to transfer stock.</p>
          </div>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContentRightSlide>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContentRightSlide className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowRightLeft className="h-5 w-5" />
            Transfer Stock
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="product">Product *</Label>
            {isLoadingProducts ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a product" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px]">
                  {Array.isArray(products) && products.map((product: any) => (
                    <SelectItem key={product.id} value={product.id.toString()}>
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        <span>{product.name}</span>
                        <span className="text-sm text-gray-500">
                          (Stock: {product.quantity || 0})
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fromBranch">From Branch *</Label>
              {isLoadingBranches ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select value={fromBranch} onValueChange={setFromBranch}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source branch" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {effectiveBranches && Array.isArray(effectiveBranches) && effectiveBranches.length > 0 ? (
                      effectiveBranches.map((branch: any) => (
                        <SelectItem key={branch.id} value={branch.id.toString()}>
                          {branch.name}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-sm text-gray-500">
                        {isLoadingBranches ? 'Loading branches...' :
                         branchesError ? `Error: ${branchesError.message}` :
                         'No branches available'}
                      </div>
                    )}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="toBranch">To Branch *</Label>
              {isLoadingBranches ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select value={toBranch} onValueChange={setToBranch}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select destination branch" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {effectiveBranches && Array.isArray(effectiveBranches) && effectiveBranches.length > 0 ? (
                      effectiveBranches
                        .filter((branch: any) => branch.id.toString() !== fromBranch)
                        .map((branch: any) => (
                          <SelectItem key={branch.id} value={branch.id.toString()}>
                            {branch.name}
                          </SelectItem>
                        ))
                    ) : (
                      <div className="p-2 text-sm text-gray-500">
                        {isLoadingBranches ? 'Loading branches...' :
                         branchesError ? `Error: ${branchesError.message}` :
                         'No branches available'}
                      </div>
                    )}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity *</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              max={selectedProductData?.quantity || undefined}
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              placeholder="Enter quantity to transfer"
            />
            {selectedProductData && (
              <p className="text-sm text-gray-500">
                Available stock: {selectedProductData.quantity || 0}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason *</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select transfer reason" />
              </SelectTrigger>
              <SelectContent className="max-h-[200px]">
                <SelectItem value="restock">Restock</SelectItem>
                <SelectItem value="rebalance">Inventory Rebalance</SelectItem>
                <SelectItem value="demand">High Demand</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes (optional)"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating Transfer..." : "Create Transfer"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContentRightSlide>
    </Dialog>
  );
}
