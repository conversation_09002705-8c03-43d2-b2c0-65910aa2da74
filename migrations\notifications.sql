-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('order', 'stock', 'marketing', 'system')),
  priority TEXT DEFAULT 'normal' NOT NULL CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  status TEXT DEFAULT 'unread' NOT NULL CHECK (status IN ('unread', 'read', 'archived')),
  recipient_type TEXT NOT NULL CHECK (recipient_type IN ('user', 'role', 'shop', 'branch')),
  recipient_id INTEGER,
  shop_id INTEGER REFERENCES shops(id),
  branch_id INTEGER REFERENCES branches(id),
  data JSONB,
  scheduled_at TIMESTAMP,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  created_by INTEGER REFERENCES users(id)
);

-- Create notification_recipients table
CREATE TABLE IF NOT EXISTS notification_recipients (
  id SERIAL PRIMARY KEY,
  notification_id INTEGER REFERENCES notifications(id) ON DELETE CASCADE NOT NULL,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  status TEXT DEFAULT 'unread' NOT NULL CHECK (status IN ('unread', 'read', 'archived')),
  read_at TIMESTAMP,
  delivered_at TIMESTAMP DEFAULT NOW()
);

-- Create notification_settings table
CREATE TABLE IF NOT EXISTS notification_settings (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  shop_id INTEGER REFERENCES shops(id),
  notification_type TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true NOT NULL,
  delivery_methods JSONB DEFAULT '["in_app"]'::jsonb,
  settings JSONB DEFAULT '{}'::jsonb,
  UNIQUE(user_id, shop_id, notification_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_shop_id ON notifications(shop_id);
CREATE INDEX IF NOT EXISTS idx_notifications_branch_id ON notifications(branch_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

CREATE INDEX IF NOT EXISTS idx_notification_recipients_notification_id ON notification_recipients(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_recipients_user_id ON notification_recipients(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_recipients_status ON notification_recipients(status);

CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_settings_shop_id ON notification_settings(shop_id);
CREATE INDEX IF NOT EXISTS idx_notification_settings_type ON notification_settings(notification_type);

-- Insert some sample notifications for testing
INSERT INTO notifications (title, message, type, priority, recipient_type, recipient_id, created_by) VALUES
('Welcome to NembooBill', 'Thank you for using our POS system! Explore all the features to get the most out of your experience.', 'system', 'normal', 'user', 1, 1),
('System Maintenance', 'Scheduled maintenance will occur tonight from 2 AM to 4 AM. Please save your work.', 'system', 'high', 'shop', 1, 1),
('New Feature Available', 'Check out our new inventory management features in the latest update!', 'marketing', 'normal', 'shop', 1, 1);

-- Create notification recipients for the sample notifications (assuming user ID 1 exists)
INSERT INTO notification_recipients (notification_id, user_id, status) 
SELECT id, 1, 'unread' FROM notifications WHERE id IN (1, 2, 3);

-- Create default notification settings for user 1 (assuming user and shop exist)
INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods) VALUES
(1, 1, 'order', true, '["in_app"]'::jsonb),
(1, 1, 'stock', true, '["in_app"]'::jsonb),
(1, 1, 'marketing', true, '["in_app"]'::jsonb),
(1, 1, 'system', true, '["in_app"]'::jsonb)
ON CONFLICT (user_id, shop_id, notification_type) DO NOTHING;
