import { storage } from './storage';
import { getSocketServer } from './socket';
import type { InsertNotification } from '@shared/schema';

export class NotificationService {
  
  /**
   * Create and broadcast a notification to specific users
   */
  async createAndBroadcastToUsers(notificationData: InsertNotification, userIds: number[]) {
    try {
      console.log('Creating notification for users:', userIds);
      
      const notification = await storage.createNotificationForUsers(notificationData, userIds);
      
      // Broadcast to each user via Socket.io
      const io = getSocketServer();
      if (io) {
        userIds.forEach(userId => {
          io.to(`user_${userId}`).emit('notification:new', {
            id: notification.id,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            priority: notification.priority,
            created_at: notification.createdAt
          });
        });

        // Update unread counts
        for (const userId of userIds) {
          const unreadCount = await storage.getUnreadNotificationCount(userId, notificationData.shopId, notificationData.branchId);
          io.to(`user_${userId}`).emit('notification:count_updated', unreadCount);
        }
      }

      return notification;
    } catch (error) {
      console.error('Error creating and broadcasting notification to users:', error);
      throw error;
    }
  }

  /**
   * Create and broadcast a notification to all users in a shop
   */
  async createAndBroadcastToShop(notificationData: InsertNotification, shopId: number) {
    try {
      console.log('Creating notification for shop:', shopId);
      
      const notification = await storage.createNotificationForShop(notificationData, shopId);
      
      // Broadcast to all users in the shop
      const io = getSocketServer();
      if (io) {
        io.to(`shop_${shopId}`).emit('notification:new', {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          priority: notification.priority,
          created_at: notification.createdAt
        });

        // Update unread counts for all shop users
        const shopUsers = await storage.getUsersByShop(shopId);
        for (const user of shopUsers) {
          const unreadCount = await storage.getUnreadNotificationCount(user.id, shopId);
          io.to(`user_${user.id}`).emit('notification:count_updated', unreadCount);
        }
      }

      return notification;
    } catch (error) {
      console.error('Error creating and broadcasting notification to shop:', error);
      throw error;
    }
  }

  /**
   * Create and broadcast a notification to all users in a branch
   */
  async createAndBroadcastToBranch(notificationData: InsertNotification, branchId: number) {
    try {
      console.log('Creating notification for branch:', branchId);
      
      const notification = await storage.createNotificationForBranch(notificationData, branchId);
      
      // Broadcast to all users in the branch
      const io = getSocketServer();
      if (io) {
        io.to(`branch_${branchId}`).emit('notification:new', {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          priority: notification.priority,
          created_at: notification.createdAt
        });

        // Update unread counts for all branch users
        const branchUsers = await storage.getUsersByBranch(branchId);
        for (const user of branchUsers) {
          const unreadCount = await storage.getUnreadNotificationCount(user.id, notificationData.shopId, branchId);
          io.to(`user_${user.id}`).emit('notification:count_updated', unreadCount);
        }
      }

      return notification;
    } catch (error) {
      console.error('Error creating and broadcasting notification to branch:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read and broadcast update
   */
  async markAsReadAndBroadcast(notificationId: number, userId: number) {
    try {
      const success = await storage.markNotificationAsRead(notificationId, userId);
      
      if (success) {
        const io = getSocketServer();
        if (io) {
          io.to(`user_${userId}`).emit('notification:read', { notificationId });
          
          // Update unread count
          const user = await storage.getUser(userId);
          if (user) {
            // Get user's current shop/branch context - this would need to be tracked
            // For now, we'll update count without specific shop/branch context
            const unreadCount = await storage.getUnreadNotificationCount(userId);
            io.to(`user_${userId}`).emit('notification:count_updated', unreadCount);
          }
        }
      }

      return success;
    } catch (error) {
      console.error('Error marking notification as read and broadcasting:', error);
      throw error;
    }
  }

  /**
   * Delete notification and broadcast update
   */
  async deleteAndBroadcast(notificationId: number, userId: number) {
    try {
      const success = await storage.deleteNotification(notificationId, userId);
      
      if (success) {
        const io = getSocketServer();
        if (io) {
          io.to(`user_${userId}`).emit('notification:deleted', { notificationId });
          
          // Update unread count
          const unreadCount = await storage.getUnreadNotificationCount(userId);
          io.to(`user_${userId}`).emit('notification:count_updated', unreadCount);
        }
      }

      return success;
    } catch (error) {
      console.error('Error deleting notification and broadcasting:', error);
      throw error;
    }
  }

  /**
   * Create order notification
   */
  async createOrderNotification(orderId: number, orderNumber: string, customerName: string, shopId: number, branchId?: number) {
    const notificationData: InsertNotification = {
      title: 'New Order Received',
      message: `Order ${orderNumber} has been placed by ${customerName}`,
      type: 'order',
      priority: 'normal',
      recipientType: 'shop',
      recipientId: shopId,
      shopId,
      branchId,
      data: { orderId, orderNumber, customerName }
    };

    return await this.createAndBroadcastToShop(notificationData, shopId);
  }

  /**
   * Create stock alert notification
   */
  async createStockAlert(productName: string, currentStock: number, minStock: number, shopId: number, branchId?: number) {
    const notificationData: InsertNotification = {
      title: 'Low Stock Alert',
      message: `Product "${productName}" is running low (${currentStock} remaining, minimum: ${minStock})`,
      type: 'stock',
      priority: currentStock === 0 ? 'urgent' : 'high',
      recipientType: branchId ? 'branch' : 'shop',
      recipientId: branchId || shopId,
      shopId,
      branchId,
      data: { productName, currentStock, minStock }
    };

    if (branchId) {
      return await this.createAndBroadcastToBranch(notificationData, branchId);
    } else {
      return await this.createAndBroadcastToShop(notificationData, shopId);
    }
  }

  /**
   * Create system notification
   */
  async createSystemNotification(title: string, message: string, priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal', shopId?: number) {
    const notificationData: InsertNotification = {
      title,
      message,
      type: 'system',
      priority,
      recipientType: shopId ? 'shop' : 'user',
      recipientId: shopId,
      shopId
    };

    if (shopId) {
      return await this.createAndBroadcastToShop(notificationData, shopId);
    } else {
      // For system-wide notifications, we'd need to broadcast to all users
      // This would require getting all active users
      throw new Error('System-wide notifications not implemented yet');
    }
  }

  /**
   * Create marketing notification
   */
  async createMarketingNotification(title: string, message: string, shopId: number, branchId?: number) {
    const notificationData: InsertNotification = {
      title,
      message,
      type: 'marketing',
      priority: 'normal',
      recipientType: branchId ? 'branch' : 'shop',
      recipientId: branchId || shopId,
      shopId,
      branchId
    };

    if (branchId) {
      return await this.createAndBroadcastToBranch(notificationData, branchId);
    } else {
      return await this.createAndBroadcastToShop(notificationData, shopId);
    }
  }
}

export const notificationService = new NotificationService();
