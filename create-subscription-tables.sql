-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  price DOUBLE PRECISION NOT NULL,
  billing_cycle TEXT NOT NULL,
  features J<PERSON>NB NOT NULL,
  max_branches INTEGER DEFAULT 1 NOT NULL,
  max_users INTEGER DEFAULT 5 NOT NULL,
  max_products INTEGER DEFAULT 100 NOT NULL,
  max_orders INTEGER DEFAULT 1000 NOT NULL,
  storage_limit INTEGER DEFAULT 1024 NOT NULL,
  support_level TEXT DEFAULT 'basic' NOT NULL,
  active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id SERIAL PRIMARY KEY,
  shop_id INTEGER NOT NULL REFERENCES shops(id),
  plan_id INTEGER NOT NULL REFERENCES subscription_plans(id),
  status TEXT DEFAULT 'active' NOT NULL,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  auto_renew BOOLEAN DEFAULT true NOT NULL,
  discount_percent DOUBLE PRECISION DEFAULT 0 NOT NULL,
  discount_amount DOUBLE PRECISION DEFAULT 0 NOT NULL,
  total_amount DOUBLE PRECISION NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  created_by INTEGER NOT NULL REFERENCES users(id)
);

-- Insert sample subscription plans
INSERT INTO subscription_plans (name, description, price, billing_cycle, features, max_branches, max_users, max_products, max_orders, storage_limit, support_level) 
VALUES 
  ('Basic', 'Perfect for small businesses just getting started', 29.99, 'monthly', 
   '["Up to 1 branch", "Up to 5 users", "Up to 100 products", "Up to 1,000 orders/month", "1GB storage", "Basic support", "Standard reporting"]'::jsonb, 
   1, 5, 100, 1000, 1024, 'basic'),
  ('Professional', 'Ideal for growing businesses with multiple locations', 79.99, 'monthly', 
   '["Up to 3 branches", "Up to 15 users", "Up to 500 products", "Up to 5,000 orders/month", "5GB storage", "Priority support", "Advanced reporting", "Inventory management", "Customer loyalty program"]'::jsonb, 
   3, 15, 500, 5000, 5120, 'priority'),
  ('Enterprise', 'For large businesses with complex requirements', 199.99, 'monthly', 
   '["Unlimited branches", "Unlimited users", "Unlimited products", "Unlimited orders", "50GB storage", "24/7 premium support", "Custom reporting", "Advanced analytics", "Multi-location management", "API access", "Custom integrations"]'::jsonb, 
   999, 999, 999999, 999999, 51200, 'premium')
ON CONFLICT DO NOTHING;
