import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Users, ChevronDown, X } from 'lucide-react';
import { useSelectedProducts } from '@/hooks/use-selected-products';
import { useAuth } from '@/context/auth-context';
import { useApp } from '@/context/app-context';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

interface SelectedProductsWidgetProps {
  className?: string;
  showTableAssignment?: boolean;
  onTableAssign?: (tableId: number, products: any[]) => void;
}

export function SelectedProductsWidget({ 
  className = "", 
  showTableAssignment = false,
  onTableAssign 
}: SelectedProductsWidgetProps) {
  const { selectedProducts, removeProduct, clearProducts, updateQuantity } = useSelectedProducts();
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();

  // Fetch tables data
  const { data: tables, isLoading: isLoadingTables, error: tablesError } = useQuery({
    queryKey: ['/api/tables', currentShop?.id, currentBranch?.id],
    queryFn: async () => {
      console.log('🪑 Fetching tables for selected products widget...');
      const response = await apiRequest('GET', '/api/tables');
      if (!response.ok) {
        throw new Error('Failed to fetch tables');
      }
      const tablesData = await response.json();
      console.log('🪑 Tables fetched:', tablesData);
      return tablesData;
    },
    enabled: !!token && !!currentShop && showTableAssignment,
  });

  const handleTableAssign = (tableId: number) => {
    console.log('🪑 Table assignment triggered:', { tableId, selectedProducts });
    if (onTableAssign) {
      console.log('🪑 Calling onTableAssign callback...');
      onTableAssign(tableId, selectedProducts);
    } else {
      console.log('❌ No onTableAssign callback provided');
    }
  };

  // Get available tables for dropdown
  const availableTables = tables || [];
  
  // Debug logging
  console.log('🪑 SelectedProductsWidget state (FIXED VERSION):', {
    showTableAssignment,
    isLoadingTables,
    tablesError,
    availableTables: availableTables.length,
    selectedProducts: selectedProducts.length,
    token: !!token,
    currentShop: !!currentShop,
    timestamp: new Date().toLocaleTimeString()
  });

  // Force log when showTableAssignment is true
  if (showTableAssignment) {
    console.log('🎯 TABLE ASSIGNMENT IS ENABLED - Component should show table selection UI');
    console.log('🎯 Selected products:', selectedProducts);
    console.log('🎯 Available tables:', availableTables);
  }

  // Log when component renders
  console.log('🔄 SelectedProductsWidget RENDER - Products:', selectedProducts.length, 'Show table assignment:', showTableAssignment);

  const getTotalCount = () => {
    return selectedProducts.reduce((total, product) => total + product.quantity, 0);
  };

  const getTotalValue = () => {
    return selectedProducts.reduce((total, product) => total + (product.price * product.quantity), 0);
  };

  if (selectedProducts.length === 0) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Selected Products (FIXED VERSION)</CardTitle>
        {process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
            🔧 DEBUG: Products: {selectedProducts.length}, Tables: {availableTables.length}, Show Assignment: {showTableAssignment ? 'YES' : 'NO'}
            <div className="mt-2 flex gap-2">
              <Button
                onClick={() => {
                  // Add a test product
                  const testProduct = { id: 999, name: 'Test Product', price: 10.00 };
                  console.log('🧪 Adding test product:', testProduct);
                  // This would need to be passed from parent component
                }}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                🧪 Add Test Product
              </Button>
              <Button
                onClick={() => {
                  console.log('🧪 Manual table assignment test');
                  if (selectedProducts.length > 0) {
                    handleTableAssign(21, selectedProducts); // Table ID 21 from logs
                  } else {
                    alert('No products selected. Add some products first.');
                  }
                }}
                variant="outline"
                size="sm"
                className="text-xs"
                disabled={selectedProducts.length === 0}
              >
                🧪 Test Table Assignment
              </Button>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {selectedProducts.map((product) => (
            <div key={product.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div className="flex-1">
                <div className="font-medium text-sm">{product.name}</div>
                <div className="text-xs text-gray-500">
                  ${product.price.toFixed(2)} × {product.quantity} = ${(product.price * product.quantity).toFixed(2)}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateQuantity(product.id, Math.max(1, product.quantity - 1))}
                    className="h-6 w-6 p-0"
                  >
                    -
                  </Button>
                  <span className="text-sm w-8 text-center">{product.quantity}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateQuantity(product.id, product.quantity + 1)}
                    className="h-6 w-6 p-0"
                  >
                    +
                  </Button>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeProduct(product.id)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 pt-4 border-t">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Total Products:</span>
            <span className="font-semibold">{getTotalCount()}</span>
          </div>
          <div className="flex justify-between items-center text-sm mt-1">
            <span className="text-gray-600">Total Value:</span>
            <span className="font-semibold text-green-600">
              ${getTotalValue().toFixed(2)}
            </span>
          </div>

          {showTableAssignment && (
            <div className="mt-4 pt-4 border-t bg-green-50 rounded-lg p-4 border-2 border-green-300">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <span className="text-sm font-medium text-green-800">🎯 Ready to Create Order!</span>
                  <p className="text-xs text-green-600 mt-1">Select a table to assign these {selectedProducts.length} products</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => {
                      console.log('🧪 TEST BUTTON CLICKED');
                      alert('Test button works! Tables available: ' + availableTables.length);
                    }}
                    variant="outline"
                    size="sm"
                    className="text-blue-600 border-blue-200"
                  >
                    🧪 Test
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="default"
                        size="lg"
                        className="bg-green-600 hover:bg-green-700 text-white shadow-lg border-2 border-green-400 animate-pulse"
                        disabled={isLoadingTables}
                      >
                        <Users className="h-5 w-5 mr-2" />
                        {isLoadingTables ? '🔄 Loading...' : '🪑 SELECT TABLE'}
                        <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      {isLoadingTables ? (
                        <DropdownMenuItem disabled>
                          🔄 Loading tables...
                        </DropdownMenuItem>
                      ) : tablesError ? (
                        <DropdownMenuItem disabled>
                          ❌ Error loading tables
                        </DropdownMenuItem>
                      ) : availableTables.length > 0 ? (
                        availableTables.map((table: any) => (
                          <DropdownMenuItem
                            key={table.id}
                            onClick={() => handleTableAssign(table.id)}
                            className="flex items-center justify-between cursor-pointer hover:bg-green-50"
                          >
                            <span className="font-medium">{table.name}</span>
                            <Badge
                              variant={table.status === 'available' ? 'default' : 'secondary'}
                              className="text-xs ml-2"
                            >
                              {table.status}
                            </Badge>
                          </DropdownMenuItem>
                        ))
                      ) : (
                        <DropdownMenuItem disabled>
                          ❌ No tables available
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <div className="mt-3 p-2 bg-white rounded border border-green-200">
                <p className="text-xs text-green-700 font-medium">
                  💡 Click "Select Table" above to assign these {getTotalCount()} products to a table and create an order
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
