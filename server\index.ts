// Load environment variables from .env file
import dotenv from "dotenv";
dotenv.config();

import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { initSocketServer } from "./socket";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  console.log('Starting server setup...');
  try {
    console.log('Calling registerRoutes...');
    const server = await registerRoutes(app);
    console.log('Routes registered successfully');

  // Initialize Socket.io server
  const io = initSocketServer(server);
  log("Socket.io server initialized", "socket");

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    console.error("Error details:", err);

    res.status(status).json({ message });
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Try to use port 5000 first, but fall back to another port if it's in use
  let port = process.env.PORT ? parseInt(process.env.PORT) : 5000;

  const startServer = (retryPort = port) => {
    server.listen({
      port: retryPort,
      host: "0.0.0.0",
      reusePort: true,
    }, () => {
      log(`serving on port ${retryPort}`);
    }).on('error', (err: any) => {
      if (err.code === 'EADDRINUSE' && retryPort < 5010) {
        // Try the next port if current one is in use (try up to port 5010)
        log(`Port ${retryPort} is in use, trying ${retryPort + 1}...`);
        startServer(retryPort + 1);
      } else {
        console.error('Error starting server:', err);
      }
    });
  };

  startServer();
  } catch (error) {
    console.error('Error during server setup:', error);
    process.exit(1);
  }
})();
