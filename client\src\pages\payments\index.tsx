import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, invalidateAndRefetch } from "@/lib/queryClient";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { exportToExcel, formatDataForExport } from "@/lib/export-utils";
import { getCacheBustingUrl } from "@/utils/cacheUtils";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON>D<PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import type { DateRange } from "react-day-picker";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import { Search, Plus, Edit, AlertCircle, Loader2, FileDown, RefreshCcw, CreditCard, Banknote, Smartphone, ArrowLeftRight } from "lucide-react";
import { PosStyleRefundModal } from "@/components/payments/pos-style-refund-modal";


// Form schema for refund
const refundSchema = z.object({
  orderId: z.number().min(1, "Order ID is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  reason: z.string().min(1, "Reason is required"),
  refundMethod: z.enum(["same", "cash", "upi", "card"], {
    required_error: "Please select a refund method",
  }),
});

type RefundFormValues = z.infer<typeof refundSchema>;

// Define Payment interface
interface Payment {
  id: number;
  orderId: number;
  orderNumber: string;
  amount: number;
  method: string;
  status: string;
  orderStatus: string;
  refundStatus: 'not_refunded' | 'refunded' | 'partial_refund';
  refundAmount: number;
  refundMethod?: string;
  createdAt: string;
  customerId?: number;
  customerName?: string;
}

// Define Refund interface
interface Refund {
  id: number;
  paymentId: number;
  orderId: number;
  orderNumber: string;
  amount: number;
  reason: string;
  status: string;
  refundMethod?: string;
  createdAt: string;
}

export default function PaymentManagement() {
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<"payments" | "refunds">("payments");
  const [searchTerm, setSearchTerm] = useState("");
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });
  const [refreshKey, setRefreshKey] = useState(0);

  // Pagination state
  const [paymentsCurrentPage, setPaymentsCurrentPage] = useState(1);
  const [refundsCurrentPage, setRefundsCurrentPage] = useState(1);
  const pageSize = 10;

  // Initialize refund form
  const refundForm = useForm<RefundFormValues>({
    resolver: zodResolver(refundSchema),
    defaultValues: {
      orderId: 0,
      amount: 0,
      reason: "",
      refundMethod: "same",
    },
  });

  // Prepare date parameters for API calls
  const getDateParams = () => {
    if (!dateRange?.from) return {};

    const params: Record<string, string> = {
      startDate: dateRange.from.toISOString(),
    };

    if (dateRange.to) {
      params.endDate = dateRange.to.toISOString();
    } else {
      params.endDate = new Date().toISOString();
    }

    return params;
  };

  // Fetch payments
  const { data: payments, isLoading: isLoadingPayments } = useQuery<Payment[]>({
    queryKey: ['/api/payments', getDateParams(), refreshKey, currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop && activeTab === "payments",
    staleTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
    queryFn: async () => {
      console.log(`🔍 PAYMENTS: Fetching payments for shop ${currentShop?.id}, branch ${currentBranch?.id}`);

      const dateParams = getDateParams();
      const params = new URLSearchParams(dateParams);
      const url = getCacheBustingUrl(`/api/payments${params.toString() ? `?${params.toString()}` : ''}`);

      console.log('🌐 PAYMENTS: Fetching with URL:', url);
      console.log('🏪 PAYMENTS: Current shop context:', currentShop?.name, currentShop?.id);
      console.log('🏢 PAYMENTS: Current branch context:', currentBranch?.name, currentBranch?.id);

      const response = await apiRequest("GET", url);
      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }
      const data = await response.json();
      console.log(`✅ PAYMENTS: Retrieved ${data.length} payments`);
      return data;
    }
  });

  // Fetch refunds
  const { data: refunds, isLoading: isLoadingRefunds } = useQuery<Refund[]>({
    queryKey: ['/api/refunds', getDateParams(), refreshKey, currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop && activeTab === "refunds",
    staleTime: 0,
    refetchOnMount: 'always',
    refetchOnWindowFocus: true,
    queryFn: async () => {
      console.log(`🔍 REFUNDS: Fetching refunds for shop ${currentShop?.id}, branch ${currentBranch?.id}`);

      const dateParams = getDateParams();
      const params = new URLSearchParams(dateParams);
      const url = getCacheBustingUrl(`/api/refunds${params.toString() ? `?${params.toString()}` : ''}`);

      console.log('🌐 REFUNDS: Fetching with URL:', url);
      console.log('🏪 REFUNDS: Current shop context:', currentShop?.name, currentShop?.id);
      console.log('🏢 REFUNDS: Current branch context:', currentBranch?.name, currentBranch?.id);

      const response = await apiRequest("GET", url);
      if (!response.ok) {
        throw new Error('Failed to fetch refunds');
      }
      const data = await response.json();
      console.log(`✅ REFUNDS: Retrieved ${data.length} refunds`);
      return data;
    }
  });



  // Filter payments based on search term
  const filteredPayments = payments?.filter(payment =>
    payment.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.method.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (payment.customerName && payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Filter refunds based on search term
  const filteredRefunds = refunds?.filter(refund =>
    refund.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    refund.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
    refund.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination utility function
  interface PaginatedData<T> {
    data: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  const paginateData = <T,>(data: T[] | undefined, page: number, size: number): PaginatedData<T> => {
    if (!data) return { data: [], total: 0, page, pageSize: size, totalPages: 0 };

    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const paginatedData = data.slice(startIndex, endIndex);
    const total = data.length;
    const totalPages = Math.ceil(total / size);

    return {
      data: paginatedData,
      total,
      page,
      pageSize: size,
      totalPages
    };
  };

  // Paginate filtered data
  const paginatedPayments = paginateData(filteredPayments, paymentsCurrentPage, pageSize);
  const displayedPayments = paginatedPayments.data;

  const paginatedRefunds = paginateData(filteredRefunds, refundsCurrentPage, pageSize);
  const displayedRefunds = paginatedRefunds.data;

  // Process refund mutation
  const processRefundMutation = useMutation({
    mutationFn: async (data: RefundFormValues) => {
      return apiRequest("POST", "/api/refunds", data);
    },
    onSuccess: async () => {
      await invalidateAndRefetch(['/api/refunds', '/api/payments']);
      setRefreshKey(prev => prev + 1);

      toast({
        title: "Refund processed",
        description: "The refund has been processed successfully",
      });

      setIsRefundModalOpen(false);
      refundForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to process refund",
        variant: "destructive",
      });
    }
  });

  // Handle refund form submission
  const onRefundSubmit = (data: RefundFormValues) => {
    processRefundMutation.mutate(data);
  };

  // Set up refund form when a payment is selected
  const setupRefundForm = (payment: Payment) => {
    setSelectedPayment(payment);
    refundForm.setValue("orderId", payment.orderId);
    refundForm.setValue("amount", payment.amount);
    refundForm.setValue("reason", "");
    refundForm.setValue("refundMethod", "same");
    setIsRefundModalOpen(true);
  };

  // Export payments to Excel
  const exportPayments = () => {
    if (!payments || payments.length === 0) {
      toast({
        title: "No data",
        description: "There are no payments to export",
        variant: "destructive",
      });
      return;
    }

    const formattedData = formatDataForExport(payments, [
      { header: 'Order Number', key: 'orderNumber' },
      { header: 'Amount', key: 'amount', format: (value) => `₹${value.toFixed(2)}` },
      { header: 'Method', key: 'method' },
      { header: 'Status', key: 'status' },
      { header: 'Date', key: 'createdAt', format: (value) => new Date(value).toLocaleDateString() },
      { header: 'Customer', key: 'customerName', format: (value) => value || 'N/A' },
    ]);

    exportToExcel(formattedData, 'Payments_Report');
  };

  // Export refunds to Excel
  const exportRefunds = () => {
    if (!refunds || refunds.length === 0) {
      toast({
        title: "No data",
        description: "There are no refunds to export",
        variant: "destructive",
      });
      return;
    }

    const formattedData = formatDataForExport(refunds, [
      { header: 'Order Number', key: 'orderNumber' },
      { header: 'Amount', key: 'amount', format: (value) => `₹${value.toFixed(2)}` },
      { header: 'Reason', key: 'reason' },
      { header: 'Status', key: 'status' },
      { header: 'Date', key: 'createdAt', format: (value) => new Date(value).toLocaleDateString() },
    ]);

    exportToExcel(formattedData, 'Refunds_Report');
  };

  // Handle pagination
  const handlePaymentsPageChange = (page: number) => {
    setPaymentsCurrentPage(page);
  };

  const handleRefundsPageChange = (page: number) => {
    setRefundsCurrentPage(page);
  };

  // Reset to first page when search term or date range changes
  useEffect(() => {
    setPaymentsCurrentPage(1);
    setRefundsCurrentPage(1);
  }, [searchTerm, dateRange]);

  // Clear cache and reset state when shop or branch changes
  useEffect(() => {
    if (currentShop) {
      console.log(`🔄 PAYMENTS: Shop changed to ${currentShop.name} (ID: ${currentShop.id}), clearing payment cache`);

      // Clear payment-related queries from cache
      queryClient.removeQueries({ queryKey: ['/api/payments'] });
      queryClient.removeQueries({ queryKey: ['/api/refunds'] });

      // Reset pagination
      setPaymentsCurrentPage(1);
      setRefundsCurrentPage(1);

      // Force refresh
      setRefreshKey(prev => prev + 1);
    }
  }, [currentShop?.id, queryClient]);

  useEffect(() => {
    if (currentBranch) {
      console.log(`🔄 PAYMENTS: Branch changed to ${currentBranch.name} (ID: ${currentBranch.id}), clearing payment cache`);

      // Clear payment-related queries from cache
      queryClient.removeQueries({ queryKey: ['/api/payments'] });
      queryClient.removeQueries({ queryKey: ['/api/refunds'] });

      // Reset pagination
      setPaymentsCurrentPage(1);
      setRefundsCurrentPage(1);

      // Force refresh
      setRefreshKey(prev => prev + 1);
    }
  }, [currentBranch?.id, queryClient]);

  // Show loading state if no shop context
  if (!currentShop) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-muted-foreground mb-2">No Shop Selected</h2>
            <p className="text-muted-foreground">Please select a shop to view payment data</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Payment Management</h2>
          <p className="text-muted-foreground">
            Manage payments, process refunds, and view payment summaries
            {currentShop && (
              <span className="block text-sm mt-1">
                Shop: <span className="font-medium">{currentShop.name}</span>
                {currentBranch && (
                  <span> • Branch: <span className="font-medium">{currentBranch.name}</span></span>
                )}
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              console.log('🔄 PAYMENTS: Manual refresh triggered, clearing cache');
              // Clear payment-related queries from cache
              queryClient.removeQueries({ queryKey: ['/api/payments'] });
              queryClient.removeQueries({ queryKey: ['/api/refunds'] });
              // Force refresh
              setRefreshKey(prev => prev + 1);
            }}
          >
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={activeTab === "payments" ? exportPayments : exportRefunds}
          >
            <FileDown className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="payments" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 md:w-auto">
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="refunds">Refunds</TabsTrigger>
        </TabsList>

        <div className="mt-4 flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="relative w-full md:w-auto">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder={`Search ${activeTab}...`}
              className="pl-8 w-full md:w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <DatePickerWithRange
            date={dateRange}
            setDate={setDateRange}
          />
        </div>

        <TabsContent value="payments" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Payment List</CardTitle>
              <CardDescription>
                View and manage all payment transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order #</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Payment Status</TableHead>
                    <TableHead>Order Status</TableHead>
                    <TableHead>Refund Status</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingPayments ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        {Array.from({ length: 9 }).map((_, cellIndex) => (
                          <TableCell key={cellIndex}>
                            <Skeleton className="h-6 w-full" />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : displayedPayments && displayedPayments.length > 0 ? (
                    displayedPayments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell className="font-medium">{payment.orderNumber}</TableCell>
                        <TableCell>{new Date(payment.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>₹{payment.amount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={
                            payment.method === 'cash' ? 'bg-green-100 text-green-800' :
                            payment.method === 'card' ? 'bg-blue-100 text-blue-800' :
                            payment.method === 'upi' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {payment.method === 'cash' ? <Banknote className="h-3 w-3 mr-1 inline" /> :
                             payment.method === 'card' ? <CreditCard className="h-3 w-3 mr-1 inline" /> :
                             payment.method === 'upi' ? <Smartphone className="h-3 w-3 mr-1 inline" /> : null}
                            {payment.method.charAt(0).toUpperCase() + payment.method.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={payment.status === 'completed' ? 'success' :
                                          payment.status === 'pending' ? 'warning' :
                                          payment.status === 'failed' ? 'destructive' : 'outline'}>
                            {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={payment.orderStatus === 'completed' ? 'success' :
                                          payment.orderStatus === 'cancelled' ? 'destructive' :
                                          payment.orderStatus === 'pending' ? 'warning' :
                                          payment.orderStatus === 'preparing' ? 'info' : 'outline'}>
                            {payment.orderStatus.charAt(0).toUpperCase() + payment.orderStatus.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={payment.refundStatus === 'refunded' ? 'success' :
                                          payment.refundStatus === 'partial_refund' ? 'warning' :
                                          'secondary'}>
                            {payment.refundStatus === 'not_refunded' ? 'Not Refunded' :
                             payment.refundStatus === 'refunded' ? `Refunded${payment.refundMethod ? ` (${payment.refundMethod})` : ''}` :
                             payment.refundStatus === 'partial_refund' ? 'Partial Refund' : 'Not Refunded'}
                          </Badge>
                        </TableCell>
                        <TableCell>{payment.customerName || 'Walk-in'}</TableCell>
                        <TableCell className="text-right">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant={payment.refundStatus === 'refunded' ? 'outline' : 'ghost'}
                                  size="sm"
                                  onClick={() => setupRefundForm(payment)}
                                  disabled={payment.orderStatus !== 'cancelled' || payment.refundStatus === 'refunded'}
                                  className={payment.refundStatus === 'refunded' ? 'text-green-600 border-green-200 bg-green-50' : ''}
                                >
                                  <ArrowLeftRight className="h-4 w-4 mr-1" />
                                  {payment.refundStatus === 'refunded' ? 'Refunded' : 'Refund'}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {payment.refundStatus === 'refunded'
                                    ? `Already refunded via ${payment.refundMethod || 'unknown method'}`
                                    : payment.orderStatus !== 'cancelled'
                                    ? 'Only cancelled orders can be refunded'
                                    : 'Process refund for this order'
                                  }
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-6">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <CreditCard className="h-10 w-10 mb-2" />
                          <p>No payments found</p>
                          <p className="text-sm">Try adjusting your search or date range</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {/* Payments Pagination */}
              {paginatedPayments.totalPages > 1 && (
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((paymentsCurrentPage - 1) * pageSize) + 1} to {Math.min(paymentsCurrentPage * pageSize, paginatedPayments.total)} of {paginatedPayments.total} payments
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePaymentsPageChange(Math.max(1, paymentsCurrentPage - 1))}
                          className={paymentsCurrentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, paginatedPayments.totalPages) }, (_, i) => {
                        // Show pages around current page
                        let pageNum;
                        if (paginatedPayments.totalPages <= 5) {
                          // If 5 or fewer pages, show all
                          pageNum = i + 1;
                        } else if (paymentsCurrentPage <= 3) {
                          // If near start, show first 5 pages
                          pageNum = i + 1;
                        } else if (paymentsCurrentPage >= paginatedPayments.totalPages - 2) {
                          // If near end, show last 5 pages
                          pageNum = paginatedPayments.totalPages - 4 + i;
                        } else {
                          // Otherwise show 2 before and 2 after current page
                          pageNum = paymentsCurrentPage - 2 + i;
                        }

                        return (
                          <PaginationItem key={pageNum}>
                            <PaginationLink
                              isActive={paymentsCurrentPage === pageNum}
                              onClick={() => handlePaymentsPageChange(pageNum)}
                              className="cursor-pointer"
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      })}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePaymentsPageChange(Math.min(paginatedPayments.totalPages, paymentsCurrentPage + 1))}
                          className={paymentsCurrentPage === paginatedPayments.totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refunds" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Refund List</CardTitle>
              <CardDescription>
                View and manage all refund transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order #</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingRefunds ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        {Array.from({ length: 6 }).map((_, cellIndex) => (
                          <TableCell key={cellIndex}>
                            <Skeleton className="h-6 w-full" />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : displayedRefunds && displayedRefunds.length > 0 ? (
                    displayedRefunds.map((refund) => (
                      <TableRow key={refund.id}>
                        <TableCell className="font-medium">{refund.orderNumber}</TableCell>
                        <TableCell>{new Date(refund.createdAt).toLocaleDateString()}</TableCell>
                        <TableCell>₹{refund.amount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={
                            refund.refundMethod === 'cash' ? 'bg-green-100 text-green-800' :
                            refund.refundMethod === 'card' ? 'bg-blue-100 text-blue-800' :
                            refund.refundMethod === 'upi' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }>
                            {refund.refundMethod === 'cash' ? <Banknote className="h-3 w-3 mr-1 inline" /> :
                             refund.refundMethod === 'card' ? <CreditCard className="h-3 w-3 mr-1 inline" /> :
                             refund.refundMethod === 'upi' ? <Smartphone className="h-3 w-3 mr-1 inline" /> : null}
                            {refund.refundMethod ? refund.refundMethod.charAt(0).toUpperCase() + refund.refundMethod.slice(1) : 'N/A'}
                          </Badge>
                        </TableCell>
                        <TableCell>{refund.reason}</TableCell>
                        <TableCell>
                          <Badge variant={refund.status === 'completed' ? 'success' :
                                          refund.status === 'pending' ? 'warning' :
                                          refund.status === 'failed' ? 'destructive' : 'outline'}>
                            {refund.status.charAt(0).toUpperCase() + refund.status.slice(1)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <ArrowLeftRight className="h-10 w-10 mb-2" />
                          <p>No refunds found</p>
                          <p className="text-sm">Try adjusting your search or date range</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {/* Refunds Pagination */}
              {paginatedRefunds.totalPages > 1 && (
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((refundsCurrentPage - 1) * pageSize) + 1} to {Math.min(refundsCurrentPage * pageSize, paginatedRefunds.total)} of {paginatedRefunds.total} refunds
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handleRefundsPageChange(Math.max(1, refundsCurrentPage - 1))}
                          className={refundsCurrentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, paginatedRefunds.totalPages) }, (_, i) => {
                        // Show pages around current page
                        let pageNum;
                        if (paginatedRefunds.totalPages <= 5) {
                          // If 5 or fewer pages, show all
                          pageNum = i + 1;
                        } else if (refundsCurrentPage <= 3) {
                          // If near start, show first 5 pages
                          pageNum = i + 1;
                        } else if (refundsCurrentPage >= paginatedRefunds.totalPages - 2) {
                          // If near end, show last 5 pages
                          pageNum = paginatedRefunds.totalPages - 4 + i;
                        } else {
                          // Otherwise show 2 before and 2 after current page
                          pageNum = refundsCurrentPage - 2 + i;
                        }

                        return (
                          <PaginationItem key={pageNum}>
                            <PaginationLink
                              isActive={refundsCurrentPage === pageNum}
                              onClick={() => handleRefundsPageChange(pageNum)}
                              className="cursor-pointer"
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      })}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handleRefundsPageChange(Math.min(paginatedRefunds.totalPages, refundsCurrentPage + 1))}
                          className={refundsCurrentPage === paginatedRefunds.totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* POS-Style Refund Modal */}
      <PosStyleRefundModal
        isOpen={isRefundModalOpen}
        onClose={() => setIsRefundModalOpen(false)}
        onSubmit={onRefundSubmit}
        payment={selectedPayment}
        form={refundForm}
        isProcessing={processRefundMutation.isPending}
      />
    </div>
  );
}
