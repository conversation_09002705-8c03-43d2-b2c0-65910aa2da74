import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { useFreshData, forceRefreshPage } from "@/hooks/use-fresh-data";
import { exportToExcel, exportToCSV } from "@/lib/export-utils";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { clearInventoryCache } from "@/utils/cacheUtils";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

import {
  ArrowRightLeft,
  Plus,
  Search,
  RefreshCw,
  FileDown,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  ChevronDown
} from "lucide-react";

export default function StockTransfers() {
  const { token } = useAuth();
  const { currentBranch, currentShop, userBranches } = useApp();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);

  // Use custom hook to ensure fresh data
  useFreshData(['/api/stock-transfers', '/api/branches'], []);

  // Reset all local state when component mounts
  useEffect(() => {
    console.log("Stock Transfers component mounted - resetting state");
    setSearchQuery("");
    setStatusFilter("all");
    setIsCreateDialogOpen(false);
    setPage(1);
  }, []);

  // Clear cache and reset state when shop changes
  useEffect(() => {
    if (currentShop) {
      console.log("Shop changed in Stock Transfers - clearing cache and resetting state", { shopId: currentShop.id, shopName: currentShop.name });

      // Reset all local state
      setSearchQuery("");
      setStatusFilter("all");
      setIsCreateDialogOpen(false);
      setPage(1);

      // Clear cache for stock transfers when shop changes
      const clearShopCache = async () => {
        // Clear ALL queries to ensure no stale data
        console.log("Clearing ALL query cache to prevent cross-shop data contamination");
        queryClient.clear();

        // Also specifically target stock transfers
        await queryClient.removeQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   queryKey[0].includes('/api/stock-transfers');
          }
        });

        await queryClient.invalidateQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   queryKey[0].includes('/api/stock-transfers');
          }
        });
      };

      clearShopCache();
    }
  }, [currentShop?.id]); // Watch for shop ID changes

  // Fetch stock transfers from backend
  const { data: transfersData, isLoading: transfersLoading, refetch: refetchTransfers } = useQuery({
    queryKey: ['/api/stock-transfers', currentShop?.id, currentBranch?.id, page, pageSize, statusFilter],
    enabled: !!token && !!currentShop && !!currentShop.id && currentShop.id > 0,
    staleTime: 0, // Always consider data stale to ensure fresh data on shop change
    gcTime: 0, // Don't cache to prevent cross-shop data contamination
    refetchOnMount: true, // Refetch on mount to ensure fresh data
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnReconnect: false, // Don't refetch on reconnect
    refetchInterval: false, // Disable automatic refetching
    queryFn: async () => {
      console.log("Fetching stock transfers data...", {
        shopId: currentShop?.id,
        shopName: currentShop?.name,
        branchId: currentBranch?.id,
        page,
        pageSize,
        statusFilter
      });

      // Ensure we have a valid shop ID before making the request
      if (!currentShop?.id || currentShop.id <= 0) {
        console.error("No valid current shop ID available for stock transfers request", {
          currentShop,
          shopId: currentShop?.id
        });
        throw new Error('Valid shop context is required');
      }

      console.log("Making stock transfers request with shop context:", {
        shopId: currentShop.id,
        shopName: currentShop.name,
        branchId: currentBranch?.id,
        branchName: currentBranch?.name
      });

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (statusFilter !== "all") {
        params.append('status', statusFilter);
      }

      // Add shop ID as a query parameter as well for extra validation
      params.append('shopId', currentShop.id.toString());

      const headers = {
        'Authorization': `Bearer ${token}`,
        'X-Shop-ID': currentShop.id.toString(),
        'X-Branch-ID': currentBranch?.id?.toString() || '',
        'Content-Type': 'application/json'
      };

      console.log("Request headers being sent:", headers);
      console.log("Request URL:", `/api/stock-transfers?${params}`);

      const response = await fetch(`/api/stock-transfers?${params}`, { headers });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Stock transfers API error:", response.status, errorText);
        throw new Error(`Failed to fetch stock transfers: ${response.status} ${errorText}`);
      }
      const data = await response.json();
      console.log("Stock transfers data fetched for shop:", currentShop?.name, data);

      // Additional client-side validation to ensure data belongs to current shop
      if (data.transfers) {
        const validTransfers = data.transfers.filter((transfer: any) => {
          // Check if transfer belongs to current shop through branches
          const fromBranchValid = transfer.fromBranch && transfer.fromBranch.shopId === currentShop.id;
          const toBranchValid = transfer.toBranch && transfer.toBranch.shopId === currentShop.id;

          if (!fromBranchValid && !toBranchValid) {
            console.warn("Filtering out transfer from different shop:", transfer.id);
            return false;
          }
          return true;
        });

        if (validTransfers.length !== data.transfers.length) {
          console.warn(`Filtered out ${data.transfers.length - validTransfers.length} transfers from other shops`);
          data.transfers = validTransfers;
          data.pagination.totalCount = validTransfers.length;
          data.pagination.totalPages = Math.ceil(validTransfers.length / pageSize);
        }
      }

      return data;
    },
  });

  // Fetch stock transfer statistics
  const { data: statsData, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['/api/stock-transfers/stats', currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop && !!currentShop.id && currentShop.id > 0,
    staleTime: 0, // Always consider data stale to ensure fresh data on shop change
    gcTime: 0, // Don't cache to prevent cross-shop data contamination
    refetchOnMount: true, // Refetch on mount to ensure fresh data
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnReconnect: false, // Don't refetch on reconnect
    refetchInterval: false, // Disable automatic refetching
    queryFn: async () => {
      console.log('Fetching transfer stats...', {
        shopId: currentShop?.id,
        shopName: currentShop?.name,
        branchId: currentBranch?.id
      });
      const response = await fetch("/api/stock-transfers/stats", {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop.id.toString(),
          'X-Branch-ID': currentBranch?.id?.toString() || '',
          'Content-Type': 'application/json'
        }
      });
      console.log('Stats response status:', response.status);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Stats API error:', response.status, errorText);
        throw new Error(`Failed to fetch transfer stats: ${response.status} ${errorText}`);
      }
      const data = await response.json();
      console.log('Stats API response for shop:', currentShop?.name, data);
      return data;
    },
  });

  // Extract transfers and stats from API response
  const transfers = transfersData?.transfers || [];
  const pagination = transfersData?.pagination || {};

  // Calculate stats from the actual transfers data if stats API fails
  const calculatedStats = {
    totalTransfers: transfers.length,
    completedTransfers: transfers.filter(t => t.status === 'completed').length,
    pendingTransfers: transfers.filter(t => t.status === 'pending').length,
    rejectedTransfers: transfers.filter(t => t.status === 'rejected').length,
    transfersByStatus: {
      completed: transfers.filter(t => t.status === 'completed').length,
      pending: transfers.filter(t => t.status === 'pending').length,
      rejected: transfers.filter(t => t.status === 'rejected').length
    }
  };

  // Use API stats if available, otherwise fall back to calculated stats
  const stats = (statsData?.stats && Object.keys(statsData.stats).length > 0)
    ? statsData.stats
    : calculatedStats;

  // Debug logging (can be removed in production)
  console.log('Stats calculation - API stats available:', !!statsData?.stats);
  console.log('Stats calculation - Using calculated stats:', !statsData?.stats);
  console.log('Final stats:', stats);

  // Fetch branches - use userBranches from context if available, otherwise fetch from API
  const { data: branches } = useQuery({
    queryKey: ['/api/branches', currentShop?.id],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      // If we already have branches in context, use them
      if (userBranches && userBranches.length > 0) {
        return userBranches;
      }

      const response = await apiRequest("GET", "/api/branches");
      if (!response.ok) {
        throw new Error('Failed to fetch branches');
      }
      return response.json();
    },
    // Use userBranches as initial data if available
    initialData: userBranches && userBranches.length > 0 ? userBranches : undefined,
  });

  // Filter transfers (client-side search only, status filter is handled by API)
  const filteredTransfers = transfers.filter((transfer) => {
    let matches = true;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      matches = matches && (
        transfer.id.toString().includes(query) ||
        (transfer.fromBranch?.name || '').toLowerCase().includes(query) ||
        (transfer.toBranch?.name || '').toLowerCase().includes(query) ||
        (transfer.notes || '').toLowerCase().includes(query)
      );
    }

    return matches;
  });

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return { label: 'Pending', variant: 'secondary' as const, icon: Clock };
      case 'in_transit':
        return { label: 'In Transit', variant: 'default' as const, icon: Truck };
      case 'completed':
        return { label: 'Completed', variant: 'default' as const, icon: CheckCircle };
      case 'cancelled':
      case 'rejected':
        return { label: 'Cancelled', variant: 'destructive' as const, icon: XCircle };
      default:
        return { label: 'Unknown', variant: 'secondary' as const, icon: Clock };
    }
  };

  // Format data for export
  const getFormattedData = () => {
    if (!filteredTransfers || filteredTransfers.length === 0) {
      return null;
    }

    return filteredTransfers.map((transfer: any) => {
      const statusInfo = getStatusInfo(transfer.status);
      const totalValue = transfer.items?.reduce((sum: number, item: any) =>
        sum + (item.quantity * item.unitPrice), 0) || 0;

      return {
        'Transfer ID': `TRF-${transfer.id.toString().padStart(3, '0')}`,
        'From Branch': transfer.fromBranch?.name || 'Unknown',
        'To Branch': transfer.toBranch?.name || 'Unknown',
        'Items Count': transfer.totalItems || transfer.items?.length || 0,
        'Total Quantity': transfer.totalQuantity || 0,
        'Total Value': `₹${totalValue.toFixed(2)}`,
        'Status': statusInfo.label,
        'Date': new Date(transfer.createdAt).toLocaleDateString(),
        'Notes': transfer.notes || ''
      };
    });
  };

  // Generate export filename
  const getExportFileName = () => {
    const date = new Date().toISOString().split('T')[0];
    const branchName = currentBranch?.name || 'All_Branches';
    return `Stock_Transfers_${branchName}_${date}`;
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No transfer data available to export",
      });
      return;
    }

    try {
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Stock transfers have been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the transfers",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No transfer data available to export",
      });
      return;
    }

    try {
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Stock transfers have been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the transfers",
      });
    }
  };

  const handleCreateTransfer = () => {
    toast({
      title: "Feature Coming Soon",
      description: "Stock transfer creation will be available in the next update",
    });
    setIsCreateDialogOpen(false);
  };

  const handleRefresh = async () => {
    try {
      console.log("Manual refresh triggered - clearing cache and refetching");

      // Reset filters to initial state
      setSearchQuery("");
      setStatusFilter("all");
      setPage(1);

      // Clear only stock transfer related cache entries
      await queryClient.removeQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) && queryKey.length > 0 &&
                 typeof queryKey[0] === 'string' &&
                 queryKey[0].includes('/api/stock-transfers');
        }
      });

      // Invalidate to force immediate refetch
      await queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) && queryKey.length > 0 &&
                 typeof queryKey[0] === 'string' &&
                 queryKey[0].includes('/api/stock-transfers');
        }
      });

      // Refetch the data
      await refetchTransfers();

      toast({
        title: "Refreshed",
        description: "Transfer data has been refreshed",
      });
    } catch (error) {
      console.error("Refresh error:", error);
      toast({
        variant: "destructive",
        title: "Refresh failed",
        description: "Failed to refresh transfer data",
      });
    }
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(1); // Reset to first page when filter changes
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Stock Transfers</h1>
          <p className="text-gray-500">Manage stock transfers between branches</p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <FileDown className="h-4 w-4 mr-2" />
                Export
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleExportToExcel}>
                Export to Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportToCSV}>
                Export to CSV
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Transfer
          </Button>
        </div>
      </div>



      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transfers</CardTitle>
            <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{stats.totalTransfers}</div>
            )}
            <p className="text-xs text-muted-foreground">
              All time transfers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-yellow-600">
                {stats.transfersByStatus?.pending || 0}
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              Awaiting approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-red-600">
                {stats.transfersByStatus?.rejected || 0}
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              Rejected transfers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold text-green-600">
                {stats.transfersByStatus?.completed || 0}
              </div>
            )}
            <p className="text-xs text-muted-foreground">
              Successfully transferred
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Transfers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Transfer History</CardTitle>
        </CardHeader>
        <CardContent className="mt-3">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search transfers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transfer #</TableHead>
                  <TableHead>From Branch</TableHead>
                  <TableHead>To Branch</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transfersLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredTransfers.length > 0 ? (
                  filteredTransfers.map((transfer) => {
                    const statusInfo = getStatusInfo(transfer.status);
                    const transferNumber = `TRF-${transfer.id.toString().padStart(3, '0')}`;
                    const totalValue = transfer.items?.reduce((sum: number, item: any) =>
                      sum + (item.quantity * item.unitPrice), 0) || 0;

                    return (
                      <TableRow key={transfer.id}>
                        <TableCell className="font-medium">{transferNumber}</TableCell>
                        <TableCell>{transfer.fromBranch?.name || 'Unknown'}</TableCell>
                        <TableCell>{transfer.toBranch?.name || 'Unknown'}</TableCell>
                        <TableCell>{transfer.totalItems || transfer.items?.length || 0} items</TableCell>
                        <TableCell>₹{totalValue.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={statusInfo.variant} className="flex items-center gap-1 w-fit">
                            <statusInfo.icon className="h-3 w-3" />
                            {statusInfo.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(transfer.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const itemsList = transfer.items?.map((item: any) =>
                                `${item.product?.name || 'Unknown'}: ${item.quantity} units`
                              ).join(', ') || 'No items';

                              toast({
                                title: "Transfer Details",
                                description: `${transferNumber} | From: ${transfer.fromBranch?.name} | To: ${transfer.toBranch?.name} | Items: ${itemsList} | Value: ₹${totalValue.toFixed(2)}`
                              });
                            }}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <ArrowRightLeft className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No transfers found</p>
                        <p className="text-sm text-gray-400">
                          {searchQuery ? 'Try adjusting your search' : 'No transfers available'}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{' '}
                {pagination.totalCount} transfers
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={!pagination.hasPreviousPage}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Transfer Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create Stock Transfer</DialogTitle>
            <DialogDescription>
              Transfer stock between branches
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="from-branch" className="text-right">
                From Branch *
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select source branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches?.map((branch: any) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="to-branch" className="text-right">
                To Branch *
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select destination branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches?.map((branch: any) => (
                    <SelectItem key={branch.id} value={branch.id.toString()}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                placeholder="Transfer notes..."
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTransfer}>
              Create Transfer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
