// Test subscription endpoints
// Using built-in fetch (Node.js 18+)

const API_BASE = 'http://localhost:5000';

async function testSubscriptionEndpoints() {
  console.log('🧪 Testing Subscription API Endpoints...\n');

  const endpoints = [
    {
      name: 'Subscription Plans',
      url: '/api/subscription-plans',
      method: 'GET'
    },
    {
      name: 'Shop Subscriptions',
      url: '/api/subscriptions',
      method: 'GET'
    },
    {
      name: 'Subscription Stats',
      url: '/api/subscriptions/stats',
      method: 'GET'
    },
    {
      name: 'Subscription Usage',
      url: '/api/subscriptions/usage',
      method: 'GET'
    },
    {
      name: 'Subscription Payment Methods',
      url: '/api/payment-methods/subscription',
      method: 'GET'
    }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint.name}...`);
      
      const response = await fetch(`${API_BASE}${endpoint.url}`, {
        method: endpoint.method,
        headers: {
          'Authorization': 'Bearer test-token',
          'X-Shop-ID': '1',
          'X-Branch-ID': '1',
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`  Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`  ✅ Success - Data length: ${Array.isArray(data) ? data.length : 'N/A'}`);
        if (Array.isArray(data) && data.length > 0) {
          console.log(`  Sample data:`, JSON.stringify(data[0], null, 2).substring(0, 200) + '...');
        } else if (typeof data === 'object') {
          console.log(`  Data:`, JSON.stringify(data, null, 2).substring(0, 200) + '...');
        }
      } else {
        const errorText = await response.text();
        console.log(`  ❌ Error: ${errorText}`);
      }
      
    } catch (error) {
      console.log(`  ❌ Network Error: ${error.message}`);
    }
    
    console.log('');
  }
}

testSubscriptionEndpoints().catch(console.error);
