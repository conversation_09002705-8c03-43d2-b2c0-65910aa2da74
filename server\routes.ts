﻿import express, { type Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { db } from "./db";
import { users, userShops, userBranches } from "@shared/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { insertUserSchema, insertRoleSchema, insertProductSchema, insertCategorySchema, insertTableSchema, insertCustomerSchema, insertOnlinePlatformSchema, insertOrderSchema, insertOrderItemSchema, insertExpenseSchema, insertPurchaseSchema, insertTaxSettingSchema, insertDiscountSettingSchema, insertShopSettingsSchema, insertShopSchema, insertUserShopSchema, insertBranchSchema, insertUserBranchSchema, insertPaymentMethodSchema, insertPrinterSettingSchema, insertUserPreferenceSchema, insertRoundingSettingSchema, insertStockMovementSchema, insertStockTransferSchema, insertStockTransferItemSchema, insertSubscriptionSchema, insertSubscriptionPlanSchema, insertSubscriptionPaymentMethodSchema } from "@shared/schema";
import bcrypt from "bcrypt";
import path from "path";

import session from "express-session";
import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import MemoryStore from "memorystore";

import { authMiddleware, permissionMiddleware } from "./middleware";
import { emitDashboardStatsUpdate, emitTopProductsUpdate, emitRecentOrdersUpdate } from "./socket";
import { upload, getFileUrl } from "./upload";
import { PaymentService, paymentSchema, refundSchema } from "./payment-service";
import { notificationService } from "./notification-service";

import jwt from "jsonwebtoken";

// Dashboard period schema
const dashboardPeriodSchema = z.object({
  period: z.enum(['today', 'week', 'month']).default('today')
});

// Create a JWT secret
const JWT_SECRET = process.env.JWT_SECRET || "nemboobill-secret-key";

// Login request schema
const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

// OTP login schemas
const sendOtpSchema = z.object({
  identifier: z.string().min(1, "Email or phone number is required"),
});

const verifyOtpSchema = z.object({
  identifier: z.string().min(1, "Email or phone number is required"),
  otp: z.string().min(1, "OTP is required"),
});

// Register request schema
const registerSchema = insertUserSchema.extend({
  confirmPassword: z.string().min(1, "Confirm password is required"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Forgot password schema
const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

// Reset password schema
const resetPasswordSchema = z.object({
  username: z.string().min(1, "Username is required"),
  otp: z.string().min(1, "OTP is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string().min(1, "Confirm password is required"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Update profile schema
const updateProfileSchema = z.object({
  name: z.string().min(1, "Full name is required"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address"),
});

// Change password schema
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(6, "New password must be at least 6 characters"),
  confirmPassword: z.string().min(1, "Confirm your new password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Order creation schemas
// Make userId and shopId optional since they will be set by the server from auth middleware
const createOrderSchema = z.object({
  order: insertOrderSchema.extend({
    userId: z.number().optional(),
    shopId: z.number().optional(),
    branchId: z.number().optional()
  }),
  items: z.array(insertOrderItemSchema),
});

// Order-only creation schema (for two-step process)
// Make userId and shopId optional since they will be set by the server from auth middleware
const createOrderOnlySchema = z.object({
  order: insertOrderSchema.extend({
    userId: z.number().optional(),
    shopId: z.number().optional(),
    branchId: z.number().optional()
  }),
});

// Split bill schema
const splitBillSchema = z.object({
  originalOrder: insertOrderSchema,
  splits: z.array(z.object({
    id: z.string(),
    name: z.string(),
    items: z.array(z.object({
      productId: z.number(),
      productName: z.string(),
      quantity: z.number(),
      unitPrice: z.number(),
      totalPrice: z.number(),
      notes: z.string().optional(),
    })),
    subtotal: z.number(),
    taxAmount: z.number(),
    total: z.number(),
  })),
});

// Table transfer schema
const tableTransferSchema = z.object({
  fromTableId: z.number(),
  toTableId: z.number(),
  orderId: z.number(),
});

// Table merge schema
const tableMergeSchema = z.object({
  primaryTableId: z.number(),
  secondaryTableIds: z.array(z.number()),
  orderIds: z.array(z.number()),
});

// Order items creation schema (for two-step process)
const createOrderItemsSchema = z.object({
  items: z.array(insertOrderItemSchema),
});

// Cart item schema
const cartItemSchema = z.object({
  productId: z.number(),
  quantity: z.number().positive(),
});

// Cart schemas
const addToCartSchema = z.object({
  item: cartItemSchema,
});

const updateCartItemSchema = z.object({
  item: cartItemSchema,
});

const removeFromCartSchema = z.object({
  productId: z.number(),
});

// Auth middleware is now imported from middleware.ts

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);
  const paymentService = new PaymentService(storage);

  // Serve uploaded files
  const uploadsPath = path.join(process.cwd(), 'uploads');
  console.log(`Serving static files from: ${uploadsPath}`);

  app.use('/uploads', (req, res, next) => {
    console.log(`Static file request: ${req.url}`);
    // Add cache control headers
    res.setHeader('Cache-Control', 'public, max-age=86400'); // 24 hours
    next();
  }, express.static(uploadsPath, {
    fallthrough: false, // Return 404 for non-existent files
    index: false,       // Disable directory listing
    redirect: false     // Don't redirect if trailing slash
  }));

  // Handle errors from static file serving
  app.use((err: any, req: Request, res: Response, next: any) => {
    // Safely check if this is an uploads path request
    const requestPath = req.path || req.url || '';
    if (typeof requestPath === 'string' && requestPath.startsWith('/uploads/')) {
      console.error('Error serving static file:', err);
      return res.status(404).json({ message: 'File not found' });
    }
    next(err);
  });

  // Setup session store
  const MemoryStoreSession = MemoryStore(session);
  app.use(session({
    secret: JWT_SECRET,
    resave: false,
    saveUninitialized: false,
    store: new MemoryStoreSession({
      checkPeriod: 86400000 // prune expired entries every 24h
    }),
    cookie: {
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
  }));

  // Initialize passport
  app.use(passport.initialize());
  app.use(passport.session());

  // Passport local strategy
  passport.use(new LocalStrategy(
    async (username, password, done) => {
      try {
        const user = await storage.getUserByUsername(username);
        if (!user) {
          return done(null, false, { message: 'Invalid username or password' });
        }

        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
          return done(null, false, { message: 'Invalid username or password' });
        }

        // Update last login timestamp
        await storage.updateUser(user.id, { lastLogin: new Date() });

        return done(null, user);
      } catch (error) {
        return done(error);
      }
    }
  ));

  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: number, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (error) {
      done(error);
    }
  });

  // API routes - prefix all with /api

  // Health check endpoint for Docker healthcheck
  app.get('/api/health', (req, res) => {
    res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  // Utilities API Endpoints

  // System Information endpoint
  app.get('/api/utilities/system-info', authMiddleware, async (req, res) => {
    try {
      const os = await import('os');
      const fs = await import('fs');
      const pathModule = await import('path');

      // Get system information
      const systemInfo = {
        server: {
          platform: os.platform(),
          architecture: os.arch(),
          nodeVersion: process.version,
          uptime: process.uptime(),
          memory: {
            total: os.totalmem(),
            free: os.freemem(),
            used: os.totalmem() - os.freemem(),
            processUsed: process.memoryUsage()
          },
          cpu: {
            model: os.cpus()[0]?.model || 'Unknown',
            cores: os.cpus().length,
            loadAverage: os.loadavg()
          }
        },
        application: {
          name: 'NembooBill',
          version: '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          startTime: new Date(Date.now() - process.uptime() * 1000).toISOString()
        }
      };

      res.json(systemInfo);
    } catch (error) {
      console.error('Error getting system info:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Database Statistics endpoint
  app.get('/api/utilities/database-stats', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      // Get counts for all major tables
      const stats = await storage.getDatabaseStats(shopId, branchId);

      res.json(stats);
    } catch (error) {
      console.error('Error getting database stats:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Business Analytics endpoint
  app.get('/api/utilities/business-analytics', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const period = req.query.period as string || 'month';

      const analytics = await storage.getBusinessAnalytics(shopId, branchId, period);

      res.json(analytics);
    } catch (error) {
      console.error('Error getting business analytics:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Cache Management endpoint
  app.post('/api/utilities/clear-cache', authMiddleware, async (req, res) => {
    try {
      // Clear any server-side caches if implemented
      // For now, just return success
      res.json({
        success: true,
        message: 'Cache cleared successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error clearing cache:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // System Health Check endpoint
  app.get('/api/utilities/health-check', authMiddleware, async (req, res) => {
    try {
      const healthStatus = {
        database: 'healthy',
        server: 'healthy',
        memory: 'healthy',
        disk: 'healthy',
        timestamp: new Date().toISOString(),
        checks: []
      };

      // Database health check
      try {
        await storage.getDashboardStats();
        healthStatus.checks.push({ name: 'Database Connection', status: 'healthy', message: 'Connected successfully' });
      } catch (error) {
        healthStatus.database = 'unhealthy';
        healthStatus.checks.push({ name: 'Database Connection', status: 'unhealthy', message: 'Connection failed' });
      }

      // Memory health check
      const memoryUsage = process.memoryUsage();
      const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      if (memoryUsagePercent > 90) {
        healthStatus.memory = 'warning';
        healthStatus.checks.push({ name: 'Memory Usage', status: 'warning', message: `High memory usage: ${memoryUsagePercent.toFixed(2)}%` });
      } else {
        healthStatus.checks.push({ name: 'Memory Usage', status: 'healthy', message: `Memory usage: ${memoryUsagePercent.toFixed(2)}%` });
      }

      // Overall status
      const hasUnhealthy = healthStatus.checks.some(check => check.status === 'unhealthy');
      const hasWarning = healthStatus.checks.some(check => check.status === 'warning');

      if (hasUnhealthy) {
        healthStatus.server = 'unhealthy';
      } else if (hasWarning) {
        healthStatus.server = 'warning';
      }

      res.json(healthStatus);
    } catch (error) {
      console.error('Error performing health check:', error);
      res.status(500).json({
        database: 'unhealthy',
        server: 'unhealthy',
        memory: 'unknown',
        disk: 'unknown',
        timestamp: new Date().toISOString(),
        checks: [{ name: 'Health Check', status: 'unhealthy', message: 'Health check failed' }]
      });
    }
  });

  // Authentication Routes
  app.post('/api/auth/login', async (req, res) => {
    try {
      console.log('Login attempt:', req.body);

      const { username, password } = loginSchema.parse(req.body);
      console.log('Parsed login data, username:', username);

      try {
        const user = await storage.getUserByUsername(username);
        console.log('User lookup result:', user ? 'User found' : 'User not found');

        if (!user) {
          return res.status(401).json({ message: 'Invalid username or password' });
        }

        try {
          const isPasswordValid = await bcrypt.compare(password, user.password);
          console.log('Password validation result:', isPasswordValid);

          if (!isPasswordValid) {
            return res.status(401).json({ message: 'Invalid username or password' });
          }

          // Update last login timestamp
          await storage.updateUser(user.id, { lastLogin: new Date() });

          // Generate JWT token
          const token = jwt.sign(
            { id: user.id, username: user.username, role: user.roleId },
            JWT_SECRET,
            { expiresIn: '24h' }
          );

          // Get user's shops
          console.log('Getting user shops for user ID:', user.id);
          const shops = await storage.getUserShops(user.id);
          console.log('User shops retrieved:', shops.length);

          // Get branches for the first shop if available
          let branches = [];
          if (shops.length > 0) {
            branches = await storage.getUserBranches(user.id, shops[0].id);
            console.log('User branches retrieved for first shop:', branches.length);
          }

          // Return user info, shops, branches, and token (excluding password)
          const { password: _, ...userWithoutPassword } = user;

          res.json({
            user: userWithoutPassword,
            shops,
            branches,
            token
          });
        } catch (bcryptError) {
          console.error('bcrypt comparison error:', bcryptError);
          throw bcryptError;
        }
      } catch (userLookupError) {
        console.error('User lookup error:', userLookupError);
        throw userLookupError;
      }
    } catch (error) {
      console.error('Login error details:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      // Log the full error stack
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/auth/register', async (req, res) => {
    try {
      const data = registerSchema.parse(req.body);
      const { confirmPassword, ...userData } = data;

      // Check if username already exists
      const existingUsername = await storage.getUserByUsername(userData.username);
      if (existingUsername) {
        return res.status(400).json({ message: 'Username already taken' });
      }

      // Check if email already exists
      const existingEmail = await storage.getUserByEmail(userData.email);
      if (existingEmail) {
        return res.status(400).json({ message: 'Email already registered' });
      }

      // Create user
      const user = await storage.createUser(userData);

      // Return user info (excluding password)
      const { password, ...userWithoutPassword } = user;

      res.status(201).json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Create user with branch association (for user management)
  app.post('/api/users', authMiddleware, permissionMiddleware('users:create'), async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Creating user by user ${userId}, shop ${shopId}, branch ${branchId}`);

      // Check if user has permission to create users
      if (shopId) {
        const userRole = await storage.getUserShopRole(userId, shopId);
        if (!userRole || !['owner', 'admin'].includes(userRole)) {
          // Check if user is branch manager
          if (branchId) {
            const branchRole = await storage.getUserBranchRole(userId, branchId);
            if (!branchRole || branchRole !== 'manager') {
              return res.status(403).json({ message: 'You do not have permission to create users' });
            }
          } else {
            return res.status(403).json({ message: 'You do not have permission to create users' });
          }
        }
      } else {
        return res.status(400).json({ message: 'Shop context is required' });
      }

      const userData = insertUserSchema.parse(req.body);

      // Check if username already exists
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ message: 'Username already exists' });
      }

      // Check if email already exists
      const existingEmail = await storage.getUserByEmail(userData.email);
      if (existingEmail) {
        return res.status(400).json({ message: 'Email already registered' });
      }

      // Create user
      const user = await storage.createUser(userData);

      // Add user to shop
      await storage.addUserToShop({
        userId: user.id,
        shopId,
        role: 'member'
      });

      // Add user to branch if branch context is available
      if (branchId) {
        await storage.addUserToBranch({
          userId: user.id,
          branchId,
          role: 'staff'
        });
      }

      // Return user info (excluding password)
      const { password, ...userWithoutPassword } = user;

      res.status(201).json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      console.error('Error creating user:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/auth/forgot-password', async (req, res) => {
    try {
      const { email } = forgotPasswordSchema.parse(req.body);

      const user = await storage.getUserByEmail(email);
      if (!user) {
        // Don't reveal that the email doesn't exist
        return res.json({ message: 'If your email is registered, you will receive an OTP shortly' });
      }

      // Generate OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Store OTP
      await storage.storeOtp(user.username, otp);

      // In a real application, send the OTP via email
      // For now, we'll just return it in the response for testing
      res.json({
        message: 'OTP sent to your email',
        otp,
        username: user.username // This would not be included in a real app
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/auth/reset-password', async (req, res) => {
    try {
      const { username, otp, password } = resetPasswordSchema.parse(req.body);

      // Verify OTP
      const isOtpValid = await storage.verifyOtp(username, otp);
      if (!isOtpValid) {
        return res.status(400).json({ message: 'Invalid or expired OTP' });
      }

      // Update password
      await storage.updatePassword(username, password);

      res.json({ message: 'Password updated successfully' });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // OTP Login Routes
  app.post('/api/auth/send-otp', async (req, res) => {
    try {
      const { identifier } = sendOtpSchema.parse(req.body);

      // Check if identifier is an email or phone number
      const isEmail = identifier.includes('@');

      // Find user by email or username (assuming username could be a phone number)
      let user;
      if (isEmail) {
        user = await storage.getUserByEmail(identifier);
      } else {
        user = await storage.getUserByUsername(identifier);
      }

      if (!user) {
        // Don't reveal that the identifier doesn't exist
        return res.json({
          message: 'If your email/phone is registered, you will receive an OTP shortly'
        });
      }

      // Generate OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Store OTP
      await storage.storeOtp(user.username, otp);

      // In a real application, send the OTP via email or SMS
      // For now, we'll just return it in the response for testing
      res.json({
        message: 'OTP sent to your email/phone',
        otp, // This would not be included in a real app
        username: user.username // This would not be included in a real app
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/auth/verify-otp', async (req, res) => {
    try {
      const { identifier, otp } = verifyOtpSchema.parse(req.body);

      // Check if identifier is an email or phone number
      const isEmail = identifier.includes('@');

      // Find user by email or username (assuming username could be a phone number)
      let user;
      if (isEmail) {
        user = await storage.getUserByEmail(identifier);
      } else {
        user = await storage.getUserByUsername(identifier);
      }

      if (!user) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Verify OTP
      const isOtpValid = await storage.verifyOtp(user.username, otp);
      if (!isOtpValid) {
        return res.status(401).json({ message: 'Invalid or expired OTP' });
      }

      // Update last login timestamp
      await storage.updateUser(user.id, { lastLogin: new Date() });

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.roleId },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // Get user's shops
      const shops = await storage.getUserShops(user.id);

      // Get branches for the first shop if available
      let branches = [];
      if (shops.length > 0) {
        branches = await storage.getUserBranches(user.id, shops[0].id);
      }

      // Return user info, shops, branches, and token (excluding password)
      const { password: _, ...userWithoutPassword } = user;

      res.json({
        user: userWithoutPassword,
        shops,
        branches,
        token
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // User Routes (protected by auth middleware)
  app.get('/api/users/me', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Return user info (excluding password)
      const { password, ...userWithoutPassword } = user;

      res.json(userWithoutPassword);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Update user profile
  app.patch('/api/users/me', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const profileData = updateProfileSchema.parse(req.body);

      // Check if username is being changed and if it's already taken
      if (profileData.username) {
        const existingUser = await storage.getUserByUsername(profileData.username);
        if (existingUser && existingUser.id !== userId) {
          return res.status(400).json({ message: 'Username already taken' });
        }
      }

      // Check if email is being changed and if it's already registered
      if (profileData.email) {
        const existingUser = await storage.getUserByEmail(profileData.email);
        if (existingUser && existingUser.id !== userId) {
          return res.status(400).json({ message: 'Email already registered' });
        }
      }

      // Update user profile
      const updatedUser = await storage.updateUser(userId, profileData);

      if (!updatedUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Return updated user info (excluding password)
      const { password, ...userWithoutPassword } = updatedUser;

      res.json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Change user password
  app.post('/api/users/change-password', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const { currentPassword, newPassword } = changePasswordSchema.parse(req.body);

      // Get user with password
      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: 'Current password is incorrect' });
      }

      // Update password
      await storage.updateUser(userId, { password: newPassword });

      res.json({ message: 'Password updated successfully' });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get all users for management (branch-wise)
  app.get('/api/users/all', authMiddleware, permissionMiddleware('users:view'), async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Getting users for user ${userId}, shop ${shopId}, branch ${branchId}`);

      // Check if user has access to the shop
      if (shopId) {
        const userRole = await storage.getUserShopRole(userId, shopId);
        if (!userRole) {
          return res.status(403).json({ message: 'You do not have access to this shop' });
        }

        // If user is shop owner/admin, they can see all users in the shop
        if (['owner', 'admin'].includes(userRole)) {
          const allUsers = await storage.getUsersByShop(shopId, branchId);
          const usersWithoutPasswords = allUsers.map(user => {
            const { password, ...userWithoutPassword } = user;
            return userWithoutPassword;
          });
          res.json(usersWithoutPasswords);
          return;
        }
      }

      // If user is branch manager or staff, they can only see users in their branch
      if (branchId) {
        const branchRole = await storage.getUserBranchRole(userId, branchId);
        if (branchRole === 'manager') {
          const branchUsers = await storage.getUsersByBranch(branchId);
          const usersWithoutPasswords = branchUsers.map(user => {
            const { password, ...userWithoutPassword } = user;
            return userWithoutPassword;
          });
          res.json(usersWithoutPasswords);
          return;
        }
      }

      // Default: return empty array if no proper access
      res.json([]);
    } catch (error) {
      console.error('Error getting all users:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Delete user
  app.delete('/api/users/:id', authMiddleware, permissionMiddleware('users:delete'), async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const currentUserId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      if (userId === currentUserId) {
        return res.status(400).json({ message: 'You cannot delete your own account' });
      }

      // Check if current user has permission to delete users
      if (shopId) {
        const userRole = await storage.getUserShopRole(currentUserId, shopId);
        if (!userRole || !['owner', 'admin'].includes(userRole)) {
          // Check if user is branch manager
          if (branchId) {
            const branchRole = await storage.getUserBranchRole(currentUserId, branchId);
            if (!branchRole || branchRole !== 'manager') {
              return res.status(403).json({ message: 'You do not have permission to delete users' });
            }
          } else {
            return res.status(403).json({ message: 'You do not have permission to delete users' });
          }
        }
      }

      // Check if target user exists and is in the same shop/branch context
      const targetUser = await storage.getUser(userId);
      if (!targetUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Verify the target user is in the current shop context
      if (shopId) {
        const targetUserShopRole = await storage.getUserShopRole(userId, shopId);
        if (!targetUserShopRole) {
          return res.status(403).json({ message: 'User is not in the current shop context' });
        }
      }

      await db.delete(userShops).where(eq(userShops.userId, userId));
      await db.delete(userBranches).where(eq(userBranches.userId, userId));
      const result = await db.delete(users).where(eq(users.id, userId));

      if (result.rowCount > 0) {
        res.json({ message: 'User deleted successfully' });
      } else {
        res.status(404).json({ message: 'User not found' });
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Update user (for user management)
  app.put('/api/users/:id', authMiddleware, permissionMiddleware('users:edit'), async (req, res) => {
    try {
      const targetUserId = parseInt(req.params.id);
      const currentUserId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Updating user ${targetUserId} by user ${currentUserId}, shop ${shopId}, branch ${branchId}`);

      // Validate request body
      const updateUserSchema = z.object({
        name: z.string().min(1, "Full name is required").optional(),
        username: z.string().min(3, "Username must be at least 3 characters").optional(),
        email: z.string().email("Invalid email address").optional(),
        password: z.string().min(6, "Password must be at least 6 characters").optional(),
        roleId: z.number().optional(),
        active: z.boolean().optional(),
      });

      const userData = updateUserSchema.parse(req.body);

      // Check if target user exists and is in the same branch/shop context
      const targetUser = await storage.getUser(targetUserId);
      if (!targetUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Prevent users from editing themselves through this endpoint
      if (targetUserId === currentUserId) {
        return res.status(400).json({ message: 'Use profile endpoint to update your own account' });
      }

      // Check if username is being changed and if it's already taken
      if (userData.username) {
        const existingUser = await storage.getUserByUsername(userData.username);
        if (existingUser && existingUser.id !== targetUserId) {
          return res.status(400).json({ message: 'Username already taken' });
        }
      }

      // Check if email is being changed and if it's already registered
      if (userData.email) {
        const existingUser = await storage.getUserByEmail(userData.email);
        if (existingUser && existingUser.id !== targetUserId) {
          return res.status(400).json({ message: 'Email already registered' });
        }
      }

      // Update user
      const updatedUser = await storage.updateUser(targetUserId, userData);

      if (!updatedUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Return updated user info (excluding password)
      const { password, ...userWithoutPassword } = updatedUser;

      res.json(userWithoutPassword);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      console.error('Error updating user:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Temporary route to fix user role (remove after use)
  app.post('/api/fix-user-role', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      console.log(`Fixing user role for user ID: ${userId}`);

      const adminPermissions = ['dashboard:view', 'users:view', 'users:create', 'users:edit', 'users:delete', 'roles:view', 'roles:create', 'roles:edit', 'roles:delete', 'products:view', 'products:create', 'products:edit', 'products:delete', 'categories:view', 'categories:create', 'categories:edit', 'categories:delete', 'tables:view', 'tables:create', 'tables:edit', 'tables:delete', 'customers:view', 'customers:create', 'customers:edit', 'customers:delete', 'orders:view', 'orders:create', 'orders:edit', 'orders:delete', 'expenses:view', 'expenses:create', 'expenses:edit', 'expenses:delete', 'purchases:view', 'purchases:create', 'purchases:edit', 'purchases:delete', 'settings:view', 'settings:edit', 'reports:view', 'reports:export', 'branches:view', 'branches:create', 'branches:edit', 'branches:delete'];

      // Create or update Admin role
      let adminRole = await storage.getRoleByName('Admin');
      if (!adminRole) {
        console.log('Creating new Admin role');
        adminRole = await storage.createRole({ name: 'Admin', permissions: adminPermissions });
      } else {
        console.log('Updating existing Admin role');
        adminRole = await storage.updateRole(adminRole.id, { name: 'Admin', permissions: adminPermissions });
      }

      // Update user's role
      console.log(`Assigning Admin role (ID: ${adminRole.id}) to user ${userId}`);
      const updatedUser = await storage.updateUserRole(userId, adminRole.id);

      res.json({
        message: 'User role fixed successfully!',
        user: updatedUser,
        role: adminRole,
        permissions: adminPermissions
      });
    } catch (error) {
      console.error('Error fixing user role:', error);
      res.status(500).json({ message: 'Failed to fix user role', error: error.message });
    }
  });

  // Role Routes
  app.get('/api/roles', authMiddleware, permissionMiddleware('roles:view'), async (req, res) => {
    try {
      const roles = await storage.getAllRoles();
      res.json(roles);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/roles/:id', authMiddleware, permissionMiddleware('roles:view'), async (req, res) => {
    try {
      const roleId = parseInt(req.params.id);
      const role = await storage.getRoleById(roleId);

      if (!role) {
        return res.status(404).json({ message: 'Role not found' });
      }

      res.json(role);
    } catch (error) {
      console.error('Error fetching role:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/roles', authMiddleware, permissionMiddleware('roles:create'), async (req, res) => {
    try {
      const roleData = insertRoleSchema.parse(req.body);
      const role = await storage.createRole(roleData);
      res.status(201).json(role);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/roles/:id', authMiddleware, permissionMiddleware('roles:edit'), async (req, res) => {
    try {
      const roleId = parseInt(req.params.id);
      const roleData = insertRoleSchema.parse(req.body);

      // Check if role exists
      const existingRole = await storage.getRoleById(roleId);
      if (!existingRole) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Update role
      const updatedRole = await storage.updateRole(roleId, roleData);
      res.json(updatedRole);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/roles/:id', authMiddleware, permissionMiddleware('roles:delete'), async (req, res) => {
    try {
      const roleId = parseInt(req.params.id);

      // Check if role exists
      const existingRole = await storage.getRoleById(roleId);
      if (!existingRole) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Delete role
      const success = await storage.deleteRole(roleId);
      if (success) {
        res.status(204).send();
      } else {
        res.status(500).json({ message: 'Failed to delete role' });
      }
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get available permissions
  app.get('/api/permissions', authMiddleware, permissionMiddleware('roles:view'), async (req, res) => {
    try {
      // Define all available permissions in the system
      const permissions = [
        // Dashboard permissions
        { id: 'dashboard:view', name: 'View Dashboard', module: 'Dashboard' },

        // User management permissions
        { id: 'users:view', name: 'View Users', module: 'Users' },
        { id: 'users:create', name: 'Create Users', module: 'Users' },
        { id: 'users:edit', name: 'Edit Users', module: 'Users' },
        { id: 'users:delete', name: 'Delete Users', module: 'Users' },

        // Role management permissions
        { id: 'roles:view', name: 'View Roles', module: 'Roles' },
        { id: 'roles:create', name: 'Create Roles', module: 'Roles' },
        { id: 'roles:edit', name: 'Edit Roles', module: 'Roles' },
        { id: 'roles:delete', name: 'Delete Roles', module: 'Roles' },

        // Product permissions
        { id: 'products:view', name: 'View Products', module: 'Products' },
        { id: 'products:create', name: 'Create Products', module: 'Products' },
        { id: 'products:edit', name: 'Edit Products', module: 'Products' },
        { id: 'products:delete', name: 'Delete Products', module: 'Products' },

        // Category permissions
        { id: 'categories:view', name: 'View Categories', module: 'Categories' },
        { id: 'categories:create', name: 'Create Categories', module: 'Categories' },
        { id: 'categories:edit', name: 'Edit Categories', module: 'Categories' },
        { id: 'categories:delete', name: 'Delete Categories', module: 'Categories' },

        // Table permissions
        { id: 'tables:view', name: 'View Tables', module: 'Tables' },
        { id: 'tables:create', name: 'Create Tables', module: 'Tables' },
        { id: 'tables:edit', name: 'Edit Tables', module: 'Tables' },
        { id: 'tables:delete', name: 'Delete Tables', module: 'Tables' },

        // Customer permissions
        { id: 'customers:view', name: 'View Customers', module: 'Customers' },
        { id: 'customers:create', name: 'Create Customers', module: 'Customers' },
        { id: 'customers:edit', name: 'Edit Customers', module: 'Customers' },
        { id: 'customers:delete', name: 'Delete Customers', module: 'Customers' },

        // Order permissions
        { id: 'orders:view', name: 'View Orders', module: 'Orders' },
        { id: 'orders:create', name: 'Create Orders', module: 'Orders' },
        { id: 'orders:edit', name: 'Edit Orders', module: 'Orders' },
        { id: 'orders:delete', name: 'Delete Orders', module: 'Orders' },

        // Expense permissions
        { id: 'expenses:view', name: 'View Expenses', module: 'Expenses' },
        { id: 'expenses:create', name: 'Create Expenses', module: 'Expenses' },
        { id: 'expenses:edit', name: 'Edit Expenses', module: 'Expenses' },
        { id: 'expenses:delete', name: 'Delete Expenses', module: 'Expenses' },

        // Purchase permissions
        { id: 'purchases:view', name: 'View Purchases', module: 'Purchases' },
        { id: 'purchases:create', name: 'Create Purchases', module: 'Purchases' },
        { id: 'purchases:edit', name: 'Edit Purchases', module: 'Purchases' },
        { id: 'purchases:delete', name: 'Delete Purchases', module: 'Purchases' },

        // Settings permissions
        { id: 'settings:view', name: 'View Settings', module: 'Settings' },
        { id: 'settings:edit', name: 'Edit Settings', module: 'Settings' },

        // Reports permissions
        { id: 'reports:view', name: 'View Reports', module: 'Reports' },
        { id: 'reports:export', name: 'Export Reports', module: 'Reports' },
      ];

      res.json(permissions);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Legacy Shop Settings Routes (for backward compatibility)
  app.get('/api/settings/shop', authMiddleware, async (req, res) => {
    try {
      const settings = await storage.getShopSettings();
      res.json(settings || {});
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/shop', authMiddleware, async (req, res) => {
    try {
      const settingsData = insertShopSettingsSchema.parse(req.body);
      const settings = await storage.updateShopSettings(settingsData);
      res.json(settings);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Shop Routes
  app.get('/api/shops', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shops = await storage.getUserShops(userId);
      res.json(shops);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/shops/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const shop = await storage.getShopById(id);

      if (!shop) {
        return res.status(404).json({ message: 'Shop not found' });
      }

      res.json(shop);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Create new shop
  app.post('/api/shops', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopData = insertShopSchema.parse({
        ...req.body,
        createdBy: userId
      });

      const shop = await storage.createShop(shopData);
      res.status(201).json(shop);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Update shop
  app.patch('/api/shops/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user.id;

      // Check if user has permission to update this shop
      const userRole = await storage.getUserShopRole(userId, id);
      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        return res.status(403).json({ message: 'You do not have permission to update this shop' });
      }

      const shopData = insertShopSchema.partial().parse(req.body);
      const shop = await storage.updateShop(id, shopData);

      if (!shop) {
        return res.status(404).json({ message: 'Shop not found' });
      }

      res.json(shop);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Join shop by access code
  app.post('/api/shops/join', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const { accessCode } = z.object({ accessCode: z.string() }).parse(req.body);

      const shop = await storage.getShopByAccessCode(accessCode);
      if (!shop) {
        return res.status(404).json({ message: 'Invalid access code' });
      }

      // Add user to shop with 'member' role
      await storage.addUserToShop({
        userId,
        shopId: shop.id,
        role: 'member'
      });

      res.json(shop);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Branch Routes
  // Get all branches for a shop
  app.get('/api/shops/:shopId/branches', authMiddleware, async (req, res) => {
    try {
      const shopId = parseInt(req.params.shopId);
      const userId = (req as any).user.id;

      console.log(`Fetching branches for shop ${shopId} by user ${userId}`);

      // Check if user has access to this shop
      const userRole = await storage.getUserShopRole(userId, shopId);
      console.log(`User role for shop ${shopId}: ${userRole}`);

      if (!userRole) {
        return res.status(403).json({ message: 'You do not have access to this shop' });
      }

      // Get branches based on user role
      const branches = await storage.getUserBranches(userId, shopId);
      console.log(`Found ${branches.length} branches for shop ${shopId}:`, branches);

      res.json(branches);
    } catch (error) {
      console.error('Error fetching branches:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get a specific branch
  app.get('/api/branches/:id', authMiddleware, async (req, res) => {
    try {
      const branchId = parseInt(req.params.id);
      const userId = (req as any).user.id;

      console.log(`Getting branch with ID ${branchId} for user ${userId}`);

      // Get the branch
      const branch = await storage.getBranchById(branchId);
      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      // Check if user has access to this branch's shop
      const userRole = await storage.getUserShopRole(userId, branch.shopId);
      console.log(`User shop role: ${userRole}`);

      if (!userRole) {
        return res.status(403).json({ message: 'You do not have access to this shop' });
      }

      // If user is not shop owner/admin, check if they have access to this specific branch
      if (!['owner', 'admin'].includes(userRole)) {
        const branchRole = await storage.getUserBranchRole(userId, branchId);
        if (!branchRole) {
          return res.status(403).json({ message: 'You do not have access to this branch' });
        }
      }

      console.log(`Returning branch: ${JSON.stringify(branch)}`);
      res.json(branch);
    } catch (error) {
      console.error('Error getting branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });


  // Get all branches for current user
  app.get('/api/branches', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;

      console.log(`Getting all branches for user ${userId} in shop ${shopId}`);

      if (!shopId) {
        return res.status(400).json({ message: 'No shop selected' });
      }

      // Get branches for the current shop
      const branches = await storage.getUserBranches(userId, shopId);
      console.log(`Found ${branches.length} branches`);

      res.json(branches);
    } catch (error) {
      console.error('Error getting branches:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });
  // Get current branch
  app.get('/api/branches/current', authMiddleware, async (req, res) => {
    try {
      const branchId = parseInt((req as any).headers['x-branch-id'] || '0');

      if (!branchId) {
        return res.status(400).json({ message: 'No branch ID provided' });
      }

      const userId = (req as any).user.id;

      console.log(`Getting current branch (ID: ${branchId}) for user ${userId}`);

      const branch = await storage.getBranchById(branchId);
      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      // Check if user has access to this branch's shop
      const userRole = await storage.getUserShopRole(userId, branch.shopId);

      if (!userRole) {
        return res.status(403).json({ message: 'You do not have access to this shop' });
      }

      // If user is not shop owner/admin, check if they have access to this specific branch
      if (!['owner', 'admin'].includes(userRole)) {
        const branchRole = await storage.getUserBranchRole(userId, branchId);
        if (!branchRole) {
          return res.status(403).json({ message: 'You do not have access to this branch' });
        }
      }

      console.log(`Returning current branch: ${JSON.stringify(branch)}`);
      res.json(branch);
    } catch (error) {
      console.error('Error getting current branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Create a new branch
  app.post('/api/shops/:shopId/branches', authMiddleware, async (req, res) => {
    try {
      const shopId = parseInt(req.params.shopId);
      const userId = (req as any).user.id;

      console.log(`Creating branch for shop ${shopId} by user ${userId}`);
      console.log('Request body:', req.body);

      // Check if user has permission to create branches in this shop
      const userRole = await storage.getUserShopRole(userId, shopId);
      console.log(`User role for shop ${shopId}: ${userRole}`);

      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        return res.status(403).json({ message: 'You do not have permission to create branches in this shop' });
      }

      // Create branch
      const branchData = insertBranchSchema.parse({
        ...req.body,
        shopId,
        createdBy: userId
      });

      console.log('Parsed branch data:', branchData);

      const branch = await storage.createBranch(branchData);
      console.log('Created branch:', branch);

      res.status(201).json(branch);
    } catch (error) {
      console.error('Error creating branch:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Update a branch
  app.patch('/api/branches/:id', authMiddleware, async (req, res) => {
    try {
      const branchId = parseInt(req.params.id);
      const userId = (req as any).user.id;

      // Get the branch
      const branch = await storage.getBranchById(branchId);
      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      // Check if user has permission to update this branch
      const userRole = await storage.getUserShopRole(userId, branch.shopId);
      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        // Check if user is a branch manager
        const branchRole = await storage.getUserBranchRole(userId, branchId);
        if (!branchRole || branchRole !== 'manager') {
          return res.status(403).json({ message: 'You do not have permission to update this branch' });
        }
      }

      // Update branch
      const branchData = insertBranchSchema.partial().parse(req.body);
      const updatedBranch = await storage.updateBranch(branchId, branchData);

      res.json(updatedBranch);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Add user to branch
  app.post('/api/branches/:id/users', authMiddleware, async (req, res) => {
    try {
      const branchId = parseInt(req.params.id);
      const userId = (req as any).user.id;

      // Get the branch
      const branch = await storage.getBranchById(branchId);
      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      // Check if user has permission to add users to this branch
      const userRole = await storage.getUserShopRole(userId, branch.shopId);
      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        // Check if user is a branch manager
        const branchRole = await storage.getUserBranchRole(userId, branchId);
        if (!branchRole || branchRole !== 'manager') {
          return res.status(403).json({ message: 'You do not have permission to add users to this branch' });
        }
      }

      // Parse request body
      const { userId: targetUserId, role } = z.object({
        userId: z.number(),
        role: z.string()
      }).parse(req.body);

      // Check if target user exists and has access to the shop
      const targetUserShopRole = await storage.getUserShopRole(targetUserId, branch.shopId);
      if (!targetUserShopRole) {
        return res.status(400).json({ message: 'User does not have access to this shop' });
      }

      // Add user to branch
      const userBranch = await storage.addUserToBranch({
        userId: targetUserId,
        branchId,
        role
      });

      res.status(201).json(userBranch);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Remove user from branch
  app.delete('/api/branches/:branchId/users/:userId', authMiddleware, async (req, res) => {
    try {
      const branchId = parseInt(req.params.branchId);
      const targetUserId = parseInt(req.params.userId);
      const userId = (req as any).user.id;

      // Get the branch
      const branch = await storage.getBranchById(branchId);
      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      // Check if user has permission to remove users from this branch
      const userRole = await storage.getUserShopRole(userId, branch.shopId);
      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        // Check if user is a branch manager
        const branchRole = await storage.getUserBranchRole(userId, branchId);
        if (!branchRole || branchRole !== 'manager') {
          return res.status(403).json({ message: 'You do not have permission to remove users from this branch' });
        }
      }

      // Remove user from branch
      const result = await storage.removeUserFromBranch(targetUserId, branchId);

      if (!result) {
        return res.status(404).json({ message: 'User not found in this branch' });
      }

      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });


  // Get all branches for current user
  app.get('/api/branches', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;

      console.log(`Getting all branches for user ${userId} in shop ${shopId}`);

      if (!shopId) {
        return res.status(400).json({ message: 'No shop selected' });
      }

      // Get branches for the current shop
      const branches = await storage.getUserBranches(userId, shopId);
      console.log(`Found ${branches.length} branches`);

      res.json(branches);
    } catch (error) {
      console.error('Error getting branches:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });
  // Get current branch
  app.get('/api/branches/current', authMiddleware, async (req, res) => {
    try {
      const branchId = (req as any).branchId;

      if (!branchId) {
        return res.status(400).json({ message: 'No branch selected' });
      }

      const branch = await storage.getBranchById(branchId);

      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      res.json(branch);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // File Upload Route
  app.post('/api/upload', authMiddleware, (req, res, next) => {
    console.log('Upload request received');

    try {
      // Handle multer errors
      upload.single('image')(req, res, (err) => {
        if (err) {
          console.error('Multer error:', err);
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ message: 'File too large. Maximum size is 5MB.' });
          }
          return res.status(400).json({ message: err.message || 'Error uploading file' });
        }

        // Continue to the next middleware if no error
        next();
      });
    } catch (error) {
      console.error('Unexpected error in upload middleware:', error);
      return res.status(500).json({ message: 'Server error processing upload' });
    }
  }, async (req, res) => {
    try {
      console.log('Processing upload request');

      // Check if req.file exists
      if (!req.file) {
        console.error('No file in request');
        return res.status(400).json({ message: 'No file uploaded' });
      }

      console.log('File uploaded successfully:', req.file.filename);

      // Return the file URL
      const fileUrl = getFileUrl(req.file.filename);
      return res.json({
        url: fileUrl,
        filename: req.file.filename,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size
      });
    } catch (error) {
      console.error('Error processing uploaded file:', error);
      return res.status(500).json({
        message: error instanceof Error ? error.message : 'Internal server error',
        error: error instanceof Error ? error.stack : 'No stack trace'
      });
    }
  });

  // Product Routes
  app.get('/api/products', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting products with shopId: ${shopId}, branchId: ${branchId}`);

      const products = await storage.getAllProducts(undefined, shopId, branchId);
      console.log(`Retrieved ${products.length} products`);

      res.json(products);
    } catch (error) {
      console.error('Error getting products:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Barcode search endpoint
  app.get('/api/products/barcode/:barcode', authMiddleware, async (req, res) => {
    try {
      const { barcode } = req.params;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Searching product by barcode: ${barcode} with shopId: ${shopId}, branchId: ${branchId}`);

      if (!barcode || barcode.trim() === '') {
        return res.status(400).json({ message: 'Barcode is required' });
      }

      const product = await storage.getProductByBarcode(barcode.trim(), shopId, branchId);

      if (!product) {
        return res.status(404).json({ message: 'Product not found with this barcode' });
      }

      // Check if product is active
      if (!product.active) {
        return res.status(404).json({ message: 'Product is inactive' });
      }

      console.log(`Found product: ${product.name} for barcode: ${barcode}`);
      res.json(product);
    } catch (error) {
      console.error('Error searching product by barcode:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Barcode validation endpoint
  app.post('/api/products/barcode/validate', authMiddleware, async (req, res) => {
    try {
      const { barcode } = req.body;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Validating barcode: ${barcode} with shopId: ${shopId}, branchId: ${branchId}`);

      if (!barcode || barcode.trim() === '') {
        return res.status(400).json({
          valid: false,
          message: 'Barcode is required'
        });
      }

      // Check if barcode already exists
      const existingProduct = await storage.getProductByBarcode(barcode.trim(), shopId, branchId);

      if (existingProduct) {
        return res.json({
          valid: false,
          message: 'Barcode already exists',
          existingProduct: {
            id: existingProduct.id,
            name: existingProduct.name,
            price: existingProduct.price
          }
        });
      }

      res.json({
        valid: true,
        message: 'Barcode is available'
      });
    } catch (error) {
      console.error('Error validating barcode:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Generate barcode endpoint
  app.post('/api/products/barcode/generate', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Generating barcode for shopId: ${shopId}, branchId: ${branchId}`);

      // Generate a unique barcode
      let barcode: string;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        // Generate a 13-digit EAN-13 style barcode
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        barcode = (timestamp.slice(-10) + random).slice(0, 13);

        // Check if this barcode already exists
        const existingProduct = await storage.getProductByBarcode(barcode, shopId, branchId);
        if (!existingProduct) {
          break;
        }

        attempts++;
      } while (attempts < maxAttempts);

      if (attempts >= maxAttempts) {
        return res.status(500).json({
          message: 'Unable to generate unique barcode. Please try again.'
        });
      }

      console.log(`Generated barcode: ${barcode}`);
      res.json({
        barcode,
        message: 'Barcode generated successfully'
      });
    } catch (error) {
      console.error('Error generating barcode:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Bulk barcode search endpoint
  app.post('/api/products/barcode/bulk-search', authMiddleware, async (req, res) => {
    try {
      const { barcodes } = req.body;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Bulk searching products by barcodes: ${barcodes?.length} barcodes with shopId: ${shopId}, branchId: ${branchId}`);

      if (!barcodes || !Array.isArray(barcodes) || barcodes.length === 0) {
        return res.status(400).json({ message: 'Barcodes array is required' });
      }

      // Limit to 100 barcodes per request
      if (barcodes.length > 100) {
        return res.status(400).json({ message: 'Maximum 100 barcodes allowed per request' });
      }

      const products = await storage.getProductsByBarcodes(barcodes, shopId, branchId);

      // Filter only active products
      const activeProducts = products.filter(product => product.active);

      console.log(`Found ${activeProducts.length} active products for ${barcodes.length} barcodes`);

      // Create a map of barcode to product for easy lookup
      const productMap = activeProducts.reduce((map, product) => {
        if (product.barcode) {
          map[product.barcode] = product;
        }
        return map;
      }, {} as Record<string, any>);

      // Create response with found and not found barcodes
      const result = {
        found: activeProducts,
        foundCount: activeProducts.length,
        notFound: barcodes.filter(barcode => !productMap[barcode]),
        notFoundCount: barcodes.length - activeProducts.length,
        totalRequested: barcodes.length
      };

      res.json(result);
    } catch (error) {
      console.error('Error bulk searching products by barcode:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get all products with barcodes endpoint
  app.get('/api/products/with-barcodes', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Getting all products with barcodes for shopId: ${shopId}, branchId: ${branchId}`);

      const products = await storage.getAllProductsWithBarcodes(shopId, branchId);

      // Filter only active products
      const activeProducts = products.filter(product => product.active);

      console.log(`Found ${activeProducts.length} active products with barcodes`);
      res.json(activeProducts);
    } catch (error) {
      console.error('Error getting products with barcodes:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Barcode inventory report endpoint
  app.get('/api/inventory/barcode-report', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Getting barcode inventory report for shopId: ${shopId}, branchId: ${branchId}`);

      // Get all products with barcodes
      const productsWithBarcodes = await storage.getAllProductsWithBarcodes(shopId, branchId);

      // Get all products without barcodes
      const allProducts = await storage.getAllProducts(true, shopId, branchId);
      const productsWithoutBarcodes = allProducts.filter(product => !product.barcode);

      const report = {
        summary: {
          totalProducts: allProducts.length,
          productsWithBarcodes: productsWithBarcodes.length,
          productsWithoutBarcodes: productsWithoutBarcodes.length,
          barcodePercentage: allProducts.length > 0 ? Math.round((productsWithBarcodes.length / allProducts.length) * 100) : 0
        },
        productsWithBarcodes: productsWithBarcodes.map(product => ({
          id: product.id,
          name: product.name,
          barcode: product.barcode,
          price: product.price,
          quantity: product.quantity,
          categoryId: product.categoryId
        })),
        productsWithoutBarcodes: productsWithoutBarcodes.map(product => ({
          id: product.id,
          name: product.name,
          price: product.price,
          quantity: product.quantity,
          categoryId: product.categoryId
        }))
      };

      console.log(`Barcode report: ${report.summary.productsWithBarcodes}/${report.summary.totalProducts} products have barcodes`);
      res.json(report);
    } catch (error) {
      console.error('Error getting barcode inventory report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Create test products with barcodes for testing
  app.post('/api/products/create-test-products', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Creating test products with barcodes for shopId: ${shopId}, branchId: ${branchId}`);

      const testProducts = [
        {
          name: 'Sample Coffee',
          description: 'Premium coffee blend',
          price: 99.99,
          barcode: '8901234567890',
          sku: 'COFFEE001',
          active: true,
          quantity: 50,
          shopId,
          branchId
        },
        {
          name: 'Sample Tea',
          description: 'Organic green tea',
          price: 149.99,
          barcode: '5901234123457',
          sku: 'TEA001',
          active: true,
          quantity: 30,
          shopId,
          branchId
        },
        {
          name: 'Sample Sandwich',
          description: 'Fresh club sandwich',
          price: 199.99,
          barcode: '4001234567890',
          sku: 'SAND001',
          active: true,
          quantity: 25,
          shopId,
          branchId
        },
        {
          name: 'Sample Juice',
          description: 'Fresh orange juice',
          price: 79.99,
          barcode: '9781234567897',
          sku: 'JUICE001',
          active: true,
          quantity: 40,
          shopId,
          branchId
        },
        {
          name: 'Sample Cake',
          description: 'Chocolate cake slice',
          price: 129.99,
          barcode: '0123456789012',
          sku: 'CAKE001',
          active: true,
          quantity: 15,
          shopId,
          branchId
        }
      ];

      const createdProducts = [];
      for (const productData of testProducts) {
        try {
          // Check if product with this barcode already exists
          const existingProduct = await storage.getProductByBarcode(productData.barcode, shopId, branchId);
          if (!existingProduct) {
            const product = await storage.createProduct(productData);
            createdProducts.push(product);
            console.log(`Created test product: ${product.name} with barcode: ${product.barcode}`);
          } else {
            console.log(`Product with barcode ${productData.barcode} already exists: ${existingProduct.name}`);
          }
        } catch (error) {
          console.error(`Error creating product ${productData.name}:`, error);
        }
      }

      res.json({
        message: `Created ${createdProducts.length} test products`,
        products: createdProducts,
        totalTestProducts: testProducts.length
      });
    } catch (error) {
      console.error('Error creating test products:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Barcode API documentation endpoint
  app.get('/api/barcode/docs', (req, res) => {
    const documentation = {
      title: "NembooBill Barcode API Documentation",
      version: "1.0.0",
      description: "Complete barcode functionality for product management",
      endpoints: [
        {
          method: "GET",
          path: "/api/products/barcode/:barcode",
          description: "Search for a product by barcode",
          parameters: {
            path: {
              barcode: "string - The barcode to search for"
            }
          },
          responses: {
            200: "Product found",
            404: "Product not found or inactive",
            400: "Invalid barcode"
          },
          example: {
            request: "GET /api/products/barcode/8901234567890",
            response: {
              id: 1,
              name: "Sample Product",
              price: 99.99,
              barcode: "8901234567890",
              active: true
            }
          }
        },
        {
          method: "POST",
          path: "/api/products/barcode/validate",
          description: "Validate if a barcode is available for use",
          body: {
            barcode: "string - The barcode to validate"
          },
          responses: {
            200: "Validation result",
            400: "Invalid request"
          },
          example: {
            request: { barcode: "8901234567890" },
            response: {
              valid: false,
              message: "Barcode already exists",
              existingProduct: { id: 1, name: "Sample Product", price: 99.99 }
            }
          }
        },
        {
          method: "POST",
          path: "/api/products/barcode/generate",
          description: "Generate a unique barcode",
          responses: {
            200: "Barcode generated successfully",
            500: "Unable to generate unique barcode"
          },
          example: {
            response: {
              barcode: "1234567890123",
              message: "Barcode generated successfully"
            }
          }
        },
        {
          method: "POST",
          path: "/api/products/barcode/bulk-search",
          description: "Search for multiple products by barcodes",
          body: {
            barcodes: "array - Array of barcodes to search for (max 100)"
          },
          responses: {
            200: "Search results",
            400: "Invalid request"
          },
          example: {
            request: { barcodes: ["8901234567890", "5901234123457"] },
            response: {
              found: [{ id: 1, name: "Product 1", barcode: "8901234567890" }],
              foundCount: 1,
              notFound: ["5901234123457"],
              notFoundCount: 1,
              totalRequested: 2
            }
          }
        },
        {
          method: "GET",
          path: "/api/products/with-barcodes",
          description: "Get all products that have barcodes",
          responses: {
            200: "List of products with barcodes"
          }
        },
        {
          method: "GET",
          path: "/api/inventory/barcode-report",
          description: "Get comprehensive barcode inventory report",
          responses: {
            200: "Barcode inventory report"
          },
          example: {
            response: {
              summary: {
                totalProducts: 100,
                productsWithBarcodes: 75,
                productsWithoutBarcodes: 25,
                barcodePercentage: 75
              },
              productsWithBarcodes: [],
              productsWithoutBarcodes: []
            }
          }
        }
      ],
      notes: [
        "All endpoints require authentication via Bearer token",
        "Endpoints automatically filter by shop and branch based on user context",
        "Only active products are returned in search results",
        "Barcodes are case-sensitive and must be exact matches",
        "Generated barcodes follow EAN-13 format (13 digits)"
      ]
    };

    res.json(documentation);
  });

  app.post('/api/products', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating product with shopId: ${shopId}, branchId: ${branchId}`);

      const productData = insertProductSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed product data:', productData);
      const product = await storage.createProduct(productData);
      console.log('Product created successfully:', product);

      res.status(201).json(product);
    } catch (error) {
      console.error('Error creating product:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/products/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const product = await storage.getProductById(id);

      if (!product) {
        return res.status(404).json({ message: 'Product not found' });
      }

      res.json(product);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/products/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const productData = insertProductSchema.partial().parse(req.body);

      const product = await storage.updateProduct(id, productData);

      if (!product) {
        return res.status(404).json({ message: 'Product not found' });
      }

      res.json(product);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/products/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteProduct(id);

      if (!success) {
        return res.status(404).json({ message: 'Product not found' });
      }

      res.json({ message: 'Product deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Product Branch Prices Routes
  app.get('/api/products/:id/branch-prices', authMiddleware, async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      const branchPrices = await storage.getProductBranchPrices(productId);
      res.json(branchPrices);
    } catch (error) {
      console.error('Error getting product branch prices:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/products/:id/branch-prices', authMiddleware, async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      const branchPriceData = insertProductBranchPriceSchema.parse({
        ...req.body,
        productId
      });

      const branchPrice = await storage.createProductBranchPrice(branchPriceData);
      res.status(201).json(branchPrice);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      console.error('Error creating product branch price:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/products/:productId/branch-prices/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const productId = parseInt(req.params.productId);
      const branchPriceData = insertProductBranchPriceSchema.partial().parse(req.body);

      const branchPrice = await storage.updateProductBranchPrice(id, branchPriceData);
      if (!branchPrice) {
        return res.status(404).json({ message: 'Product branch price not found' });
      }

      res.json(branchPrice);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      console.error('Error updating product branch price:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/products/:productId/branch-prices/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteProductBranchPrice(id);

      if (!success) {
        return res.status(404).json({ message: 'Product branch price not found' });
      }

      res.json({ message: 'Product branch price deleted successfully' });
    } catch (error) {
      console.error('Error deleting product branch price:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Category Routes
  app.get('/api/categories', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting categories with shopId: ${shopId}, branchId: ${branchId}`);

      const categories = await storage.getAllCategories(shopId, branchId);
      console.log(`Retrieved ${categories.length} categories`);

      res.json(categories);
    } catch (error) {
      console.error('Error getting categories:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/categories', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating category with shopId: ${shopId}, branchId: ${branchId}`);

      const categoryData = insertCategorySchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed category data:', categoryData);
      const category = await storage.createCategory(categoryData);
      console.log('Category created successfully:', category);

      res.status(201).json(category);
    } catch (error) {
      console.error('Error creating category:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/categories/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const category = await storage.getCategoryById(id);

      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }

      res.json(category);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/categories/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log(`Updating category ${id} with data:`, req.body);

      // Get the current category to preserve shopId if not provided
      const existingCategory = await storage.getCategoryById(id);
      if (!existingCategory) {
        return res.status(404).json({ message: 'Category not found' });
      }

      // If shopId is not provided in the request, use the existing one
      if (!req.body.shopId && existingCategory.shopId) {
        req.body.shopId = existingCategory.shopId;
        console.log(`Added missing shopId: ${existingCategory.shopId}`);
      }

      // Add shop ID from request if available and not already set
      const shopId = (req as any).shopId;
      if (shopId && !req.body.shopId) {
        req.body.shopId = shopId;
        console.log(`Added shopId from request: ${shopId}`);
      }

      const categoryData = insertCategorySchema.partial().parse(req.body);
      console.log('Parsed category data:', categoryData);

      const category = await storage.updateCategory(id, categoryData);
      console.log('Category updated successfully:', category);

      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }

      res.json(category);
    } catch (error) {
      console.error('Error updating category:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ message: 'Internal server error', error: errorMessage });
    }
  });

  // Table Routes
  app.get('/api/tables', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting tables with shopId: ${shopId}, branchId: ${branchId}`);

      const tables = await storage.getAllTables(shopId, branchId);
      console.log(`Retrieved ${tables.length} tables`);

      res.json(tables);
    } catch (error) {
      console.error('Error getting tables:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/tables', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating table with shopId: ${shopId}, branchId: ${branchId}`);

      const tableData = insertTableSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed table data:', tableData);
      const table = await storage.createTable(tableData);
      console.log('Table created successfully:', table);

      res.status(201).json(table);
    } catch (error) {
      console.error('Error creating table:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/tables/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const tableData = insertTableSchema.partial().parse(req.body);

      // Get shop ID and branch ID from request
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      const table = await storage.updateTable(id, tableData);

      if (!table) {
        return res.status(404).json({ message: 'Table not found' });
      }

      // If status is being updated, emit table status update
      if (tableData.status) {
        // Import the emitTableStatusUpdate function
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit table status update
        await emitTableStatusUpdate(id, tableData.status, shopId, branchId);

        // Also emit full tables update to refresh all tables
        await emitTablesUpdate(shopId, branchId);
      }

      res.json(table);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Customer Routes
  app.get('/api/customers', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting customers with shopId: ${shopId}, branchId: ${branchId}`);

      const customers = await storage.getAllCustomers(shopId, branchId);
      console.log(`Retrieved ${customers.length} customers`);

      res.json(customers);
    } catch (error) {
      console.error('Error getting customers:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/customers', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating customer with shopId: ${shopId}, branchId: ${branchId}`);

      const customerData = insertCustomerSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed customer data:', customerData);
      const customer = await storage.createCustomer(customerData);
      console.log('Customer created successfully:', customer);

      res.status(201).json(customer);
    } catch (error) {
      console.error('Error creating customer:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/customers/phone/:phone', authMiddleware, async (req, res) => {
    try {
      const phone = req.params.phone;
      const customer = await storage.getCustomerByPhone(phone);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      res.json(customer);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/customers/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const customerData = insertCustomerSchema.partial().parse(req.body);

      const customer = await storage.updateCustomer(id, customerData);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      res.json(customer);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/customers/:id/orders', authMiddleware, async (req, res) => {
    try {
      const customerId = parseInt(req.params.id);
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 10;

      console.log(`Getting orders for customer ID: ${customerId}, page: ${page}, pageSize: ${pageSize}`);

      // Verify customer exists
      const customer = await storage.getCustomerById(customerId);
      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Get customer orders with pagination
      const result = await storage.getCustomerOrders(customerId, page, pageSize);

      console.log(`Retrieved ${result.orders.length} orders for customer ${customerId}`);

      res.json(result);
    } catch (error) {
      console.error('Error getting customer orders:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/customers/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // Verify customer exists
      const customer = await storage.getCustomerById(id);
      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Check if customer has orders
      const customerOrders = await storage.getCustomerOrders(id, 1, 1);
      if (customerOrders.total > 0) {
        return res.status(400).json({
          message: 'Cannot delete customer with existing orders. Please delete the orders first or implement soft delete.'
        });
      }

      // Delete customer
      const deletedCustomer = await storage.deleteCustomer(id);

      res.json(deletedCustomer);
    } catch (error) {
      console.error('Error deleting customer:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Customer Loyalty Points Update
  app.patch('/api/customers/:id/loyalty', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { points, operation } = z.object({
        points: z.number().positive(),
        operation: z.enum(['add', 'subtract', 'set'])
      }).parse(req.body);

      // Verify customer exists
      const customer = await storage.getCustomerById(id);
      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Update loyalty points
      const updatedCustomer = await storage.updateCustomerLoyaltyPoints(id, points, operation);

      if (!updatedCustomer) {
        return res.status(404).json({ message: 'Failed to update customer loyalty points' });
      }

      // Update last visit date
      await storage.updateCustomerLastVisit(id);

      res.json(updatedCustomer);
    } catch (error) {
      console.error('Error updating customer loyalty points:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Online Platform Routes
  app.get('/api/platforms', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting platforms with shopId: ${shopId}, branchId: ${branchId}`);

      const platforms = await storage.getAllOnlinePlatforms(shopId, branchId);
      console.log(`Retrieved ${platforms.length} platforms`);

      res.json(platforms);
    } catch (error) {
      console.error('Error getting platforms:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/platforms', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating platform with shopId: ${shopId}, branchId: ${branchId}`);

      const platformData = insertOnlinePlatformSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed platform data:', platformData);
      const platform = await storage.createOnlinePlatform(platformData);
      console.log('Platform created successfully:', platform);

      res.status(201).json(platform);
    } catch (error) {
      console.error('Error creating platform:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/platforms/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const platform = await storage.getOnlinePlatformById(id);

      if (!platform) {
        return res.status(404).json({ message: 'Platform not found' });
      }

      res.json(platform);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/platforms/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log(`Updating platform ${id} with data:`, req.body);

      const platformData = insertOnlinePlatformSchema.partial().parse(req.body);
      console.log('Parsed platform data:', platformData);

      const platform = await storage.updateOnlinePlatform(id, platformData);
      console.log('Platform updated successfully:', platform);

      if (!platform) {
        return res.status(404).json({ message: 'Platform not found' });
      }

      res.json(platform);
    } catch (error) {
      console.error('Error updating platform:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Order Routes - Two-Step Process

  // Step 1: Create order without items
  app.post('/api/orders/create', authMiddleware, async (req, res) => {
    try {
      console.log('Creating order (step 1) with data:', JSON.stringify(req.body, null, 2));

      // Validate the request body
      const { order } = createOrderOnlySchema.parse(req.body);
      console.log('Parsed order data:', JSON.stringify(order, null, 2));

      // Set user ID from auth token
      if (!(req as any).user || !(req as any).user.id) {
        console.error('User ID not found in request:', (req as any).user);
        return res.status(400).json({ message: 'User ID is required' });
      }

      order.userId = Number((req as any).user.id);
      console.log('Added userId:', order.userId);

      // Set shop ID if available
      const shopId = (req as any).shopId;
      if (shopId) {
        order.shopId = Number(shopId);
        console.log('Added shopId:', shopId);
      } else {
        console.error('Shop ID not found in request');
        return res.status(400).json({ message: 'Shop ID is required' });
      }

      // Set branch ID if available
      const branchId = (req as any).branchId;
      if (branchId) {
        order.branchId = Number(branchId);
        console.log('Added branchId:', branchId);
      } else {
        console.log('Branch ID not found in request, using default branch');
      }

      // Ensure all required fields are present and have the correct types
      console.log('Final order data before creation:', JSON.stringify(order, null, 2));

      // Create order without items
      const createdOrder = await storage.createOrderOnly(order);
      console.log('Order created successfully (step 1):', createdOrder);

      // If order is for a table, emit table status update
      if (createdOrder.tableId && createdOrder.orderType === 'dine_in') {
        // Import the emitTableStatusUpdate function
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit table status update (table is now occupied)
        await emitTableStatusUpdate(createdOrder.tableId, 'occupied', shopId, branchId);

        // Also emit full tables update
        await emitTablesUpdate(shopId, branchId);
      }

      res.status(201).json(createdOrder);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error creating order (step 1):', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error creating order (step 1):', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      // Return more detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ message: 'Internal server error', error: errorMessage });
    }
  });

  // Step 2: Add items to an existing order
  app.post('/api/orders/:orderId/items', authMiddleware, async (req, res) => {
    try {
      const orderId = parseInt(req.params.orderId);
      console.log(`Adding items to order ${orderId} (step 2) with data:`, JSON.stringify(req.body, null, 2));

      // Check if items array exists in the request body
      if (!req.body.items || !Array.isArray(req.body.items)) {
        console.error('Invalid request body: items array is missing or not an array');
        return res.status(400).json({
          message: 'Invalid request body',
          details: 'items array is required and must be an array'
        });
      }

      // Log each item in the items array to check for missing or invalid properties
      console.log('Items before validation:');
      req.body.items.forEach((item: any, index: number) => {
        console.log(`Item ${index}:`, item);
        if (!item.productId) console.log(`  - Missing productId in item ${index}`);
        if (!item.quantity) console.log(`  - Missing quantity in item ${index}`);
        if (!item.unitPrice) console.log(`  - Missing unitPrice in item ${index}`);
        if (!item.totalPrice) console.log(`  - Missing totalPrice in item ${index}`);
      });

      // Manually add orderId to each item before validation
      const itemsWithOrderId = req.body.items.map((item: any) => ({
        ...item,
        orderId: Number(orderId)
      }));

      console.log('Items with orderId added:', JSON.stringify(itemsWithOrderId, null, 2));

      // Update the request body with the modified items
      req.body.items = itemsWithOrderId;

      // Validate the request body
      const { items } = createOrderItemsSchema.parse(req.body);
      console.log('Parsed items data after validation:', JSON.stringify(items, null, 2));

      // Check if order exists and belongs to the current user/shop
      const order = await storage.getOrderById(orderId);
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // Verify ownership (user ID or shop ID)
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;

      if (order.userId !== userId && order.shopId !== shopId) {
        return res.status(403).json({ message: 'Not authorized to modify this order' });
      }

      // Add items to the order
      const updatedItems = await storage.addOrderItems(orderId, items);
      console.log('Items added successfully (step 2):', updatedItems);

      // If order is for a table, ensure table status is occupied
      if (order.tableId && order.orderType === 'dine_in') {
        console.log(`Ensuring table ${order.tableId} status is occupied after adding items`);

        // Update table status to occupied
        await storage.updateTableStatus(order.tableId, 'occupied');
        console.log('Table status updated to occupied');

        // Import the emitTableStatusUpdate function
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit table status update (table is now occupied)
        await emitTableStatusUpdate(order.tableId, 'occupied', shopId, order.branchId);

        // Also emit full tables update
        await emitTablesUpdate(shopId, order.branchId);
      }

      // Emit real-time updates for dashboard data
      if (shopId) {
        // Emit dashboard stats update (sales, orders, etc.)
        await emitDashboardStatsUpdate(shopId);

        // Emit recent orders update
        await emitRecentOrdersUpdate(shopId);

        // Emit top products update for today
        await emitTopProductsUpdate(shopId, 'today');
      }

      res.status(201).json(updatedItems);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error adding items to order (step 2):', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error adding items to order (step 2):', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      // Return more detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({
        message: 'Internal server error',
        error: errorMessage,
        details: 'Error occurred while adding items to order'
      });
    }
  });

  // Split Bill endpoint
  app.post('/api/orders/split-bill', authMiddleware, async (req, res) => {
    try {
      console.log('Creating split bill orders with data:', JSON.stringify(req.body, null, 2));

      // Validate the request body
      const { originalOrder, splits } = splitBillSchema.parse(req.body);
      console.log('Parsed split bill data:', JSON.stringify({ originalOrder, splits }, null, 2));

      // Set user ID from auth token
      if (!(req as any).user || !(req as any).user.id) {
        console.error('User ID not found in request:', (req as any).user);
        return res.status(400).json({ message: 'User ID is required' });
      }

      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      // Ensure required fields are set
      originalOrder.userId = userId;
      if (shopId) originalOrder.shopId = shopId;
      if (branchId) originalOrder.branchId = branchId;

      const createdOrders = [];

      // Create separate orders for each split
      for (let i = 0; i < splits.length; i++) {
        const split = splits[i];

        // Generate unique order number for each split
        const randomSuffix = Math.floor(10000 + Math.random() * 90000);
        const splitOrderNumber = `${originalOrder.orderNumber}-SPLIT-${i + 1}-${randomSuffix}`;

        // Create order data for this split
        const splitOrderData = {
          ...originalOrder,
          orderNumber: splitOrderNumber,
          subtotal: split.subtotal,
          taxAmount: split.taxAmount,
          totalAmount: split.total,
          notes: `Split bill for ${split.name}. Original order: ${originalOrder.orderNumber}`,
        };

        // Convert split items to order items format
        const splitItems = split.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          notes: item.notes || '',
        }));

        console.log(`Creating split order ${i + 1} for ${split.name}:`, splitOrderData);
        console.log(`Split items:`, splitItems);

        // Create the split order
        const createdOrder = await storage.createOrder(splitOrderData, splitItems);
        console.log(`Split order ${i + 1} created successfully:`, createdOrder);

        createdOrders.push({
          ...createdOrder,
          splitInfo: {
            personName: split.name,
            splitNumber: i + 1,
            totalSplits: splits.length,
          }
        });
      }

      // If original order was for a table, emit table status update
      if (originalOrder.tableId && originalOrder.orderType === 'dine_in') {
        // Import the emitTableStatusUpdate function
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Table remains occupied since we have multiple orders for it
        await emitTableStatusUpdate(originalOrder.tableId, 'occupied', shopId, branchId);
        await emitTablesUpdate(shopId, branchId);
      }

      // Emit real-time updates for dashboard data
      if (shopId) {
        // Emit dashboard stats update (sales, orders, etc.)
        await emitDashboardStatsUpdate(shopId);

        // Emit recent orders update
        await emitRecentOrdersUpdate(shopId);

        // Emit top products update for today
        await emitTopProductsUpdate(shopId, 'today');
      }

      res.status(201).json({
        message: 'Split bill orders created successfully',
        orders: createdOrders,
        totalOrders: createdOrders.length,
      });
    } catch (error) {
      console.error('Error creating split bill orders:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Table Transfer endpoint
  app.post('/api/orders/transfer-table', authMiddleware, async (req, res) => {
    try {
      console.log('Transferring order to different table with data:', JSON.stringify(req.body, null, 2));

      // Validate the request body
      const { fromTableId, toTableId, orderId } = tableTransferSchema.parse(req.body);
      console.log('Parsed transfer data:', { fromTableId, toTableId, orderId });

      // Set user ID from auth token
      if (!(req as any).user || !(req as any).user.id) {
        console.error('User ID not found in request:', (req as any).user);
        return res.status(400).json({ message: 'User ID is required' });
      }

      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      // Check if order exists and belongs to the current user/shop
      const order = await storage.getOrderById(orderId);
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // Verify ownership (user ID or shop ID)
      if (order.userId !== userId && order.shopId !== shopId) {
        return res.status(403).json({ message: 'Not authorized to modify this order' });
      }

      // Verify the order is currently assigned to the fromTable
      if (order.tableId !== fromTableId) {
        return res.status(400).json({
          message: 'Order is not currently assigned to the specified source table'
        });
      }

      // Check if the destination table exists and is available
      const toTable = await storage.getTableById(toTableId);
      if (!toTable) {
        return res.status(404).json({ message: 'Destination table not found' });
      }

      if (toTable.status !== 'available') {
        return res.status(400).json({
          message: 'Destination table is not available for transfer'
        });
      }

      // Check if the source table exists
      const fromTable = await storage.getTableById(fromTableId);
      if (!fromTable) {
        return res.status(404).json({ message: 'Source table not found' });
      }

      // Perform the transfer
      console.log(`Transferring order ${orderId} from table ${fromTableId} to table ${toTableId}`);

      // Update the order's table assignment
      const updatedOrder = await storage.updateOrderTable(orderId, toTableId);
      console.log('Order table updated successfully:', updatedOrder);

      // Update table statuses
      // Set destination table to occupied
      await storage.updateTableStatus(toTableId, 'occupied');
      console.log(`Table ${toTableId} status updated to occupied`);

      // Set source table to available (assuming no other orders on this table)
      await storage.updateTableStatus(fromTableId, 'available');
      console.log(`Table ${fromTableId} status updated to available`);

      // Emit real-time updates for table status changes
      if (shopId) {
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit status updates for both tables
        await emitTableStatusUpdate(fromTableId, 'available', shopId, branchId);
        await emitTableStatusUpdate(toTableId, 'occupied', shopId, branchId);

        // Also emit full tables update
        await emitTablesUpdate(shopId, branchId);

        // Emit recent orders update
        await emitRecentOrdersUpdate(shopId);
      }

      res.json({
        message: 'Order transferred successfully',
        order: updatedOrder,
        transfer: {
          fromTable: fromTable.name,
          toTable: toTable.name,
          orderId: orderId,
          transferredAt: new Date().toISOString(),
        }
      });
    } catch (error) {
      console.error('Error transferring order to different table:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Table Merge endpoint
  app.post('/api/orders/merge-tables', authMiddleware, async (req, res) => {
    try {
      console.log('Merging tables with data:', JSON.stringify(req.body, null, 2));

      // Validate the request body
      const { primaryTableId, secondaryTableIds, orderIds } = tableMergeSchema.parse(req.body);
      console.log('Parsed merge data:', { primaryTableId, secondaryTableIds, orderIds });

      // Set user ID from auth token
      if (!(req as any).user || !(req as any).user.id) {
        console.error('User ID not found in request:', (req as any).user);
        return res.status(400).json({ message: 'User ID is required' });
      }

      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      // Validate that we have at least 2 orders to merge
      if (orderIds.length < 2) {
        return res.status(400).json({ message: 'At least 2 orders are required for merging' });
      }

      // Get all orders to be merged
      const orders = await Promise.all(orderIds.map(id => storage.getOrderWithItems(id)));
      const validOrders = orders.filter(order => order !== undefined);

      if (validOrders.length !== orderIds.length) {
        return res.status(404).json({ message: 'One or more orders not found' });
      }

      // Verify ownership (user ID or shop ID) for all orders
      for (const orderData of validOrders) {
        const order = orderData.order;
        if (order.userId !== userId && order.shopId !== shopId) {
          return res.status(403).json({ message: 'Not authorized to modify one or more orders' });
        }
      }

      // Get the primary order (first order in the list)
      const primaryOrderData = validOrders[0];
      const primaryOrder = primaryOrderData.order;
      const primaryItems = primaryOrderData.items;

      // Verify the primary order is assigned to the primary table
      if (primaryOrder.tableId !== primaryTableId) {
        return res.status(400).json({
          message: 'Primary order is not assigned to the specified primary table'
        });
      }

      // Check if all tables exist
      const allTableIds = [primaryTableId, ...secondaryTableIds];
      const tables = await Promise.all(allTableIds.map(id => storage.getTableById(id)));
      const validTables = tables.filter(table => table !== undefined);

      if (validTables.length !== allTableIds.length) {
        return res.status(404).json({ message: 'One or more tables not found' });
      }

      // Calculate combined totals
      let combinedSubtotal = 0;
      let combinedTaxAmount = 0;
      let combinedDiscountAmount = 0;
      let combinedTotal = 0;
      const allItems: any[] = [];

      for (const orderData of validOrders) {
        const order = orderData.order;
        const items = orderData.items;

        combinedSubtotal += order.subtotal;
        combinedTaxAmount += order.taxAmount;
        combinedDiscountAmount += order.discountAmount;
        combinedTotal += order.totalAmount;

        // Add items to the combined list
        allItems.push(...items);
      }

      console.log('Combined totals:', {
        subtotal: combinedSubtotal,
        taxAmount: combinedTaxAmount,
        discountAmount: combinedDiscountAmount,
        total: combinedTotal,
        itemCount: allItems.length
      });

      // Generate new order number for the merged order
      const mergedOrderNumber = `MERGED-${Math.floor(10000 + Math.random() * 90000)}`;

      // Create the merged order
      const mergedOrderData = {
        orderNumber: mergedOrderNumber,
        orderType: primaryOrder.orderType,
        status: primaryOrder.status,
        tableId: primaryTableId,
        numberOfPersons: primaryOrder.numberOfPersons,
        customerId: primaryOrder.customerId,
        subtotal: combinedSubtotal,
        taxAmount: combinedTaxAmount,
        discountAmount: combinedDiscountAmount,
        totalAmount: combinedTotal,
        paymentMethod: primaryOrder.paymentMethod,
        paymentStatus: primaryOrder.paymentStatus,
        notes: `Merged from orders: ${validOrders.map(o => o.order.orderNumber).join(', ')}`,
        shopId: primaryOrder.shopId,
        branchId: primaryOrder.branchId,
        userId: primaryOrder.userId,
      };

      // Prepare merged items
      const mergedItems = allItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        notes: item.notes || '',
      }));

      console.log('Creating merged order:', mergedOrderData);
      console.log('Merged items count:', mergedItems.length);

      // Create the merged order using two-step process
      const mergedOrder = await storage.createOrder(mergedOrderData, mergedItems);
      console.log('Merged order created successfully:', mergedOrder);

      // Cancel the original orders
      for (const orderData of validOrders) {
        await storage.updateOrderStatus(orderData.order.id, 'cancelled');
        console.log(`Cancelled original order ${orderData.order.id}`);
      }

      // Update table statuses
      // Keep primary table as occupied (it now has the merged order)
      await storage.updateTableStatus(primaryTableId, 'occupied');

      // Set secondary tables to available
      for (const tableId of secondaryTableIds) {
        await storage.updateTableStatus(tableId, 'available');
        console.log(`Table ${tableId} status updated to available`);
      }

      // Emit real-time updates for table status changes
      if (shopId) {
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit status updates for all affected tables
        await emitTableStatusUpdate(primaryTableId, 'occupied', shopId, branchId);
        for (const tableId of secondaryTableIds) {
          await emitTableStatusUpdate(tableId, 'available', shopId, branchId);
        }

        // Also emit full tables update
        await emitTablesUpdate(shopId, branchId);

        // Emit recent orders update
        await emitRecentOrdersUpdate(shopId);
      }

      res.json({
        message: 'Tables merged successfully',
        mergedOrder: mergedOrder,
        merge: {
          primaryTable: validTables[0].name,
          secondaryTables: validTables.slice(1).map(t => t.name),
          originalOrders: validOrders.map(o => o.order.orderNumber),
          mergedOrderNumber: mergedOrderNumber,
          totalItems: allItems.length,
          totalAmount: combinedTotal,
          mergedAt: new Date().toISOString(),
        }
      });
    } catch (error) {
      console.error('Error merging tables:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Legacy endpoint (for backward compatibility)
  app.post('/api/orders', authMiddleware, async (req, res) => {
    try {
      console.log('Creating order with data (legacy endpoint):', JSON.stringify(req.body, null, 2));

      // Get the request body
      let { order, items } = req.body;

      // Ensure order has userId
      if (!order.userId) {
        order.userId = (req as any).user.id;
        console.log('Added missing userId:', order.userId);
      }

      // Set shop ID if available and not already set
      const shopId = (req as any).shopId;
      if (shopId && !order.shopId) {
        order.shopId = shopId;
        console.log('Added shopId:', shopId);
      }

      // Set branch ID if available and not already set
      const branchId = (req as any).branchId;
      if (branchId && !order.branchId) {
        order.branchId = branchId;
        console.log('Added branchId:', branchId);
      }

      // Validate the request body after adding required fields
      const validatedData = createOrderSchema.parse({ order, items });
      order = validatedData.order;
      items = validatedData.items;

      console.log('Parsed order data:', JSON.stringify(order, null, 2));
      console.log('Parsed items data:', JSON.stringify(items, null, 2));

      // Process items to ensure they have orderId if needed
      // This will be handled by the storage.createOrder method

      const createdOrder = await storage.createOrder(order, items);
      console.log('Order created successfully:', createdOrder);

      // Emit real-time updates for dashboard data
      if (shopId) {
        // Emit dashboard stats update (sales, orders, etc.)
        await emitDashboardStatsUpdate(shopId);

        // Emit recent orders update
        await emitRecentOrdersUpdate(shopId);

        // Emit top products update for today
        await emitTopProductsUpdate(shopId, 'today');

        // If order is for a table, emit table status update
        if (createdOrder.tableId && createdOrder.orderType === 'dine_in') {
          // Import the emitTableStatusUpdate function
          const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

          // Emit table status update (table is now occupied)
          await emitTableStatusUpdate(createdOrder.tableId, 'occupied', shopId, branchId);

          // Also emit full tables update
          await emitTablesUpdate(shopId, branchId);
        }
      }

      res.status(201).json(createdOrder);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error creating order:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error creating order:', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/orders/recent', authMiddleware, async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      // Use shop ID from request if available
      const shopId = (req as any).shopId;
      // Use branch ID from request if available
      const branchId = (req as any).branchId;

      console.log(`Getting recent orders with shopId: ${shopId}, branchId: ${branchId}, limit: ${limit}`);

      const orders = await storage.getRecentOrders(limit, shopId, branchId);
      console.log(`Retrieved ${orders.length} recent orders`);

      res.json(orders);
    } catch (error) {
      console.error('Error getting recent orders:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/orders/all', authMiddleware, async (req, res) => {
    try {
      console.log('=== ORDERS API REQUEST ===');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Received request for paginated orders with query:', req.query);
      console.log('User from token:', (req as any).user);
      console.log('Shop ID from request:', (req as any).shopId);
      console.log('Branch ID from request:', (req as any).branchId);

      // Parse pagination parameters
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 10;

      // Get status filter if provided
      const status = req.query.status as string | undefined;

      console.log('Parsed parameters:', { page, pageSize, status });

      // Use shop ID from request if available
      const shopId = (req as any).shopId;

      // Use branch ID from request if available
      const branchId = (req as any).branchId;
      console.log('Using branch ID for orders query:', branchId);

      // Add cache-busting headers
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // Get paginated orders
      console.log('Calling storage.getAllOrdersPaginated with params:', { page, pageSize, shopId, branchId, status });
      const result = await storage.getAllOrdersPaginated(page, pageSize, shopId, branchId, status);
      console.log('Retrieved paginated orders:', {
        total: result.total,
        totalPages: result.totalPages,
        ordersCount: result.orders.length,
        firstOrderNumber: result.orders[0]?.orderNumber,
        firstOrderCreatedAt: result.orders[0]?.createdAt
      });

      res.json(result);
    } catch (error) {
      console.error('Error fetching paginated orders:', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Add endpoint to check for recent orders (for debugging)
  app.get('/api/orders/recent-check', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      // Get the 5 most recent orders
      const recentOrders = await storage.getAllOrdersPaginated(1, 5, shopId, branchId);

      console.log('Recent orders check:', {
        total: recentOrders.total,
        orders: recentOrders.orders.map(o => ({
          orderNumber: o.orderNumber,
          createdAt: o.createdAt,
          status: o.status,
          orderType: o.orderType
        }))
      });

      res.json({
        timestamp: new Date().toISOString(),
        total: recentOrders.total,
        recentOrders: recentOrders.orders.map(o => ({
          orderNumber: o.orderNumber,
          createdAt: o.createdAt,
          status: o.status,
          orderType: o.orderType
        }))
      });
    } catch (error) {
      console.error('Error checking recent orders:', error);
      res.status(500).json({ message: 'Failed to check recent orders' });
    }
  });

  // Get order status summary (today's orders only)
  app.get('/api/orders/status-summary', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Getting order status summary with shopId: ${shopId}, branchId: ${branchId}`);

      // First, let's get ALL orders to see what we have
      const allOrders = await storage.getAllOrders(undefined, undefined, shopId, branchId);
      console.log(`Total orders in database for shop ${shopId}: ${allOrders.length}`);

      if (allOrders.length > 0) {
        console.log('Sample orders with dates:');
        allOrders.slice(0, 5).forEach(order => {
          console.log(`Order ${order.id}: ${order.orderNumber} - Status: ${order.status} - Created: ${order.createdAt}`);
        });
      }

      // Get today's date range
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      console.log(`Getting orders for today: ${today.toISOString()} to ${tomorrow.toISOString()}`);

      // Get today's orders for the shop/branch
      const orders = await storage.getAllOrders(today, tomorrow, shopId, branchId);

      console.log(`Found ${orders.length} orders for today`);

      if (orders.length > 0) {
        console.log('Today\'s orders:');
        orders.forEach(order => {
          console.log(`Order ${order.id}: ${order.orderNumber} - Status: ${order.status} - Created: ${order.createdAt}`);
        });
      }

      // Count orders by status
      const statusCounts = {
        pending: 0,
        confirmed: 0,
        preparing: 0,
        ready: 0,
        completed: 0,
        cancelled: 0,
        draft: 0,
        hold: 0
      };

      orders.forEach(order => {
        if (statusCounts.hasOwnProperty(order.status)) {
          statusCounts[order.status]++;
        }
      });

      // Calculate total
      const total = Object.values(statusCounts).reduce((sum, count) => sum + count, 0);

      const response = {
        ...statusCounts,
        total
      };

      console.log('Order status summary:', response);
      res.json(response);
    } catch (error) {
      console.error('Error getting order status summary:', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/orders/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const orderData = await storage.getOrderWithItems(id);

      if (!orderData) {
        return res.status(404).json({ message: 'Order not found' });
      }

      res.json(orderData);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Get active order for a table
  app.get('/api/tables/:tableId/active-order', authMiddleware, async (req, res) => {
    try {
      const tableId = parseInt(req.params.tableId);
      console.log(`Getting active order for table ID: ${tableId}`);

      const orderData = await storage.getActiveOrderByTableId(tableId);

      if (!orderData) {
        console.log(`No active order found for table ID: ${tableId}`);
        return res.status(404).json({ message: 'No active order found for this table' });
      }

      console.log(`Found active order for table ID: ${tableId}`, orderData.order.id);
      res.json(orderData);
    } catch (error) {
      console.error('Error getting active order for table:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/orders/:id/status', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = z.object({ status: z.string() }).parse(req.body);

      // Get shop ID and branch ID from request
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      const order = await storage.updateOrderStatus(id, status);

      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // If order is completed or cancelled and is a dine-in order with a table,
      // emit table status update to notify all clients
      if ((status === 'completed' || status === 'cancelled') &&
          order.tableId &&
          order.orderType === 'dine_in') {

        // Import the emitTableStatusUpdate function
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit table status update (table is now available)
        await emitTableStatusUpdate(order.tableId, 'available', shopId, branchId);

        // Also emit full tables update
        await emitTablesUpdate(shopId, branchId);
      }

      res.json(order);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Delete order
  app.delete('/api/orders/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      console.log(`Deleting order with id: ${id}`);

      // Verify order exists and get its details first
      const order = await storage.getOrderById(id);
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // Get shop ID and branch ID from request
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      // Delete the order
      const success = await storage.deleteOrder(id);

      if (!success) {
        return res.status(500).json({ message: 'Failed to delete order' });
      }

      // If order was for a table, update table status to available
      if (order.tableId && order.orderType === 'dine_in') {
        // Import the emitTableStatusUpdate function
        const { emitTableStatusUpdate, emitTablesUpdate } = await import('./socket');

        // Emit table status update (table is now available)
        await emitTableStatusUpdate(order.tableId, 'available', shopId, branchId);

        // Also emit full tables update
        await emitTablesUpdate(shopId, branchId);
      }

      console.log('Order deleted successfully');
      res.json({ message: 'Order deleted successfully' });
    } catch (error) {
      console.error('Error deleting order:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Expense Routes
  app.post('/api/expenses', authMiddleware, async (req, res) => {
    try {
      console.log('Creating expense with data:', req.body);
      console.log('User from token:', (req as any).user);

      // Add userId, shopId, and branchId to the request body before validation
      const dataWithIds = {
        ...req.body,
        userId: (req as any).user.id,
        shopId: (req as any).shopId,
        branchId: (req as any).branchId
      };
      console.log('Data with userId, shopId, and branchId added:', dataWithIds);

      const expenseData = insertExpenseSchema.parse(dataWithIds);
      console.log('Parsed expense data:', expenseData);

      const expense = await storage.createExpense(expenseData);
      console.log('Expense created successfully:', expense);

      res.status(201).json(expense);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error creating expense:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error creating expense:', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/expenses', authMiddleware, async (req, res) => {
    try {
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const category = req.query.category as string | undefined;
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting expenses with shopId: ${shopId}, branchId: ${branchId}, date range: ${startDate} - ${endDate}, category: ${category}`);

      const expenses = await storage.getAllExpenses(startDate, endDate, shopId, branchId, category);
      console.log(`Retrieved ${expenses.length} expenses`);

      res.json(expenses);
    } catch (error) {
      console.error('Error getting expenses:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/expenses/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user.id;
      console.log(`Updating expense with id: ${id} by user: ${userId}`);

      // Validate user has access to this expense
      const hasAccess = await storage.validateExpenseAccess(userId, id);
      if (!hasAccess) {
        return res.status(403).json({ message: 'You do not have permission to modify this expense' });
      }

      // Add userId, shopId, and branchId to the request body before validation
      const dataWithIds = {
        ...req.body,
        userId: (req as any).user.id,
        shopId: (req as any).shopId,
        branchId: (req as any).branchId
      };

      const expenseData = insertExpenseSchema.partial().parse(dataWithIds);
      console.log('Parsed expense update data:', expenseData);

      const expense = await storage.updateExpense(id, expenseData);

      if (!expense) {
        return res.status(404).json({ message: 'Expense not found' });
      }

      console.log('Expense updated successfully:', expense);
      res.json(expense);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error updating expense:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error updating expense:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/expenses/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user.id;
      console.log(`Deleting expense with id: ${id} by user: ${userId}`);

      // Validate user has access to this expense
      const hasAccess = await storage.validateExpenseAccess(userId, id);
      if (!hasAccess) {
        return res.status(403).json({ message: 'You do not have permission to delete this expense' });
      }

      const success = await storage.deleteExpense(id);

      if (!success) {
        return res.status(404).json({ message: 'Expense not found' });
      }

      console.log('Expense deleted successfully');
      res.json({ message: 'Expense deleted successfully' });
    } catch (error) {
      console.error('Error deleting expense:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Purchase Routes
  app.post('/api/purchases', authMiddleware, async (req, res) => {
    try {
      console.log('Creating purchase with data:', req.body);
      console.log('User from token:', (req as any).user);

      // Add userId, shopId, and branchId to the request body before validation
      const dataWithIds = {
        ...req.body,
        userId: (req as any).user.id,
        shopId: (req as any).shopId,
        branchId: (req as any).branchId
      };
      console.log('Data with userId, shopId, and branchId added:', dataWithIds);

      const purchaseData = insertPurchaseSchema.parse(dataWithIds);
      console.log('Parsed purchase data:', purchaseData);

      const purchase = await storage.createPurchase(purchaseData);
      console.log('Purchase created successfully:', purchase);

      res.status(201).json(purchase);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error creating purchase:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error creating purchase:', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/purchases', authMiddleware, async (req, res) => {
    try {
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const category = req.query.category as string | undefined;
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting purchases with shopId: ${shopId}, branchId: ${branchId}, date range: ${startDate} - ${endDate}, category: ${category}`);

      const purchases = await storage.getAllPurchases(startDate, endDate, shopId, branchId, category);
      console.log(`Retrieved ${purchases.length} purchases`);

      res.json(purchases);
    } catch (error) {
      console.error('Error getting purchases:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/purchases/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user.id;
      console.log(`Updating purchase with id: ${id} by user: ${userId}`);

      // Validate user has access to this purchase
      const hasAccess = await storage.validatePurchaseAccess(userId, id);
      if (!hasAccess) {
        return res.status(403).json({ message: 'You do not have permission to modify this purchase' });
      }

      // Add userId, shopId, and branchId to the request body before validation
      const dataWithIds = {
        ...req.body,
        userId: (req as any).user.id,
        shopId: (req as any).shopId,
        branchId: (req as any).branchId
      };

      const purchaseData = insertPurchaseSchema.partial().parse(dataWithIds);
      console.log('Parsed purchase update data:', purchaseData);

      const purchase = await storage.updatePurchase(id, purchaseData);

      if (!purchase) {
        return res.status(404).json({ message: 'Purchase not found' });
      }

      console.log('Purchase updated successfully:', purchase);
      res.json(purchase);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error updating purchase:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error updating purchase:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/purchases/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = (req as any).user.id;
      console.log(`Deleting purchase with id: ${id} by user: ${userId}`);

      // Validate user has access to this purchase
      const hasAccess = await storage.validatePurchaseAccess(userId, id);
      if (!hasAccess) {
        return res.status(403).json({ message: 'You do not have permission to delete this purchase' });
      }

      const success = await storage.deletePurchase(id);

      if (!success) {
        return res.status(404).json({ message: 'Purchase not found' });
      }

      console.log('Purchase deleted successfully');
      res.json({ message: 'Purchase deleted successfully' });
    } catch (error) {
      console.error('Error deleting purchase:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Tax Settings Routes
  app.get('/api/settings/tax', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting tax settings with shopId: ${shopId}, branchId: ${branchId}`);

      const taxSettings = await storage.getAllTaxSettings(shopId, branchId);
      console.log(`Retrieved ${taxSettings.length} tax settings`);

      res.json(taxSettings);
    } catch (error) {
      console.error('Error getting tax settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/tax', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating tax setting with shopId: ${shopId}, branchId: ${branchId}`);

      const taxSettingData = insertTaxSettingSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed tax setting data:', taxSettingData);
      const taxSetting = await storage.createTaxSetting(taxSettingData);
      console.log('Tax setting created successfully:', taxSetting);

      res.status(201).json(taxSetting);
    } catch (error) {
      console.error('Error creating tax setting:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/settings/tax/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const taxSetting = await storage.getTaxSettingById(id);

      if (!taxSetting) {
        return res.status(404).json({ message: 'Tax setting not found' });
      }

      res.json(taxSetting);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/settings/tax/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const taxSettingData = insertTaxSettingSchema.partial().parse(req.body);

      const taxSetting = await storage.updateTaxSetting(id, taxSettingData);

      if (!taxSetting) {
        return res.status(404).json({ message: 'Tax setting not found' });
      }

      res.json(taxSetting);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/settings/tax/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteTaxSetting(id);

      if (!success) {
        return res.status(404).json({ message: 'Tax setting not found' });
      }

      res.json({ message: 'Tax setting deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Discount Settings Routes
  app.get('/api/settings/discount', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting discount settings with shopId: ${shopId}, branchId: ${branchId}`);

      const discountSettings = await storage.getAllDiscountSettings(shopId, branchId);
      console.log(`Retrieved ${discountSettings.length} discount settings`);

      res.json(discountSettings);
    } catch (error) {
      console.error('Error getting discount settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/discount', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating discount setting with shopId: ${shopId}, branchId: ${branchId}`);

      const discountSettingData = insertDiscountSettingSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      console.log('Parsed discount setting data:', discountSettingData);
      const discountSetting = await storage.createDiscountSetting(discountSettingData);
      console.log('Discount setting created successfully:', discountSetting);

      res.status(201).json(discountSetting);
    } catch (error) {
      console.error('Error creating discount setting:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/settings/discount/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const discountSetting = await storage.getDiscountSettingById(id);

      if (!discountSetting) {
        return res.status(404).json({ message: 'Discount setting not found' });
      }

      res.json(discountSetting);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/settings/discount/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const discountSettingData = insertDiscountSettingSchema.partial().parse(req.body);

      const discountSetting = await storage.updateDiscountSetting(id, discountSettingData);

      if (!discountSetting) {
        return res.status(404).json({ message: 'Discount setting not found' });
      }

      res.json(discountSetting);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/settings/discount/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteDiscountSetting(id);

      if (!success) {
        return res.status(404).json({ message: 'Discount setting not found' });
      }

      res.json({ message: 'Discount setting deleted successfully' });
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Cart Management Routes
  app.post('/api/cart/add', authMiddleware, async (req, res) => {
    try {
      console.log('Adding item to cart with data:', req.body);

      // Validate the request body
      const { item } = addToCartSchema.parse(req.body);
      console.log('Parsed cart item data:', item);

      // Get product details
      const product = await storage.getProductById(item.productId);
      if (!product) {
        return res.status(404).json({ message: 'Product not found' });
      }

      // Return the cart item with product details
      res.status(200).json({
        productId: item.productId,
        quantity: item.quantity,
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image
        }
      });
    } catch (error) {
      console.error('Error adding item to cart:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/cart/update', authMiddleware, async (req, res) => {
    try {
      console.log('Updating cart item with data:', req.body);

      // Validate the request body
      const { item } = updateCartItemSchema.parse(req.body);
      console.log('Parsed cart item update data:', item);

      // Get product details
      const product = await storage.getProductById(item.productId);
      if (!product) {
        return res.status(404).json({ message: 'Product not found' });
      }

      // Return the updated cart item with product details
      res.status(200).json({
        productId: item.productId,
        quantity: item.quantity,
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
          image: product.image
        }
      });
    } catch (error) {
      console.error('Error updating cart item:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/cart/remove', authMiddleware, async (req, res) => {
    try {
      console.log('Removing item from cart with data:', req.body);

      // Validate the request body
      const { productId } = removeFromCartSchema.parse(req.body);
      console.log('Parsed cart item removal data, productId:', productId);

      // Return success response
      res.status(200).json({
        success: true,
        message: 'Item removed from cart',
        productId
      });
    } catch (error) {
      console.error('Error removing item from cart:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Payment Methods Routes
  app.get('/api/payment-methods', authMiddleware, async (req, res) => {
    try {
      const active = req.query.active === 'true' ? true :
                    req.query.active === 'false' ? false : undefined;
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting payment methods with shopId: ${shopId}, branchId: ${branchId}, active: ${active}`);

      const paymentMethods = await storage.getAllPaymentMethods(active, shopId, branchId);
      console.log(`Retrieved ${paymentMethods.length} payment methods`);

      res.json(paymentMethods);
    } catch (error) {
      console.error('Error getting payment methods:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/payment-methods', authMiddleware, async (req, res) => {
    try {
      console.log('Creating payment method with data:', req.body);

      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating payment method with shopId: ${shopId}, branchId: ${branchId}`);

      const paymentMethodData = insertPaymentMethodSchema.parse({
        ...req.body,
        shopId,
        branchId
      });
      console.log('Parsed payment method data:', paymentMethodData);

      const paymentMethod = await storage.createPaymentMethod(paymentMethodData);
      console.log('Payment method created successfully:', paymentMethod);

      res.status(201).json(paymentMethod);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('Validation error creating payment method:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      console.error('Error creating payment method:', error);
      console.error('Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Subscription Payment Methods Routes - MUST BE BEFORE /:id routes
  app.get('/api/payment-methods/subscription', authMiddleware, (req, res) => {
    try {
      console.log('=== PAYMENT METHODS: Returning empty array (temporary fix) ===');
      console.log('Request headers:', req.headers);
      console.log('Shop ID:', (req as any).shopId);
      res.json([]);
    } catch (error) {
      console.error('Error in payment methods subscription route:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/payment-methods/subscription', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const userId = (req as any).user.id;

      if (!shopId) {
        return res.status(400).json({ message: 'Shop ID is required' });
      }

      const paymentMethodData = {
        ...req.body,
        shopId,
        createdBy: userId
      };

      const paymentMethod = await storage.createSubscriptionPaymentMethod(paymentMethodData);
      res.status(201).json(paymentMethod);
    } catch (error) {
      console.error('Error creating subscription payment method:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/payment-methods/subscription/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const paymentMethodData = req.body;

      // Validate ID
      if (isNaN(id) || id <= 0) {
        return res.status(400).json({ message: 'Invalid payment method ID' });
      }

      const paymentMethod = await storage.updateSubscriptionPaymentMethod(id, paymentMethodData);

      if (!paymentMethod) {
        return res.status(404).json({ message: 'Payment method not found' });
      }

      res.json(paymentMethod);
    } catch (error) {
      console.error('Error updating subscription payment method:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/payment-methods/subscription/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // Validate ID
      if (isNaN(id) || id <= 0) {
        return res.status(400).json({ message: 'Invalid payment method ID' });
      }

      const paymentMethod = await storage.deleteSubscriptionPaymentMethod(id);

      if (!paymentMethod) {
        return res.status(404).json({ message: 'Payment method not found' });
      }

      res.json({ message: 'Payment method deleted successfully' });
    } catch (error) {
      console.error('Error deleting subscription payment method:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/payment-methods/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const paymentMethod = await storage.getPaymentMethodById(id);

      if (!paymentMethod) {
        return res.status(404).json({ message: 'Payment method not found' });
      }

      res.json(paymentMethod);
    } catch (error) {
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/payment-methods/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const paymentMethodData = insertPaymentMethodSchema.partial().parse(req.body);

      const paymentMethod = await storage.updatePaymentMethod(id, paymentMethodData);

      if (!paymentMethod) {
        return res.status(404).json({ message: 'Payment method not found' });
      }

      res.json(paymentMethod);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Printer Settings Routes
  app.get('/api/settings/printer', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting printer settings with shopId: ${shopId}, branchId: ${branchId}`);

      const printerSetting = await storage.getPrinterSettingByShop(shopId, branchId);

      if (!printerSetting) {
        return res.json(null);
      }

      res.json(printerSetting);
    } catch (error) {
      console.error('Error getting printer settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/printer', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating printer setting with shopId: ${shopId}, branchId: ${branchId}`);

      // Check if printer setting already exists for this shop/branch
      const existingPrinterSetting = await storage.getPrinterSettingByShop(shopId, branchId);

      if (existingPrinterSetting) {
        // Update existing printer setting
        const printerSettingData = insertPrinterSettingSchema.partial().parse({
          ...req.body,
          shopId,
          branchId
        });

        const updatedPrinterSetting = await storage.updatePrinterSetting(existingPrinterSetting.id, printerSettingData);
        return res.json(updatedPrinterSetting);
      }

      // Create new printer setting
      const printerSettingData = insertPrinterSettingSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      const printerSetting = await storage.createPrinterSetting(printerSettingData);
      res.status(201).json(printerSetting);
    } catch (error) {
      console.error('Error creating/updating printer setting:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // User Preferences Routes
  app.get('/api/settings/preferences', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      console.log(`Getting user preferences for userId: ${userId}`);

      const userPreference = await storage.getUserPreferenceByUserId(userId);

      if (!userPreference) {
        return res.json(null);
      }

      res.json(userPreference);
    } catch (error) {
      console.error('Error getting user preferences:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/preferences', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      console.log(`Creating/updating user preferences for userId: ${userId}`);

      // Check if user preference already exists
      const existingUserPreference = await storage.getUserPreferenceByUserId(userId);

      if (existingUserPreference) {
        // Update existing user preference
        const userPreferenceData = insertUserPreferenceSchema.partial().parse({
          ...req.body,
          userId
        });

        const updatedUserPreference = await storage.updateUserPreference(existingUserPreference.id, userPreferenceData);
        return res.json(updatedUserPreference);
      }

      // Create new user preference
      const userPreferenceData = insertUserPreferenceSchema.parse({
        ...req.body,
        userId
      });

      const userPreference = await storage.createUserPreference(userPreferenceData);
      res.status(201).json(userPreference);
    } catch (error) {
      console.error('Error creating/updating user preferences:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Rounding Settings Routes
  app.get('/api/settings/rounding', authMiddleware, async (req, res) => {
    try {
      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting rounding settings with shopId: ${shopId}, branchId: ${branchId}`);

      const roundingSetting = await storage.getRoundingSettingByShop(shopId, branchId);

      if (!roundingSetting) {
        return res.json(null);
      }

      res.json(roundingSetting);
    } catch (error) {
      console.error('Error getting rounding settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/rounding', authMiddleware, async (req, res) => {
    try {
      // Add shop ID and branch ID to the request body if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Creating rounding setting with shopId: ${shopId}, branchId: ${branchId}`);

      // Check if rounding setting already exists for this shop/branch
      const existingRoundingSetting = await storage.getRoundingSettingByShop(shopId, branchId);

      if (existingRoundingSetting) {
        // Update existing rounding setting
        const roundingSettingData = insertRoundingSettingSchema.partial().parse({
          ...req.body,
          shopId,
          branchId
        });

        const updatedRoundingSetting = await storage.updateRoundingSetting(existingRoundingSetting.id, roundingSettingData);
        return res.json(updatedRoundingSetting);
      }

      // Create new rounding setting
      const roundingSettingData = insertRoundingSettingSchema.parse({
        ...req.body,
        shopId,
        branchId
      });

      const roundingSetting = await storage.createRoundingSetting(roundingSettingData);
      res.status(201).json(roundingSetting);
    } catch (error) {
      console.error('Error creating/updating rounding setting:', error);

      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Dashboard Routes
  app.get('/api/dashboard/stats', authMiddleware, async (req, res) => {
    try {
      // Use shop ID from request if available
      const shopId = (req as any).shopId;
      // Use branch ID from request if available
      const branchId = (req as any).branchId;

      console.log(`Getting dashboard stats with shopId: ${shopId}, branchId: ${branchId}`);

      const stats = await storage.getDashboardStats(shopId, branchId);
      console.log('Retrieved dashboard stats:', stats);

      res.json(stats);
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Reports Routes
  app.get('/api/reports/sales', authMiddleware, async (req, res) => {
    try {
      // Parse query parameters
      const period = req.query.period as string || 'today';
      const orderType = req.query.orderType as string || 'all';
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const pageSize = req.query.pageSize ? parseInt(req.query.pageSize as string) : 20;

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : (req as any).branchId;

      console.log(`Getting sales report with shopId: ${shopId}, branchId: ${branchId}, period: ${period}, orderType: ${orderType}`);

      // Calculate date range based on period if not explicitly provided
      let periodStartDate = startDate;
      let periodEndDate = endDate;

      if (!startDate && !endDate && period !== 'custom') {
        const now = new Date();
        periodEndDate = new Date(now);
        periodEndDate.setHours(23, 59, 59, 999);

        if (period === 'today') {
          periodStartDate = new Date(now);
          periodStartDate.setHours(0, 0, 0, 0);
        } else if (period === 'yesterday') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 1);
          periodStartDate.setHours(0, 0, 0, 0);

          periodEndDate = new Date(now);
          periodEndDate.setDate(periodEndDate.getDate() - 1);
          periodEndDate.setHours(23, 59, 59, 999);
        } else if (period === 'week') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 7);
        } else if (period === 'month') {
          periodStartDate = new Date(now);
          periodStartDate.setMonth(periodStartDate.getMonth() - 1);
        }
      }

      // Get orders for the specified period
      const result = await storage.getAllOrdersPaginated(
        page,
        pageSize,
        shopId,
        branchId,
        undefined, // status filter
        orderType === 'all' ? undefined : orderType,
        periodStartDate,
        periodEndDate
      );

      // Calculate sales statistics
      const totalSales = result.orders.reduce((sum, order) => sum + parseFloat(order.totalAmount || '0'), 0);
      const avgOrderValue = result.orders.length > 0 ? totalSales / result.orders.length : 0;

      // Count orders by type
      const orderCounts = {
        dine_in: 0,
        takeaway: 0,
        online: 0
      };

      result.orders.forEach(order => {
        if (order.orderType && orderCounts.hasOwnProperty(order.orderType)) {
          orderCounts[order.orderType as keyof typeof orderCounts]++;
        }
      });

      // Return report data
      res.json({
        orders: result.orders,
        total: result.total,
        totalPages: result.totalPages,
        stats: {
          totalSales,
          avgOrderValue,
          orderCounts
        },
        period,
        dateRange: {
          startDate: periodStartDate,
          endDate: periodEndDate
        }
      });
    } catch (error) {
      console.error('Error generating sales report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Tax Report API
  app.get('/api/reports/tax', authMiddleware, async (req, res) => {
    try {
      // Parse query parameters
      const period = req.query.period as string || 'today';
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : (req as any).branchId;

      console.log(`Getting tax report with shopId: ${shopId}, branchId: ${branchId}, period: ${period}`);

      // Calculate date range based on period if not explicitly provided
      let periodStartDate = startDate;
      let periodEndDate = endDate;

      if (!startDate && !endDate && period !== 'custom') {
        const now = new Date();
        periodEndDate = new Date(now);
        periodEndDate.setHours(23, 59, 59, 999);

        if (period === 'today') {
          periodStartDate = new Date(now);
          periodStartDate.setHours(0, 0, 0, 0);
        } else if (period === 'yesterday') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 1);
          periodStartDate.setHours(0, 0, 0, 0);

          periodEndDate = new Date(now);
          periodEndDate.setDate(periodEndDate.getDate() - 1);
          periodEndDate.setHours(23, 59, 59, 999);
        } else if (period === 'week') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 7);
        } else if (period === 'month') {
          periodStartDate = new Date(now);
          periodStartDate.setMonth(periodStartDate.getMonth() - 1);
        }
      }

      // Get orders for the specified period
      const orders = await storage.getAllOrders(periodStartDate, periodEndDate, shopId, branchId);

      // Get tax settings
      const taxSettings = await storage.getAllTaxSettings(shopId);

      // Calculate tax statistics
      const totalSales = orders.reduce((sum, order) => sum + parseFloat(order.totalAmount || '0'), 0);
      const totalTaxAmount = orders.reduce((sum, order) => sum + parseFloat(order.taxAmount || '0'), 0);
      const taxPercentage = totalSales > 0 ? (totalTaxAmount / totalSales) * 100 : 0;

      // Group tax by categories if available
      const taxByCategory: Record<string, { amount: number, count: number }> = {};

      // If we have tax settings with categories, use them
      if (taxSettings && taxSettings.length > 0) {
        taxSettings.forEach(tax => {
          if (tax.name) {
            taxByCategory[tax.name] = { amount: 0, count: 0 };
          }
        });
      } else {
        // Default tax category
        taxByCategory['Standard Tax'] = { amount: 0, count: 0 };
      }

      // Assign tax amounts to categories
      orders.forEach(order => {
        const taxAmount = parseFloat(order.taxAmount || '0');
        if (taxAmount > 0) {
          // If we have tax settings, try to match by rate
          const taxRate = order.taxRate || (taxAmount / parseFloat(order.subtotal || '1')) * 100;
          const matchingTax = taxSettings?.find(tax => Math.abs(tax.rate - taxRate) < 0.01);

          if (matchingTax && matchingTax.name) {
            taxByCategory[matchingTax.name].amount += taxAmount;
            taxByCategory[matchingTax.name].count++;
          } else {
            // Default category
            const defaultCategory = Object.keys(taxByCategory)[0] || 'Standard Tax';
            taxByCategory[defaultCategory].amount += taxAmount;
            taxByCategory[defaultCategory].count++;
          }
        }
      });

      // Return report data
      res.json({
        orders,
        stats: {
          totalSales,
          totalTaxAmount,
          taxPercentage,
          taxByCategory
        },
        taxSettings,
        period,
        dateRange: {
          startDate: periodStartDate,
          endDate: periodEndDate
        }
      });
    } catch (error) {
      console.error('Error generating tax report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Payments Report API
  app.get('/api/reports/payments', authMiddleware, async (req, res) => {
    try {
      // Parse query parameters
      const period = req.query.period as string || 'today';
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : (req as any).branchId;

      console.log(`Getting payments report with shopId: ${shopId}, branchId: ${branchId}, period: ${period}`);

      // Calculate date range based on period if not explicitly provided
      let periodStartDate = startDate;
      let periodEndDate = endDate;

      if (!startDate && !endDate && period !== 'custom') {
        const now = new Date();
        periodEndDate = new Date(now);
        periodEndDate.setHours(23, 59, 59, 999);

        if (period === 'today') {
          periodStartDate = new Date(now);
          periodStartDate.setHours(0, 0, 0, 0);
        } else if (period === 'yesterday') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 1);
          periodStartDate.setHours(0, 0, 0, 0);

          periodEndDate = new Date(now);
          periodEndDate.setDate(periodEndDate.getDate() - 1);
          periodEndDate.setHours(23, 59, 59, 999);
        } else if (period === 'week') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 7);
        } else if (period === 'month') {
          periodStartDate = new Date(now);
          periodStartDate.setMonth(periodStartDate.getMonth() - 1);
        }
      }

      // Get orders for the specified period
      const orders = await storage.getAllOrders(periodStartDate, periodEndDate, shopId, branchId);

      // Get payment methods
      const paymentMethods = await storage.getAllPaymentMethods(shopId);

      // Calculate payment statistics
      const totalSales = orders.reduce((sum, order) => sum + parseFloat(order.totalAmount || '0'), 0);

      // Group payments by method
      const paymentsByMethod: Record<string, { amount: number, count: number }> = {};

      // Initialize with available payment methods
      if (paymentMethods && paymentMethods.length > 0) {
        paymentMethods.forEach(method => {
          if (method.name) {
            paymentsByMethod[method.name] = { amount: 0, count: 0 };
          }
        });
      } else {
        // Default payment methods
        paymentsByMethod['Cash'] = { amount: 0, count: 0 };
        paymentsByMethod['Card'] = { amount: 0, count: 0 };
        paymentsByMethod['Online'] = { amount: 0, count: 0 };
      }

      // Assign payment amounts to methods
      orders.forEach(order => {
        const amount = parseFloat(order.totalAmount || '0');
        if (amount > 0) {
          const paymentMethod = order.paymentMethod || 'Cash';

          // Check if this payment method exists in our tracking object
          if (paymentsByMethod[paymentMethod]) {
            paymentsByMethod[paymentMethod].amount += amount;
            paymentsByMethod[paymentMethod].count++;
          } else {
            // Create a new entry for this payment method
            paymentsByMethod[paymentMethod] = { amount, count: 1 };
          }
        }
      });

      // Return report data
      res.json({
        orders,
        stats: {
          totalSales,
          paymentsByMethod
        },
        paymentMethods,
        period,
        dateRange: {
          startDate: periodStartDate,
          endDate: periodEndDate
        }
      });
    } catch (error) {
      console.error('Error generating payments report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Inventory Report API
  app.get('/api/reports/inventory', authMiddleware, async (req, res) => {
    try {
      // Parse query parameters
      const categoryId = req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined;
      const stockStatus = req.query.stockStatus as string || 'all';
      const searchQuery = req.query.search as string || '';

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : (req as any).branchId;

      console.log(`Getting inventory report with shopId: ${shopId}, branchId: ${branchId}, categoryId: ${categoryId}, stockStatus: ${stockStatus}`);

      // Get all products
      const products = await storage.getAllProducts(undefined, shopId, branchId);

      // Get all categories
      const categories = await storage.getAllCategories(shopId);

      // Filter products based on query parameters
      let filteredProducts = [...products];

      // Filter by category
      if (categoryId) {
        filteredProducts = filteredProducts.filter(product => product.categoryId === categoryId);
      }

      // Filter by stock status
      if (stockStatus === 'low') {
        filteredProducts = filteredProducts.filter(product =>
          (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0
        );
      } else if (stockStatus === 'out') {
        filteredProducts = filteredProducts.filter(product =>
          (product.quantity || 0) <= 0
        );
      } else if (stockStatus === 'available') {
        filteredProducts = filteredProducts.filter(product =>
          (product.quantity || 0) > (product.reorderLevel || 10)
        );
      }

      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filteredProducts = filteredProducts.filter(product =>
          product.name.toLowerCase().includes(query) ||
          (product.sku && product.sku.toLowerCase().includes(query)) ||
          (product.barcode && product.barcode.toLowerCase().includes(query))
        );
      }

      // Calculate inventory statistics
      const totalValue = filteredProducts.reduce(
        (sum, product) => sum + ((product.quantity || 0) * (product.price || 0)),
        0
      );

      const lowStockCount = filteredProducts.filter(
        product => (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0
      ).length;

      const outOfStockCount = filteredProducts.filter(
        product => (product.quantity || 0) <= 0
      ).length;

      const availableCount = filteredProducts.filter(
        product => (product.quantity || 0) > (product.reorderLevel || 10)
      ).length;

      // Return report data
      res.json({
        products: filteredProducts,
        categories,
        stats: {
          totalProducts: filteredProducts.length,
          totalValue,
          lowStockCount,
          outOfStockCount,
          availableCount
        }
      });
    } catch (error) {
      console.error('Error generating inventory report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Item Sales Report API
  app.get('/api/reports/item-sales', authMiddleware, async (req, res) => {
    try {
      // Parse query parameters
      const period = req.query.period as string || 'today';
      const categoryId = req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined;
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const sortBy = req.query.sortBy as string || 'quantity';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : (req as any).branchId;

      console.log(`Getting item sales report with shopId: ${shopId}, branchId: ${branchId}, period: ${period}, categoryId: ${categoryId}`);

      // Calculate date range based on period if not explicitly provided
      let periodStartDate = startDate;
      let periodEndDate = endDate;

      if (!startDate && !endDate && period !== 'custom') {
        const now = new Date();
        periodEndDate = new Date(now);
        periodEndDate.setHours(23, 59, 59, 999);

        if (period === 'today') {
          periodStartDate = new Date(now);
          periodStartDate.setHours(0, 0, 0, 0);
        } else if (period === 'yesterday') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 1);
          periodStartDate.setHours(0, 0, 0, 0);

          periodEndDate = new Date(now);
          periodEndDate.setDate(periodEndDate.getDate() - 1);
          periodEndDate.setHours(23, 59, 59, 999);
        } else if (period === 'week') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 7);
        } else if (period === 'month') {
          periodStartDate = new Date(now);
          periodStartDate.setMonth(periodStartDate.getMonth() - 1);
        }
      }

      // Get orders for the specified period
      const orders = await storage.getAllOrders(periodStartDate, periodEndDate, shopId, branchId);

      // Get all order items for these orders
      const orderIds = orders.map(order => order.id);
      const orderItems = await storage.getAllOrderItemsByOrderIds(orderIds);

      // Get all products and categories
      const products = await storage.getAllProducts(undefined, shopId, branchId);
      const categories = await storage.getAllCategories(shopId);

      // Group items by product
      const itemsByProduct: Record<string, any> = {};

      orderItems.forEach(item => {
        const productId = item.productId;
        if (!productId) return;

        if (!itemsByProduct[productId]) {
          const product = products.find(p => p.id === productId);
          itemsByProduct[productId] = {
            productId,
            productName: product ? product.name : item.name || `Product ${productId}`,
            categoryId: product ? product.categoryId : null,
            quantity: 0,
            totalSales: 0,
            orders: new Set(),
            details: []
          };
        }

        itemsByProduct[productId].quantity += (item.quantity || 1);
        itemsByProduct[productId].totalSales += ((item.price || 0) * (item.quantity || 1));

        // Add order number to the set of orders for this product
        const order = orders.find(o => o.id === item.orderId);
        if (order && order.orderNumber) {
          itemsByProduct[productId].orders.add(order.orderNumber);
        }

        itemsByProduct[productId].details.push(item);
      });

      // Convert to array and filter by category if needed
      let itemSalesArray = Object.values(itemsByProduct);

      if (categoryId) {
        itemSalesArray = itemSalesArray.filter(item => item.categoryId === categoryId);
      }

      // Sort the results
      if (sortBy === 'quantity') {
        itemSalesArray.sort((a, b) => sortOrder === 'desc' ? b.quantity - a.quantity : a.quantity - b.quantity);
      } else if (sortBy === 'revenue') {
        itemSalesArray.sort((a, b) => sortOrder === 'desc' ? b.totalSales - a.totalSales : a.totalSales - b.totalSales);
      } else if (sortBy === 'name') {
        itemSalesArray.sort((a, b) => sortOrder === 'desc' ?
          b.productName.localeCompare(a.productName) :
          a.productName.localeCompare(b.productName)
        );
      }

      // Calculate total statistics
      const totalQuantity = itemSalesArray.reduce((sum, item) => sum + item.quantity, 0);
      const totalSales = itemSalesArray.reduce((sum, item) => sum + item.totalSales, 0);

      // Convert order sets to counts for JSON serialization
      itemSalesArray = itemSalesArray.map(item => ({
        ...item,
        orderCount: item.orders.size,
        orders: Array.from(item.orders)
      }));

      // Return report data
      res.json({
        items: itemSalesArray,
        categories,
        products,
        stats: {
          totalProducts: itemSalesArray.length,
          totalQuantity,
          totalSales
        },
        period,
        dateRange: {
          startDate: periodStartDate,
          endDate: periodEndDate
        }
      });
    } catch (error) {
      console.error('Error generating item sales report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // User Sales Report API
  app.get('/api/reports/user-sales', authMiddleware, async (req, res) => {
    try {
      // Parse query parameters
      const period = req.query.period as string || 'today';
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
      const sortBy = req.query.sortBy as string || 'sales';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : (req as any).branchId;

      console.log(`Getting user sales report with shopId: ${shopId}, branchId: ${branchId}, period: ${period}`);

      // Calculate date range based on period if not explicitly provided
      let periodStartDate = startDate;
      let periodEndDate = endDate;

      if (!startDate && !endDate && period !== 'custom') {
        const now = new Date();
        periodEndDate = new Date(now);
        periodEndDate.setHours(23, 59, 59, 999);

        if (period === 'today') {
          periodStartDate = new Date(now);
          periodStartDate.setHours(0, 0, 0, 0);
        } else if (period === 'yesterday') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 1);
          periodStartDate.setHours(0, 0, 0, 0);

          periodEndDate = new Date(now);
          periodEndDate.setDate(periodEndDate.getDate() - 1);
          periodEndDate.setHours(23, 59, 59, 999);
        } else if (period === 'week') {
          periodStartDate = new Date(now);
          periodStartDate.setDate(periodStartDate.getDate() - 7);
        } else if (period === 'month') {
          periodStartDate = new Date(now);
          periodStartDate.setMonth(periodStartDate.getMonth() - 1);
        }
      }

      // Get orders for the specified period
      const orders = await storage.getAllOrders(periodStartDate, periodEndDate, shopId, branchId);

      // Get all users
      const users = await storage.getAllUsers();

      // Get all order items for these orders
      const orderIds = orders.map(order => order.id);
      const orderItems = await storage.getAllOrderItemsByOrderIds(orderIds);

      // Group orders by user
      const salesByUser: Record<string, any> = {};

      orders.forEach(order => {
        const userId = order.userId;
        if (!userId) return;

        if (!salesByUser[userId]) {
          const user = users.find(u => u.id === userId);
          salesByUser[userId] = {
            userId,
            userName: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : `User ${userId}`,
            orderCount: 0,
            totalSales: 0,
            itemCount: 0,
            orders: []
          };
        }

        salesByUser[userId].orderCount += 1;
        salesByUser[userId].totalSales += parseFloat(order.totalAmount || '0');

        // Count items
        const orderItemsForThisOrder = orderItems.filter(item => item.orderId === order.id);
        salesByUser[userId].itemCount += orderItemsForThisOrder.reduce((sum, item) => sum + (item.quantity || 1), 0);

        salesByUser[userId].orders.push(order);
      });

      // Convert to array
      let userSalesArray = Object.values(salesByUser);

      // Sort the results
      if (sortBy === 'sales') {
        userSalesArray.sort((a, b) => sortOrder === 'desc' ? b.totalSales - a.totalSales : a.totalSales - b.totalSales);
      } else if (sortBy === 'orders') {
        userSalesArray.sort((a, b) => sortOrder === 'desc' ? b.orderCount - a.orderCount : a.orderCount - b.orderCount);
      } else if (sortBy === 'items') {
        userSalesArray.sort((a, b) => sortOrder === 'desc' ? b.itemCount - a.itemCount : a.itemCount - b.itemCount);
      } else if (sortBy === 'name') {
        userSalesArray.sort((a, b) => sortOrder === 'desc' ?
          b.userName.localeCompare(a.userName) :
          a.userName.localeCompare(b.userName)
        );
      }

      // Calculate total statistics
      const totalOrders = userSalesArray.reduce((sum, user) => sum + user.orderCount, 0);
      const totalSales = userSalesArray.reduce((sum, user) => sum + user.totalSales, 0);
      const totalItems = userSalesArray.reduce((sum, user) => sum + user.itemCount, 0);

      // Return report data
      res.json({
        users: userSalesArray,
        stats: {
          totalUsers: userSalesArray.length,
          totalOrders,
          totalSales,
          totalItems
        },
        period,
        dateRange: {
          startDate: periodStartDate,
          endDate: periodEndDate
        }
      });
    } catch (error) {
      console.error('Error generating user sales report:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/dashboard/top-products', authMiddleware, async (req, res) => {
    try {
      console.log('Received request for top products with query:', req.query);
      console.log('Shop ID from request:', (req as any).shopId);
      console.log('Branch ID from request:', (req as any).branchId);

      // Parse the period parameter with a default value of 'today'
      const { period } = dashboardPeriodSchema.parse(req.query);
      console.log('Parsed period:', period);

      const limit = req.query.limit ? parseInt(req.query.limit as string) : 5;
      console.log('Limit:', limit);

      // Use shop ID from request if available
      const shopId = (req as any).shopId;

      // Use branch ID from request if available
      const branchId = (req as any).branchId;
      console.log('Using branch ID for top products query:', branchId);

      console.log('Calling storage.getTopProducts with params:', { period, limit, shopId, branchId });
      const topProducts = await storage.getTopProducts(period, limit, shopId, branchId);
      console.log('Retrieved top products:', topProducts);

      // If no products found, return an empty array instead of null
      if (!topProducts || topProducts.length === 0) {
        console.log('No top products found, returning empty array');
        return res.json([]);
      }

      res.json(topProducts);
    } catch (error) {
      console.error('Error in /api/dashboard/top-products:', error);

      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return res.status(400).json({ errors: error.errors });
      }

      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Payment Management Routes
  app.get('/api/payments', authMiddleware, async (req, res) => {
    try {
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting payments with shopId: ${shopId}, branchId: ${branchId}, date range: ${startDate} - ${endDate}`);

      const payments = await paymentService.getAllPayments(startDate, endDate, shopId, branchId);
      console.log(`Retrieved ${payments.length} payments`);

      res.json(payments);
    } catch (error) {
      console.error('Error getting payments:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/payments/stats', authMiddleware, async (req, res) => {
    try {
      const { period } = dashboardPeriodSchema.parse(req.query);

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting payment stats with shopId: ${shopId}, branchId: ${branchId}, period: ${period}`);

      const stats = await paymentService.getPaymentStats(period, shopId, branchId);
      console.log(`Retrieved payment stats with ${Object.keys(stats.paymentsByMethod).length} payment methods`);

      res.json(stats);
    } catch (error) {
      console.error('Error getting payment stats:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/refunds', authMiddleware, async (req, res) => {
    try {
      console.log('Processing refund with data:', req.body);

      const refundData = refundSchema.parse(req.body);
      const refund = await paymentService.processRefund(refundData);

      console.log('Refund processed successfully:', refund);
      res.status(201).json(refund);
    } catch (error) {
      console.error('Error processing refund:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ errors: error.errors });
      }
      if (error instanceof Error) {
        return res.status(400).json({ message: error.message });
      }
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/refunds', authMiddleware, async (req, res) => {
    try {
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      // Use shop ID and branch ID from request if available
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      console.log(`Getting refunds with shopId: ${shopId}, branchId: ${branchId}, date range: ${startDate} - ${endDate}`);

      // Create sample refunds data with different dates for testing
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const twoDaysAgo = new Date(today);
      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
      const threeDaysAgo = new Date(today);
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
      const oneWeekAgo = new Date(today);
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      const twoWeeksAgo = new Date(today);
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

      let allRefunds = [
        {
          id: 1,
          paymentId: 1,
          orderId: 1,
          orderNumber: 'ORD-001',
          amount: 50.00,
          reason: 'Customer complaint - food quality issue',
          status: 'completed',
          createdAt: today.toISOString()
        },
        {
          id: 2,
          paymentId: 1,
          orderId: 1,
          orderNumber: 'ORD-001',
          amount: 25.00,
          reason: 'Wrong order delivered',
          status: 'completed',
          createdAt: yesterday.toISOString()
        },
        {
          id: 3,
          paymentId: 2,
          orderId: 2,
          orderNumber: 'ORD-002',
          amount: 75.00,
          reason: 'Order cancelled by customer',
          status: 'completed',
          createdAt: twoDaysAgo.toISOString()
        },
        {
          id: 4,
          paymentId: 3,
          orderId: 3,
          orderNumber: 'ORD-003',
          amount: 30.00,
          reason: 'Item not available',
          status: 'completed',
          createdAt: threeDaysAgo.toISOString()
        },
        {
          id: 5,
          paymentId: 4,
          orderId: 4,
          orderNumber: 'ORD-004',
          amount: 120.00,
          reason: 'Customer changed mind',
          status: 'completed',
          createdAt: oneWeekAgo.toISOString()
        },
        {
          id: 6,
          paymentId: 5,
          orderId: 5,
          orderNumber: 'ORD-005',
          amount: 85.00,
          reason: 'Duplicate order',
          status: 'completed',
          createdAt: twoWeeksAgo.toISOString()
        }
      ];

      // Apply date filtering
      let filteredRefunds = allRefunds;
      
      if (startDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        filteredRefunds = filteredRefunds.filter(refund => {
          const refundDate = new Date(refund.createdAt);
          return refundDate >= filterStartDate;
        });
      }
      
      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        filteredRefunds = filteredRefunds.filter(refund => {
          const refundDate = new Date(refund.createdAt);
          return refundDate <= filterEndDate;
        });
      }

      const refunds = filteredRefunds;
      console.log(`Retrieved ${refunds.length} refunds`);

      res.json(refunds);
    } catch (error) {
      console.error('Error getting refunds:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Revenue data for dashboard chart
  app.get('/api/dashboard/revenue', authMiddleware, async (req, res) => {
    try {
      // Use shop ID from request if available
      const shopId = (req as any).shopId;
      // Use branch ID from request if available
      const branchId = (req as any).branchId;

      console.log(`Getting revenue data with shopId: ${shopId}, branchId: ${branchId}`);

      // Get the last 7 days of revenue data
      const today = new Date();
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);

      // Format dates for query
      const startDate = sevenDaysAgo;
      const endDate = new Date(today);
      endDate.setHours(23, 59, 59, 999);

      // Get orders for the last 7 days
      const orders = await storage.getAllOrders(startDate, endDate, shopId, branchId);

      // Group orders by day and calculate revenue
      const revenueByDay = new Map();
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

      // Initialize all days with zero revenue
      for (let i = 0; i < 7; i++) {
        const date = new Date(sevenDaysAgo);
        date.setDate(date.getDate() + i);
        const dayName = dayNames[date.getDay()];
        revenueByDay.set(dayName, 0);
      }

      // Sum up revenue by day
      orders.forEach(order => {
        const orderDate = new Date(order.createdAt);
        const dayName = dayNames[orderDate.getDay()];
        const currentRevenue = revenueByDay.get(dayName) || 0;
        revenueByDay.set(dayName, currentRevenue + Number(order.totalAmount));
      });

      // Convert to array for chart
      const revenueData = Array.from(revenueByDay.entries()).map(([name, revenue]) => ({
        name,
        revenue
      }));

      // Sort by day of week (starting from today)
      const todayIndex = today.getDay();
      revenueData.sort((a, b) => {
        const aIndex = dayNames.indexOf(a.name);
        const bIndex = dayNames.indexOf(b.name);
        const aRelativeIndex = (aIndex - todayIndex + 7) % 7;
        const bRelativeIndex = (bIndex - todayIndex + 7) % 7;
        return aRelativeIndex - bRelativeIndex;
      });

      res.json(revenueData);
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Order distribution data for dashboard chart
  app.get('/api/dashboard/order-distribution', authMiddleware, async (req, res) => {
    try {
      // Use shop ID from request if available
      const shopId = (req as any).shopId;

      // Get orders for the current month
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);

      // Get orders for the current month
      const orders = await storage.getAllOrders(firstDayOfMonth, lastDayOfMonth, shopId);

      // Count orders by type
      const orderCounts = {
        dine_in: 0,
        takeaway: 0,
        online: 0
      };

      // Count orders by type
      orders.forEach(order => {
        if (order.orderType in orderCounts) {
          orderCounts[order.orderType as keyof typeof orderCounts]++;
        }
      });

      // Calculate total orders
      const totalOrders = Object.values(orderCounts).reduce((sum, count) => sum + count, 0);

      // Calculate percentages
      const orderDistribution = [
        { name: 'Dine-in', value: totalOrders > 0 ? Math.round((orderCounts.dine_in / totalOrders) * 100) : 0 },
        { name: 'Takeaway', value: totalOrders > 0 ? Math.round((orderCounts.takeaway / totalOrders) * 100) : 0 },
        { name: 'Online', value: totalOrders > 0 ? Math.round((orderCounts.online / totalOrders) * 100) : 0 }
      ];

      res.json(orderDistribution);
    } catch (error) {
      console.error('Error fetching order distribution data:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Inventory Management APIs
  // Get inventory overview
  app.get('/api/inventory', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const categoryId = req.query.categoryId ? parseInt(req.query.categoryId as string) : undefined;
      const stockStatus = req.query.stockStatus as string || 'all';
      const searchQuery = req.query.search as string || '';

      console.log(`Getting inventory report with shopId: ${shopId}, branchId: ${branchId}, categoryId: ${categoryId}, stockStatus: ${stockStatus}`);

      // Get all products
      const products = await storage.getAllProducts(undefined, shopId, branchId);

      // Get all categories
      const categories = await storage.getAllCategories(shopId);

      // Filter products based on query parameters
      let filteredProducts = [...products];

      // Filter by category
      if (categoryId) {
        filteredProducts = filteredProducts.filter(product => product.categoryId === categoryId);
      }

      // Filter by stock status
      if (stockStatus === 'low') {
        filteredProducts = filteredProducts.filter(product =>
          (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0
        );
      } else if (stockStatus === 'out') {
        filteredProducts = filteredProducts.filter(product =>
          (product.quantity || 0) <= 0
        );
      } else if (stockStatus === 'available') {
        filteredProducts = filteredProducts.filter(product =>
          (product.quantity || 0) > (product.reorderLevel || 10)
        );
      }

      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filteredProducts = filteredProducts.filter(product =>
          product.name.toLowerCase().includes(query) ||
          (product.sku && product.sku.toLowerCase().includes(query)) ||
          (product.barcode && product.barcode.toLowerCase().includes(query))
        );
      }

      // Calculate inventory statistics
      const totalValue = filteredProducts.reduce(
        (sum, product) => sum + ((product.quantity || 0) * (product.price || 0)), 0
      );

      const lowStockCount = filteredProducts.filter(
        product => (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0
      ).length;

      const outOfStockCount = filteredProducts.filter(
        product => (product.quantity || 0) <= 0
      ).length;
      const inStockCount = products.filter(product => (product.quantity || 0) > (product.reorderLevel || 10)).length;
      const totalProducts = products.length;

      res.json({
        stats: {
          totalProducts,
          totalValue,
          lowStockCount,
          outOfStockCount,
          inStockCount
        },
        products: products.slice(0, 10) // Return first 10 products for overview
      });
    } catch (error) {
      console.error('Error getting inventory overview:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Notification Routes
  app.get('/api/notifications', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const page = parseInt(req.query.page as string) || 1;
      const pageSize = parseInt(req.query.pageSize as string) || 10;

      const result = await storage.getUserNotifications(userId, shopId, branchId, page, pageSize);
      res.json(result);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/notifications/unread-count', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      const count = await storage.getUnreadNotificationCount(userId, shopId, branchId);
      res.json({ count });
    } catch (error) {
      console.error('Error fetching unread notification count:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/notifications/:id/read', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const notificationId = parseInt(req.params.id);

      // Use notification service to mark as read and broadcast update
      await notificationService.markAsReadAndBroadcast(notificationId, userId);
      res.json({ message: 'Notification marked as read' });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.delete('/api/notifications/:id', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const notificationId = parseInt(req.params.id);

      const success = await storage.deleteNotification(notificationId, userId);
      if (success) {
        res.json({ message: 'Notification deleted' });
      } else {
        res.status(404).json({ message: 'Notification not found' });
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/notifications/settings', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;

      const settings = await storage.getUserNotificationSettings(userId, shopId);
      res.json(settings);
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.put('/api/notifications/settings', authMiddleware, async (req, res) => {
    try {
      const userId = (req as any).user.id;
      const shopId = (req as any).shopId;
      const { notificationType, enabled, deliveryMethods } = req.body;

      const setting = await storage.updateNotificationSetting(
        userId,
        shopId,
        notificationType,
        enabled,
        deliveryMethods
      );

      res.json(setting);
    } catch (error) {
      console.error('Error updating notification setting:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Order Number Settings endpoints
  app.get('/api/settings/order-number', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Getting order number settings for shopId: ${shopId}, branchId: ${branchId}`);

      const settings = await storage.getOrderNumberSettings(shopId, branchId);

      if (!settings) {
        return res.status(404).json({ message: 'Order number settings not found' });
      }

      res.json(settings);
    } catch (error) {
      console.error('Error getting order number settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/order-number', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const userId = (req as any).user.id;

      console.log(`Creating order number settings for shopId: ${shopId}, branchId: ${branchId}`);

      const settingsData = {
        ...req.body,
        shopId: Number(shopId),
        branchId: branchId ? Number(branchId) : null,
        createdBy: Number(userId),
      };

      const settings = await storage.createOrderNumberSettings(settingsData);
      res.json(settings);
    } catch (error) {
      console.error('Error creating order number settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/settings/order-number/:id', authMiddleware, async (req, res) => {
    try {
      const settingsId = parseInt(req.params.id);
      const userId = (req as any).user.id;

      console.log(`Updating order number settings ID: ${settingsId}`);

      const updateData = {
        ...req.body,
        updatedBy: Number(userId),
      };

      const settings = await storage.updateOrderNumberSettings(settingsId, updateData);

      if (!settings) {
        return res.status(404).json({ message: 'Order number settings not found' });
      }

      res.json(settings);
    } catch (error) {
      console.error('Error updating order number settings:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/settings/order-number/preview', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Generating order number preview for shopId: ${shopId}, branchId: ${branchId}`);

      // Create a temporary settings object for preview
      const tempSettings = {
        ...req.body,
        shopId: Number(shopId),
        branchId: branchId ? Number(branchId) : null,
        active: true,
      };

      // Generate preview using the same logic as generateOrderNumber but without saving
      let orderNumber = '';
      const parts = [];

      // Add prefix
      if (tempSettings.prefix) {
        parts.push(tempSettings.prefix);
      }

      // Add date component if date_based
      if (tempSettings.numberType === 'date_based' && tempSettings.dateFormat) {
        const now = new Date();
        let dateStr = '';

        switch (tempSettings.dateFormat) {
          case 'YYYY':
            dateStr = now.getFullYear().toString();
            break;
          case 'YYYYMM':
            dateStr = now.getFullYear().toString() + (now.getMonth() + 1).toString().padStart(2, '0');
            break;
          case 'YYYYMMDD':
            dateStr = now.getFullYear().toString() +
                     (now.getMonth() + 1).toString().padStart(2, '0') +
                     now.getDate().toString().padStart(2, '0');
            break;
          case 'DDMMYY':
            dateStr = now.getDate().toString().padStart(2, '0') +
                     (now.getMonth() + 1).toString().padStart(2, '0') +
                     now.getFullYear().toString().slice(-2);
            break;
          default:
            dateStr = now.getFullYear().toString();
        }
        parts.push(dateStr);
      }

      // Add number component
      if (tempSettings.numberType === 'sequential') {
        const sequenceNumber = (tempSettings.sequentialStart || 1).toString().padStart(tempSettings.minDigits || 2, '0');
        parts.push(sequenceNumber);
      } else if (tempSettings.numberType === 'random') {
        const randomNumber = Math.floor(Math.random() * Math.pow(10, tempSettings.minDigits || 2)).toString().padStart(tempSettings.minDigits || 2, '0');
        parts.push(randomNumber);
      } else {
        // Default to sequential for preview
        const sequenceNumber = '1'.padStart(tempSettings.minDigits || 2, '0');
        parts.push(sequenceNumber);
      }

      // Add suffix
      if (tempSettings.suffix) {
        parts.push(tempSettings.suffix);
      }

      // Join with separator
      orderNumber = parts.join(tempSettings.separator || '-');

      res.json({ preview: orderNumber });
    } catch (error) {
      console.error('Error generating order number preview:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/settings/order-number/next', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Getting next order number preview for shopId: ${shopId}, branchId: ${branchId}`);

      const nextOrderNumber = await storage.previewNextOrderNumber(shopId, branchId);

      // Set cache-busting headers to ensure fresh data
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      res.json({ nextOrderNumber });
    } catch (error) {
      console.error('Error getting next order number preview:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Reset order number sequence
  app.post('/api/settings/order-number/reset-sequence', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Resetting order number sequence for shopId: ${shopId}, branchId: ${branchId}`);

      // Get current settings
      const settings = await storage.getOrderNumberSettings(shopId, branchId);

      if (!settings) {
        return res.status(404).json({ message: 'Order number settings not found' });
      }

      // Reset current sequence to sequential start
      const updateData = {
        currentSequence: settings.sequentialStart,
        lastResetDate: new Date(),
        updatedBy: (req as any).user.id,
      };

      const updatedSettings = await storage.updateOrderNumberSettings(settings.id, updateData);

      res.json({
        message: 'Order number sequence reset successfully',
        oldSequence: settings.currentSequence,
        newSequence: settings.sequentialStart,
        settings: updatedSettings
      });
    } catch (error) {
      console.error('Error resetting order number sequence:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Temporary endpoint to fix existing order number settings
  app.post('/api/settings/order-number/fix-digits', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;

      console.log(`Fixing order number digits for shopId: ${shopId}, branchId: ${branchId}`);

      // Get current settings
      const settings = await storage.getOrderNumberSettings(shopId, branchId);

      if (!settings) {
        return res.status(404).json({ message: 'Order number settings not found' });
      }

      // Update to 2 digits if currently using more than 2
      if (settings.minDigits > 2) {
        const updateData = {
          minDigits: 2,
          updatedBy: (req as any).user.id,
        };

        const updatedSettings = await storage.updateOrderNumberSettings(settings.id, updateData);

        res.json({
          message: 'Order number settings updated successfully',
          oldMinDigits: settings.minDigits,
          newMinDigits: 2,
          settings: updatedSettings
        });
      } else {
        res.json({
          message: 'Order number settings already use 2 or fewer digits',
          currentMinDigits: settings.minDigits
        });
      }
    } catch (error) {
      console.error('Error fixing order number digits:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Notification creation endpoints for different types
  app.post('/api/notifications/order', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const { orderId, orderNumber, customerName } = req.body;

      const notification = await notificationService.createOrderNotification(
        orderId,
        orderNumber,
        customerName,
        shopId,
        branchId
      );

      res.status(201).json(notification);
    } catch (error) {
      console.error('Error creating order notification:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/notifications/stock', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const { productName, currentStock, minStock } = req.body;

      const notification = await notificationService.createStockAlert(
        productName,
        currentStock,
        minStock,
        shopId,
        branchId
      );

      res.status(201).json(notification);
    } catch (error) {
      console.error('Error creating stock notification:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/notifications/marketing', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const branchId = (req as any).branchId;
      const { title, message } = req.body;

      const notification = await notificationService.createMarketingNotification(
        title,
        message,
        shopId,
        branchId
      );

      res.status(201).json(notification);
    } catch (error) {
      console.error('Error creating marketing notification:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/notifications/system', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const { title, message, priority = 'normal' } = req.body;

      const notification = await notificationService.createSystemNotification(
        title,
        message,
        priority,
        shopId
      );

      res.status(201).json(notification);
    } catch (error) {
      console.error('Error creating system notification:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Subscription Plans Routes
  app.get('/api/subscription-plans', authMiddleware, async (req, res) => {
    try {
      const plans = await storage.getAllSubscriptionPlans();
      res.json(plans);
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/subscription-plans/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // Validate ID
      if (isNaN(id) || id <= 0) {
        return res.status(400).json({ message: 'Invalid subscription plan ID' });
      }

      const plan = await storage.getSubscriptionPlanById(id);

      if (!plan) {
        return res.status(404).json({ message: 'Subscription plan not found' });
      }

      res.json(plan);
    } catch (error) {
      console.error('Error fetching subscription plan:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Subscription Routes
  app.get('/api/subscriptions', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;

      if (!shopId) {
        return res.status(400).json({ message: 'Shop ID is required' });
      }

      // Get subscriptions with plan information included
      const subscriptionsWithPlans = await storage.getShopSubscriptionsWithPlans(shopId);

      // Transform to match the expected frontend format
      const subscriptions = subscriptionsWithPlans.map(sub => ({
        id: sub.id,
        shopId: sub.shopId,
        planId: sub.planId,
        status: sub.status,
        startDate: sub.startDate,
        endDate: sub.endDate,
        autoRenew: sub.autoRenew,
        discountPercent: sub.discountPercent || 0,
        discountAmount: sub.discountAmount || 0,
        totalAmount: sub.totalAmount,
        createdAt: sub.createdAt,
        updatedAt: sub.updatedAt,
        createdBy: sub.createdBy,
        // Include plan information
        plan: {
          id: sub.planId,
          name: sub.planName,
          description: sub.planDescription,
          price: sub.planPrice,
          billingCycle: sub.planBillingCycle,
          features: sub.planFeatures || []
        }
      }));

      console.log(`Returning ${subscriptions.length} subscriptions with plan info for shop ${shopId}`);
      res.json(subscriptions);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.post('/api/subscriptions', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;
      const userId = (req as any).user.id;

      if (!shopId) {
        return res.status(400).json({ message: 'Shop ID is required' });
      }

      // Parse and validate the request body
      const { planId, startDate, endDate, autoRenew, discountPercent, discountAmount, totalAmount } = req.body;

      // Convert date strings to Date objects
      const parsedStartDate = new Date(startDate);
      const parsedEndDate = new Date(endDate);

      // Validate dates
      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        return res.status(400).json({ message: 'Invalid date format' });
      }

      const subscriptionData = {
        planId: parseInt(planId),
        startDate: parsedStartDate,
        endDate: parsedEndDate,
        autoRenew: Boolean(autoRenew),
        discountPercent: parseFloat(discountPercent) || 0,
        discountAmount: parseFloat(discountAmount) || 0,
        totalAmount: parseFloat(totalAmount),
        shopId,
        createdBy: userId
      };

      console.log('Creating subscription with parsed data:', subscriptionData);

      const subscription = await storage.createSubscription(subscriptionData);
      res.status(201).json(subscription);
    } catch (error) {
      console.error('Error creating subscription:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Subscription Stats endpoint (must be before /:id route)
  app.get('/api/subscriptions/stats', authMiddleware, async (req, res) => {
    try {
      const stats = await storage.getSubscriptionStats();
      res.json(stats);
    } catch (error) {
      console.error('Error fetching subscription stats:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // NEW Subscription Billing History endpoint with fixed plan names
  app.get('/api/subscriptions/billing-history-v2', authMiddleware, async (req, res) => {
    // Force no cache to ensure fresh data
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    try {
      const shopId = (req as any).shopId;

      if (!shopId) {
        return res.status(400).json({ message: 'Shop ID is required' });
      }

      console.log(`Fetching billing history for shop ${shopId}`);

      // Get all subscriptions for the shop (including past ones) with plan details
      const subscriptions = await storage.getShopSubscriptionsWithPlans(shopId);
      console.log(`Retrieved ${subscriptions.length} subscriptions for billing history`);

      // Transform subscriptions into billing history format
      const billingHistory = subscriptions.map(sub => {
        console.log(`Transforming subscription ${sub.id}: plan=${sub.planName}, planId=${sub.planId}`);
        return {
          id: sub.id,
          date: sub.createdAt,
          amount: sub.totalAmount,
          status: sub.status,
          plan: sub.planName, // Now includes fallback names
          planDescription: sub.planDescription,
          startDate: sub.startDate,
          endDate: sub.endDate,
          autoRenew: sub.autoRenew || false,
          period: `${sub.startDate} - ${sub.endDate}`,
          invoiceNumber: `INV-${sub.id.toString().padStart(6, '0')}`
        };
      });

      console.log(`Returning ${billingHistory.length} billing history records`);
      res.json(billingHistory);
    } catch (error) {
      console.error('Error fetching billing history:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Subscription Usage endpoint (must be before /:id route)
  app.get('/api/subscriptions/usage', authMiddleware, async (req, res) => {
    try {
      const shopId = (req as any).shopId;

      if (!shopId) {
        return res.status(400).json({ message: 'Shop ID is required' });
      }

      // Get current subscription for the shop
      const subscriptions = await storage.getShopSubscriptions(shopId);
      const activeSubscription = subscriptions.find(sub => sub.status === 'active');

      if (!activeSubscription) {
        return res.status(404).json({ message: 'No active subscription found' });
      }

      // Validate planId before using it
      const planId = activeSubscription.planId;
      if (!planId || isNaN(planId) || planId <= 0) {
        console.error('Invalid planId in active subscription:', planId, 'Subscription:', activeSubscription);
        return res.status(400).json({ message: 'Invalid subscription plan ID' });
      }

      // Get subscription plan details
      const plan = await storage.getSubscriptionPlanById(planId);

      if (!plan) {
        return res.status(404).json({ message: 'Subscription plan not found' });
      }

      // Calculate current usage (you can expand this based on your needs)
      const currentUsage = {
        branches: 1, // You can get actual count from storage
        users: 5,    // You can get actual count from storage
        products: 50, // You can get actual count from storage
        orders: 100,  // You can get actual count from storage
        storage: 512  // You can calculate actual storage usage
      };

      const usageData = {
        subscription: activeSubscription,
        plan: plan,
        usage: currentUsage,
        limits: {
          branches: plan.maxBranches,
          users: plan.maxUsers,
          products: plan.maxProducts,
          orders: plan.maxOrders,
          storage: plan.storageLimit
        }
      };

      res.json(usageData);
    } catch (error) {
      console.error('Error fetching subscription usage:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/subscriptions/:id', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // Validate ID
      if (isNaN(id) || id <= 0) {
        return res.status(400).json({ message: 'Invalid subscription ID' });
      }

      const subscription = await storage.getSubscriptionById(id);

      if (!subscription) {
        return res.status(404).json({ message: 'Subscription not found' });
      }

      res.json(subscription);
    } catch (error) {
      console.error('Error fetching subscription:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/subscriptions/:id/status', authMiddleware, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;

      // Validate ID
      if (isNaN(id) || id <= 0) {
        return res.status(400).json({ message: 'Invalid subscription ID' });
      }

      if (!status) {
        return res.status(400).json({ message: 'Status is required' });
      }

      const subscription = await storage.updateSubscriptionStatus(id, status);

      if (!subscription) {
        return res.status(404).json({ message: 'Subscription not found' });
      }

      res.json(subscription);
    } catch (error) {
      console.error('Error updating subscription status:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });



  return httpServer;
}
