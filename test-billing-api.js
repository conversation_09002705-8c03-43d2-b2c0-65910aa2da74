// Test the billing history API endpoint
const API_BASE = 'http://localhost:5000';

async function testBillingHistoryAPI() {
  console.log('🧪 Testing Billing History API...\n');

  try {
    // First, let's try to get a valid token by logging in
    console.log('1. Attempting to login...');
    const loginResponse = await fetch(`${API_BASE}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed, trying with different credentials...');
      
      // Try with a different login
      const altLoginResponse = await fetch(`${API_BASE}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });
      
      if (!altLoginResponse.ok) {
        console.log('❌ Alternative login also failed');
        console.log('Let\'s try without authentication first...');
        
        // Test without auth to see the endpoint structure
        const noAuthResponse = await fetch(`${API_BASE}/api/subscriptions/billing-history`);
        console.log('Response status:', noAuthResponse.status);
        const noAuthText = await noAuthResponse.text();
        console.log('Response:', noAuthText);
        return;
      }
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    
    const token = loginData.token;
    const shopId = loginData.user?.shopId || 1; // Use shop ID from login or default to 1

    console.log(`2. Testing billing history API with shop ID: ${shopId}`);
    
    // Test the billing history endpoint
    const billingResponse = await fetch(`${API_BASE}/api/subscriptions/billing-history`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-Shop-ID': shopId.toString(),
        'Content-Type': 'application/json'
      }
    });

    console.log('Billing History Response Status:', billingResponse.status);
    
    if (billingResponse.ok) {
      const billingData = await billingResponse.json();
      console.log('✅ Billing History API Response:');
      console.log(`Found ${billingData.length} billing records`);
      
      billingData.forEach((record, index) => {
        console.log(`\n📋 Record ${index + 1}:`);
        console.log(`  - ID: ${record.id}`);
        console.log(`  - Plan: "${record.plan}"`);
        console.log(`  - Amount: $${record.amount}`);
        console.log(`  - Status: ${record.status}`);
        console.log(`  - Start Date: ${record.startDate}`);
        console.log(`  - End Date: ${record.endDate}`);
      });
    } else {
      const errorText = await billingResponse.text();
      console.log('❌ Billing History API Error:', errorText);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testBillingHistoryAPI();
