import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function checkShopNotifications() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking which shops have notifications and which don\'t...\n');

    // Get all shops
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    
    console.log('🏪 Checking each shop for notifications:');
    
    for (const shop of shopsResult.rows) {
      // Check notifications for this shop
      const notificationsResult = await client.query(`
        SELECT 
          n.id,
          n.title,
          n.type,
          n.priority,
          n.created_at,
          COUNT(nr.id) as recipient_count,
          COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_count
        FROM notifications n
        LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE n.shop_id = $1
        GROUP BY n.id, n.title, n.type, n.priority, n.created_at
        ORDER BY n.created_at DESC
      `, [shop.id]);

      console.log(`\n   📍 ${shop.name} (Shop ID: ${shop.id}):`);
      
      if (notificationsResult.rows.length === 0) {
        console.log('     ❌ NO NOTIFICATIONS FOUND');
      } else {
        console.log(`     ✅ ${notificationsResult.rows.length} notifications found:`);
        notificationsResult.rows.forEach((notif, index) => {
          console.log(`        ${index + 1}. ${notif.title}`);
          console.log(`           Type: ${notif.type}, Priority: ${notif.priority}`);
          console.log(`           Recipients: ${notif.recipient_count}, Unread: ${notif.unread_count}`);
          console.log(`           Created: ${notif.created_at}`);
        });
      }
    }

    // Check if there are notifications without shop_id
    const orphanNotifications = await client.query(`
      SELECT id, title, shop_id
      FROM notifications
      WHERE shop_id IS NULL OR shop_id NOT IN (SELECT id FROM shops)
    `);

    if (orphanNotifications.rows.length > 0) {
      console.log('\n⚠️  Found notifications without valid shop_id:');
      orphanNotifications.rows.forEach(notif => {
        console.log(`   📧 ID: ${notif.id} - ${notif.title} (shop_id: ${notif.shop_id})`);
      });
    }

    // Summary
    console.log('\n📊 SUMMARY:');
    
    const shopsWithNotifications = [];
    const shopsWithoutNotifications = [];
    
    for (const shop of shopsResult.rows) {
      const count = await client.query(`
        SELECT COUNT(*) as count
        FROM notifications
        WHERE shop_id = $1
      `, [shop.id]);
      
      if (count.rows[0].count > 0) {
        shopsWithNotifications.push(`${shop.name} (${count.rows[0].count} notifications)`);
      } else {
        shopsWithoutNotifications.push(shop.name);
      }
    }
    
    console.log('\n✅ Shops WITH notifications:');
    if (shopsWithNotifications.length === 0) {
      console.log('   None');
    } else {
      shopsWithNotifications.forEach(shop => console.log(`   - ${shop}`));
    }
    
    console.log('\n❌ Shops WITHOUT notifications:');
    if (shopsWithoutNotifications.length === 0) {
      console.log('   None');
    } else {
      shopsWithoutNotifications.forEach(shop => console.log(`   - ${shop}`));
    }

    console.log('\n🎯 SOLUTION:');
    if (shopsWithoutNotifications.length > 0) {
      console.log('   I will create notifications for the shops that don\'t have any.');
      console.log('   This will ensure ALL shops have the same notification data.');
    } else {
      console.log('   All shops have notifications. The issue might be with recipients or API calls.');
    }

  } catch (error) {
    console.error('❌ Error checking shop notifications:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

checkShopNotifications();
