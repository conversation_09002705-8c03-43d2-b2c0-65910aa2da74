import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function createNotificationForArun() {
  const client = await pool.connect();
  try {
    console.log('🔔 Creating notifications for Arun S (User ID: 2, Shop: Cafe)...\n');

    const userId = 2; // Arun S
    const shopId = 2; // Cafe

    // Clear existing notifications for this user/shop
    await client.query(`
      DELETE FROM notification_recipients 
      WHERE user_id = $1 AND notification_id IN (
        SELECT id FROM notifications WHERE shop_id = $2
      )
    `, [userId, shopId]);
    
    await client.query(`DELETE FROM notifications WHERE shop_id = $1`, [shopId]);
    console.log('🗑️  Cleared existing notifications for Cafe shop');

    // Create notifications
    const notifications = [
      {
        title: 'Stock Alert: Dosa Mix Low!',
        message: 'Dosa mix is running low (0 pieces remaining). Please reorder immediately!',
        type: 'stock',
        priority: 'urgent'
      },
      {
        title: 'Stock Alert: Ice Cream Low!', 
        message: 'Ice cream stock is low (1 piece remaining, minimum: 3)',
        type: 'stock',
        priority: 'high'
      },
      {
        title: 'New Order Received',
        message: 'Order #ORD-001 has been placed by customer John Doe',
        type: 'order',
        priority: 'normal'
      },
      {
        title: 'Welcome to Your Cafe!',
        message: 'Your notification system is now active. You will receive alerts for orders, stock, and more!',
        type: 'system',
        priority: 'normal'
      }
    ];

    for (const notif of notifications) {
      // Create notification
      const notificationResult = await client.query(`
        INSERT INTO notifications (
          title, message, type, priority, recipient_type, recipient_id, shop_id, created_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
        RETURNING id
      `, [
        notif.title,
        notif.message,
        notif.type,
        notif.priority,
        'shop',
        shopId,
        shopId
      ]);

      const notificationId = notificationResult.rows[0].id;

      // Create recipient
      await client.query(`
        INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
        VALUES ($1, $2, $3, NOW())
      `, [notificationId, userId, 'unread']);

      console.log(`✅ Created: ${notif.title} (${notif.priority})`);
    }

    // Create notification settings
    const types = ['stock', 'order', 'system', 'marketing'];
    for (const type of types) {
      await client.query(`
        INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
        enabled = $4, delivery_methods = $5
      `, [userId, shopId, type, true, JSON.stringify(['in_app'])]);
    }

    // Verify
    const countResult = await client.query(`
      SELECT COUNT(*) as count
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.user_id = $1 AND n.shop_id = $2 AND nr.status = 'unread'
    `, [userId, shopId]);

    console.log(`\n🎉 SUCCESS! Created ${countResult.rows[0].count} notifications for Arun S`);
    console.log('\n🎯 If you are logged in as Arun S with Cafe shop selected:');
    console.log('   1. Hard refresh browser (Ctrl+Shift+R)');
    console.log(`   2. Notification bell should show red badge "${countResult.rows[0].count}"`);
    console.log('   3. Click bell to see notifications');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createNotificationForArun();
