import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pencil, Trash2, Plus, Save, X } from "lucide-react";
import { useApp } from "@/context/app-context";
import { apiRequest } from '@/lib/queryClient';

export default function ExpenseCategories() {
  const { toast } = useToast();
  const { currentShop } = useApp();
  const { token } = useAuth();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState({ name: "", description: "" });
  const [isAdding, setIsAdding] = useState(false);
  const [editForm, setEditForm] = useState({ name: "", description: "" });

  // Fetch unique expense categories from the backend
  const { data: expenses = [], isLoading } = useQuery({
    queryKey: ['/api/expenses'],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/expenses");
      if (!response.ok) {
        throw new Error("Failed to fetch expenses");
      }
      const data = await response.json();
      return data || [];
    },
    enabled: !!token,
    retry: 1
  });

  // Extract unique categories from expenses
  const categories = expenses.reduce((acc: any[], expense: any) => {
    if (expense.category && !acc.find(cat => cat.name === expense.category)) {
      acc.push({
        name: expense.category,
        description: `Category for ${expense.category} expenses`,
        count: expenses.filter((e: any) => e.category === expense.category).length
      });
    }
    return acc;
  }, []);

  const handleEdit = (name: string) => {
    const category = categories.find(c => c.name === name);
    if (category) {
      setEditForm({ name: category.name, description: category.description });
      setEditingId(name);
    }
  };

  const handleSaveEdit = (originalName: string) => {
    if (!editForm.name.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Category name cannot be empty",
      });
      return;
    }

    // Note: Since categories are derived from expenses,
    // editing would require updating all expenses with this category
    toast({
      variant: "destructive",
      title: "Not Implemented",
      description: "Category editing requires updating all related expenses. This feature will be implemented in a future update.",
    });
    setEditingId(null);
  };

  const handleDelete = (name: string) => {
    // Note: Since categories are derived from expenses,
    // deleting would require handling all expenses with this category
    toast({
      variant: "destructive",
      title: "Not Implemented",
      description: "Category deletion requires handling all related expenses. This feature will be implemented in a future update.",
    });
  };

  const handleAddNew = () => {
    if (!newCategory.name.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Category name cannot be empty",
      });
      return;
    }

    // Note: New categories will be created when expenses are added with this category
    toast({
      title: "Info",
      description: "New categories are automatically created when you add expenses. Go to the Expenses page to add expenses with this category.",
    });
    setNewCategory({ name: "", description: "" });
    setIsAdding(false);
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Expense Categories</CardTitle>
              <CardDescription>
                View expense categories from your expenses for {currentShop?.name}.
                Categories are automatically created when you add expenses.
              </CardDescription>
            </div>
            <Button
              onClick={() => setIsAdding(true)}
              className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isAdding && (
            <div className="mb-6 p-4 border border-border rounded-md bg-muted/20">
              <h3 className="text-lg font-medium mb-3">Add New Category</h3>
              <div className="grid gap-4">
                <div>
                  <label className="text-sm font-medium">Name</label>
                  <Input
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({...newCategory, name: e.target.value})}
                    placeholder="Category name"
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Input
                    value={newCategory.description}
                    onChange={(e) => setNewCategory({...newCategory, description: e.target.value})}
                    placeholder="Category description"
                    className="mt-1"
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsAdding(false)}>
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                  <Button onClick={handleAddNew}>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </Button>
                </div>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="text-center py-8">Loading expense categories...</div>
          ) : categories.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No expense categories found. Add some expenses to see categories here.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Expense Count</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.map((category) => (
                  <TableRow key={category.name}>
                    <TableCell>
                      {editingId === category.name ? (
                        <Input
                          value={editForm.name}
                          onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                        />
                      ) : (
                        category.name
                      )}
                    </TableCell>
                    <TableCell>
                      {editingId === category.name ? (
                        <Input
                          value={editForm.description}
                          onChange={(e) => setEditForm({...editForm, description: e.target.value})}
                        />
                      ) : (
                        category.description
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {category.count} expense{category.count !== 1 ? 's' : ''}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {editingId === category.name ? (
                          <>
                            <Button size="sm" onClick={() => handleSaveEdit(category.name)}>
                              <Save className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => setEditingId(null)}>
                              <X className="h-4 w-4" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button size="sm" variant="outline" onClick={() => handleEdit(category.name)}>
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="destructive" onClick={() => handleDelete(category.name)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
