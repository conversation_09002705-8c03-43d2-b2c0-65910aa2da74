import { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Banknote, ShoppingBag, TrendingUp, Receipt } from "lucide-react";
import StatsCard from "@/components/dashboard/stats-card";
import RecentOrders from "@/components/dashboard/recent-orders";
import QuickActions from "@/components/dashboard/quick-actions";
import TopProducts from "@/components/dashboard/top-products";
import { RevenueChart, OrderDistribution } from "@/components/dashboard/charts";
import { PaymentSummary } from "@/components/payments/payment-summary";
import { TestChart } from "@/components/test-chart";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useSocket, useSocketEvent } from "@/context/socket-context";
import { DashboardEvents } from "@/lib/socket";
import { apiRequest } from "@/lib/queryClient";


export default function Dashboard() {
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const { isConnected } = useSocket();
  const queryClient = useQueryClient();

  // State for dashboard stats
  const [refreshInterval, setRefreshInterval] = useState<number>(30000); // 30 seconds by default

  // Clear dashboard data when shop changes to prevent data leakage between shops
  useEffect(() => {
    if (currentShop) {
      console.log(`Dashboard: Shop changed to ${currentShop.name} (ID: ${currentShop.id}), invalidating dashboard queries`);
      // Invalidate all dashboard queries when shop changes
      queryClient.invalidateQueries({ predicate: (query) =>
        query.queryKey[0] === '/api/dashboard/stats' ||
        query.queryKey[0] === '/api/dashboard/revenue' ||
        query.queryKey[0] === '/api/dashboard/order-distribution' ||
        query.queryKey[0] === '/api/dashboard/top-products'
      });
    }
  }, [currentShop?.id, queryClient]); // Depend on shop ID to trigger when shop changes

  // Fetch dashboard stats from API
  const {
    data: stats,
    isLoading: isLoadingStats,
    error: statsError,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['/api/dashboard/stats', currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop,
    refetchInterval: refreshInterval,
    onError: (error) => {
      console.error('Dashboard stats query error:', error);
    },
    onSuccess: (data) => {
      console.log('Dashboard stats query success:', data);
    }
  });

  // Debug logging for dashboard
  useEffect(() => {
    console.log('=== Dashboard Debug ===');
    console.log('stats:', stats);
    console.log('isLoadingStats:', isLoadingStats);
    console.log('statsError:', statsError);
    console.log('token exists:', !!token);
    console.log('currentShop:', currentShop);
    console.log('query enabled:', !!token && !!currentShop);
    console.log('======================');
  }, [stats, isLoadingStats, statsError, token, currentShop]);

  // Set up socket event listeners for real-time updates
  useSocketEvent(DashboardEvents.STATS_UPDATED, (newStats) => {
    console.log('Received real-time dashboard stats update:', newStats);
    // The query client will automatically update the cache with the new data
    refetchStats();
  });

  // Force a refetch when the component mounts or when the shop changes
  useEffect(() => {
    if (token && currentShop) {
      console.log('Dashboard component mounted or shop changed - fetching fresh data');
      refetchStats();
    }
  }, [token, currentShop, refetchStats]);

  return (
    <div className="space-y-8 p-2 bg-gradient-to-br from-background to-background/95 rounded-lg">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-primary/10 to-secondary/10 p-6 rounded-lg shadow-md mb-6 border border-border/30">
        <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">Welcome to your Dashboard</h1>
        <p className="text-muted-foreground">Here's what's happening with your business today.</p>
      </div>



      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
        <StatsCard
          title="Today's Sales"
          value={stats ? `₹${stats.todaySales.toLocaleString()}` : "₹0"}
          trendDirection="up"
          trendValue="12%"
          trendText="from yesterday"
          icon={<Banknote className="w-5 h-5" />}
          iconBgColor="bg-primary-100"
          iconColor="text-primary-500"
          isLoading={isLoadingStats}
          refreshInterval={refreshInterval}
        />

        <StatsCard
          title="Total Orders"
          value={stats ? stats.totalOrders : "0"}
          trendDirection="up"
          trendValue="8%"
          trendText="from yesterday"
          icon={<ShoppingBag className="w-5 h-5" />}
          iconBgColor="bg-yellow-100"
          iconColor="text-yellow-500"
          isLoading={isLoadingStats}
          refreshInterval={refreshInterval}
        />

        <StatsCard
          title="Avg. Order Value"
          value={stats ? `₹${stats.avgOrderValue.toLocaleString()}` : "₹0"}
          trendDirection="down"
          trendValue="3%"
          trendText="from yesterday"
          icon={<TrendingUp className="w-5 h-5" />}
          iconBgColor="bg-green-100"
          iconColor="text-green-500"
          isLoading={isLoadingStats}
          refreshInterval={refreshInterval}
        />

        <StatsCard
          title="Today's Expenses"
          value={stats ? `₹${stats.todayExpenses.toLocaleString()}` : "₹0"}
          trendDirection="neutral"
          trendText="Same as yesterday"
          icon={<Receipt className="w-5 h-5" />}
          iconBgColor="bg-red-100"
          iconColor="text-red-500"
          isLoading={isLoadingStats}
          refreshInterval={refreshInterval}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-3">
        <RevenueChart
          isLoading={isLoadingStats}
          refreshInterval={refreshInterval}
        />
        <OrderDistribution
          isLoading={isLoadingStats}
          refreshInterval={refreshInterval}
        />
      </div>

      {/* Payment Summary */}
      <PaymentSummary period="week" />

      {/* Quick Actions */}
      <QuickActions />

      {/* Recent Orders */}
      <RecentOrders
        refreshInterval={refreshInterval}
        socketEnabled={isConnected}
      />

      {/* Top Products */}
      <TopProducts
        refreshInterval={refreshInterval}
        socketEnabled={isConnected}
      />
    </div>
  );
}
