import pg from 'pg';
const { Pool } = pg;

const pool = new Pool({
  connectionString: '*******************************************************************************'
});

async function quickAddSubscriptions() {
  const client = await pool.connect();
  
  try {
    console.log('Quick adding subscription data...');
    
    // Get basic data
    const shopResult = await client.query('SELECT id FROM shops LIMIT 1');
    const userResult = await client.query('SELECT id FROM users LIMIT 1');
    const planResults = await client.query('SELECT id, price FROM subscription_plans ORDER BY price');
    
    if (shopResult.rows.length === 0 || userResult.rows.length === 0 || planResults.rows.length === 0) {
      console.log('Missing basic data');
      return;
    }
    
    const shopId = shopResult.rows[0].id;
    const userId = userResult.rows[0].id;
    const plans = planResults.rows;
    
    console.log(`Using shop ${shopId}, user ${userId}, ${plans.length} plans available`);
    
    // Add 5 more subscriptions with different statuses
    const subscriptions = [
      {
        planId: plans[0].id,
        status: 'active',
        totalAmount: plans[0].price,
        discount: 0
      },
      {
        planId: plans[1]?.id || plans[0].id,
        status: 'active', 
        totalAmount: (plans[1]?.price || plans[0].price) * 0.9,
        discount: 10
      },
      {
        planId: plans[2]?.id || plans[0].id,
        status: 'expired',
        totalAmount: plans[2]?.price || plans[0].price,
        discount: 0
      },
      {
        planId: plans[0].id,
        status: 'cancelled',
        totalAmount: plans[0].price * 0.85,
        discount: 15
      },
      {
        planId: plans[1]?.id || plans[0].id,
        status: 'inactive',
        totalAmount: plans[1]?.price || plans[0].price,
        discount: 0
      }
    ];
    
    let added = 0;
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - (i * 30)); // Stagger start dates
      
      const endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1);
      
      try {
        await client.query(`
          INSERT INTO subscriptions (
            shop_id, plan_id, status, start_date, end_date, 
            auto_renew, discount_percent, discount_amount, total_amount, created_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
          shopId, sub.planId, sub.status, startDate, endDate,
          true, sub.discount, sub.totalAmount * (sub.discount/100), sub.totalAmount, userId
        ]);
        
        console.log(`✓ Added ${sub.status} subscription with plan ${sub.planId}`);
        added++;
      } catch (error) {
        console.log(`- Skipped subscription ${i + 1}: ${error.message}`);
      }
    }
    
    // Get final count
    const finalCount = await client.query('SELECT COUNT(*) FROM subscriptions');
    console.log(`\n✓ Added ${added} subscriptions. Total: ${finalCount.rows[0].count}`);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

quickAddSubscriptions();
