import React from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, RefreshCw, Activity, Trash2 } from 'lucide-react';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface HealthStatus {
  database: string;
  server: string;
  memory: string;
  disk: string;
  timestamp: string;
  checks: Array<{
    name: string;
    status: string;
    message: string;
  }>;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'healthy':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'warning':
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    case 'unhealthy':
      return <XCircle className="h-5 w-5 text-red-500" />;
    default:
      return <AlertTriangle className="h-5 w-5 text-gray-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'healthy':
      return 'bg-green-500';
    case 'warning':
      return 'bg-yellow-500';
    case 'unhealthy':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'healthy':
      return 'default';
    case 'warning':
      return 'secondary';
    case 'unhealthy':
      return 'destructive';
    default:
      return 'outline';
  }
};

export function HealthCheck() {
  const { toast } = useToast();

  const { data: healthStatus, isLoading, error, refetch } = useQuery<HealthStatus>({
    queryKey: ['/api/utilities/health-check'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const clearCacheMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/utilities/clear-cache');
      if (!response.ok) {
        throw new Error('Failed to clear cache');
      }
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: 'Cache Cleared',
        description: data.message || 'Cache cleared successfully',
        variant: 'default',
      });
      // Invalidate all queries to refresh data
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to clear cache',
        variant: 'destructive',
      });
    },
  });

  const handleRefresh = () => {
    refetch();
    toast({
      title: 'Refreshing',
      description: 'Health check status refreshed',
    });
  };

  const handleClearCache = () => {
    clearCacheMutation.mutate();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            System Health Check
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !healthStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            System Health Check
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load health status. The system may be experiencing issues.
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const overallStatus = healthStatus.server;
  const isHealthy = overallStatus === 'healthy';
  const hasWarnings = overallStatus === 'warning';
  const isUnhealthy = overallStatus === 'unhealthy';

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                System Health Check
              </CardTitle>
              <CardDescription>
                Real-time system health monitoring and diagnostics
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button onClick={handleRefresh} variant="outline" size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
              <Button 
                onClick={handleClearCache} 
                variant="outline" 
                size="sm"
                disabled={clearCacheMutation.isPending}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {clearCacheMutation.isPending ? 'Clearing...' : 'Clear Cache'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            {getStatusIcon(overallStatus)}
            <div>
              <h3 className="text-lg font-semibold">
                System Status: <Badge variant={getStatusVariant(overallStatus)}>{overallStatus.toUpperCase()}</Badge>
              </h3>
              <p className="text-sm text-muted-foreground">
                Last checked: {new Date(healthStatus.timestamp).toLocaleString()}
              </p>
            </div>
          </div>

          {isUnhealthy && (
            <Alert variant="destructive" className="mt-4">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                Critical system issues detected. Immediate attention required.
              </AlertDescription>
            </Alert>
          )}

          {hasWarnings && (
            <Alert className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                System warnings detected. Monitor closely.
              </AlertDescription>
            </Alert>
          )}

          {isHealthy && (
            <Alert className="mt-4 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                All systems are operating normally.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Component Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              {getStatusIcon(healthStatus.database)}
              <span className="ml-2">Database</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={getStatusVariant(healthStatus.database)}>
              {healthStatus.database.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              {getStatusIcon(healthStatus.server)}
              <span className="ml-2">Server</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={getStatusVariant(healthStatus.server)}>
              {healthStatus.server.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              {getStatusIcon(healthStatus.memory)}
              <span className="ml-2">Memory</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={getStatusVariant(healthStatus.memory)}>
              {healthStatus.memory.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              {getStatusIcon(healthStatus.disk)}
              <span className="ml-2">Disk</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={getStatusVariant(healthStatus.disk)}>
              {healthStatus.disk.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Checks */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Health Checks</CardTitle>
          <CardDescription>
            Individual component health check results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {healthStatus.checks.map((check, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(check.status)}
                  <div>
                    <p className="font-medium">{check.name}</p>
                    <p className="text-sm text-muted-foreground">{check.message}</p>
                  </div>
                </div>
                <Badge variant={getStatusVariant(check.status)}>
                  {check.status.toUpperCase()}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
