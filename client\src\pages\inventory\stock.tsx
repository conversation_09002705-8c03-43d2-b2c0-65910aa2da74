﻿import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { NotificationHelpers } from "@/lib/notification-helpers";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";
import { jsPDF } from "jspdf";
import "jspdf-autotable";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";

import {
  Package,
  Search,
  Plus,
  Minus,
  Edit,
  RefreshCw,
  FileDown,
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronsLeft,
  ChevronsRight
} from "lucide-react";

export default function StockManagement() {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [stockFilter, setStockFilter] = useState("all");
  const [isAdjustDialogOpen, setIsAdjustDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [adjustmentQuantity, setAdjustmentQuantity] = useState("");
  const [adjustmentReason, setAdjustmentReason] = useState("");
  const [adjustmentNotes, setAdjustmentNotes] = useState("");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // Fixed at 10 records per page

  // Fetch products with inventory data using paginated API
  const { data: inventoryData, isLoading: isLoadingProducts, refetch: refetchProducts } = useQuery({
    queryKey: ['/api/products', currentShop?.id, currentBranch?.id, categoryFilter, stockFilter, searchQuery, currentPage, pageSize],
    enabled: !!token,
    queryFn: async () => {
      // Build query parameters
      const params = new URLSearchParams();
      if (categoryFilter !== 'all') params.append('categoryId', categoryFilter);
      if (stockFilter !== 'all') params.append('stockStatus', stockFilter);
      if (searchQuery) params.append('search', searchQuery);
      params.append('page', currentPage.toString());
      params.append('pageSize', pageSize.toString());

      const response = await fetch(`/api/products?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      return response.json();
    }
  });

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['/api/categories', currentShop?.id, currentBranch?.id],
    enabled: !!token,
    queryFn: async () => {
      const response = await fetch('/api/categories', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }
      return response.json();
    }
  });

  // Extract data from API response
  const products = inventoryData?.products || [];
  const pagination = inventoryData?.pagination || {
    page: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [categoryFilter, stockFilter, searchQuery]);

  const getStockStatus = (product: any) => {
    const quantity = product.quantity || 0;
    const reorderLevel = product.reorderLevel || 10;

    if (quantity <= 0) return { label: 'Out of Stock', variant: 'destructive' as const, icon: AlertTriangle };
    if (quantity <= reorderLevel) return { label: 'Low Stock', variant: 'secondary' as const, icon: AlertTriangle };
    return { label: 'In Stock', variant: 'default' as const, icon: CheckCircle,
  ChevronDown };
  };

  const handleStockAdjustment = async () => {
    if (!selectedProduct || !adjustmentQuantity || !adjustmentReason) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please fill in all required fields",
      });
      return;
    }

    try {
      const response = await fetch(`/api/inventory/products/${selectedProduct.id}/adjust`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        },
        body: JSON.stringify({
          quantity: parseInt(adjustmentQuantity),
          reason: adjustmentReason,
          notes: adjustmentNotes
        })
      });

      if (!response.ok) {
        throw new Error('Failed to adjust stock');
      }

      const result = await response.json();

      // Check if stock is low after adjustment and create notification
      const newQuantity = result.newQuantity || 0;
      const reorderLevel = selectedProduct.reorderLevel || 10;

      if (newQuantity <= reorderLevel) {
        try {
          await NotificationHelpers.createStockAlert(
            selectedProduct.name,
            newQuantity,
            reorderLevel
          );
        } catch (error) {
          console.error('Failed to create stock alert notification:', error);
        }
      }

      toast({
        title: "Stock Adjusted",
        description: `Stock for ${selectedProduct.name} has been adjusted successfully`,
      });

      // Reset form and close dialog
      setIsAdjustDialogOpen(false);
      setSelectedProduct(null);
      setAdjustmentQuantity("");
      setAdjustmentReason("");
      setAdjustmentNotes("");

      // Refresh products
      refetchProducts();
    } catch (error) {
      console.error('Error adjusting stock:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to adjust stock. Please try again.",
      });
    }
  };

  const openAdjustDialog = (product: any) => {
    setSelectedProduct(product);
    setIsAdjustDialogOpen(true);
  };

  const handleRefresh = () => {
    refetchProducts();
    toast({
      title: "Refreshed",
      description: "Stock data has been refreshed",
    });
  };

  // Format data for export (fetch all data for export)
  const getFormattedData = async () => {
    try {
      // Fetch all products without pagination for export
      const params = new URLSearchParams();
      if (categoryFilter !== 'all') params.append('categoryId', categoryFilter);
      if (stockFilter !== 'all') params.append('stockStatus', stockFilter);
      if (searchQuery) params.append('search', searchQuery);
      params.append('page', '1');
      params.append('pageSize', '1000'); // Large number to get all results

      const response = await fetch(`/api/products?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch products for export');
      }

      const data = await response.json();
      const allProducts = data.products || [];

      if (allProducts.length === 0) {
        return null;
      }

      return allProducts.map((product: any) => {
      const categoryName = categories?.find((c: any) => c.id === product.categoryId)?.name || "Uncategorized";
      const stockStatus = getStockStatus(product);
      
      return {
        "Product Name": product.name,
        "SKU": product.sku || "N/A",
        "Category": categoryName,
        "Current Stock": `${product.quantity || 0} ${product.unitOfMeasure || "pcs"}`,
        "Reorder Level": product.reorderLevel || 10,
        "Unit Price": `${(product.price || 0).toFixed(2)}`,
        "Total Value": `${((product.quantity || 0) * (product.price || 0)).toFixed(2)}`,
        "Status": stockStatus.label,
        "Description": product.description || "",
        "Barcode": product.barcode || "N/A"
      };
    });
    } catch (error) {
      console.error('Error fetching data for export:', error);
      return null;
    }
  };

  // Generate export filename
  const getExportFileName = () => {
    const date = new Date().toISOString().split("T")[0];
    const branchName = currentBranch?.name || "All_Branches";
    return `Stock_Report_${branchName}_${date}`;
  };

  // Handle export to Excel
  const handleExportToExcel = async () => {
    const formattedData = await getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Stock report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = async () => {
    const formattedData = await getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Stock report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Stock Management</h1>
          <p className="text-gray-500">Manage product stock levels and adjustments</p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <FileDown className="h-4 w-4 mr-2" />
                Export
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleExportToExcel}>
                Export to Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportToCSV}>
                Export to CSV
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Overview</CardTitle>
        </CardHeader>
        <CardContent className="mt-3">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories?.map((category: any) => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={stockFilter} onValueChange={setStockFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Stock Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Stock</SelectItem>
                <SelectItem value="available">In Stock</SelectItem>
                <SelectItem value="low">Low Stock</SelectItem>
                <SelectItem value="out">Out of Stock</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Products Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Reorder Level</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingProducts ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                      <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : products.length > 0 ? (
                  products.map((product: any) => {
                    const categoryName = categories?.find((c: any) => c.id === product.categoryId)?.name || 'Uncategorized';
                    const stockStatus = getStockStatus(product);

                    return (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>{product.sku || 'N/A'}</TableCell>
                        <TableCell>{categoryName}</TableCell>
                        <TableCell>{product.quantity || 0} {product.unitOfMeasure || 'pcs'}</TableCell>
                        <TableCell>{product.reorderLevel || 10}</TableCell>
                        <TableCell>{(product.price || 0).toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={stockStatus.variant} className="flex items-center gap-1">
                            <stockStatus.icon className="h-3 w-3" />
                            {stockStatus.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openAdjustDialog(product)}
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Adjust
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Package className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No products found</p>
                        <p className="text-sm text-gray-400">Try adjusting your filters</p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          {!isLoadingProducts && pagination.totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of {pagination.totalCount} items
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(1)}
                      className={pagination.page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </PaginationLink>
                  </PaginationItem>

                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, pagination.page - 1))}
                      className={pagination.page === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = pagination.page - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={pagination.page === pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(pagination.totalPages, pagination.page + 1))}
                      className={pagination.page === pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(pagination.totalPages)}
                      className={pagination.page === pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </PaginationLink>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Adjustment Dialog */}
      <Dialog open={isAdjustDialogOpen} onOpenChange={setIsAdjustDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Adjust Stock</DialogTitle>
            <DialogDescription>
              Adjust stock quantity for {selectedProduct?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="current-stock" className="text-right">
                Current Stock
              </Label>
              <div className="col-span-3 text-sm text-gray-600">
                {selectedProduct?.quantity || 0} {selectedProduct?.unitOfMeasure || 'pcs'}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="adjustment" className="text-right">
                Adjustment *
              </Label>
              <Input
                id="adjustment"
                type="number"
                placeholder="e.g., +10 or -5"
                value={adjustmentQuantity}
                onChange={(e) => setAdjustmentQuantity(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reason" className="text-right">
                Reason *
              </Label>
              <Select value={adjustmentReason} onValueChange={setAdjustmentReason}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="restock">Restock</SelectItem>
                  <SelectItem value="damage">Damage</SelectItem>
                  <SelectItem value="theft">Theft</SelectItem>
                  <SelectItem value="correction">Stock Correction</SelectItem>
                  <SelectItem value="return">Return</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notes" className="text-right">
                Notes
              </Label>
              <Textarea
                id="notes"
                placeholder="Additional notes..."
                value={adjustmentNotes}
                onChange={(e) => setAdjustmentNotes(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAdjustDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleStockAdjustment}>
              Adjust Stock
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

