import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";
import { apiRequest } from "@/lib/queryClient";
import { jsPDF } from "jspdf";
import 'jspdf-autotable';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Pie<PERSON><PERSON> } from "@/components/ui/chart";

import {
  Download,
  Printer,
  Calendar,
  FilterX,
  FileText,
  File,
  FileDown,
  ChevronDown,
  ChevronUp,
  CalendarRange,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

export default function TaxReport() {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const { toast } = useToast();

  const [period, setPeriod] = useState<string>("today");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [expandedTaxCategory, setExpandedTaxCategory] = useState<string | null>(null);

  // Fetch tax report data
  const { data: taxReportData, isLoading: isLoadingTaxReport } = useQuery({
    queryKey: ['/api/reports/tax', currentShop?.id, period, startDate, endDate],
    queryFn: async () => {
      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('period', period);
        if (startDate && endDate && period === 'custom') {
          params.append('startDate', startDate);
          params.append('endDate', endDate);
        }

        // Use apiRequest instead of fetch to ensure proper shop/branch context
        const response = await apiRequest('GET', `/api/reports/tax?${params.toString()}`);
        if (!response.ok) {
          throw new Error('Failed to fetch tax report');
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching tax report:', error);
        return null;
      }
    },
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
    onSuccess: (data) => {
      console.log('Tax Report - Data received:', data);
    },
    onError: (error) => {
      console.error('Tax Report - Error fetching tax report:', error);
    }
  });

  // Calculate tax statistics
  const calculateTaxStats = () => {
    if (!taxReportData) {
      return {
        totalTax: 0,
        taxByCategory: {},
        taxDetails: []
      };
    }

    const { orders, stats, taxSettings } = taxReportData;

    // Get total tax amount from the API response
    const totalTax = stats?.totalTaxAmount || 0;

    // Get tax by category from the API response
    const taxByCategory: Record<string, number> = {};

    if (stats?.taxByCategory) {
      Object.entries(stats.taxByCategory).forEach(([category, data]: [string, any]) => {
        taxByCategory[category] = data.amount || 0;
      });
    } else if (totalTax > 0) {
      // Only add a fallback category if there's actually tax data
      taxByCategory['Standard Tax'] = totalTax;
    }
    // If totalTax is 0, leave taxByCategory empty (no categories)

    // Create detailed tax records
    const taxDetails: any[] = [];

    if (orders && Array.isArray(orders)) {
      orders.forEach((order: any) => {
        if (!order.taxAmount || parseFloat(order.taxAmount) <= 0) {
          return;
        }

        const taxAmount = parseFloat(order.taxAmount);

        // Try to find matching tax setting
        let taxName = 'Standard Tax';
        let taxRate = 'Standard Rate';

        if (taxSettings && Array.isArray(taxSettings) && taxSettings.length > 0) {
          // If we have tax settings, try to match by rate
          const orderTaxRate = order.taxRate || (taxAmount / parseFloat(order.subtotal || '1')) * 100;
          const matchingTax = taxSettings.find((tax: any) => Math.abs(tax.rate - orderTaxRate) < 0.01);

          if (matchingTax) {
            taxName = matchingTax.name || 'Standard Tax';
            taxRate = `${matchingTax.rate}%`;
          }
        }

        taxDetails.push({
          orderId: order.id || 'unknown',
          orderNumber: order.orderNumber || `Order-${order.id || 'unknown'}`,
          date: order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Unknown date',
          taxName: taxName,
          taxRate: taxRate,
          taxAmount: taxAmount,
          orderTotal: order.totalAmount ? parseFloat(order.totalAmount) : 0
        });
      });
    }

    return {
      totalTax,
      taxByCategory,
      taxDetails
    };
  };

  const taxStats = calculateTaxStats();

  // Handle custom date range
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    if (value !== "custom") {
      setStartDate("");
      setEndDate("");
    }
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    if (!taxStats.taxDetails || taxStats.taxDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No tax data available to export",
      });
      return;
    }

    try {
      // Format the data for export
      const formattedData = formatDataForExport(taxStats.taxDetails, {
        taxAmount: (value) => `₹${parseFloat(value).toFixed(2)}`,
        orderTotal: (value) => `₹${parseFloat(value).toFixed(2)}`,
        taxRate: (value) => typeof value === 'number' ? `${value}%` : value,
      });

      // Export to Excel
      const success = exportToExcel(
        formattedData,
        `Tax_Report_${period === 'custom' ?
          `${startDate}_to_${endDate}` :
          period}_${new Date().toISOString().split('T')[0]}`
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Tax report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the tax report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    if (!taxStats.taxDetails || taxStats.taxDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No tax data available to export",
      });
      return;
    }

    try {
      // Format the data for export
      const formattedData = formatDataForExport(taxStats.taxDetails, {
        taxAmount: (value) => `₹${parseFloat(value).toFixed(2)}`,
        orderTotal: (value) => `₹${parseFloat(value).toFixed(2)}`,
        taxRate: (value) => typeof value === 'number' ? `${value}%` : value,
      });

      // Export to CSV
      const success = exportToCSV(
        formattedData,
        `Tax_Report_${period === 'custom' ?
          `${startDate}_to_${endDate}` :
          period}_${new Date().toISOString().split('T')[0]}`
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Tax report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the tax report",
      });
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    if (!taxStats.taxDetails || taxStats.taxDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No tax data available to export",
      });
      return;
    }

    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(18);
      doc.text("Tax Report", 14, 22);

      // Add period information
      doc.setFontSize(12);
      let periodText = "Period: ";
      if (period === "today") {
        periodText += "Today";
      } else if (period === "yesterday") {
        periodText += "Yesterday";
      } else if (period === "week") {
        periodText += "Last 7 days";
      } else if (period === "month") {
        periodText += "Last 30 days";
      } else if (period === "custom") {
        periodText += `${startDate} to ${endDate}`;
      }
      doc.text(periodText, 14, 32);

      // Add total tax information
      doc.text(`Total Tax Collected: ₹${taxStats.totalTax.toFixed(2)}`, 14, 42);

      // Add tax breakdown table
      const tableData = Object.entries(taxStats.taxByCategory).map(([category, amount]) => [
        category,
        `₹${amount.toFixed(2)}`,
        `${((amount / taxStats.totalTax) * 100).toFixed(2)}%`
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: 50,
        head: [['Tax Category', 'Amount', 'Percentage']],
        body: tableData.length > 0 ? tableData : [['No tax categories found', '₹0.00', '0.00%']],
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 10 }
      });

      // Add detailed tax table
      const detailedData = taxStats.taxDetails.length > 0 ?
        taxStats.taxDetails.map(item => [
          item.orderNumber,
          item.date,
          item.taxName,
          typeof item.taxRate === 'number' ? `${item.taxRate}%` : item.taxRate,
          `₹${item.taxAmount.toFixed(2)}`,
          `₹${parseFloat(item.orderTotal).toFixed(2)}`
        ]) :
        [['No tax details found', '', '', '', '₹0.00', '₹0.00']];

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: doc.lastAutoTable.finalY + 15,
        head: [['Order #', 'Date', 'Tax Name', 'Rate', 'Tax Amount', 'Order Total']],
        body: detailedData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 9 },
        columnStyles: {
          0: { cellWidth: 25 },
          1: { cellWidth: 25 },
          2: { cellWidth: 40 },
          3: { cellWidth: 20 },
          4: { cellWidth: 30 },
          5: { cellWidth: 30 }
        }
      });

      // Save the PDF
      doc.save(`Tax_Report_${period === 'custom' ?
        `${startDate}_to_${endDate}` :
        period}_${new Date().toISOString().split('T')[0]}.pdf`);

      toast({
        title: "Export successful",
        description: "Tax report has been exported to PDF",
      });
    } catch (error) {
      console.error("PDF export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the tax report to PDF",
      });
    }
  };

  // Prepare chart data
  const chartData = Object.entries(taxStats.taxByCategory).map(([category, amount]) => ({
    name: category,
    value: amount
  }));

  // Don't add fallback data - let the chart handle empty state

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h1 className="text-2xl font-bold">Tax Summary Report</h1>

        <div className="flex flex-wrap gap-2">
          <Select value={period} onValueChange={handlePeriodChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="week">Last 7 days</SelectItem>
              <SelectItem value="month">Last 30 days</SelectItem>
              <SelectItem value="custom">Custom date range</SelectItem>
            </SelectContent>
          </Select>

          {period === "custom" && (
            <div className="flex gap-2">
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-[150px]"
              />
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-[150px]"
              />
            </div>
          )}

          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={handleExportToExcel}>
              <File className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleExportToCSV}>
              <FileText className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleExportToPDF}>
              <FileDown className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Tax Collected</span>
              {isLoadingTaxReport ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{taxStats?.totalTax.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Tax Categories</span>
              {isLoadingTaxReport ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {Object.keys(taxStats?.taxByCategory || {}).length || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tax Distribution Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Tax Distribution</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          {isLoadingTaxReport ? (
            <Skeleton className="h-full w-full" />
          ) : chartData.length > 0 ? (
            <PieChart
              data={chartData}
              index="name"
              category="value"
              valueFormatter={(value) => `₹${parseFloat(value).toFixed(2)}`}
              showAnimation={true}
              showLegend={true}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center space-y-2">
                <p className="text-muted-foreground">No tax data available</p>
                <p className="text-sm text-muted-foreground">No orders with tax information found for the selected period</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tax Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Tax Breakdown by Category</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingTaxReport ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : Object.keys(taxStats.taxByCategory).length > 0 ? (
            <div className="space-y-4">
              {Object.entries(taxStats.taxByCategory).map(([category, amount]) => (
                <Collapsible
                  key={category}
                  open={expandedTaxCategory === category}
                  onOpenChange={() => setExpandedTaxCategory(expandedTaxCategory === category ? null : category)}
                  className="border rounded-md"
                >
                  <CollapsibleTrigger className="flex justify-between items-center w-full p-4 hover:bg-muted/50">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{category}</span>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">
                        {taxStats.totalTax > 0 ? ((amount / taxStats.totalTax) * 100).toFixed(2) : "0.00"}%
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4">
                      <span className="font-semibold">₹{amount.toFixed(2)}</span>
                      {expandedTaxCategory === category ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <div className="p-4 pt-0 border-t">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Order #</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Tax Rate</TableHead>
                            <TableHead>Tax Amount</TableHead>
                            <TableHead>Order Total</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {taxStats.taxDetails
                            .filter(item => item.taxName === category)
                            .map((item, index) => (
                              <TableRow key={`${item.orderId}-${index}`}>
                                <TableCell className="font-medium">#{item.orderNumber}</TableCell>
                                <TableCell>{item.date}</TableCell>
                                <TableCell>{typeof item.taxRate === 'number' ? `${item.taxRate}%` : item.taxRate}</TableCell>
                                <TableCell>₹{item.taxAmount.toFixed(2)}</TableCell>
                                <TableCell>₹{parseFloat(item.orderTotal).toFixed(2)}</TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 space-y-4">
              <p className="text-muted-foreground">No tax data available for the selected period</p>
              <p className="text-sm text-muted-foreground">Try selecting a different date range or check if there are orders with tax information</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
