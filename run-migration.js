import pg from 'pg';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function runMigration() {
  const client = await pool.connect();
  try {
    console.log('🚀 Running subscription payment methods table migration...\n');

    // Read the migration file
    const migrationSQL = fs.readFileSync('migrations/add_subscription_payment_methods_table.sql', 'utf8');
    
    // Execute the migration
    await client.query(migrationSQL);
    
    console.log('✅ Migration completed successfully!');
    console.log('📋 Created subscription_payment_methods table with:');
    console.log('   - Card holder name and last 4 digits');
    console.log('   - Expiry month and year');
    console.log('   - Card type detection');
    console.log('   - Default and active status');
    console.log('   - Shop-specific payment methods');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    
    // Check if table already exists
    if (error.message.includes('already exists')) {
      console.log('ℹ️  Table already exists, skipping migration');
    } else {
      throw error;
    }
  } finally {
    client.release();
    await pool.end();
  }
}

runMigration();
