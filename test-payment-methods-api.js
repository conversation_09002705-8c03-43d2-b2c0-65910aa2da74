// Test subscription payment methods API specifically
console.log('🧪 Testing Subscription Payment Methods API...\n');

const testPaymentMethodsAPI = async () => {
  const baseURL = 'http://localhost:5000';
  const headers = {
    'Authorization': 'Bearer test-token',
    'X-Shop-ID': '12',
    'Content-Type': 'application/json'
  };

  try {
    console.log('Testing GET /api/payment-methods/subscription...');
    const response = await fetch(`${baseURL}/api/payment-methods/subscription`, { 
      method: 'GET',
      headers 
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success! Payment methods:', JSON.stringify(data, null, 2));
    } else {
      const error = await response.text();
      console.log('❌ Error response:', error);
    }

  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
};

testPaymentMethodsAPI();
