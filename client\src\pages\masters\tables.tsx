import { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient, invalidateAndRefetch, logQueryCache } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { POSEvents, subscribeToPOSEvent, unsubscribeFromPOSEvent } from "@/lib/socket";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import {
  Plus,
  Search,
  Edit,
  Trash2,
  AlertCircle,
  Loader2,
  Users,
} from "lucide-react";

// Table form schema
const tableSchema = z.object({
  name: z.string().min(1, "Table name is required"),
  capacity: z.string().min(1, "Capacity is required").refine(
    (value) => !isNaN(Number(value)) && Number(value) > 0,
    {
      message: "Capacity must be a positive number",
    }
  ),
  status: z.enum(["available", "occupied", "reserved", "billing"]).default("available"),
});

type TableFormValues = z.infer<typeof tableSchema>;

// Define Table interface
interface Table {
  id: number;
  name: string;
  capacity: number;
  status: "available" | "occupied" | "reserved" | "billing";
  shopId: number;
}

export default function Tables() {
  const { token } = useAuth();
  const { toast } = useToast();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [tableToDelete, setTableToDelete] = useState<Table | null>(null);

  // State to force component re-render
  const [refreshKey, setRefreshKey] = useState(0);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Fetch tables
  const { data: tables, isLoading: isLoadingTables, refetch: refetchTables } = useQuery<Table[]>({
    queryKey: ['/api/tables', refreshKey], // Add refreshKey to force refetch when it changes
    enabled: !!token,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Force a refetch when component mounts
  useEffect(() => {
    if (token) {
      console.log("Tables component mounted - forcing data refresh");
      // Force a refetch to ensure we have the latest data
      queryClient.removeQueries({ queryKey: ['/api/tables'] });
      refetchTables();

      // Log the query cache state for debugging
      logQueryCache();
    }
  }, [token, refetchTables]);

  // Handle real-time table status updates
  const handleTableStatusUpdate = useCallback((data: { tableId: number, status: string }) => {
    console.log('Received table status update:', data);

    // Update the table status in the cache
    queryClient.setQueryData(['/api/tables'], (oldData: any) => {
      if (!oldData) return oldData;

      return oldData.map((table: any) => {
        if (table.id === data.tableId) {
          return { ...table, status: data.status };
        }
        return table;
      });
    });

    // Show a toast notification
    toast({
      title: "Table Status Updated",
      description: `Table status has been updated to ${data.status}`,
      variant: "default",
    });
  }, [toast]);

  // Handle full tables update
  const handleTablesUpdate = useCallback((data: any[]) => {
    console.log('Received full tables update:', data);

    // Update the tables data in the cache
    queryClient.setQueryData(['/api/tables'], data);

    // Show a toast notification
    toast({
      title: "Tables Updated",
      description: "Tables have been updated",
      variant: "default",
    });
  }, [toast]);

  // Subscribe to real-time table status updates
  useEffect(() => {
    if (token) {
      console.log('Subscribing to table status updates');
      subscribeToPOSEvent(POSEvents.TABLE_STATUS_UPDATED, handleTableStatusUpdate);
      subscribeToPOSEvent(POSEvents.TABLES_UPDATED, handleTablesUpdate);

      return () => {
        console.log('Unsubscribing from table status updates');
        unsubscribeFromPOSEvent(POSEvents.TABLE_STATUS_UPDATED, handleTableStatusUpdate);
        unsubscribeFromPOSEvent(POSEvents.TABLES_UPDATED, handleTablesUpdate);
      };
    }
  }, [token, handleTableStatusUpdate, handleTablesUpdate]);

  // Function to force a refresh of the tables data
  const forceRefresh = () => {
    console.log("Forcing refresh of tables data");
    setRefreshKey(prevKey => prevKey + 1);
    queryClient.removeQueries({ queryKey: ['/api/tables'] });
    setTimeout(() => {
      refetchTables();
    }, 100); // Small delay to ensure query is removed before refetching
  };

  // Log when tables data changes
  useEffect(() => {
    console.log("Tables data changed:", tables);
  }, [tables]);

  // Filter tables
  const filteredTables = tables?.filter((table: Table) => {
    let matchesSearch = true;
    let matchesStatus = true;

    if (searchTerm) {
      matchesSearch = table.name.toLowerCase().includes(searchTerm.toLowerCase());
    }

    if (statusFilter !== "all") {
      matchesStatus = table.status === statusFilter;
    }

    return matchesSearch && matchesStatus;
  });

  // Pagination utility function
  interface PaginatedData<T> {
    data: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  const paginateData = (data: Table[] | undefined, page: number, size: number): PaginatedData<Table> => {
    if (!data || !Array.isArray(data)) return { data: [], total: 0, page, pageSize: size, totalPages: 0 };

    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const paginatedData = data.slice(startIndex, endIndex);
    const total = data.length;
    const totalPages = Math.ceil(total / size);

    return {
      data: paginatedData,
      total,
      page,
      pageSize: size,
      totalPages
    };
  };

  // Paginate filtered tables
  const paginatedData = paginateData(filteredTables, currentPage, pageSize);
  const displayedTables = paginatedData.data;

  // Add table form
  const addForm = useForm<TableFormValues>({
    resolver: zodResolver(tableSchema),
    defaultValues: {
      name: "",
      capacity: "",
      status: "available",
    },
  });

  // Edit table form (separate instance)
  const editForm = useForm<TableFormValues>({
    resolver: zodResolver(tableSchema),
    defaultValues: {
      name: "",
      capacity: "",
      status: "available",
    },
  });

  // Reset add form
  const resetAddForm = () => {
    addForm.reset({
      name: "",
      capacity: "",
      status: "available",
    });
  };

  // Reset edit form
  const resetEditForm = () => {
    editForm.reset({
      name: "",
      capacity: "",
      status: "available",
    });
  };

  // Set form values for editing
  const setFormForEdit = (table: Table) => {
    editForm.reset({
      name: table.name,
      capacity: table.capacity.toString(),
      status: table.status,
    });
  };

  // Create table mutation
  const createTableMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log("Creating table with data:", data);
      const response = await apiRequest("POST", "/api/tables", data);
      console.log("Create table response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Create table error:", errorText);
        throw new Error(errorText);
      }
      return response;
    },
    onSuccess: async (response) => {
      console.log("Table created successfully");

      // Get the created table data
      const createdTable = await response.json();
      console.log("Created table:", createdTable);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/tables');

      // Force a refresh of the tables data
      forceRefresh();

      toast({
        title: "Table created",
        description: "Table has been created successfully",
      });

      setIsAddDialogOpen(false);
      resetAddForm();
    },
    onError: (error) => {
      console.error("Failed to create table:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create table",
      });
    },
  });

  // Update table mutation
  const updateTableMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      console.log(`Updating table ${id} with data:`, data);
      const response = await apiRequest("PATCH", `/api/tables/${id}`, data);
      console.log("Update table response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Update table error:", errorText);
        throw new Error(errorText);
      }
      return response;
    },
    onSuccess: async (response) => {
      console.log("Table updated successfully");

      // Get the updated table data
      const updatedTable = await response.json();
      console.log("Updated table:", updatedTable);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/tables');

      // Force a refresh of the tables data
      forceRefresh();

      toast({
        title: "Table updated",
        description: "Table has been updated successfully",
      });

      setIsEditDialogOpen(false);
      setSelectedTable(null);
    },
    onError: (error) => {
      console.error("Failed to update table:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update table",
      });
    },
  });

  // Delete table mutation
  const deleteTableMutation = useMutation({
    mutationFn: async (tableId: number) => {
      console.log("Delete mutation: Starting delete for table ID:", tableId);
      const response = await apiRequest("DELETE", `/api/tables/${tableId}`);
      console.log("Delete mutation: Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.log("Delete mutation: Error data:", errorData);
        throw new Error(errorData.message || "Failed to delete table");
      }

      const result = await response.json();
      console.log("Delete mutation: Success result:", result);
      return result;
    },
    onSuccess: async () => {
      // Invalidate and refetch tables
      await invalidateAndRefetch('/api/tables');

      setIsDeleteDialogOpen(false);
      setTableToDelete(null);

      toast({
        title: "Success",
        description: "Table deleted successfully",
      });

      // Force a refresh of the tables data
      forceRefresh();
    },
    onError: (err: any) => {
      console.error("Error deleting table:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to delete table";

      // Close the delete dialog on error
      setIsDeleteDialogOpen(false);
      setTableToDelete(null);

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    },
  });

  // Form submission handlers
  const handleCreateTable = (data: TableFormValues) => {
    createTableMutation.mutate({
      ...data,
      capacity: parseInt(data.capacity),
    });
  };

  const handleUpdateTable = (data: TableFormValues) => {
    if (!selectedTable) return;

    updateTableMutation.mutate({
      id: selectedTable.id,
      data: {
        ...data,
        capacity: parseInt(data.capacity),
      },
    });
  };

  const handleDeleteTable = async () => {
    if (!tableToDelete) return;

    console.log("Attempting to delete table:", tableToDelete);

    try {
      await deleteTableMutation.mutateAsync(tableToDelete.id);
      console.log("Delete mutation completed successfully");
    } catch (error) {
      // Error handling is done in mutation's onError
      console.error("Failed to delete table:", error);
    }
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, statusFilter]);

  // Status badge config
  const statusColors = {
    available: "bg-green-100 text-green-800",
    occupied: "bg-red-100 text-red-800",
    reserved: "bg-yellow-100 text-yellow-800",
    billing: "bg-blue-100 text-blue-800",
  };

  // Loading state
  const isLoading =
    isLoadingTables ||
    createTableMutation.isPending ||
    updateTableMutation.isPending ||
    deleteTableMutation.isPending;



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Tables</h1>
        <Sheet open={isAddDialogOpen} onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            // If opening add modal, close edit modal if it's open
            if (open && isEditDialogOpen) {
              setIsEditDialogOpen(false);
              setSelectedTable(null);
            }
          }}>
          <SheetTrigger asChild>
            <Button onClick={() => {
              // Close edit modal if it's open when clicking Add Table
              if (isEditDialogOpen) {
                setIsEditDialogOpen(false);
                setSelectedTable(null);
              }
            }}>
              <Plus className="h-4 w-4 mr-2" />
              Add Table
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <SheetHeader>
              <SheetTitle>Add New Table</SheetTitle>
              <SheetDescription>
                Add a new table to your restaurant
              </SheetDescription>
            </SheetHeader>

            <Form {...addForm}>
              <form onSubmit={addForm.handleSubmit(handleCreateTable)} className="space-y-4">
                <FormField
                  control={addForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Table Name/Number</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Table 1" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="capacity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Capacity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="Number of seats"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="available">Available</SelectItem>
                          <SelectItem value="occupied">Occupied</SelectItem>
                          <SelectItem value="reserved">Reserved</SelectItem>
                          <SelectItem value="billing">Billing</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <SheetFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false);
                      resetAddForm();
                    }}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {createTableMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Table"
                    )}
                  </Button>
                </SheetFooter>
              </form>
            </Form>
          </SheetContent>
        </Sheet>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Table List</CardTitle>
        </CardHeader>
        <CardContent className="pt-4" >
          {/* Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search tables..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="occupied">Occupied</SelectItem>
                <SelectItem value="reserved">Reserved</SelectItem>
                <SelectItem value="billing">Billing</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tables Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Table</TableHead>
                  <TableHead className="text-center">Capacity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingTables ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="text-center"><Skeleton className="h-4 w-8 mx-auto" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : displayedTables && displayedTables.length > 0 ? (
                  displayedTables.map((table: Table) => (
                    <TableRow key={table.id}>
                      <TableCell className="font-medium">{table.name}</TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center">
                          <Users className="w-4 h-4 mr-2 text-gray-500" />
                          <span>{table.capacity}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={
                          statusColors[table.status as keyof typeof statusColors]
                        }>
                          {table.status === 'available' ? 'Available' :
                           table.status === 'occupied' ? 'Occupied' :
                           table.status === 'reserved' ? 'Reserved' : 'Billing'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              // Close add modal if it's open when clicking Edit
                              if (isAddDialogOpen) {
                                setIsAddDialogOpen(false);
                                resetAddForm();
                              }
                              setSelectedTable(table);
                              setFormForEdit(table);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            disabled={deleteTableMutation.isPending}
                            onClick={() => {
                              console.log("Delete button clicked for table:", table);
                              setTableToDelete(table);
                              setIsDeleteDialogOpen(true);
                              console.log("Delete dialog should now be open");
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            {deleteTableMutation.isPending && tableToDelete?.id === table.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-6 text-gray-500">
                      No tables found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {paginatedData.totalPages > 1 && (
            <div className="flex items-center justify-between px-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, paginatedData.total)} of {paginatedData.total} tables
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (paginatedData.totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (currentPage >= paginatedData.totalPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = paginatedData.totalPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={currentPage === pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(paginatedData.totalPages, currentPage + 1))}
                      className={currentPage === paginatedData.totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Table Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        // If closing edit modal, reset selected table
        if (!open) {
          setSelectedTable(null);
          resetEditForm();
        }
        // If opening edit modal, close add modal if it's open
        if (open && isAddDialogOpen) {
          setIsAddDialogOpen(false);
          resetAddForm();
        }
      }}>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Edit Table</SheetTitle>
            <SheetDescription>
              Make changes to the table details
            </SheetDescription>
          </SheetHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdateTable)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Table Name/Number</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Table 1" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="capacity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Capacity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Number of seats"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage/>
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="occupied">Occupied</SelectItem>
                        <SelectItem value="reserved">Reserved</SelectItem>
                        <SelectItem value="billing">Billing</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setSelectedTable(null);
                    resetEditForm();
                  }}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {updateTableMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Table"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Table</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the table "{tableToDelete?.name}"?
              This action cannot be undone and may affect orders using this table.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteTableMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                console.log("Delete confirmation button clicked");
                handleDeleteTable();
              }}
              disabled={deleteTableMutation.isPending}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {deleteTableMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
