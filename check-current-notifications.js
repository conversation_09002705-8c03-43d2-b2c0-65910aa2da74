import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function checkCurrentNotifications() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking current notifications and shop setup...\n');

    // 1. Check what shops exist
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    console.log('🏪 Available shops:');
    shopsResult.rows.forEach(shop => {
      console.log(`   Shop ${shop.id}: ${shop.name}`);
    });

    // 2. Check what users exist
    const usersResult = await client.query('SELECT id, name, email FROM users ORDER BY id');
    console.log('\n👥 Available users:');
    usersResult.rows.forEach(user => {
      console.log(`   User ${user.id}: ${user.name} (${user.email})`);
    });

    // 3. Check existing notifications by shop
    console.log('\n📋 Current notifications by shop:');
    for (const shop of shopsResult.rows) {
      const notificationsResult = await client.query(`
        SELECT 
          n.id,
          n.title,
          n.type,
          n.priority,
          COUNT(nr.id) as recipient_count,
          COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_count
        FROM notifications n
        LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE n.shop_id = $1
        GROUP BY n.id, n.title, n.type, n.priority
        ORDER BY n.created_at DESC
      `, [shop.id]);

      console.log(`\n   ${shop.name} (Shop ID: ${shop.id}):`);
      if (notificationsResult.rows.length === 0) {
        console.log('     ❌ No notifications found');
      } else {
        notificationsResult.rows.forEach(notif => {
          console.log(`     📧 ID: ${notif.id} - ${notif.title}`);
          console.log(`        Type: ${notif.type}, Priority: ${notif.priority}`);
          console.log(`        Recipients: ${notif.recipient_count}, Unread: ${notif.unread_count}`);
        });
      }
    }

    // 4. Check notifications for specific users
    console.log('\n📊 Notifications by user:');
    for (const user of usersResult.rows) {
      const userNotificationsResult = await client.query(`
        SELECT 
          n.shop_id,
          s.name as shop_name,
          COUNT(*) as total_notifications,
          COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_notifications
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        JOIN shops s ON n.shop_id = s.id
        WHERE nr.user_id = $1
        GROUP BY n.shop_id, s.name
        ORDER BY n.shop_id
      `, [user.id]);

      console.log(`\n   ${user.name} (User ID: ${user.id}):`);
      if (userNotificationsResult.rows.length === 0) {
        console.log('     ❌ No notifications found for this user');
      } else {
        userNotificationsResult.rows.forEach(row => {
          console.log(`     🏪 Shop: ${row.shop_name} (ID: ${row.shop_id})`);
          console.log(`        Total: ${row.total_notifications}, Unread: ${row.unread_notifications}`);
        });
      }
    }

    // 5. Check if there are any notifications without proper recipients
    const orphanNotificationsResult = await client.query(`
      SELECT n.id, n.title, n.shop_id, s.name as shop_name
      FROM notifications n
      JOIN shops s ON n.shop_id = s.id
      LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.id IS NULL
    `);

    if (orphanNotificationsResult.rows.length > 0) {
      console.log('\n⚠️  Found notifications without recipients:');
      orphanNotificationsResult.rows.forEach(notif => {
        console.log(`   📧 ID: ${notif.id} - ${notif.title} (Shop: ${notif.shop_name})`);
      });
    }

    // 6. Show the most likely issue and solution
    console.log('\n🎯 Analysis:');
    
    const totalNotifications = await client.query('SELECT COUNT(*) as count FROM notifications');
    const totalRecipients = await client.query('SELECT COUNT(*) as count FROM notification_recipients');
    
    console.log(`   - Total notifications in database: ${totalNotifications.rows[0].count}`);
    console.log(`   - Total notification recipients: ${totalRecipients.rows[0].count}`);
    
    if (totalNotifications.rows[0].count > 0 && totalRecipients.rows[0].count === 0) {
      console.log('\n❌ PROBLEM FOUND: Notifications exist but no recipients!');
      console.log('   This means notifications were created but not assigned to any users.');
      console.log('   Solution: Run the fix script to assign notifications to users.');
    } else if (totalNotifications.rows[0].count === 0) {
      console.log('\n❌ PROBLEM FOUND: No notifications exist in database!');
      console.log('   Solution: Run the script to create notifications.');
    } else {
      console.log('\n✅ Notifications and recipients exist in database.');
      console.log('   The issue might be with shop/user context in the frontend.');
    }

    console.log('\n💡 Next Steps:');
    console.log('   1. Note which shop you are currently using in the browser');
    console.log('   2. Check if that shop has notifications in the list above');
    console.log('   3. If not, I will create notifications for your current shop');
    console.log('   4. If yes, the issue is with the frontend API calls');

  } catch (error) {
    console.error('❌ Error checking notifications:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

checkCurrentNotifications();
