import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useSocket } from "@/context/socket-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useEffect } from "react";

interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'order' | 'stock' | 'marketing' | 'system';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'unread' | 'read' | 'archived';
  created_at: string;
  read_at?: string;
  data?: Record<string, any>;
}

interface NotificationSetting {
  id: number;
  userId: number;
  shopId: number;
  notificationType: string;
  enabled: boolean;
  deliveryMethods: string[];
  settings: Record<string, any>;
}

export function useNotifications() {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const { socket, isConnected } = useSocket();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch notifications
  const {
    data: notificationsData,
    isLoading: notificationsLoading,
    error: notificationsError
  } = useQuery({
    queryKey: ["/api/notifications", currentShop?.id],
    enabled: !!token && !!currentShop,
    refetchInterval: 30000,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/notifications");

      if (!response.ok) {
        throw new Error(`Failed to fetch notifications: ${response.status}`);
      }

      const data = await response.json();
      return data;
    },
  });

  // Extract notifications array from paginated response
  const notifications = notificationsData?.notifications || [];

  // Fetch unread count
  const {
    data: unreadCount = 0,
    isLoading: countLoading,
    error: countError
  } = useQuery({
    queryKey: ["/api/notifications/unread-count", currentShop?.id],
    enabled: !!token && !!currentShop,
    refetchInterval: 30000,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/notifications/unread-count");

      if (!response.ok) {
        throw new Error(`Failed to fetch unread count: ${response.status}`);
      }

      const data = await response.json();
      // Extract count from response object
      return data.count || 0;
    },
  });

  // Fetch notification settings
  const {
    data: settings = [],
    isLoading: settingsLoading
  } = useQuery({
    queryKey: ["/api/notifications/settings", currentShop?.id],
    enabled: !!token && !!currentShop,
  });

  // Mark notification as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: number) =>
      apiRequest("POST", `/api/notifications/${notificationId}/read`),
    onSuccess: () => {
      // Force refresh all notification-related queries for current shop
      queryClient.invalidateQueries({ queryKey: ["/api/notifications", currentShop?.id] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread-count", currentShop?.id] });

      // Also remove from cache to force fresh fetch
      queryClient.removeQueries({ queryKey: ["/api/notifications", currentShop?.id] });
      queryClient.removeQueries({ queryKey: ["/api/notifications/unread-count", currentShop?.id] });

      // Show success toast
      toast({
        title: "Notification marked as read",
        description: "The notification has been marked as read.",
        duration: 2000,
      });
    },
    onError: (error) => {
      console.error('Error marking notification as read:', error);
      toast({
        title: "Error",
        description: "Failed to mark notification as read. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: (notificationId: number) =>
      apiRequest("DELETE", `/api/notifications/${notificationId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/notifications", currentShop?.id] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread-count", currentShop?.id] });
      toast({
        title: "Notification deleted",
        description: "The notification has been removed.",
      });
    },
  });

  // Update notification setting mutation
  const updateSettingMutation = useMutation({
    mutationFn: (data: { notificationType: string; enabled: boolean; deliveryMethods?: string[] }) =>
      apiRequest("PUT", "/api/notifications/settings", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/settings", currentShop?.id] });
      toast({
        title: "Settings updated",
        description: "Your notification preferences have been saved.",
      });
    },
  });

  // Create notification mutations
  const createOrderNotificationMutation = useMutation({
    mutationFn: (data: { orderId: number; orderNumber: string; customerName: string }) =>
      apiRequest("POST", "/api/notifications/order", data),
  });

  const createStockNotificationMutation = useMutation({
    mutationFn: (data: { productName: string; currentStock: number; minStock: number }) =>
      apiRequest("POST", "/api/notifications/stock", data),
  });

  const createMarketingNotificationMutation = useMutation({
    mutationFn: (data: { title: string; message: string }) =>
      apiRequest("POST", "/api/notifications/marketing", data),
  });

  const createSystemNotificationMutation = useMutation({
    mutationFn: (data: { title: string; message: string; priority?: string }) =>
      apiRequest("POST", "/api/notifications/system", data),
  });

  // Socket.io event listeners for real-time updates
  useEffect(() => {
    if (!socket || !isConnected) return;

    const handleNewNotification = (notification: any) => {
      console.log('New notification received:', notification);
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread-count"] });
      
      // Show toast notification with priority-based styling
      const getToastVariant = (priority: string) => {
        switch (priority) {
          case 'urgent': return 'destructive';
          case 'high': return 'destructive';
          default: return 'default';
        }
      };

      toast({
        title: notification.title,
        description: notification.message,
        variant: getToastVariant(notification.priority),
        duration: notification.priority === 'urgent' ? 10000 : 5000,
      });
    };

    const handleNotificationRead = (data: any) => {
      console.log('Notification marked as read:', data);
      queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread-count"] });
    };

    const handleNotificationDeleted = (data: any) => {
      console.log('Notification deleted:', data);
      queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
      queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread-count"] });
    };

    const handleCountUpdated = (count: number) => {
      console.log('Unread count updated:', count);
      queryClient.setQueryData(["/api/notifications/unread-count", currentShop?.id], count);
    };

    // Register event listeners
    socket.on('notification:new', handleNewNotification);
    socket.on('notification:read', handleNotificationRead);
    socket.on('notification:deleted', handleNotificationDeleted);
    socket.on('notification:count_updated', handleCountUpdated);

    // Cleanup on unmount
    return () => {
      socket.off('notification:new', handleNewNotification);
      socket.off('notification:read', handleNotificationRead);
      socket.off('notification:deleted', handleNotificationDeleted);
      socket.off('notification:count_updated', handleCountUpdated);
    };
  }, [socket, isConnected, queryClient, toast]);

  // Helper functions
  const markAsRead = (notificationId: number) => {
    markAsReadMutation.mutate(notificationId);
  };

  const deleteNotification = (notificationId: number) => {
    deleteNotificationMutation.mutate(notificationId);
  };

  const updateSetting = (notificationType: string, enabled: boolean, deliveryMethods?: string[]) => {
    updateSettingMutation.mutate({ notificationType, enabled, deliveryMethods });
  };

  const createOrderNotification = (orderId: number, orderNumber: string, customerName: string) => {
    createOrderNotificationMutation.mutate({ orderId, orderNumber, customerName });
  };

  const createStockNotification = (productName: string, currentStock: number, minStock: number) => {
    createStockNotificationMutation.mutate({ productName, currentStock, minStock });
  };

  const createMarketingNotification = (title: string, message: string) => {
    createMarketingNotificationMutation.mutate({ title, message });
  };

  const createSystemNotification = (title: string, message: string, priority?: string) => {
    createSystemNotificationMutation.mutate({ title, message, priority });
  };

  const getSettingForType = (type: string): NotificationSetting | undefined => {
    return settings.find((setting: NotificationSetting) => setting.notificationType === type);
  };

  const refreshNotifications = () => {
    // Force refresh all notification data
    queryClient.invalidateQueries({ queryKey: ["/api/notifications"] });
    queryClient.invalidateQueries({ queryKey: ["/api/notifications/unread-count"] });
    queryClient.removeQueries({ queryKey: ["/api/notifications"] });
    queryClient.removeQueries({ queryKey: ["/api/notifications/unread-count"] });
  };

  return {
    // Data
    notifications,
    unreadCount,
    settings,
    
    // Loading states
    notificationsLoading,
    countLoading,
    settingsLoading,
    
    // Error states
    notificationsError,
    
    // Actions
    markAsRead,
    deleteNotification,
    updateSetting,
    createOrderNotification,
    createStockNotification,
    createMarketingNotification,
    createSystemNotification,
    getSettingForType,
    refreshNotifications,
    
    // Mutation states
    isMarkingAsRead: markAsReadMutation.isPending,
    isDeleting: deleteNotificationMutation.isPending,
    isUpdatingSetting: updateSettingMutation.isPending,
    isCreatingNotification: 
      createOrderNotificationMutation.isPending ||
      createStockNotificationMutation.isPending ||
      createMarketingNotificationMutation.isPending ||
      createSystemNotificationMutation.isPending,
  };
}
