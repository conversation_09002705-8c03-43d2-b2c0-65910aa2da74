import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function simpleFixRecipients() {
  const client = await pool.connect();
  try {
    console.log('🔧 Simple fix: Adding recipients to existing notifications...\n');

    // Clear existing recipients first
    await client.query('DELETE FROM notification_recipients');
    console.log('🗑️  Cleared existing recipients');

    // Get all notifications and users
    const notifications = await client.query('SELECT id FROM notifications');
    const users = await client.query('SELECT id FROM users');

    console.log(`📧 Found ${notifications.rows.length} notifications`);
    console.log(`👥 Found ${users.rows.length} users`);

    // Create recipients in batches
    console.log('🔄 Creating recipients...');
    
    let count = 0;
    for (const notification of notifications.rows) {
      for (const user of users.rows) {
        await client.query(`
          INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
          VALUES ($1, $2, 'unread', NOW())
        `, [notification.id, user.id]);
        count++;
      }
    }

    console.log(`✅ Created ${count} recipient records`);

    // Verify by checking one shop
    const likithaCheck = await client.query(`
      SELECT 
        COUNT(DISTINCT n.id) as total_notifications,
        COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_notifications
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE n.shop_id = 12
    `);

    const result = likithaCheck.rows[0];
    console.log(`\n📊 Verification for "likitha" shop:`);
    console.log(`   Total notifications: ${result.total_notifications}`);
    console.log(`   Unread notifications: ${result.unread_notifications}`);

    console.log('\n🎉 SUCCESS! Now refresh your browser and check the notification bell!');
    console.log('   Expected: Red badge showing number of unread notifications');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

simpleFixRecipients();
