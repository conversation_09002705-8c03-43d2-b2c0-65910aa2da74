-- Create order_number_settings table
CREATE TABLE "order_number_settings" (
  "id" serial PRIMARY KEY NOT NULL,
  "prefix" text DEFAULT 'ORD' NOT NULL,
  "suffix" text DEFAULT '' NOT NULL,
  "number_type" text DEFAULT 'sequential' NOT NULL, -- sequential, random, date_based
  "sequential_start" integer DEFAULT 1 NOT NULL,
  "current_sequence" integer DEFAULT 1 NOT NULL,
  "date_format" text DEFAULT '' NOT NULL, -- YYYY, YYYYMM, YYYYMMDD, etc.
  "separator" text DEFAULT '-' NOT NULL,
  "min_digits" integer DEFAULT 2 NOT NULL,
  "reset_period" text DEFAULT 'never' NOT NULL, -- never, daily, monthly, yearly
  "last_reset_date" timestamp,
  "active" boolean DEFAULT true NOT NULL,
  "shop_id" integer REFERENCES "shops"("id") NOT NULL,
  "branch_id" integer REFERENCES "branches"("id"),
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- <PERSON>reate unique constraint to ensure one setting per shop/branch combination
CREATE UNIQUE INDEX "order_number_settings_shop_branch_unique" ON "order_number_settings" ("shop_id", "branch_id");

-- Create index for faster lookups
CREATE INDEX "order_number_settings_shop_id_idx" ON "order_number_settings" ("shop_id");
CREATE INDEX "order_number_settings_active_idx" ON "order_number_settings" ("active");
