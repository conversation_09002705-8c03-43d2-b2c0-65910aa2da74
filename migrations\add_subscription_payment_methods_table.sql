-- Add subscription_payment_methods table
CREATE TABLE IF NOT EXISTS "subscription_payment_methods" (
  "id" serial PRIMARY KEY NOT NULL,
  "shop_id" integer NOT NULL,
  "cardholder_name" text NOT NULL,
  "card_number" text NOT NULL,
  "expiry_month" text NOT NULL,
  "expiry_year" text NOT NULL,
  "card_type" text,
  "is_default" boolean DEFAULT true NOT NULL,
  "active" boolean DEFAULT true NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL,
  CONSTRAINT "subscription_payment_methods_shop_id_shops_id_fk" FOREIGN KEY ("shop_id") REFERENCES "shops"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
);
