import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, Phone, MessageSquare } from "lucide-react";

export default function Support() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Client-side validation
    if (name.length < 2) {
      alert("Name must be at least 2 characters long");
      setIsSubmitting(false);
      return;
    }
    if (subject.length < 5) {
      alert("Subject must be at least 5 characters long");
      setIsSubmitting(false);
      return;
    }
    if (message.length < 10) {
      alert("Message must be at least 10 characters long");
      setIsSubmitting(false);
      return;
    }

    try {
      const requestData = {
        name,
        email,
        subject,
        message,
        priority: 'medium',
        category: 'general'
      };

      console.log('Sending support ticket request:', requestData);

      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Support ticket response status:', response.status);

      if (response.ok) {
        alert("Support ticket submitted successfully!");
        setName("");
        setEmail("");
        setSubject("");
        setMessage("");
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('Support ticket submission error:', errorData);
        alert(`Failed to submit support ticket: ${errorData.message || errorData.details || 'Please try again.'}`);
      }
    } catch (error) {
      console.error('Error:', error);
      alert("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Support</h1>
        <p className="text-gray-500">Get help with NembooBill</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Send us a message</CardTitle>
            <CardDescription>
              Fill out the form below and we'll get back to you as soon as possible.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name</label>
                <Input
                  placeholder="Your name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Email</label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Subject</label>
                <Input
                  placeholder="How can we help you?"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Message</label>
                <Textarea
                  placeholder="Please describe your issue or question in detail..."
                  className="min-h-[120px]"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Sending..." : "Send Message"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>
              Other ways to get in touch with us
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center">
              <Mail className="h-5 w-5 mr-3 text-gray-500" />
              <div>
                <p className="font-medium">Email</p>
                <a href="mailto:<EMAIL>" className="text-sm text-blue-600 hover:underline">
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="flex items-center">
              <Phone className="h-5 w-5 mr-3 text-gray-500" />
              <div>
                <p className="font-medium">Phone</p>
                <a href="tel:+919731519519" className="text-sm text-blue-600 hover:underline">
                  +91 97315 19519
                </a>
              </div>
            </div>
            <div className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-3 text-gray-500" />
              <div>
                <p className="font-medium">Support Hours</p>
                <p className="text-sm text-gray-500">
                  Monday - Friday, 9:00 AM - 6:00 PM IST
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
          <CardDescription>
            Find answers to common questions about NembooBill
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="border-b pb-4">
              <h4 className="font-medium mb-2">How do I create a new shop?</h4>
              <p className="text-sm text-gray-600">
                To create a new shop, go to the "Select Shop" page and click on the "Create New Shop" button.
                Fill in the required information and click "Create Shop".
              </p>
            </div>
            <div className="border-b pb-4">
              <h4 className="font-medium mb-2">How do I add products to my inventory?</h4>
              <p className="text-sm text-gray-600">
                Navigate to Masters → Products and click on the "Add Product" button.
                Fill in the product details and click "Save".
              </p>
            </div>
            <div className="border-b pb-4">
              <h4 className="font-medium mb-2">How do I create a new order?</h4>
              <p className="text-sm text-gray-600">
                Go to Billing → POS. Select products, add customer details if needed,
                and complete the order by selecting a payment method.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
