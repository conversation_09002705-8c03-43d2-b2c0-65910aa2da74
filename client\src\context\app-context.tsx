import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { Shop, ShopSettings, Branch } from "@shared/schema";
import { apiRequest, queryClient, invalidateAndRefetch } from "@/lib/queryClient";

// Agent Mode Types
export type AgentModeType = 'normal' | 'customer-service' | 'sales' | 'training' | 'demo';

export interface AgentModeConfig {
  type: AgentModeType;
  label: string;
  description: string;
  color: string;
  permissions?: string[];
  features?: {
    restrictedActions?: string[];
    enhancedFeatures?: string[];
    customUI?: boolean;
  };
}

interface AppContextType {
  currentShop: Shop | null;
  userShops: Shop[];
  currentBranch: Branch | null;
  userBranches: Branch[];
  shopSettings: ShopSettings | null;
  isSidebarOpen: boolean;
  isLoading: boolean;
  agentMode: AgentModeType;
  agentModeConfig: AgentModeConfig;
  isAgentModeEnabled: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  fetchShopSettings: () => Promise<void>;
  fetchUserShops: () => Promise<void>;
  fetchUserBranches: (shopId: number) => Promise<void>;
  setCurrentShop: (shop: Shop) => void;
  setCurrentBranch: (branch: Branch) => void;
  switchShop: (shopId: number) => Promise<void>;
  switchBranch: (branchId: number) => Promise<void>;
  forceRefreshReports: () => Promise<void>;
  setAgentMode: (mode: AgentModeType) => void;
  toggleAgentMode: () => void;
  getAgentModeConfig: (mode: AgentModeType) => AgentModeConfig;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [currentShop, setCurrentShop] = useState<Shop | null>(null);
  const [userShops, setUserShops] = useState<Shop[]>([]);
  const [currentBranch, setCurrentBranch] = useState<Branch | null>(null);
  const [userBranches, setUserBranches] = useState<Branch[]>([]);
  const [shopSettings, setShopSettings] = useState<ShopSettings | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Load current shop from localStorage on init
  useEffect(() => {
    const savedShop = localStorage.getItem("current_shop");
    if (savedShop) {
      try {
        const shop = JSON.parse(savedShop);
        setCurrentShop(shop);
      } catch (error) {
        console.error("Error parsing saved shop:", error);
        localStorage.removeItem("current_shop");
      }
    }
  }, []);

  // Load current branch from localStorage on init
  useEffect(() => {
    const savedBranch = localStorage.getItem("current_branch");
    if (savedBranch) {
      try {
        setCurrentBranch(JSON.parse(savedBranch));
      } catch (error) {
        console.error("Error parsing saved branch:", error);
        localStorage.removeItem("current_branch");
      }
    }
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  // Set current shop and save to localStorage
  const handleSetCurrentShop = (shop: Shop) => {
    setCurrentShop(shop);
    localStorage.setItem("current_shop", JSON.stringify(shop));
  };

  // Set current branch and save to localStorage
  const handleSetCurrentBranch = (branch: Branch) => {
    setCurrentBranch(branch);
    localStorage.setItem("current_branch", JSON.stringify(branch));
  };

  // Fetch branches for a specific shop
  const fetchUserBranches = async (shopId: number) => {
    try {
      console.log(`Fetching branches for shop ${shopId}`);
      setIsLoading(true);
      const token = localStorage.getItem("auth_token");

      if (!token) {
        console.log('No auth token found, aborting branch fetch');
        return;
      }

      console.log(`Making API request to /api/shops/${shopId}/branches`);
      const response = await apiRequest("GET", `/api/shops/${shopId}/branches`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error response from branch API: ${response.status} ${response.statusText}`, errorText);
        return;
      }

      const branches = await response.json();
      console.log(`Received ${branches.length} branches from API:`, branches);

      setUserBranches(branches);
      console.log('Updated userBranches state with fetched branches');

      // If no current branch is set and we have branches, set the first one (main branch)
      if (!currentBranch && branches.length > 0) {
        console.log('No current branch set, selecting one from fetched branches');

        // Find the main branch first
        const mainBranch = branches.find(b => b.isMainBranch);
        if (mainBranch) {
          console.log('Found main branch, setting as current:', mainBranch);
          handleSetCurrentBranch(mainBranch);
        } else if (branches.length > 0) {
          // If no main branch, set the first one
          console.log('No main branch found, setting first branch as current:', branches[0]);
          handleSetCurrentBranch(branches[0]);
        }
      } else {
        console.log('Current branch already set or no branches available');
      }
    } catch (error) {
      console.error("Error fetching user branches:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fetch branches when shop is loaded and we have auth token
  useEffect(() => {
    const token = localStorage.getItem("auth_token");
    if (currentShop && token && userBranches.length === 0) {
      console.log(`Auto-fetching branches for shop ${currentShop.name} (ID: ${currentShop.id})`);
      fetchUserBranches(currentShop.id);
    }
  }, [currentShop]); // Only depend on currentShop to avoid infinite loops

  // Switch to a different shop
  const switchShop = async (shopId: number) => {
    console.log(`Switching to shop with ID: ${shopId}`);

    const shop = userShops.find(s => s.id === shopId);
    if (shop) {
      console.log(`Found shop in userShops: ${shop.name}`);

      // CRITICAL: Clear all cached data to prevent data leakage between shops
      console.log("🔄 SHOP SWITCH: Clearing ALL cached data to prevent data leakage");

      // Step 1: Clear the entire query cache completely
      queryClient.clear();

      // Step 2: Remove any persisted data that might contain shop-specific information
      console.log("🧹 SHOP SWITCH: Clearing localStorage data");
      localStorage.removeItem('pos_selected_customer');
      localStorage.removeItem('pos_order_items');
      localStorage.removeItem('pos_table_selection');
      localStorage.removeItem('selectedProducts'); // Clear selected products when switching shops

      // Step 3: Set the new shop BEFORE any data fetching
      console.log(`🏪 SHOP SWITCH: Setting current shop to ${shop.name} (ID: ${shopId})`);
      handleSetCurrentShop(shop);

      // Step 4: Clear current branch to prevent branch data leakage
      setCurrentBranch(null);
      localStorage.removeItem("current_branch");

      // Step 5: Fetch branches for this shop
      await fetchUserBranches(shopId);

      // Step 6: Force invalidate and refetch all shop-specific data with new shop context
      console.log("🔄 SHOP SWITCH: Force refetching all data with new shop context");
      await invalidateAndRefetch([
        '/api/products',
        '/api/categories',
        '/api/customers',
        '/api/tables',
        '/api/platforms',
        '/api/payment-methods',
        '/api/purchases',
        '/api/expenses',
        '/api/orders/recent',
        '/api/orders/all',
        '/api/settings/tax',
        '/api/settings/discount',
        '/api/reports/tax',
        '/api/reports/sales',
        '/api/reports/payments',
        '/api/reports/inventory',
        '/api/payments',
        '/api/dashboard/stats',
        '/api/dashboard/revenue',
        '/api/dashboard/order-distribution',
        '/api/dashboard/top-products',
        `/api/shops/${shopId}`,
        '/api/shops/current'
      ]);

      // Step 7: Force refresh reports after ensuring shop context is updated
      setTimeout(async () => {
        console.log("🔄 SHOP SWITCH: Final reports refresh");
        await forceRefreshReports();
      }, 1000); // Increased delay to ensure all data is properly refreshed

      console.log(`✅ SHOP SWITCH: Successfully switched to ${shop.name}`);
      return;
    }

    // If shop not found in userShops, fetch it
    try {
      setIsLoading(true);
      console.log(`Fetching shop with ID: ${shopId}`);
      const response = await apiRequest("GET", `/api/shops/${shopId}`);
      const shopData = await response.json();
      console.log(`Fetched shop data: ${JSON.stringify(shopData)}`);

      // CRITICAL: Clear all cached data to prevent data leakage between shops
      console.log("🔄 SHOP SWITCH: Clearing ALL cached data to prevent data leakage");

      // Step 1: Clear the entire query cache completely
      queryClient.clear();

      // Step 2: Remove any persisted data that might contain shop-specific information
      console.log("🧹 SHOP SWITCH: Clearing localStorage data");
      localStorage.removeItem('pos_selected_customer');
      localStorage.removeItem('pos_order_items');
      localStorage.removeItem('pos_table_selection');

      // Step 3: Set the new shop BEFORE any data fetching
      console.log(`🏪 SHOP SWITCH: Setting current shop to ${shopData.name} (ID: ${shopId})`);
      handleSetCurrentShop(shopData);

      // Step 4: Clear current branch to prevent branch data leakage
      setCurrentBranch(null);
      localStorage.removeItem("current_branch");

      // Step 5: Fetch branches for this shop
      await fetchUserBranches(shopId);

      // Step 6: Force invalidate and refetch all shop-specific data with new shop context
      console.log("🔄 SHOP SWITCH: Force refetching all data with new shop context");
      await invalidateAndRefetch([
        '/api/products',
        '/api/categories',
        '/api/customers',
        '/api/tables',
        '/api/platforms',
        '/api/payment-methods',
        '/api/purchases',
        '/api/expenses',
        '/api/orders/recent',
        '/api/orders/all',
        '/api/settings/tax',
        '/api/settings/discount',
        '/api/reports/tax',
        '/api/reports/sales',
        '/api/reports/payments',
        '/api/reports/inventory',
        '/api/payments',
        '/api/dashboard/stats',
        '/api/dashboard/revenue',
        '/api/dashboard/order-distribution',
        '/api/dashboard/top-products',
        `/api/shops/${shopId}`,
        '/api/shops/current'
      ]);

      // Step 7: Force refresh reports after ensuring shop context is updated
      setTimeout(async () => {
        console.log("🔄 SHOP SWITCH: Final reports refresh");
        await forceRefreshReports();
      }, 1000); // Increased delay to ensure all data is properly refreshed

      console.log(`✅ SHOP SWITCH: Successfully switched to ${shopData.name}`);
    } catch (error) {
      console.error("Error switching shop:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch all shops the user belongs to
  const fetchUserShops = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem("auth_token");

      if (!token) return;

      const response = await apiRequest("GET", "/api/shops");
      const shops = await response.json();
      setUserShops(shops);

      // If no current shop is set and we have shops, set the first one
      if (!currentShop && shops.length > 0) {
        handleSetCurrentShop(shops[0]);

        // Also fetch branches for this shop
        await fetchUserBranches(shops[0].id);
      }
    } catch (error) {
      console.error("Error fetching user shops:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Switch to a different branch
  const switchBranch = async (branchId: number) => {
    console.log(`Switching to branch with ID: ${branchId}`);

    const branch = userBranches.find(b => b.id === branchId);
    if (branch) {
      console.log(`Found branch in userBranches: ${branch.name}`);

      // First invalidate all queries to refresh data for the new branch
      console.log("Invalidating and refetching all queries to refresh data");
      // Use our improved function to invalidate and immediately refetch all queries
      await invalidateAndRefetch([
        '/api/products',
        '/api/categories',
        '/api/customers',
        '/api/tables',
        '/api/platforms',
        '/api/payment-methods',
        '/api/purchases',
        '/api/expenses',
        '/api/orders/recent',
        '/api/orders/all',
        '/api/settings/tax',
        '/api/settings/discount',
        '/api/reports/tax',
        '/api/reports/sales',
        '/api/reports/payments',
        '/api/reports/inventory',
        '/api/payments'
      ]);

      // Then set the current branch
      handleSetCurrentBranch(branch);

      // Instead of reloading the page, just return and let the caller navigate
      return;
    }

    // If branch not found in userBranches, fetch it
    try {
      setIsLoading(true);
      console.log(`Fetching branch with ID: ${branchId}`);
      const response = await apiRequest("GET", `/api/branches/${branchId}`);
      const branchData = await response.json();
      console.log(`Fetched branch data: ${JSON.stringify(branchData)}`);

      // First invalidate all queries to refresh data for the new branch
      console.log("Invalidating and refetching all queries to refresh data");
      // Use our improved function to invalidate and immediately refetch all queries
      await invalidateAndRefetch([
        '/api/products',
        '/api/categories',
        '/api/customers',
        '/api/tables',
        '/api/platforms',
        '/api/payment-methods',
        '/api/purchases',
        '/api/expenses',
        '/api/orders/recent',
        '/api/orders/all',
        '/api/settings/tax',
        '/api/settings/discount',
        '/api/reports/tax',
        '/api/reports/sales',
        '/api/reports/payments',
        '/api/reports/inventory',
        '/api/payments'
      ]);

      // Then set the current branch
      handleSetCurrentBranch(branchData);

      // Instead of reloading the page, just return and let the caller navigate
    } catch (error) {
      console.error("Error switching branch:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch settings for the current shop
  const fetchShopSettings = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem("auth_token");

      if (!token || !currentShop) return;

      // Fetch the current shop data which includes settings
      const response = await apiRequest("GET", `/api/shops/${currentShop.id}`);
      const data = await response.json();

      // Update the current shop with the latest data
      handleSetCurrentShop(data);

      // Set shop settings from the shop data
      setShopSettings({
        name: data.name,
        address: data.address,
        phone: data.phone,
        email: data.email,
        taxRegistration: data.taxRegistration,
        logo: data.logo,
        currencySymbol: data.currencySymbol || "₹",
        footerText: data.footerText,
        receiptTemplate: data.receiptTemplate
      });
    } catch (error) {
      console.error("Error fetching shop settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Force refresh all report data
  const forceRefreshReports = async () => {
    console.log("Force refreshing all report data");

    // Clear all report queries from cache
    await queryClient.invalidateQueries({
      predicate: (query) => {
        const queryKey = query.queryKey;
        return Array.isArray(queryKey) && queryKey.length > 0 &&
               typeof queryKey[0] === 'string' && queryKey[0].startsWith('/api/reports/');
      }
    });

    // Force refetch all report queries
    await queryClient.refetchQueries({
      predicate: (query) => {
        const queryKey = query.queryKey;
        return Array.isArray(queryKey) && queryKey.length > 0 &&
               typeof queryKey[0] === 'string' && queryKey[0].startsWith('/api/reports/');
      },
      type: 'all'
    });
  };

  const value = {
    currentShop,
    userShops,
    currentBranch,
    userBranches,
    shopSettings,
    isSidebarOpen,
    isLoading,
    toggleSidebar,
    closeSidebar,
    fetchShopSettings,
    fetchUserShops,
    fetchUserBranches,
    setCurrentShop: handleSetCurrentShop,
    setCurrentBranch: handleSetCurrentBranch,
    switchShop,
    switchBranch,
    forceRefreshReports,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useApp must be used within an AppProvider");
  }
  return context;
};
