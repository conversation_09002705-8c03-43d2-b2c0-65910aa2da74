import { useEffect } from 'react';
import { queryClient } from '@/lib/queryClient';

/**
 * Custom hook to ensure fresh data by clearing cache once on mount
 * Use this for pages that need to start with fresh data
 */
export function useFreshData(apiEndpoints: string[], dependencies: any[] = []) {
  useEffect(() => {
    const clearCacheForEndpoints = async () => {
      console.log('useFreshData: Clearing cache for endpoints on mount:', apiEndpoints);

      // Only clear specific endpoint queries, not all cache
      for (const endpoint of apiEndpoints) {
        await queryClient.removeQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   queryKey[0].includes(endpoint);
          }
        });
      }

      console.log('useFreshData: Cache cleared for endpoints:', apiEndpoints);
    };

    clearCacheForEndpoints();
  }, []); // Only run once on mount
}

/**
 * Force refresh function that completely clears cache and reloads the page
 */
export function forceRefreshPage() {
  console.log('forceRefreshPage: Clearing all cache and reloading page');
  queryClient.clear();
  window.location.reload();
}
