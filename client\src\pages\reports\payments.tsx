import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";
import { jsPDF } from "jspdf";
import 'jspdf-autotable';

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart";

import {
  Download,
  Printer,
  Calendar,
  FilterX,
  FileText,
  File,
  FileDown,
  CalendarRange,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

export default function PaymentsReport() {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const { toast } = useToast();

  const [period, setPeriod] = useState<string>("today");
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [chartType, setChartType] = useState<string>("pie");

  // Fetch payments report data
  const { data: paymentsReportData, isLoading: isLoadingPaymentsReport } = useQuery({
    queryKey: ['/api/reports/payments', currentShop?.id, period, startDate, endDate],
    queryFn: async () => {
      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('period', period);
        if (startDate && endDate && period === 'custom') {
          params.append('startDate', startDate);
          params.append('endDate', endDate);
        }

        // Use apiRequest instead of fetch to ensure proper shop/branch context
        const response = await apiRequest('GET', `/api/reports/payments?${params.toString()}`);
        if (!response.ok) {
          throw new Error('Failed to fetch payments report');
        }
        return await response.json();
      } catch (error) {
        console.error('Error fetching payments report:', error);
        return null;
      }
    },
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Calculate payment statistics
  const calculatePaymentStats = () => {
    if (!paymentsReportData) {
      return {
        totalAmount: 0,
        paymentsByMethod: {},
        paymentDetails: [],
        orderCount: 0
      };
    }

    const { orders, stats } = paymentsReportData;

    // Get statistics from the API response
    const totalAmount = stats?.totalSales || 0;
    const paymentsByMethod: Record<string, number> = {};

    // Convert API payment data to the expected format
    if (stats?.paymentsByMethod) {
      Object.entries(stats.paymentsByMethod).forEach(([method, data]: [string, any]) => {
        paymentsByMethod[method] = data.amount || 0;
      });
    }

    // Create detailed payment records from orders
    const paymentDetails: any[] = [];

    if (orders && Array.isArray(orders)) {
      orders.forEach((order: any) => {
        const orderAmount = parseFloat(order.totalAmount || '0');
        if (orderAmount <= 0) return;

        // Normalize payment method
        let paymentMethod = order.paymentMethod || 'Unknown';
        if (typeof paymentMethod === 'string') {
          paymentMethod = paymentMethod.charAt(0).toUpperCase() + paymentMethod.slice(1);
        }

        // Normalize payment status
        let paymentStatus = order.paymentStatus || order.status || 'Unknown';
        if (typeof paymentStatus === 'string') {
          paymentStatus = paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1);
        }

        paymentDetails.push({
          orderId: order.id || 'unknown',
          orderNumber: order.orderNumber || `Order-${order.id || 'unknown'}`,
          date: order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Unknown date',
          paymentMethod: paymentMethod,
          amount: orderAmount,
          status: paymentStatus
        });
      });
    }

    return {
      totalAmount,
      paymentsByMethod,
      paymentDetails,
      orderCount: orders?.length || 0
    };
  };

  const paymentStats = calculatePaymentStats();

  // Handle custom date range
  const handlePeriodChange = (value: string) => {
    setPeriod(value);
    if (value !== "custom") {
      setStartDate("");
      setEndDate("");
    }
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    if (!paymentStats.paymentDetails || paymentStats.paymentDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No payment data available to export",
      });
      return;
    }

    try {
      // Format the data for export
      const formattedData = formatDataForExport(paymentStats.paymentDetails, {
        amount: (value) => `₹${parseFloat(value).toFixed(2)}`,
        paymentMethod: (value) => value.charAt(0).toUpperCase() + value.slice(1),
        status: (value) => value.charAt(0).toUpperCase() + value.slice(1),
      });

      // Export to Excel
      const success = exportToExcel(
        formattedData,
        `Payment_Report_${period === 'custom' ?
          `${startDate}_to_${endDate}` :
          period}_${new Date().toISOString().split('T')[0]}`
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Payment report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the payment report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    if (!paymentStats.paymentDetails || paymentStats.paymentDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No payment data available to export",
      });
      return;
    }

    try {
      // Format the data for export
      const formattedData = formatDataForExport(paymentStats.paymentDetails, {
        amount: (value) => `₹${parseFloat(value).toFixed(2)}`,
        paymentMethod: (value) => value.charAt(0).toUpperCase() + value.slice(1),
        status: (value) => value.charAt(0).toUpperCase() + value.slice(1),
      });

      // Export to CSV
      const success = exportToCSV(
        formattedData,
        `Payment_Report_${period === 'custom' ?
          `${startDate}_to_${endDate}` :
          period}_${new Date().toISOString().split('T')[0]}`
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Payment report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the payment report",
      });
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    if (!paymentStats.paymentDetails || paymentStats.paymentDetails.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No payment data available to export",
      });
      return;
    }

    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(18);
      doc.text("Payment Method Analysis", 14, 22);

      // Add period information
      doc.setFontSize(12);
      let periodText = "Period: ";
      if (period === "today") {
        periodText += "Today";
      } else if (period === "yesterday") {
        periodText += "Yesterday";
      } else if (period === "week") {
        periodText += "Last 7 days";
      } else if (period === "month") {
        periodText += "Last 30 days";
      } else if (period === "custom") {
        periodText += `${startDate} to ${endDate}`;
      }
      doc.text(periodText, 14, 32);

      // Add total information
      doc.text(`Total Amount: ₹${paymentStats.totalAmount.toFixed(2)}`, 14, 42);
      doc.text(`Total Orders: ${paymentStats.orderCount}`, 14, 52);

      // Add payment method breakdown table
      const tableData = Object.entries(paymentStats.paymentsByMethod).map(([method, amount]) => [
        method.charAt(0).toUpperCase() + method.slice(1),
        `₹${amount.toFixed(2)}`,
        `${((amount / paymentStats.totalAmount) * 100).toFixed(2)}%`
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: 60,
        head: [['Payment Method', 'Amount', 'Percentage']],
        body: tableData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 10 }
      });

      // Add detailed payment table
      const detailedData = paymentStats.paymentDetails.map(item => [
        item.orderNumber,
        item.date,
        item.paymentMethod.charAt(0).toUpperCase() + item.paymentMethod.slice(1),
        `₹${item.amount.toFixed(2)}`,
        item.status.charAt(0).toUpperCase() + item.status.slice(1)
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: doc.lastAutoTable.finalY + 15,
        head: [['Order #', 'Date', 'Payment Method', 'Amount', 'Status']],
        body: detailedData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 9 },
        columnStyles: {
          0: { cellWidth: 25 },
          1: { cellWidth: 30 },
          2: { cellWidth: 40 },
          3: { cellWidth: 30 },
          4: { cellWidth: 30 }
        }
      });

      // Save the PDF
      doc.save(`Payment_Report_${period === 'custom' ?
        `${startDate}_to_${endDate}` :
        period}_${new Date().toISOString().split('T')[0]}.pdf`);

      toast({
        title: "Export successful",
        description: "Payment report has been exported to PDF",
      });
    } catch (error) {
      console.error("PDF export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the payment report to PDF",
      });
    }
  };

  // Prepare chart data
  const chartData = Object.entries(paymentStats.paymentsByMethod).map(([method, amount]) => ({
    name: method,
    value: amount
  }));

  // Add fallback data if no payment data is available
  if (chartData.length === 0) {
    console.log('Payment Report - No chart data available, adding fallback data');
    chartData.push({
      name: 'No Payment Data',
      value: 0
    });
  }

  // Payment method colors for badges
  const paymentMethodColors: Record<string, string> = {
    Cash: "bg-green-100 text-green-800",
    Card: "bg-blue-100 text-blue-800",
    UPI: "bg-purple-100 text-purple-800",
    Wallet: "bg-orange-100 text-orange-800",
    Online: "bg-indigo-100 text-indigo-800",
    Unknown: "bg-gray-100 text-gray-800",
    default: "bg-gray-100 text-gray-800"
  };

  // Payment status colors for badges
  const paymentStatusColors: Record<string, string> = {
    Paid: "bg-green-100 text-green-800",
    Pending: "bg-yellow-100 text-yellow-800",
    Failed: "bg-red-100 text-red-800",
    Refunded: "bg-blue-100 text-blue-800",
    Unknown: "bg-gray-100 text-gray-800",
    default: "bg-gray-100 text-gray-800"
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h1 className="text-2xl font-bold">Payment Method Analysis</h1>

        <div className="flex flex-wrap gap-2">
          <Select value={period} onValueChange={handlePeriodChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="week">Last 7 days</SelectItem>
              <SelectItem value="month">Last 30 days</SelectItem>
              <SelectItem value="custom">Custom date range</SelectItem>
            </SelectContent>
          </Select>

          {period === "custom" && (
            <div className="flex gap-2">
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-[150px]"
              />
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-[150px]"
              />
            </div>
          )}

          <div className="flex gap-2">
            <Button variant="outline" size="icon" onClick={handleExportToExcel}>
              <File className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleExportToCSV}>
              <FileText className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" onClick={handleExportToPDF}>
              <FileDown className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Sales</span>
              {isLoadingPaymentsReport ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{paymentStats?.totalAmount.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Orders</span>
              {isLoadingPaymentsReport ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {paymentStats?.orderCount || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Payment Methods</span>
              {isLoadingPaymentsReport ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {Object.keys(paymentStats?.paymentsByMethod || {}).length || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Avg. Order Value</span>
              {isLoadingPaymentsReport ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{paymentStats?.orderCount ? (paymentStats.totalAmount / paymentStats.orderCount).toFixed(2) : "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Distribution Chart */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle>Payment Method Distribution</CardTitle>
          <Select value={chartType} onValueChange={setChartType}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Chart Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pie">Pie Chart</SelectItem>
              <SelectItem value="bar">Bar Chart</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent className="h-[300px]">
          {isLoadingPaymentsReport ? (
            <Skeleton className="h-full w-full" />
          ) : chartType === "pie" ? (
            <PieChart
              data={chartData}
              index="name"
              category="value"
              valueFormatter={(value) => `₹${parseFloat(value).toFixed(2)}`}
              showAnimation={true}
              showLegend={true}
            />
          ) : (
            <BarChart
              data={chartData}
              index="name"
              categories={["value"]}
              valueFormatter={(value) => `₹${parseFloat(value).toFixed(2)}`}
              showAnimation={true}
              showLegend={false}
              showGridLines={true}
            />
          )}
        </CardContent>
      </Card>

      {/* Payment Method Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Method Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingPaymentsReport ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : Object.keys(paymentStats.paymentsByMethod).length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Payment Method</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Percentage</TableHead>
                  <TableHead>Orders</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(paymentStats.paymentsByMethod).map(([method, amount]) => {
                  // Count orders for this payment method
                  const orderCount = paymentStats.paymentDetails.filter(
                    item => item.paymentMethod === method
                  ).length;

                  // Calculate percentage
                  const percentage = (amount / paymentStats.totalAmount) * 100;

                  return (
                    <TableRow key={method}>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={cn(
                            "rounded-full py-1",
                            paymentMethodColors[method] || paymentMethodColors.default
                          )}
                        >
                          {method}
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">₹{amount.toFixed(2)}</TableCell>
                      <TableCell>{percentage.toFixed(2)}%</TableCell>
                      <TableCell>{orderCount}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          ) : (
            <div className="flex items-center justify-center py-8">
              <p className="text-muted-foreground">No payment data available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detailed Payment Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order #</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Payment Method</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingPaymentsReport ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                    </TableRow>
                  ))
                ) : paymentStats.paymentDetails.length > 0 ? (
                  paymentStats.paymentDetails.map((item, index) => (
                    <TableRow key={`${item.orderId}-${index}`}>
                      <TableCell className="font-medium">#{item.orderNumber}</TableCell>
                      <TableCell>{item.date}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={cn(
                            "rounded-full py-1",
                            paymentMethodColors[item.paymentMethod] || paymentMethodColors.default
                          )}
                        >
                          {item.paymentMethod}
                        </Badge>
                      </TableCell>
                      <TableCell>₹{item.amount.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={cn(
                            "rounded-full py-1",
                            paymentStatusColors[item.status] || paymentStatusColors.default
                          )}
                        >
                          {item.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                      No payment transactions found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
