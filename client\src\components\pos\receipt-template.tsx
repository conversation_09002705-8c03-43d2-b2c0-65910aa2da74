import React, { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { Button } from '@/components/ui/button';
import { Printer, Download, Share } from 'lucide-react';
import { useApp } from '@/context/app-context';

interface OrderItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

interface Order {
  id: number;
  orderNumber: string;
  orderType: string;
  status: string;
  tableId?: number | null;
  customerId?: number | null;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paymentMethod: string;
  paymentStatus: string;
  createdAt: string;
  items: OrderItem[];
}

interface Customer {
  id: number;
  name: string;
  phone: string;
  email?: string;
  address?: string;
}

interface ReceiptTemplateProps {
  order: Order;
  customer?: Customer;
  tableName?: string;
}

export function ReceiptTemplate({ order, customer, tableName }: ReceiptTemplateProps) {
  const { currentShop } = useApp();
  const receiptRef = useRef<HTMLDivElement>(null);

  // Handle case where order might be wrapped in a response object
  const actualOrder = (order as any)?.order || order;

  // Ensure financial fields have default values to prevent undefined errors
  const safeOrder = {
    ...actualOrder,
    subtotal: Number(actualOrder?.subtotal || 0),
    taxAmount: Number(actualOrder?.taxAmount || 0),
    discountAmount: Number(actualOrder?.discountAmount || 0),
    totalAmount: Number(actualOrder?.totalAmount || 0),
    items: actualOrder?.items || []
  };

  // Format date
  const orderDate = new Date(safeOrder.createdAt).toLocaleString();

  // Get order type display name
  const orderTypeDisplay =
    safeOrder.orderType === "dine_in" ? "Table" :
    safeOrder.orderType === "takeaway" ? "Parcel" :
    safeOrder.orderType === "eat_and_pay" ? "Eat & Pay" : "Online";

  // Use stored tax rate if available, otherwise calculate from amounts
  const taxPercentage = (safeOrder as any).taxRate && (safeOrder as any).taxRate > 0
    ? (safeOrder as any).taxRate.toFixed(1)
    : safeOrder.subtotal > 0
      ? ((safeOrder.taxAmount / safeOrder.subtotal) * 100).toFixed(1)
      : "0.0";

  // Format tax display - show the actual tax percentage
  const taxDisplayText = safeOrder.taxAmount > 0
    ? `Tax (${taxPercentage}%)`
    : "Tax (0.0%)";

  // Handle print receipt
  const handlePrint = useReactToPrint({
    content: () => receiptRef.current,
    documentTitle: `Receipt-${safeOrder.orderNumber}`,
  });

  // Handle export to PDF
  const handleExportPDF = async () => {
    if (!receiptRef.current) return;

    const canvas = await html2canvas(receiptRef.current, {
      scale: 2,
      logging: false,
      useCORS: true,
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: [80, 200], // Receipt width: 80mm (standard thermal receipt width)
    });

    // Calculate the height based on the aspect ratio
    const imgWidth = 80;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    pdf.save(`Receipt-${safeOrder.orderNumber}.pdf`);
  };

  // Handle share receipt (mobile only)
  const handleShareReceipt = async () => {
    if (!receiptRef.current || !navigator.share) return;

    try {
      const canvas = await html2canvas(receiptRef.current, {
        scale: 2,
        logging: false,
        useCORS: true,
      });

      canvas.toBlob(async (blob) => {
        if (!blob) return;

        const file = new File([blob], `Receipt-${safeOrder.orderNumber}.png`, { type: 'image/png' });

        try {
          await navigator.share({
            title: `Receipt #${safeOrder.orderNumber}`,
            text: 'Your receipt from NembooBill',
            files: [file],
          });
        } catch (error) {
          console.error('Error sharing receipt:', error);
        }
      });
    } catch (error) {
      console.error('Error creating receipt image:', error);
    }
  };

  return (
    <div className="flex flex-col">
      {/* Receipt Actions */}
      <div className="flex justify-center space-x-4 mb-4">
        <Button onClick={handlePrint} className="flex items-center">
          <Printer className="mr-2 h-4 w-4" />
          Print
        </Button>
        <Button onClick={handleExportPDF} variant="outline" className="flex items-center">
          <Download className="mr-2 h-4 w-4" />
          Save PDF
        </Button>
        {navigator.share && (
          <Button onClick={handleShareReceipt} variant="outline" className="flex items-center">
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>
        )}
      </div>

      {/* Receipt Template */}
      <div className="border rounded-lg mx-auto w-full max-w-[300px] p-4 text-sm bg-white" ref={receiptRef}>
        {/* Shop Header */}
        <div className="text-center mb-4">
          <h2 className="font-bold text-lg">{currentShop?.name || 'NembooBill'}</h2>
          <p className="text-xs">{currentShop?.address || ''}</p>
          <p className="text-xs">{currentShop?.phone || ''}</p>
          {currentShop?.email && <p className="text-xs">{currentShop.email}</p>}
        </div>

        {/* Order Info */}
        <div className="border-t border-b py-2 mb-3">
          <div className="flex justify-between">
            <span>Receipt #:</span>
            <span>{safeOrder.orderNumber}</span>
          </div>
          <div className="flex justify-between">
            <span>Date:</span>
            <span>{orderDate}</span>
          </div>
          <div className="flex justify-between">
            <span>Type:</span>
            <span>{orderTypeDisplay}</span>
          </div>
          {tableName && (
            <div className="flex justify-between">
              <span>Table:</span>
              <span>{tableName}</span>
            </div>
          )}
          {customer && (
            <div className="flex justify-between">
              <span>Customer:</span>
              <span>{customer.name}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span>Payment:</span>
            <span>{safeOrder.paymentMethod}</span>
          </div>
        </div>

        {/* Order Items */}
        <div className="mb-3">
          <div className="flex font-bold border-b pb-1 mb-2">
            <div className="w-1/2">Item</div>
            <div className="w-1/6 text-center">Qty</div>
            <div className="w-1/3 text-right">Amount</div>
          </div>

          {safeOrder.items.map((item, index) => (
            <div key={index} className="py-1">
              <div className="flex text-xs">
                <div className="w-1/2">{item.productName}</div>
                <div className="w-1/6 text-center">{item.quantity}</div>
                <div className="w-1/3 text-right">₹{Number(item.totalPrice || 0).toFixed(2)}</div>
              </div>
              {item.notes && (
                <div className="text-xs text-gray-600 italic ml-2 mt-1">
                  Note: {item.notes}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Order Totals */}
        <div className="border-t pt-2">
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>₹{safeOrder.subtotal.toFixed(2)}</span>
          </div>
          {safeOrder.discountAmount > 0 && (
            <div className="flex justify-between">
              <span>Discount:</span>
              <span>-₹{safeOrder.discountAmount.toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span>{taxDisplayText}:</span>
            <span>₹{safeOrder.taxAmount.toFixed(2)}</span>
          </div>
          <div className="flex justify-between font-bold mt-2 pt-2 border-t">
            <span>Total:</span>
            <span>₹{safeOrder.totalAmount.toFixed(2)}</span>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-4 text-center text-xs">
          <p>{currentShop?.footerText || 'Thank you for your business!'}</p>
          <p className="mt-2">Powered by NembooBill</p>
        </div>
      </div>
    </div>
  );
}
