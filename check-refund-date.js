import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkRefundDate() {
  const client = await pool.connect();
  try {
    console.log('📅 Checking refund dates...\n');

    // Get refund with date info
    const refundQuery = `
      SELECT id, order_id, amount, created_at, 
             DATE(created_at) as refund_date,
             EXTRACT(YEAR FROM created_at) as year,
             EXTRACT(MONTH FROM created_at) as month,
             EXTRACT(DAY FROM created_at) as day
      FROM refunds 
      ORDER BY created_at DESC
    `;
    
    const result = await client.query(refundQuery);
    
    console.log('📊 Refund dates:');
    result.rows.forEach(refund => {
      console.log(`  Refund ID: ${refund.id}`);
      console.log(`  Order ID: ${refund.order_id}`);
      console.log(`  Amount: ${refund.amount}`);
      console.log(`  Created At: ${refund.created_at}`);
      console.log(`  Date: ${refund.refund_date}`);
      console.log(`  Year: ${refund.year}, Month: ${refund.month}, Day: ${refund.day}`);
      console.log('  ---');
    });

    // Test the date range that was used in the API call
    const testStartDate = new Date('2025-05-12T09:26:41.511Z');
    const testEndDate = new Date('2025-06-11T09:26:41.511Z');
    
    console.log('\n🧪 Testing date range filtering:');
    console.log(`  API Start Date: ${testStartDate.toISOString()}`);
    console.log(`  API End Date: ${testEndDate.toISOString()}`);
    
    const dateFilterQuery = `
      SELECT COUNT(*) as count, MIN(created_at) as earliest, MAX(created_at) as latest
      FROM refunds 
      WHERE created_at >= $1 AND created_at <= $2
    `;
    
    const dateResult = await client.query(dateFilterQuery, [testStartDate, testEndDate]);
    console.log(`  Refunds in range: ${dateResult.rows[0].count}`);
    console.log(`  Earliest in range: ${dateResult.rows[0].earliest}`);
    console.log(`  Latest in range: ${dateResult.rows[0].latest}`);

    // Test with a wider date range
    const wideStartDate = new Date('2024-01-01');
    const wideEndDate = new Date('2026-12-31');
    
    console.log('\n📅 Testing with wider date range:');
    console.log(`  Wide Start Date: ${wideStartDate.toISOString()}`);
    console.log(`  Wide End Date: ${wideEndDate.toISOString()}`);
    
    const wideResult = await client.query(dateFilterQuery, [wideStartDate, wideEndDate]);
    console.log(`  Refunds in wide range: ${wideResult.rows[0].count}`);

  } catch (error) {
    console.error('❌ Error checking refund dates:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

checkRefundDate();
