// Test the API directly to see the exact response
const fetch = require('node-fetch');

async function testAPI() {
  try {
    console.log('Testing billing history API directly...');
    
    // Test the API endpoint directly
    const response = await fetch('http://localhost:5000/api/subscriptions/billing-history', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MiwidXNlcm5hbWUiOiI5NTY2NTY3MTczIiwicm9sZSI6OCwiaWF0IjoxNzUwNzY1NTEwLCJleHAiOjE3NTA4NTE5MTB9.ZiGO0ErpJhtF4JtfbHgpyC-vPYbLf1uFnz9EM6JuboU',
        'X-Shop-ID': '12',
        'Content-Type': 'application/json'
      }
    });

    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('\n✅ API Response:');
      console.log(`Found ${data.length} billing records`);
      
      // Show first few records
      data.slice(0, 3).forEach((record, index) => {
        console.log(`\n📋 Record ${index + 1}:`);
        console.log(`  - ID: ${record.id}`);
        console.log(`  - Plan: "${record.plan}"`);
        console.log(`  - Plan Description: "${record.planDescription}"`);
        console.log(`  - Amount: $${record.amount}`);
        console.log(`  - Status: ${record.status}`);
      });
      
      // Check if any records have "Unknown Plan"
      const unknownPlans = data.filter(record => record.plan === 'Unknown Plan');
      if (unknownPlans.length > 0) {
        console.log(`\n⚠️  Found ${unknownPlans.length} records with "Unknown Plan"`);
      } else {
        console.log('\n✅ No "Unknown Plan" records found in API response');
      }
      
    } else {
      const errorText = await response.text();
      console.log('❌ API Error:', errorText);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();
