import { useState, useEffect, useCallback, useRef } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest, invalidateAndRefetch, createOrderTwoStep, queryClient, cartOperations } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useFullscreen } from "@/hooks/use-fullscreen";
import { POSEvents, subscribeToPOSEvent, unsubscribeFromPOSEvent } from "@/lib/socket";
import { TableGrid } from "@/components/pos/table-grid";
import { OrderTypeSelector, OrderType } from "@/components/pos/order-type-selector";
import { CartSidebar } from "@/components/pos/cart-sidebar";
import { CategorySelector } from "@/components/pos/category-selector";
import { PaymentModal } from "@/components/pos/payment-modal";
import { ReceiptModal } from "@/components/pos/receipt-modal";
import { SwipeableCartItem } from "@/components/pos/swipeable-cart-item";
import { z } from "zod";
import "./mobile-pos.css";

import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from "@/components/ui/sheet";


import {
  Search,
  Plus,
  Minus,
  ShoppingBag,
  Home,
  Package,
  Printer,
  Save,
  X,
  Receipt,
  Loader2,
  ChevronDown,
  ChevronUp,
  User,
  Phone,
  MapPin,
  Users,
  ScanLine,
  Settings,
  Clock,
  CreditCard,
  Trash2,
  Check,
  Menu,
  ArrowRight,
  ArrowLeft,
  Maximize,
  Minimize
} from "lucide-react";

export default function MobilePOS() {
  const { token, user } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { isFullscreen, toggleFullscreen } = useFullscreen();

  // State for order
  const [orderItems, setOrderItems] = useState<any[]>([]);
  const [discountValue, setDiscountValue] = useState("0.00");
  const [orderType, setOrderType] = useState<OrderType>("dine_in");
  const [tableId, setTableId] = useState<number | null>(null);
  const [selectedTable, setSelectedTable] = useState<any>(null);
  const [customerId, setCustomerId] = useState<number | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("cash");
  const [searchQuery, setSearchQuery] = useState("");
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [completedOrder, setCompletedOrder] = useState<any>(null);
  const [activeCategory, setActiveCategory] = useState<number | null>(null);
  const [isCartButtonHidden, setIsCartButtonHidden] = useState(false);
  const lastScrollTop = useRef(0);

  // Fetch data from API - request all products for POS
  const { data: products, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products'],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      // Request all products by setting a large page size
      const response = await fetch('/api/products?pageSize=1000', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      return response.json();
    }
  });

  const { data: categories, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['/api/categories'],
    enabled: !!token && !!currentShop,
  });

  const { data: tables, isLoading: isLoadingTables, refetch: refetchTables } = useQuery({
    queryKey: ['/api/tables'],
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Handle real-time table status updates
  const handleTableStatusUpdate = useCallback((data: { tableId: number, status: string }) => {
    console.log('Mobile POS: Received table status update:', data);

    // Update the table status in the cache
    queryClient.setQueryData(['/api/tables'], (oldData: any) => {
      if (!oldData) return oldData;

      return oldData.map((table: any) => {
        if (table.id === data.tableId) {
          return { ...table, status: data.status };
        }
        return table;
      });
    });

    // If the currently selected table is updated, update the selectedTable state
    if (tableId === data.tableId) {
      setSelectedTable((prev: any) => {
        if (!prev) return prev;
        return { ...prev, status: data.status };
      });
    }
  }, [tableId, queryClient]);

  // Handle full tables update
  const handleTablesUpdate = useCallback((data: any[]) => {
    console.log('Mobile POS: Received full tables update:', data);

    // Update the tables data in the cache
    queryClient.setQueryData(['/api/tables'], data);

    // If there's a selected table, update it with the new data
    if (tableId) {
      const updatedTable = data.find((table: any) => table.id === tableId);
      if (updatedTable) {
        setSelectedTable(updatedTable);
      }
    }
  }, [tableId, queryClient]);

  // Subscribe to real-time table status updates
  useEffect(() => {
    if (token && currentShop) {
      console.log('Mobile POS: Subscribing to table status updates');
      subscribeToPOSEvent(POSEvents.TABLE_STATUS_UPDATED, handleTableStatusUpdate);
      subscribeToPOSEvent(POSEvents.TABLES_UPDATED, handleTablesUpdate);

      return () => {
        console.log('Mobile POS: Unsubscribing from table status updates');
        unsubscribeFromPOSEvent(POSEvents.TABLE_STATUS_UPDATED, handleTableStatusUpdate);
        unsubscribeFromPOSEvent(POSEvents.TABLES_UPDATED, handleTablesUpdate);
      };
    }
  }, [token, currentShop, handleTableStatusUpdate, handleTablesUpdate]);

  const { data: taxSettings, isLoading: isLoadingTaxSettings } = useQuery({
    queryKey: ['/api/settings/tax'],
    enabled: !!token && !!currentShop,
  });

  const { data: customers, isLoading: isLoadingCustomers } = useQuery({
    queryKey: ['/api/customers'],
    enabled: !!token && !!currentShop,
  });

  const { data: paymentMethods, isLoading: isLoadingPaymentMethods } = useQuery({
    queryKey: ['/api/payment-methods'],
    enabled: !!token && !!currentShop,
  });

  // Update default payment method when payment methods are loaded
  useEffect(() => {
    if (paymentMethods && paymentMethods.length > 0) {
      const firstMethodCode = paymentMethods[0].name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
      setSelectedPaymentMethod(firstMethodCode);
    }
  }, [paymentMethods]);

  // Clear data when shop changes to prevent data leakage between shops
  useEffect(() => {
    if (currentShop) {
      console.log(`Mobile POS: Shop changed to ${currentShop.name} (ID: ${currentShop.id}), clearing all data`);
      // Clear all mobile POS state when shop changes
      setOrderItems([]);
      setTableId(null);
      setSelectedTable(null);
      setCustomerId(null);
      setSelectedCustomer(null);
      setDiscountValue("0.00");
      setSearchQuery("");
      setActiveCategory(null);
      setIsCartOpen(false);

      // Clear localStorage data
      localStorage.removeItem('mobile_pos_order_items');
      localStorage.removeItem('mobile_pos_table_selection');
      localStorage.removeItem('mobile_pos_customer_selection');
    }
  }, [currentShop?.id]); // Depend on shop ID to trigger when shop changes

  // Function to load existing order items for a table
  const loadExistingOrderItems = async (tableId: number) => {
    try {
      console.log(`Mobile POS: Loading existing order items for table ${tableId}`);
      const response = await apiRequest("GET", `/api/tables/${tableId}/active-order`);

      if (response.ok) {
        const orderData = await response.json();
        console.log("Mobile POS: Loaded existing order data:", orderData);

        // Convert order items to mobile cart items format
        const existingOrderItems = orderData.items.map((item: any) => ({
          id: item.productId,
          name: item.productName,
          price: item.unitPrice,
          quantity: item.quantity,
          notes: item.notes || ""
        }));

        // Set the order items
        setOrderItems(existingOrderItems);

        toast({
          title: "Order Loaded",
          description: `Loaded existing order with ${existingOrderItems.length} items`,
        });

        return true;
      } else if (response.status === 404) {
        // No active order for this table - this is normal
        console.log(`Mobile POS: No active order found for table ${tableId}`);
        return false;
      } else {
        throw new Error(`Failed to load order: ${response.status}`);
      }
    } catch (error) {
      console.error("Mobile POS: Error loading existing order items:", error);
      toast({
        title: "Error",
        description: "Failed to load existing order items",
        variant: "destructive",
      });
      return false;
    }
  };

  // Handle both paginated response format {products: [...], pagination: {...}} and direct array format
  const productsArray = Array.isArray(products) ? products : (products?.products || []);

  // Calculate order totals
  const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const discount = parseFloat(discountValue) || 0;

  // Calculate tax based on individual product tax rates
  let taxAmount = 0;

  orderItems.forEach(item => {
    // Get the product to access its tax rate
    const product = productsArray.find((p: any) => p.id === item.id);
    let itemTaxRate = 0;

    if (product?.taxRate && product.taxRate > 0) {
      // Use product-specific tax rate
      itemTaxRate = product.taxRate;
    } else {
      // Fall back to global tax rate if product has no specific rate
      itemTaxRate = Array.isArray(taxSettings) && taxSettings.length > 0 && taxSettings[0].active
        ? taxSettings[0].rate
        : 5;
    }

    // Calculate tax for this item (after discount is applied proportionally)
    const itemSubtotal = item.price * item.quantity;
    const itemDiscountRatio = subtotal > 0 ? itemSubtotal / subtotal : 0;
    const itemAfterDiscount = itemSubtotal - (discount * itemDiscountRatio);
    const itemTax = itemAfterDiscount * (itemTaxRate / 100);
    taxAmount += itemTax;
  });

  const total = subtotal - discount + taxAmount;

  // Handle scroll to hide/show cart button
  useEffect(() => {
    const productsContainer = document.querySelector('.mobile-pos-products');

    if (!productsContainer) return;

    const handleScroll = () => {
      const scrollTop = productsContainer.scrollTop;

      // Hide button when scrolling down, show when scrolling up
      if (scrollTop > lastScrollTop.current && scrollTop > 50) {
        setIsCartButtonHidden(true);
      } else {
        setIsCartButtonHidden(false);
      }

      lastScrollTop.current = scrollTop;
    };

    productsContainer.addEventListener('scroll', handleScroll);

    return () => {
      productsContainer.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Filter products by search query, category, and active status
  const filteredProducts = productsArray.filter((product: any) => {
    const matchesSearch = searchQuery === "" ||
      product.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === null || product.categoryId === activeCategory;
    const isActive = product.active !== false; // Filter out products where active is false
    return matchesSearch && matchesCategory && isActive;
  });

  // Add item to order
  const addItemToOrder = async (product: any) => {
    try {
      const existingItem = orderItems.find(item => item.id === product.id);

      if (existingItem) {
        // Update existing item quantity
        const newQuantity = existingItem.quantity + 1;

        // Call the API to update cart item
        await cartOperations.updateCartItem(product.id, newQuantity);

        // Update local state
        setOrderItems(prev =>
          prev.map(item =>
            item.id === product.id
              ? { ...item, quantity: newQuantity }
              : item
          )
        );
      } else {
        // Call the API to add new item to cart
        await cartOperations.addToCart(product.id, 1);

        // Add new item to local state
        setOrderItems(prev => [...prev, {
          id: product.id,
          name: product.name,
          price: product.price,
          quantity: 1,
          notes: ""
        }]);
      }

      // Open cart when first item is added
      if (orderItems.length === 0) {
        setIsCartOpen(true);
      }
    } catch (error) {
      console.error("Error adding product to cart:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to add product to cart. Please try again.",
      });
    }
  };

  // Update item quantity
  const updateItemQuantity = async (id: number, newQuantity: number) => {
    try {
      if (newQuantity <= 0) {
        // Call the API to remove item from cart
        await cartOperations.removeFromCart(id);

        // Update local state
        setOrderItems(prev => prev.filter(item => item.id !== id));

        toast({
          title: "Item Removed",
          description: "Item removed from cart",
        });
      } else {
        // Call the API to update cart item
        await cartOperations.updateCartItem(id, newQuantity);

        // Update local state
        setOrderItems(prev =>
          prev.map(item =>
            item.id === id ? { ...item, quantity: newQuantity } : item
          )
        );
      }
    } catch (error) {
      console.error("Error updating cart item:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update cart item. Please try again.",
      });
    }
  };

  // Create order mutation using two-step process
  const createOrderMutation = useMutation({
    mutationFn: async (data: any) => {
      setIsProcessing(true);

      // Extract order and items data
      const { order, items, ...options } = data;

      // Verify shop ID is present
      if (!order.shopId) {
        throw new Error("Shop ID is required for creating an order");
      }

      // Ensure shop ID is a number
      order.shopId = Number(order.shopId);

      // Use the two-step order creation process
      return createOrderTwoStep(order, items);
    },
    onSuccess: async (data, variables) => {
      // Immediately invalidate and refetch orders data and all related queries
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/products',
        '/api/tables'
      ]);

      // Store the completed order for receipt with items included
      console.log('Mobile POS order creation success data:', data);

      // Map the items to include product names from the current order items
      const itemsWithProductNames = (data.items || []).map((item: any) => {
        const orderItem = orderItems.find(oi => oi.productId === item.productId);
        return {
          ...item,
          productName: orderItem?.productName || `Product ${item.productId}`,
          notes: orderItem?.notes || item.notes || ''
        };
      });

      // Combine order and items data for the receipt
      const orderWithItems = {
        ...(data.order || data),
        items: itemsWithProductNames
      };

      setCompletedOrder(orderWithItems);

      // Show receipt modal if receipt printing was requested
      if (variables.printReceipt) {
        setIsReceiptModalOpen(true);
      }

      const successMessage = variables.printReceipt ?
        "Order saved and receipt ready!" :
        variables.printKOT ?
          "Kitchen Order Ticket sent to printer!" :
          "Order saved successfully!";

      toast({
        title: "Success",
        description: successMessage,
      });

      // Reset order
      setOrderItems([]);
      setDiscountValue("0.00");
      setTableId(null);
      setSelectedTable(null);
      setCustomerId(null);
      setSelectedCustomer(null);
      // Reset to first available payment method
      if (paymentMethods && paymentMethods.length > 0) {
        const firstMethodCode = paymentMethods[0].name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
        setSelectedPaymentMethod(firstMethodCode);
      } else {
        setSelectedPaymentMethod("cash");
      }
      setIsCartOpen(false);
      setIsProcessing(false);
    },
    onError: (error: any) => {
      console.error("Order creation error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create order. Please try again.",
      });
      setIsProcessing(false);
    }
  });

  // Handle payment submission
  const handlePaymentSubmit = (paymentDetails: {
    method: string,
    amount: number,
    printReceipt: boolean
  }) => {
    setSelectedPaymentMethod(paymentDetails.method);
    setIsPaymentModalOpen(false);

    // Submit the order with the payment details
    handleSubmitOrder({
      saveOnly: true,
      printReceipt: paymentDetails.printReceipt
    });
  };

  // Submit order with options
  const handleSubmitOrder = (options: {
    saveOnly?: boolean,
    printReceipt?: boolean,
    kotOnly?: boolean,
    printKOT?: boolean,
    draft?: boolean,
    hold?: boolean,
    cancel?: boolean
  } = {}) => {
    if (!currentShop || !currentShop.id) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No shop selected. Please select a shop first.",
      });
      return;
    }

    // Ensure shop ID is available in localStorage
    if (!localStorage.getItem("current_shop")) {
      // If currentShop exists in state but not in localStorage, save it
      localStorage.setItem("current_shop", JSON.stringify(currentShop));
    }

    if (orderItems.length === 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please add at least one item to the order",
      });
      return;
    }

    // Validate table selection for dine-in orders
    if (orderType === "dine_in" && !tableId) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a table for dine-in order",
      });
      return;
    }

    // Order number will be generated by the server using the configured format
    const orderNumber = undefined; // Let server generate the order number

    // Calculate effective tax rate (for future use)
    const effectiveTaxRate = subtotal > 0 ? (taxAmount / subtotal) * 100 : 0;

    // Prepare order data with explicit type conversions
    const orderData = {
      order: {
        orderNumber, // Add the order number field
        shopId: Number(currentShop.id),
        orderType: String(orderType),
        tableId: orderType === "dine_in" ? Number(tableId) : null,
        customerId: customerId ? Number(customerId) : null,
        subtotal: Number(subtotal),
        taxAmount: Number(taxAmount),
        taxRate: Number(effectiveTaxRate), // Store the effective tax rate
        discountAmount: Number(parseFloat(discountValue) || 0),
        totalAmount: Number(total),
        paymentMethod: String(selectedPaymentMethod),
        paymentStatus: "paid",
        status: options.draft ? "draft" :
                options.hold ? "hold" :
                options.cancel ? "cancelled" :
                options.kotOnly ? "preparing" : "completed",
        userId: user?.id ? Number(user.id) : undefined, // Add user ID field
        notes: "",
      },
      items: orderItems.map(item => ({
        productId: Number(item.id),
        quantity: Number(item.quantity),
        unitPrice: Number(item.price),
        totalPrice: Number(item.price * item.quantity),
        notes: item.notes || ""
        // Note: orderId will be added by the createOrderTwoStep function
      })),
      // Pass options to the mutation for handling in onSuccess
      saveOnly: options.saveOnly,
      printReceipt: options.printReceipt,
      kotOnly: options.kotOnly,
      printKOT: options.printKOT
    };

    // Submit the order data

    createOrderMutation.mutate(orderData);
  };

  // Loading state
  const isLoading =
    isLoadingProducts ||
    isLoadingCategories ||
    isLoadingTables ||
    isLoadingTaxSettings ||
    isLoadingCustomers ||
    isLoadingPaymentMethods;

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading...</span>
      </div>
    );
  }

  return (
    <div className="mobile-pos-container">
      {/* Header */}
      <header className="mobile-pos-header">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="text-white mr-2"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-bold truncate">{currentShop?.name || "POS"}</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4" />
          <span className="text-sm">{new Date().toLocaleDateString()}</span>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={toggleFullscreen}
            title={isFullscreen ? "Exit Full Screen" : "Enter Full Screen"}
          >
            {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
          </Button>
          <Button variant="ghost" size="icon" className="rounded-full" onClick={() => setLocation("/profile")}>
            <User className="h-5 w-5" />
          </Button>
        </div>
      </header>

      {/* Search Bar */}
      <div className="mobile-pos-search">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search products..."
            className="pl-9 pr-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 rounded-full p-0"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <Button variant="outline" size="icon" className="ml-2 flex-shrink-0">
          <ScanLine className="h-4 w-4" />
        </Button>
      </div>

      {/* Categories */}
      <ScrollArea className="mobile-pos-categories">
        <div className="flex space-x-2 px-4 py-2">
          <Button
            variant={activeCategory === null ? "default" : "outline"}
            size="sm"
            className="rounded-full"
            onClick={() => setActiveCategory(null)}
          >
            All
          </Button>
          {categories?.map((category: any) => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? "default" : "outline"}
              size="sm"
              className="rounded-full whitespace-nowrap"
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </Button>
          ))}
        </div>
      </ScrollArea>

      {/* Products Grid */}
      <div className="mobile-pos-products">
        <div className="grid grid-cols-2 gap-3 p-3">
          {filteredProducts.map((product: any) => (
            <Card
              key={product.id}
              className="product-card"
              onClick={() => addItemToOrder(product)}
            >
              <div className="product-image">
                {product.imageUrl ? (
                  <img src={product.imageUrl} alt={product.name} />
                ) : (
                  <div className="product-image-placeholder">
                    <Package className="h-8 w-8 text-muted-foreground" />
                  </div>
                )}
              </div>
              <CardContent className="p-2">
                <h3 className="product-name">{product.name}</h3>
                <p className="product-price">₹{product.price.toFixed(2)}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Cart Button (Fixed at bottom) */}
      <div className={`mobile-pos-cart-button ${isCartButtonHidden ? 'hidden' : ''}`}>
        <Button
          className="cart-button"
          size="lg"
          onClick={() => setIsCartOpen(!isCartOpen)}
        >
          <ShoppingBag className="mr-2 h-5 w-5" />
          <span>{orderItems.length} items</span>
          <Badge className="ml-2">₹{total.toFixed(2)}</Badge>
        </Button>
      </div>

      {/* Cart Sheet (Fixed at bottom) */}
      {isCartOpen && (
        <div className="mobile-pos-cart-sheet">
          <div className="mobile-pos-cart-overlay" onClick={() => setIsCartOpen(false)}></div>
          <div className="mobile-pos-cart-content">
            {/* Cart Header - Fixed */}
            <div className="mobile-pos-cart-header p-4 border-b">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-bold">Cart</h2>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm" onClick={() => setOrderItems([])}>
                    <Trash2 className="h-4 w-4 mr-1" />
                    Clear
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => setIsCartOpen(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Cart Body - Scrollable */}
            <div className="mobile-pos-cart-body">
              <ScrollArea className="p-4 pb-0">
                {orderItems.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
                    <ShoppingBag className="h-8 w-8 mb-2" />
                    <p>Your cart is empty</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {orderItems.map((item) => (
                      <SwipeableCartItem
                        key={item.id}
                        item={item}
                        onUpdateQuantity={updateItemQuantity}
                        onRemove={(id) => updateItemQuantity(id, 0)}
                      />
                    ))}
                  </div>
                )}

                <div className="mt-6 space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>₹{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Discount</span>
                    <span>-₹{discount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax ({subtotal > 0 ? ((taxAmount / subtotal) * 100).toFixed(1) : '0.0'}%)</span>
                    <span>₹{taxAmount.toFixed(2)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>₹{total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="mt-4 space-y-3">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Order Type</p>
                    <OrderTypeSelector
                      value={orderType}
                      onChange={(value) => {
                        setOrderType(value);

                        // Reset appropriate state based on order type
                        if (value === "dine_in") {
                          // For dine-in, table is required
                          // Keep table selection if any
                        } else {
                          // For other order types, table is not needed
                          setTableId(null);
                          setSelectedTable(null);
                        }

                        toast({
                          title: "Order Type Changed",
                          description: `Changed to ${value.replace('_', ' ')} order type`,
                        });
                      }}
                      variant="icons"
                      className="flex justify-between"
                    />
                  </div>

                  {orderType === "dine_in" && (
                    <div>
                      <p className="text-sm font-medium mb-2">Select Table</p>
                      <div className="border rounded-md p-2 max-h-[300px] overflow-auto">
                        <TableGrid
                          tables={tables || []}
                          isLoading={isLoadingTables}
                          onTableSelect={async (table) => {
                            setTableId(table.id);
                            setSelectedTable(table);

                            // If table is occupied, try to load existing order items
                            if (table.status === "occupied") {
                              await loadExistingOrderItems(table.id);
                            } else {
                              // Clear any existing items for available tables
                              setOrderItems([]);
                            }

                            toast({
                              title: "Table Selected",
                              description: `Selected table: ${table.name}`,
                            });
                          }}
                          selectedTableId={tableId}
                        />
                      </div>
                      {selectedTable && (
                        <div className="mt-2 p-2 bg-muted rounded-md">
                          <p className="text-sm font-medium">Selected: {selectedTable.name}</p>
                          <div className="flex items-center mt-1">
                            <Users className="h-3 w-3 mr-1" />
                            <span className="text-xs">{selectedTable.capacity} seats</span>
                            <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-primary/10">
                              {selectedTable.status}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {orderType !== "dine_in" && (
                    <div className="space-y-2">
                      <p className="text-sm font-medium mb-2">Select Customer (Optional)</p>
                      <Select
                        value={customerId?.toString() || ""}
                        onValueChange={(value) => setCustomerId(value ? parseInt(value) : null)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select a customer" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers?.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id.toString()}>
                              <div className="flex flex-col">
                                <span className="font-medium">{customer.name}</span>
                                <span className="text-xs text-muted-foreground">{customer.phone}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full flex items-center justify-center gap-1"
                        onClick={() => {
                          // Navigate to customers page with a query parameter to open the add form
                          window.open('/masters/customers?action=add', '_blank');
                        }}
                      >
                        <Plus className="h-4 w-4" />
                        Add New Customer
                      </Button>


                      {selectedCustomer && (
                        <div className="mt-2 p-2 bg-muted rounded-md">
                          <p className="text-sm font-medium">Selected: {selectedCustomer.name}</p>
                          <div className="flex items-center mt-1">
                            <Phone className="h-3 w-3 mr-1" />
                            <span className="text-xs">{selectedCustomer.phone}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <Select
                    value={selectedPaymentMethod}
                    onValueChange={setSelectedPaymentMethod}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Payment Method" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods?.map((method: any) => {
                        const methodCode = method.name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
                        return (
                          <SelectItem key={method.id} value={methodCode}>
                            {method.name}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                {/* Add some bottom padding to ensure content doesn't get hidden behind the footer */}
                <div className="h-16"></div>
              </ScrollArea>
            </div>

            {/* Cart Footer - Fixed */}
            <div className="mobile-pos-cart-footer">
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => handleSubmitOrder({ kotOnly: true, printKOT: true })}
                  disabled={isProcessing || orderItems.length === 0}
                >
                  {isProcessing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Receipt className="mr-2 h-4 w-4" />}
                  KOT
                </Button>
                <Button
                  className="w-full"
                  onClick={() => setIsPaymentModalOpen(true)}
                  disabled={isProcessing || orderItems.length === 0}
                >
                  {isProcessing ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Check className="mr-2 h-4 w-4" />}
                  Checkout
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        onSubmit={handlePaymentSubmit}
        totalAmount={total}
        paymentMethods={paymentMethods || []}
        isProcessing={isProcessing}
      />

      {/* Receipt Modal */}
      <ReceiptModal
        isOpen={isReceiptModalOpen}
        onClose={() => setIsReceiptModalOpen(false)}
        order={completedOrder}
        customer={selectedCustomer}
        tableName={selectedTable?.name}
      />
    </div>
  );
}
