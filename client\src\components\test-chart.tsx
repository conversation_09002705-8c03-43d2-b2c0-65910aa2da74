import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const testData = [
  { name: 'Mon', revenue: 12000 },
  { name: '<PERSON><PERSON>', revenue: 18000 },
  { name: 'Wed', revenue: 15000 },
  { name: 'Thu', revenue: 22000 },
  { name: 'Fri', revenue: 24000 },
  { name: 'Sat', revenue: 30000 },
  { name: 'Sun', revenue: 28000 },
];

export function TestChart() {
  return (
    <div className="w-full h-64 p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Test Chart (Direct Recharts)</h3>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={testData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip formatter={(value) => [`₹${value.toLocaleString()}`, 'Revenue']} />
          <Line 
            type="monotone" 
            dataKey="revenue" 
            stroke="#8884d8" 
            strokeWidth={2}
            dot={{ fill: '#8884d8' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
