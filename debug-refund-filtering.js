import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function debugRefundFiltering() {
  const client = await pool.connect();
  try {
    console.log('🔍 Debugging refund filtering...\n');

    // Get the refund data with shop/branch info
    const refundQuery = `
      SELECT r.*, o.order_number, o.shop_id as order_shop_id, o.branch_id as order_branch_id
      FROM refunds r 
      LEFT JOIN orders o ON r.order_id = o.id 
      ORDER BY r.created_at DESC
    `;
    
    const refundsResult = await client.query(refundQuery);
    
    console.log('📊 Refunds with shop/branch info:');
    refundsResult.rows.forEach(refund => {
      console.log(`  Refund ID: ${refund.id}`);
      console.log(`  Order: ${refund.order_number} (ID: ${refund.order_id})`);
      console.log(`  Amount: ${refund.amount}`);
      console.log(`  Refund Shop ID: ${refund.shop_id}`);
      console.log(`  Refund Branch ID: ${refund.branch_id}`);
      console.log(`  Order Shop ID: ${refund.order_shop_id}`);
      console.log(`  Order Branch ID: ${refund.order_branch_id}`);
      console.log(`  Created: ${refund.created_at}`);
      console.log('  ---');
    });

    // Check what shop/branch IDs are typically used
    const shopBranchQuery = `
      SELECT DISTINCT shop_id, branch_id, COUNT(*) as order_count
      FROM orders 
      GROUP BY shop_id, branch_id
      ORDER BY order_count DESC
    `;
    
    const shopBranchResult = await client.query(shopBranchQuery);
    
    console.log('\n🏪 Shop/Branch distribution in orders:');
    shopBranchResult.rows.forEach(row => {
      console.log(`  Shop ID: ${row.shop_id}, Branch ID: ${row.branch_id}, Orders: ${row.order_count}`);
    });

    // Test the filtering logic with different parameters
    console.log('\n🧪 Testing filtering scenarios:');
    
    // Test 1: No filters
    const noFilterQuery = `SELECT COUNT(*) FROM refunds`;
    const noFilterResult = await client.query(noFilterQuery);
    console.log(`  No filters: ${noFilterResult.rows[0].count} refunds`);
    
    // Test 2: With shop ID 1
    const shopFilterQuery = `SELECT COUNT(*) FROM refunds WHERE shop_id = 1`;
    const shopFilterResult = await client.query(shopFilterQuery);
    console.log(`  Shop ID = 1: ${shopFilterResult.rows[0].count} refunds`);
    
    // Test 3: With shop ID 1 and branch ID 1
    const shopBranchFilterQuery = `SELECT COUNT(*) FROM refunds WHERE shop_id = 1 AND branch_id = 1`;
    const shopBranchFilterResult = await client.query(shopBranchFilterQuery);
    console.log(`  Shop ID = 1, Branch ID = 1: ${shopBranchFilterResult.rows[0].count} refunds`);
    
    // Test 4: With shop ID 1 and branch ID NULL
    const shopNullBranchQuery = `SELECT COUNT(*) FROM refunds WHERE shop_id = 1 AND branch_id IS NULL`;
    const shopNullBranchResult = await client.query(shopNullBranchQuery);
    console.log(`  Shop ID = 1, Branch ID = NULL: ${shopNullBranchResult.rows[0].count} refunds`);

    // Test date filtering
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    console.log('\n📅 Testing date filtering:');
    console.log(`  Looking for refunds between ${yesterday.toISOString()} and ${today.toISOString()}`);
    
    const dateFilterQuery = `
      SELECT COUNT(*) FROM refunds 
      WHERE created_at >= $1 AND created_at <= $2
    `;
    const dateFilterResult = await client.query(dateFilterQuery, [yesterday, today]);
    console.log(`  Date filtered: ${dateFilterResult.rows[0].count} refunds`);

  } catch (error) {
    console.error('❌ Error debugging refund filtering:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

debugRefundFiltering();
