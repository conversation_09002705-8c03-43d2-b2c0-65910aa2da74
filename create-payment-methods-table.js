import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function createPaymentMethodsTable() {
  const client = await pool.connect();
  try {
    console.log('🔧 Creating subscription_payment_methods table...\n');
    
    // First check if table exists
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'subscription_payment_methods'
      );
    `);
    
    if (tableExists.rows[0].exists) {
      console.log('✅ Table already exists');
      
      // Check table structure
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'subscription_payment_methods' 
        ORDER BY ordinal_position;
      `);
      
      console.log('📋 Current columns:');
      columns.rows.forEach(col => {
        console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
      
    } else {
      console.log('❌ Table does not exist, creating...');
      
      await client.query(`
        CREATE TABLE subscription_payment_methods (
          id SERIAL PRIMARY KEY,
          shop_id INTEGER NOT NULL REFERENCES shops(id),
          method_type TEXT NOT NULL DEFAULT 'card',
          card_number TEXT,
          card_holder_name TEXT,
          expiry_month INTEGER,
          expiry_year INTEGER,
          billing_address TEXT,
          is_default BOOLEAN DEFAULT false NOT NULL,
          active BOOLEAN DEFAULT true NOT NULL,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
          created_by INTEGER NOT NULL REFERENCES users(id)
        );
      `);
      
      console.log('✅ Table created successfully!');
    }
    
    // Test the table
    console.log('\n🧪 Testing table...');
    const testResult = await client.query('SELECT COUNT(*) FROM subscription_payment_methods');
    console.log(`✅ Table is working. Current records: ${testResult.rows[0].count}`);
    
    console.log('\n🎉 Payment methods table is ready!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createPaymentMethodsTable();
