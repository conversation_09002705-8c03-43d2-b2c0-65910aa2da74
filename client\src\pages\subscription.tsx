import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Crown, Users, Building, Package, Calendar, CreditCard, CheckCircle, AlertCircle, TrendingUp, BarChart3, Zap, ChevronLeft, ChevronRight, Settings, Shield, Clock, Bell, HelpCircle, Download, Pause, X, RefreshCw, Database, ShoppingCart, FileText, Phone, Plus } from "lucide-react";

interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  price: number;
  billingCycle: string;
  features: string[];
  maxBranches: number;
  maxUsers: number;
  maxProducts: number;
  maxOrders: number;
  storageLimit: number;
  supportLevel: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ShopSubscription {
  id: number;
  shopId: number;
  planId: number;
  status: string;
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  discountPercent: number;
  discountAmount: number;
  totalAmount: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  plan?: SubscriptionPlan;
  usage?: {
    branches: number;
    users: number;
    products: number;
    orders: number;
    storage: number;
  };
}

export default function SubscriptionManagement() {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const [isBillingDialogOpen, setIsBillingDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isPauseDialogOpen, setIsPauseDialogOpen] = useState(false);
  const [isUsageDialogOpen, setIsUsageDialogOpen] = useState(false);
  const [isPaymentMethodDialogOpen, setIsPaymentMethodDialogOpen] = useState(false);

  // Payment method form state
  const [paymentMethodForm, setPaymentMethodForm] = useState({
    cardNumber: '',
    cardholderName: '',
    expiryDate: '',
    cvv: ''
  });
  const [isAutoRenewalDialogOpen, setIsAutoRenewalDialogOpen] = useState(false);
  const [isBillingHistoryDialogOpen, setIsBillingHistoryDialogOpen] = useState(false);

  // Pagination state for billing history
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Debug effect to track selectedPlan changes
  useEffect(() => {
    console.log('selectedPlan state changed:', selectedPlan);
  }, [selectedPlan]);

  // No fallback data - only use real API data

  // Fetch subscription plans
  const { data: plansData, isLoading: isLoadingPlans, error: plansError } = useQuery({
    queryKey: ["/api/subscription-plans"],
    enabled: !!token,
    retry: 2,
    onError: (error) => {
      console.error('Error fetching subscription plans:', error);
    }
  });

  // Use only real API data
  const plans = plansData || [];

  // Fetch current subscription
  const { data: subscriptionsData, isLoading: isLoadingSubscriptions, error: subscriptionsError } = useQuery({
    queryKey: ["/api/subscriptions"],
    enabled: !!token && !!currentShop,
    retry: 2,
    onError: (error) => {
      console.error('Error fetching subscriptions:', error);
    }
  });

  // Use only real API data
  const subscriptions = subscriptionsData || [];

  // Fetch subscription stats
  const { data: statsData, error: statsError } = useQuery({
    queryKey: ["/api/subscriptions/stats"],
    enabled: !!token,
    retry: 2,
    onError: (error) => {
      console.error('Error fetching subscription stats:', error);
    }
  });

  // Use only real API data
  const stats = statsData;

  // Debug logging
  console.log('Subscription Debug Info:', {
    token: !!token,
    currentShop: currentShop,
    plans: plans,
    subscriptions: subscriptions,
    stats: stats,
    plansError: plansError?.message,
    subscriptionsError: subscriptionsError?.message,
    statsError: statsError?.message,
    isLoadingPlans,
    isLoadingSubscriptions
  });

  // Additional debugging for API calls
  if (plansError) {
    console.error('Plans Error Details:', plansError);
  }
  if (subscriptionsError) {
    console.error('Subscriptions Error Details:', subscriptionsError);
  }

  const currentSubscription = subscriptions.find((sub: ShopSubscription) => sub.status === 'active');

  // Create payment method mutation
  const createPaymentMethodMutation = useMutation({
    mutationFn: async (paymentData: typeof paymentMethodForm) => {
      const response = await apiRequest("POST", "/api/payment-methods/subscription", paymentData);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add payment method');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Payment Method Added",
        description: "Your payment method has been successfully added.",
      });
      setIsPaymentMethodDialogOpen(false);
      setPaymentMethodForm({
        cardNumber: '',
        cardholderName: '',
        expiryDate: '',
        cvv: ''
      });
      // Refetch payment methods with a small delay to ensure the server has processed the request
      console.log('Invalidating payment methods queries...');
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['/api/payment-methods/subscription'] });
        console.log('Payment methods queries invalidated');
      }, 100);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add payment method",
        variant: "destructive",
      });
    },
  });

  // Create subscription mutation
  const createSubscriptionMutation = useMutation({
    mutationFn: async (planId: number) => {
      // Validate plan exists
      const plan = plans.find((p: SubscriptionPlan) => p.id === planId);
      if (!plan) {
        throw new Error('Selected plan not found. Please refresh the page and try again.');
      }

      // Validate plan price
      if (!plan.price || plan.price <= 0) {
        throw new Error('Invalid plan pricing. Please contact support.');
      }

      const startDate = new Date();
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 1);

      const subscriptionData = {
        planId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        autoRenew: true,
        discountPercent: 0,
        discountAmount: 0,
        totalAmount: plan.price
      };

      console.log('Creating subscription with data:', subscriptionData);

      const response = await apiRequest("POST", "/api/subscriptions", subscriptionData);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || `Failed to create subscription. Status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: (data) => {
      console.log('Subscription created/upgraded successfully:', data);

      // Refresh all subscription-related data immediately
      queryClient.invalidateQueries({ queryKey: ["/api/subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["/api/subscriptions/stats"] });
      queryClient.invalidateQueries({ queryKey: ['billing-history'] });
      queryClient.invalidateQueries({ queryKey: ['billing-history-v2'] });
      queryClient.invalidateQueries({ queryKey: ['subscription-usage'] });

      const isUpgrade = currentSubscription ? true : false;
      const planName = selectedPlan?.name || 'the selected plan';

      toast({
        title: "Success",
        description: isUpgrade ? `Successfully upgraded to ${planName}!` : `Successfully subscribed to ${planName}!`,
      });

      // Close the dialog and clear state
      setIsUpgradeDialogOpen(false);
      setSelectedPlan(null);

      // Force a refetch to ensure UI updates immediately
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["/api/subscriptions"] });
      }, 100);
    },
    onError: (error: any) => {
      console.error('Subscription creation error:', error);

      let errorMessage = "Failed to create subscription";

      // Handle different types of errors
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.errors) {
        // Handle validation errors
        const validationErrors = error.response.data.errors;
        if (Array.isArray(validationErrors) && validationErrors.length > 0) {
          errorMessage = validationErrors.map((err: any) => err.message || err.path?.join('.') + ': ' + err.message).join(', ');
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Subscription Error",
        description: errorMessage,
        variant: "destructive",
      });


    },
  });

  const handleUpgrade = (plan: SubscriptionPlan) => {
    console.log('handleUpgrade called with plan:', plan);
    setSelectedPlan(plan);
    setIsUpgradeDialogOpen(true);
  };

  const handleConfirmUpgrade = (planToUse?: SubscriptionPlan) => {
    // Try multiple sources for the plan
    const planForSubscription = planToUse || selectedPlan || (plans && plans.length > 0 ? plans[0] : null);

    console.log('handleConfirmUpgrade called');
    console.log('planToUse:', planToUse);
    console.log('selectedPlan state:', selectedPlan);
    console.log('planForSubscription (final):', planForSubscription);
    console.log('available plans:', plans);

    if (!planForSubscription) {
      console.error('No plan available for subscription');
      toast({
        title: "Error",
        description: "No plan available. Please refresh the page and try again.",
        variant: "destructive",
      });
      return;
    }

    if (!planForSubscription.id || !planForSubscription.price) {
      console.error('Invalid plan data:', planForSubscription);
      toast({
        title: "Error",
        description: "Invalid plan data. Please refresh the page and try again.",
        variant: "destructive",
      });
      return;
    }

    console.log('Creating subscription for plan:', planForSubscription.name, 'ID:', planForSubscription.id);
    createSubscriptionMutation.mutate(planForSubscription.id);
  };

  const handleManageBilling = () => {
    setIsBillingDialogOpen(true);
  };

  // Handle payment method form changes
  const handlePaymentMethodChange = (field: string, value: string) => {
    setPaymentMethodForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle payment method form submission
  const handlePaymentMethodSubmit = () => {
    // Basic validation
    if (!paymentMethodForm.cardNumber || !paymentMethodForm.cardholderName ||
        !paymentMethodForm.expiryDate || !paymentMethodForm.cvv) {
      toast({
        title: "Error",
        description: "Please fill in all payment method fields",
        variant: "destructive",
      });
      return;
    }

    // Validate expiry date format (MM/YY)
    const expiryRegex = /^(0[1-9]|1[0-2])\/\d{2}$/;
    if (!expiryRegex.test(paymentMethodForm.expiryDate)) {
      toast({
        title: "Error",
        description: "Please enter expiry date in MM/YY format",
        variant: "destructive",
      });
      return;
    }

    // Validate card number (basic check for 13-19 digits)
    const cardNumberClean = paymentMethodForm.cardNumber.replace(/\s/g, '');
    if (cardNumberClean.length < 13 || cardNumberClean.length > 19 || !/^\d+$/.test(cardNumberClean)) {
      toast({
        title: "Error",
        description: "Please enter a valid card number",
        variant: "destructive",
      });
      return;
    }

    // Validate CVV (3-4 digits)
    if (paymentMethodForm.cvv.length < 3 || paymentMethodForm.cvv.length > 4 || !/^\d+$/.test(paymentMethodForm.cvv)) {
      toast({
        title: "Error",
        description: "Please enter a valid CVV",
        variant: "destructive",
      });
      return;
    }

    createPaymentMethodMutation.mutate(paymentMethodForm);
  };

  const handleDownloadInvoice = () => {
    if (!currentSubscription) {
      toast({
        title: "Error",
        description: "No active subscription found.",
        variant: "destructive",
      });
      return;
    }

    // Generate and download invoice
    const invoiceData = {
      subscriptionId: currentSubscription.id,
      planName: currentSubscription.plan?.name,
      amount: currentSubscription.totalAmount,
      startDate: currentSubscription.startDate,
      endDate: currentSubscription.endDate,
      shopName: currentShop?.name
    };

    // Create a simple invoice content
    const invoiceContent = `
SUBSCRIPTION INVOICE
====================

Shop: ${invoiceData.shopName}
Plan: ${invoiceData.planName}
Amount: ${formatPrice(invoiceData.amount)}
Period: ${formatDate(invoiceData.startDate)} - ${formatDate(invoiceData.endDate)}
Subscription ID: ${invoiceData.subscriptionId}

Generated on: ${new Date().toLocaleDateString()}
    `;

    // Create and download the file
    const blob = new Blob([invoiceContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `invoice-${invoiceData.subscriptionId}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast({
      title: "Success",
      description: "Invoice downloaded successfully!",
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'basic':
        return <Package className="h-6 w-6" />;
      case 'professional':
        return <Building className="h-6 w-6" />;
      case 'enterprise':
        return <Crown className="h-6 w-6" />;
      default:
        return <Package className="h-6 w-6" />;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'basic':
        return 'from-blue-500 to-blue-600';
      case 'professional':
        return 'from-purple-500 to-purple-600';
      case 'enterprise':
        return 'from-amber-500 to-amber-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getUsagePercentage = (current: number, max: number) => {
    if (max === -1) return 0; // Unlimited
    return Math.min((current / max) * 100, 100);
  };

  const getDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getUsageStatus = (current: number, max: number) => {
    if (max === -1) return 'Unlimited';
    const percentage = getUsagePercentage(current, max);
    if (percentage >= 90) return 'Critical';
    if (percentage >= 75) return 'Warning';
    return 'Good';
  };

  // Fetch usage data from API
  const { data: currentUsage, isLoading: isLoadingUsage, error: usageError } = useQuery({
    queryKey: ['subscription-usage'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/subscriptions/usage');
      if (!response.ok) throw new Error('Failed to fetch usage data');
      return response.json();
    },
    enabled: !!token && !!currentShop,
    retry: 2,
    onError: (error) => {
      console.error('Error fetching usage data:', error);
    }
  });

  // Fetch billing history
  const { data: billingHistory, isLoading: isLoadingBilling, error: billingError } = useQuery({
    queryKey: ['billing-history-v2'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/subscriptions/billing-history-v2');
      if (!response.ok) throw new Error('Failed to fetch billing history');
      const data = await response.json();

      return data;
    },
    enabled: !!token && !!currentShop,
    retry: 2,

    onError: (error) => {
      console.error('Error fetching billing history:', error);
    }
  });

  // Fetch payment methods
  const { data: paymentMethods, isLoading: isLoadingPaymentMethods, error: paymentMethodsError } = useQuery({
    queryKey: ['/api/payment-methods/subscription'],
    queryFn: async () => {
      console.log('Fetching payment methods...');
      const response = await apiRequest('GET', '/api/payment-methods/subscription');
      console.log('Payment methods response:', response.status, response.ok);
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Payment methods error:', errorText);
        throw new Error('Failed to fetch payment methods');
      }
      const data = await response.json();
      console.log('Payment methods data received:', data);
      return data;
    },
    enabled: !!token && !!currentShop,
    retry: 2
  });

  // Debug payment methods query conditions
  console.log('Payment methods query conditions:', {
    token: !!token,
    currentShop: !!currentShop,
    enabled: !!token && !!currentShop,
    isLoadingPaymentMethods,
    paymentMethodsError: paymentMethodsError?.message
  });

  // No fallback data - only use real API data

  // Use only real API data
  const finalUsage = currentUsage;
  const finalBillingHistory = billingHistory || []; // Only show real billing history from API
  const finalPaymentMethods = paymentMethods || [];



  // Debug payment methods
  console.log('Payment Methods Debug:', {
    paymentMethods,
    finalPaymentMethods,
    isLoadingPaymentMethods,
    paymentMethodsError
  });

  // Pagination logic for subscriptions
  const totalSubscriptions = subscriptions.length;
  const totalPages = Math.ceil(totalSubscriptions / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedSubscriptions = subscriptions.slice(startIndex, endIndex);

  // Pagination logic for billing history
  const totalBillingRecords = finalBillingHistory.length;
  const totalBillingPages = Math.ceil(totalBillingRecords / pageSize);
  const billingStartIndex = (currentPage - 1) * pageSize;
  const billingEndIndex = billingStartIndex + pageSize;
  const paginatedBillingHistory = finalBillingHistory.slice(billingStartIndex, billingEndIndex);

  // Calculate billing summary statistics from real data
  const billingStats = {
    totalPaid: finalBillingHistory
      .filter(invoice => invoice.status === 'Paid')
      .reduce((sum, invoice) => sum + invoice.amount, 0),
    totalInvoices: finalBillingHistory.length,
    monthsActive: finalBillingHistory.length > 0 ?
      Math.ceil((new Date().getTime() - new Date(finalBillingHistory[finalBillingHistory.length - 1]?.date).getTime()) / (1000 * 60 * 60 * 24 * 30)) : 0
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (isLoadingPlans || isLoadingSubscriptions) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Manual refresh function
  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ["/api/subscription-plans"] });
    queryClient.invalidateQueries({ queryKey: ["/api/subscriptions"] });
    queryClient.invalidateQueries({ queryKey: ["/api/subscriptions/stats"] });
    toast({
      title: "Refreshing",
      description: "Reloading subscription data...",
    });
  };

  // Show error if no shop is selected
  if (!currentShop) {
    return (
      <div className="space-y-6 max-w-7xl mx-auto">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
          <p className="text-muted-foreground">
            Manage your subscription plans and billing
          </p>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">No Shop Selected</h3>
                <p className="text-muted-foreground">
                  Please select a shop to view subscription information.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
          <p className="text-muted-foreground">
            Manage your subscription plans and billing
          </p>
        </div>
        <Button variant="outline" onClick={handleRefresh} disabled={isLoadingPlans || isLoadingSubscriptions}>
          <TrendingUp className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>

      <Tabs defaultValue="current" className="space-y-6">
        <TabsList>
          <TabsTrigger value="current">Current Subscription</TabsTrigger>
          <TabsTrigger value="plans">Available Plans</TabsTrigger>
          <TabsTrigger value="billing">Billing History</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-6">
          {isLoadingSubscriptions ? (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                    <div>
                      <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-2"></div>
                      <div className="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : subscriptionsError ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                  <div>
                    <h3 className="text-lg font-semibold">Failed to Load Subscription Data</h3>
                    <p className="text-muted-foreground">
                      There was an error loading your subscription information. Please try refreshing the page.
                    </p>
                  </div>
                  <Button onClick={() => window.location.reload()}>
                    Refresh Page
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : currentSubscription ? (
            <div className="space-y-6">
              {/* Subscription Overview Card */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getPlanIcon(currentSubscription.plan?.name || '')}
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {currentSubscription.plan?.name} Plan
                          <Badge className={getStatusColor(currentSubscription.status)}>
                            {currentSubscription.status.charAt(0).toUpperCase() + currentSubscription.status.slice(1)}
                          </Badge>
                        </CardTitle>
                        <CardDescription>{currentSubscription.plan?.description}</CardDescription>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">Subscription ID</p>
                      <p className="font-mono text-sm">#{currentSubscription.id}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Subscription Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Start Date</p>
                        <p className="text-sm text-muted-foreground">{formatDate(currentSubscription.startDate)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">End Date</p>
                        <p className="text-sm text-muted-foreground">{formatDate(currentSubscription.endDate)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Amount</p>
                        <p className="text-sm text-muted-foreground">{formatPrice(currentSubscription.totalAmount)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Days Remaining</p>
                        <p className={`text-sm font-medium ${getDaysRemaining(currentSubscription.endDate) <= 7 ? 'text-red-600' : getDaysRemaining(currentSubscription.endDate) <= 30 ? 'text-yellow-600' : 'text-green-600'}`}>
                          {getDaysRemaining(currentSubscription.endDate)} days
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Auto-renewal and billing info */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <RefreshCw className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Auto-renewal</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={currentSubscription.autoRenew ? "default" : "secondary"}>
                        {currentSubscription.autoRenew ? "Enabled" : "Disabled"}
                      </Badge>
                      <Button variant="ghost" size="sm" onClick={() => setIsAutoRenewalDialogOpen(true)}>
                        <Settings className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Usage Tracking */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Usage Overview</h4>
                      <Button variant="ghost" size="sm" onClick={() => setIsUsageDialogOpen(true)}>
                        <BarChart3 className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                    {isLoadingUsage ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {Array(4).fill(0).map((_, index) => (
                          <Card key={index} className="p-4">
                            <div className="animate-pulse">
                              <div className="h-4 w-20 bg-gray-200 rounded mb-2"></div>
                              <div className="h-6 w-16 bg-gray-200 rounded mb-2"></div>
                              <div className="h-2 w-full bg-gray-200 rounded"></div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <Card className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Building className="h-4 w-4 text-blue-500" />
                            <span className="text-sm font-medium">Branches</span>
                          </div>
                          <Badge variant={getUsageStatus(finalUsage?.branches || 0, currentSubscription.plan?.maxBranches || 1) === 'Critical' ? 'destructive' : getUsageStatus(finalUsage?.branches || 0, currentSubscription.plan?.maxBranches || 1) === 'Warning' ? 'secondary' : 'default'}>
                            {getUsageStatus(finalUsage?.branches || 0, currentSubscription.plan?.maxBranches || 1)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{finalUsage?.branches || 0}</span>
                            <span>/ {currentSubscription.plan?.maxBranches === -1 ? '∞' : currentSubscription.plan?.maxBranches}</span>
                          </div>
                          <Progress
                            value={getUsagePercentage(finalUsage?.branches || 0, currentSubscription.plan?.maxBranches || 1)}
                            className="h-2"
                          />
                        </div>
                      </Card>

                      <Card className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-green-500" />
                            <span className="text-sm font-medium">Users</span>
                          </div>
                          <Badge variant={getUsageStatus(finalUsage?.users || 0, currentSubscription.plan?.maxUsers || 5) === 'Critical' ? 'destructive' : getUsageStatus(finalUsage?.users || 0, currentSubscription.plan?.maxUsers || 5) === 'Warning' ? 'secondary' : 'default'}>
                            {getUsageStatus(finalUsage?.users || 0, currentSubscription.plan?.maxUsers || 5)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{finalUsage?.users || 0}</span>
                            <span>/ {currentSubscription.plan?.maxUsers === -1 ? '∞' : currentSubscription.plan?.maxUsers}</span>
                          </div>
                          <Progress
                            value={getUsagePercentage(finalUsage?.users || 0, currentSubscription.plan?.maxUsers || 5)}
                            className="h-2"
                          />
                        </div>
                      </Card>

                      <Card className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Package className="h-4 w-4 text-purple-500" />
                            <span className="text-sm font-medium">Products</span>
                          </div>
                          <Badge variant={getUsageStatus(finalUsage?.products || 0, currentSubscription.plan?.maxProducts || 100) === 'Critical' ? 'destructive' : getUsageStatus(finalUsage?.products || 0, currentSubscription.plan?.maxProducts || 100) === 'Warning' ? 'secondary' : 'default'}>
                            {getUsageStatus(finalUsage?.products || 0, currentSubscription.plan?.maxProducts || 100)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{finalUsage?.products || 0}</span>
                            <span>/ {currentSubscription.plan?.maxProducts === -1 ? '∞' : currentSubscription.plan?.maxProducts}</span>
                          </div>
                          <Progress
                            value={getUsagePercentage(finalUsage?.products || 0, currentSubscription.plan?.maxProducts || 100)}
                            className="h-2"
                          />
                        </div>
                      </Card>

                      <Card className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Database className="h-4 w-4 text-orange-500" />
                            <span className="text-sm font-medium">Storage</span>
                          </div>
                          <Badge variant={getUsageStatus(finalUsage?.storage || 0, currentSubscription.plan?.storageLimit || 1024) === 'Critical' ? 'destructive' : getUsageStatus(finalUsage?.storage || 0, currentSubscription.plan?.storageLimit || 1024) === 'Warning' ? 'secondary' : 'default'}>
                            {getUsageStatus(finalUsage?.storage || 0, currentSubscription.plan?.storageLimit || 1024)}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{finalUsage?.storage || 0} MB</span>
                            <span>/ {currentSubscription.plan?.storageLimit === -1 ? '∞' : `${currentSubscription.plan?.storageLimit} MB`}</span>
                          </div>
                          <Progress
                            value={getUsagePercentage(finalUsage?.storage || 0, currentSubscription.plan?.storageLimit || 1024)}
                            className="h-2"
                          />
                        </div>
                      </Card>
                    </div>
                    )}
                  </div>
                  
                  {/* Plan Features */}
                  <div>
                    <h4 className="font-medium mb-3">Plan Features</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {currentSubscription.plan?.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2 p-2 bg-green-50 rounded-lg">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Payment Methods Section */}
                  <div className="p-4 border rounded-lg bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Payment Methods</h4>
                      <Button variant="ghost" size="sm" onClick={() => setIsPaymentMethodDialogOpen(true)}>
                        <Plus className="h-4 w-4 mr-1" />
                        Add New
                      </Button>
                    </div>
                    {isLoadingPaymentMethods ? (
                      <div className="animate-pulse">
                        <div className="h-4 w-32 bg-gray-200 rounded mb-2"></div>
                        <div className="h-3 w-24 bg-gray-200 rounded"></div>
                      </div>
                    ) : finalPaymentMethods && finalPaymentMethods.length > 0 ? (
                      <div className="space-y-2">
                        {finalPaymentMethods.map((method, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-6 bg-blue-600 rounded flex items-center justify-center">
                                <CreditCard className="h-3 w-3 text-white" />
                              </div>
                              <div>
                                <p className="font-medium text-sm">•••• •••• •••• {method.last4}</p>
                                <p className="text-xs text-muted-foreground">
                                  {method.cardType} • Expires {method.expiryMonth}/{method.expiryYear}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {method.isDefault && <Badge variant="default" className="text-xs">Primary</Badge>}
                              <Badge variant={method.active ? "default" : "secondary"} className="text-xs">
                                {method.active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <CreditCard className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground mb-2">No payment methods added</p>
                        <Button variant="outline" size="sm" onClick={() => setIsPaymentMethodDialogOpen(true)}>
                          Add Payment Method
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-wrap gap-2">
                    <Button variant="outline" onClick={handleManageBilling}>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Manage Billing
                    </Button>
                    <Button variant="outline" onClick={handleDownloadInvoice}>
                      <Download className="h-4 w-4 mr-2" />
                      Download Invoice
                    </Button>
                    <Button onClick={() => setIsUpgradeDialogOpen(true)}>
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Upgrade Plan
                    </Button>
                    <Button variant="outline" onClick={() => setIsPauseDialogOpen(true)}>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause Subscription
                    </Button>
                    <Button variant="outline" onClick={() => setIsCancelDialogOpen(true)} className="text-red-600 hover:text-red-700">
                      <X className="h-4 w-4 mr-2" />
                      Cancel Subscription
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Notifications & Alerts */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Notifications & Alerts
                  </CardTitle>
                  <CardDescription>Important updates about your subscription</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {getDaysRemaining(currentSubscription.endDate) <= 7 && (
                    <div className="flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-red-800">Subscription Expiring Soon</p>
                        <p className="text-sm text-red-600">Your subscription expires in {getDaysRemaining(currentSubscription.endDate)} days. Renew now to avoid service interruption.</p>
                      </div>
                    </div>
                  )}

                  {finalUsage && getUsagePercentage(finalUsage.users, currentSubscription.plan?.maxUsers || 5) >= 90 && (
                    <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-yellow-800">User Limit Almost Reached</p>
                        <p className="text-sm text-yellow-600">You're using {finalUsage.users} of {currentSubscription.plan?.maxUsers} users. Consider upgrading your plan.</p>
                      </div>
                    </div>
                  )}

                  {finalUsage && getUsagePercentage(finalUsage.storage, currentSubscription.plan?.storageLimit || 1024) >= 90 && (
                    <div className="flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-yellow-800">Storage Almost Full</p>
                        <p className="text-sm text-yellow-600">You're using {finalUsage.storage} MB of {currentSubscription.plan?.storageLimit} MB storage. Consider upgrading for more space.</p>
                      </div>
                    </div>
                  )}

                  {currentSubscription.autoRenew && (
                    <div className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Auto-renewal Enabled</p>
                        <p className="text-sm text-green-600">Your subscription will automatically renew on {formatDate(currentSubscription.endDate)}.</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quick Support */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5" />
                    Need Help?
                  </CardTitle>
                  <CardDescription>Get support and access resources</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center space-y-2"
                      onClick={() => setLocation('/support')}
                    >
                      <Phone className="h-6 w-6" />
                      <div className="text-center">
                        <p className="font-medium">Contact Support</p>
                        <p className="text-xs text-muted-foreground">Get help from our team</p>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center space-y-2"
                      onClick={() => setLocation('/subscription/docs')}
                    >
                      <FileText className="h-6 w-6" />
                      <div className="text-center">
                        <p className="font-medium">Documentation</p>
                        <p className="text-xs text-muted-foreground">Learn how to use features</p>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center space-y-2"
                      onClick={() => setLocation('/support')}
                    >
                      <HelpCircle className="h-6 w-6" />
                      <div className="text-center">
                        <p className="font-medium">FAQ</p>
                        <p className="text-xs text-muted-foreground">Find quick answers</p>
                      </div>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Statistics Cards */}
              {stats && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-5">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Monthly Growth</p>
                          <p className="text-2xl font-bold text-green-600">+{stats.monthlyGrowth}%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-5">
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Active Subscriptions</p>
                          <p className="text-2xl font-bold">{stats.activeSubscriptions}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-5">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="h-5 w-5 text-yellow-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Expiring Soon</p>
                          <p className="text-2xl font-bold text-yellow-600">{stats.expiringSoon}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Active Subscription</h3>
                <p className="text-muted-foreground text-center mb-4">
                  {subscriptions.length === 0
                    ? "You don't have any subscriptions yet. Choose a plan to get started."
                    : "You don't have an active subscription. Choose a plan to get started."
                  }
                </p>
                {subscriptions.length > 0 && (
                  <p className="text-sm text-muted-foreground mb-4">
                    You have {subscriptions.length} subscription(s) in your history.
                  </p>
                )}
                <Button
                  onClick={() => {
                    if (plans.length > 0) {
                      setSelectedPlan(plans[0]);
                      setIsUpgradeDialogOpen(true);
                    }
                  }}
                  disabled={plans.length === 0}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  {plans.length === 0 ? 'Loading Plans...' : 'Choose a Plan'}
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="plans" className="space-y-6">
          {isLoadingPlans ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array(3).fill(0).map((_, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <div className="h-6 w-6 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-6 w-24 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                    <div className="h-4 w-full bg-gray-200 rounded animate-pulse mt-2"></div>
                    <div className="h-8 w-20 bg-gray-200 rounded animate-pulse mt-2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Array(4).fill(0).map((_, i) => (
                        <div key={i} className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                      ))}
                    </div>
                    <div className="h-10 w-full bg-gray-200 rounded animate-pulse mt-4"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : plansError ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
                  <div>
                    <h3 className="text-lg font-semibold">Failed to Load Plans</h3>
                    <p className="text-muted-foreground">
                      There was an error loading subscription plans. Please try refreshing the page.
                    </p>
                  </div>
                  <Button onClick={() => window.location.reload()}>
                    Refresh Page
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : plans.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto" />
                  <div>
                    <h3 className="text-lg font-semibold">No Plans Available</h3>
                    <p className="text-muted-foreground">
                      No subscription plans are currently available. Please contact support.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {plans.map((plan: SubscriptionPlan) => (
              <Card key={plan.id} className={`relative transition-all duration-300 hover:scale-105 hover:shadow-lg ${currentSubscription?.planId === plan.id ? 'ring-2 ring-blue-500' : ''}`}>
                <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${getPlanColor(plan.name)}`}></div>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getPlanIcon(plan.name)}
                      <CardTitle>{plan.name}</CardTitle>
                    </div>
                    {currentSubscription?.planId === plan.id && (
                      <Badge variant="secondary">Current</Badge>
                    )}
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="text-3xl font-bold">
                    {formatPrice(plan.price)}
                    <span className="text-sm font-normal text-muted-foreground">/{plan.billingCycle}</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Button 
                    className="w-full" 
                    onClick={() => handleUpgrade(plan)}
                    disabled={currentSubscription?.planId === plan.id}
                  >
                    {currentSubscription?.planId === plan.id ? 'Current Plan' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>View your past invoices and payment history</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingBilling ? (
                <div className="space-y-4">
                  {Array(3).fill(0).map((_, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  ))}
                </div>
              ) : finalBillingHistory && finalBillingHistory.length > 0 ? (
                <div className="rounded-md border">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            Subscription ID
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            Plan
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            Amount
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            Status
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            Start Date
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            End Date
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                            Auto Renew
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {paginatedBillingHistory.map((subscription) => (
                          <tr key={subscription.id} className="border-b transition-colors hover:bg-muted/50">
                            <td className="p-4 align-middle">
                              <span className="font-medium">#{subscription.id}</span>
                            </td>
                            <td className="p-4 align-middle">
                              <div>
                                <div className="font-medium">{subscription.plan}</div>
                                <div className="text-sm text-muted-foreground">{subscription.planDescription}</div>
                              </div>
                            </td>
                            <td className="p-4 align-middle">
                              <span className="font-medium">{formatPrice(subscription.amount)}</span>
                            </td>
                            <td className="p-4 align-middle">
                              <Badge className={getStatusColor(subscription.status)}>
                                {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                              </Badge>
                            </td>
                            <td className="p-4 align-middle">
                              <span className="text-sm">
                                {new Date(subscription.startDate).toLocaleDateString()}
                              </span>
                            </td>
                            <td className="p-4 align-middle">
                              <span className="text-sm">
                                {new Date(subscription.endDate).toLocaleDateString()}
                              </span>
                            </td>
                            <td className="p-4 align-middle">
                              <Badge variant={subscription.autoRenew ? "default" : "secondary"}>
                                {subscription.autoRenew ? "Yes" : "No"}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination Controls */}
                  {totalBillingPages > 1 && (
                    <div className="flex items-center justify-between px-4 py-3 border-t">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-muted-foreground">
                          Showing {billingStartIndex + 1} to {Math.min(billingEndIndex, totalBillingHistory)} of {totalBillingHistory} records
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleBillingPageChange(currentBillingPage - 1)}
                          disabled={currentBillingPage === 1}
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>

                        <div className="flex items-center space-x-1">
                          {Array.from({ length: totalBillingPages }, (_, i) => i + 1).map((page) => (
                            <Button
                              key={page}
                              variant={currentBillingPage === page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handleBillingPageChange(page)}
                              className="w-8 h-8 p-0"
                            >
                              {page}
                            </Button>
                          ))}
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleBillingPageChange(currentBillingPage + 1)}
                          disabled={currentBillingPage === totalBillingPages}
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No billing history available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={isUpgradeDialogOpen} onOpenChange={(open) => {
        setIsUpgradeDialogOpen(open);
        if (!open) {
          // Clear selected plan when dialog closes
          console.log('Dialog closing, clearing selectedPlan');
          setSelectedPlan(null);
        }
      }}>
        <DialogContent className="max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Confirm Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to subscribe to the {selectedPlan?.name || (plans && plans.length > 0 ? plans[0]?.name : 'selected')} plan?
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-4 pr-2">
            {(() => {
              const displayPlan = selectedPlan || (plans && plans.length > 0 ? plans[0] : null);

              if (displayPlan) {
                return (
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{displayPlan.name} Plan</h4>
                      <span className="text-lg font-bold">{formatPrice(displayPlan.price)}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">{displayPlan.description}</p>
                    <div className="space-y-1">
                      {displayPlan.features.slice(0, 4).map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          <span className="text-xs">{feature}</span>
                        </div>
                      ))}
                    </div>
                    {!selectedPlan && (
                      <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                        <p className="text-xs text-blue-700">
                          Using default plan since no specific plan was selected.
                        </p>
                      </div>
                    )}
                  </div>
                );
              } else {
                return (
                  <div className="p-4 border rounded-lg bg-red-50 border-red-200">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="h-5 w-5 text-red-600" />
                      <div>
                        <h4 className="font-medium text-red-800">No Plans Available</h4>
                        <p className="text-sm text-red-700">
                          No subscription plans are available. Please contact support.
                        </p>
                        <p className="text-xs text-red-600 mt-1">
                          Debug: selectedPlan is {selectedPlan ? 'defined' : 'null/undefined'},
                          plans available: {plans?.length || 0}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              }
            })()}
          </div>
          <div className="flex-shrink-0 flex space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsUpgradeDialogOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button
              onClick={() => {
                const planToUse = selectedPlan || (plans && plans.length > 0 ? plans[0] : null);
                handleConfirmUpgrade(planToUse);
              }}
              className="flex-1"
              disabled={createSubscriptionMutation.isPending || (!selectedPlan && (!plans || plans.length === 0))}
            >
              {createSubscriptionMutation.isPending ? 'Processing...' : 'Confirm Subscription'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Billing Management Dialog */}
      <Dialog open={isBillingDialogOpen} onOpenChange={setIsBillingDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Billing Management</DialogTitle>
            <DialogDescription>
              Manage your subscription billing and payment methods
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-6 pr-2">
            {currentSubscription ? (
              <div className="space-y-4">
                <div className="p-4 border rounded-lg bg-gray-50">
                  <h4 className="font-medium mb-2">Current Subscription</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Plan:</span>
                      <p className="font-medium">{currentSubscription.plan?.name}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Amount:</span>
                      <p className="font-medium">{formatPrice(currentSubscription.totalAmount)}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Next Billing:</span>
                      <p className="font-medium">{formatDate(currentSubscription.endDate)}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Auto Renew:</span>
                      <p className="font-medium">{currentSubscription.autoRenew ? 'Enabled' : 'Disabled'}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Billing Actions</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Button variant="outline" onClick={handleDownloadInvoice} className="justify-start">
                      <Download className="h-4 w-4 mr-2" />
                      Download Current Invoice
                    </Button>
                    <Button variant="outline" className="justify-start" onClick={() => setIsPaymentMethodDialogOpen(true)}>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Add Payment Method
                    </Button>
                    <Button variant="outline" className="justify-start" onClick={() => setIsBillingHistoryDialogOpen(true)}>
                      <Calendar className="h-4 w-4 mr-2" />
                      View Billing History
                    </Button>
                    <Button variant="outline" className="justify-start" onClick={() => setIsAutoRenewalDialogOpen(true)}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Auto-Renewal Settings
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h5 className="font-medium text-yellow-800">Need Help?</h5>
                      <p className="text-sm text-yellow-700">
                        For billing inquiries or payment issues, please contact our support team.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-muted-foreground mb-2">No Active Subscription</h3>
                  <p className="text-sm text-muted-foreground mb-6">
                    You don't have an active subscription yet. Choose a plan to get started.
                  </p>
                  <Button onClick={() => setIsBillingDialogOpen(false)} className="mb-4">
                    Choose a Plan
                  </Button>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Available Actions</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Button variant="outline" className="justify-start" onClick={() => setIsPaymentMethodDialogOpen(true)}>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Add Payment Method
                    </Button>
                    <Button variant="outline" className="justify-start" onClick={() => setIsBillingHistoryDialogOpen(true)}>
                      <Calendar className="h-4 w-4 mr-2" />
                      View Billing History
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h5 className="font-medium text-blue-800">Get Started</h5>
                      <p className="text-sm text-blue-700">
                        Add a payment method and choose a subscription plan to unlock all features.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

          </div>
          <div className="flex-shrink-0 flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={() => setIsBillingDialogOpen(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Cancel Subscription Dialog */}
      <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <DialogContent className="max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <X className="h-5 w-5" />
              Cancel Subscription
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your subscription? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-4 pr-2">
            <div className="p-4 border border-red-200 rounded-lg bg-red-50">
              <h4 className="font-medium text-red-800 mb-2">What happens when you cancel:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• Your subscription will remain active until {currentSubscription ? formatDate(currentSubscription.endDate) : 'the end date'}</li>
                <li>• You'll lose access to premium features after the current period</li>
                <li>• Your data will be preserved for 30 days after cancellation</li>
                <li>• You can reactivate anytime before the end date</li>
              </ul>
            </div>
          </div>
          <div className="flex-shrink-0 flex space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsCancelDialogOpen(false)} className="flex-1">
              Keep Subscription
            </Button>
            <Button variant="destructive" onClick={() => {
              toast({
                title: "Subscription Cancelled",
                description: "Your subscription has been cancelled. You'll retain access until the end of your billing period.",
              });
              setIsCancelDialogOpen(false);
            }} className="flex-1">
              Cancel Subscription
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Pause Subscription Dialog */}
      <Dialog open={isPauseDialogOpen} onOpenChange={setIsPauseDialogOpen}>
        <DialogContent className="max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Pause className="h-5 w-5" />
              Pause Subscription
            </DialogTitle>
            <DialogDescription>
              Temporarily pause your subscription for up to 3 months.
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-4 pr-2">
            <div className="p-4 border rounded-lg bg-blue-50">
              <h4 className="font-medium text-blue-800 mb-2">Pause Benefits:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Billing is suspended during the pause period</li>
                <li>• Your data and settings are preserved</li>
                <li>• You can resume anytime within 3 months</li>
                <li>• Limited access to basic features during pause</li>
              </ul>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Pause Duration</label>
              <select className="w-full p-2 border rounded-md">
                <option value="1">1 Month</option>
                <option value="2">2 Months</option>
                <option value="3">3 Months</option>
              </select>
            </div>
          </div>
          <div className="flex-shrink-0 flex space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsPauseDialogOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button onClick={() => {
              toast({
                title: "Subscription Paused",
                description: "Your subscription has been paused. You can resume it anytime from your account.",
              });
              setIsPauseDialogOpen(false);
            }} className="flex-1">
              Pause Subscription
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Usage Details Dialog */}
      <Dialog open={isUsageDialogOpen} onOpenChange={setIsUsageDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Detailed Usage Analytics
            </DialogTitle>
            <DialogDescription>
              Comprehensive view of your subscription usage and limits
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-6 pr-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Resource Usage</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Branches</span>
                        <span>{finalUsage?.branches || 0} / {currentSubscription?.plan?.maxBranches === -1 ? '∞' : currentSubscription?.plan?.maxBranches}</span>
                      </div>
                      <Progress value={getUsagePercentage(finalUsage?.branches || 0, currentSubscription?.plan?.maxBranches || 1)} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Users</span>
                        <span>{finalUsage?.users || 0} / {currentSubscription?.plan?.maxUsers === -1 ? '∞' : currentSubscription?.plan?.maxUsers}</span>
                      </div>
                      <Progress value={getUsagePercentage(finalUsage?.users || 0, currentSubscription?.plan?.maxUsers || 5)} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Products</span>
                        <span>{finalUsage?.products || 0} / {currentSubscription?.plan?.maxProducts === -1 ? '∞' : currentSubscription?.plan?.maxProducts}</span>
                      </div>
                      <Progress value={getUsagePercentage(finalUsage?.products || 0, currentSubscription?.plan?.maxProducts || 100)} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Storage</span>
                        <span>{finalUsage?.storage || 0} MB / {currentSubscription?.plan?.storageLimit === -1 ? '∞' : `${currentSubscription?.plan?.storageLimit} MB`}</span>
                      </div>
                      <Progress value={getUsagePercentage(finalUsage?.storage || 0, currentSubscription?.plan?.storageLimit || 1024)} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Monthly Activity</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <ShoppingCart className="h-6 w-6 text-blue-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-blue-600">{finalUsage?.orders || 0}</p>
                      <p className="text-xs text-blue-600">Orders This Month</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <Users className="h-6 w-6 text-green-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-green-600">{finalUsage?.users || 0}</p>
                      <p className="text-xs text-green-600">Active Users</p>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <Package className="h-6 w-6 text-purple-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-purple-600">{finalUsage?.products || 0}</p>
                      <p className="text-xs text-purple-600">Total Products</p>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <Database className="h-6 w-6 text-orange-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-orange-600">{finalUsage?.storage || 0}</p>
                      <p className="text-xs text-orange-600">MB Storage Used</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

          </div>
          <div className="flex-shrink-0 flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={() => setIsUsageDialogOpen(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Payment Method Dialog */}
      <Dialog open={isPaymentMethodDialogOpen} onOpenChange={setIsPaymentMethodDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Add Payment Method
            </DialogTitle>
            <DialogDescription>
              Add a new payment method for your subscription billing
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-6 pr-2">
            {/* Current Payment Method */}
            <div className="p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium mb-3">Current Payment Method</h4>
              {isLoadingPaymentMethods ? (
                <div className="animate-pulse">
                  <div className="h-4 w-32 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 w-24 bg-gray-200 rounded"></div>
                </div>
              ) : finalPaymentMethods && finalPaymentMethods.length > 0 ? (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium">•••• •••• •••• {finalPaymentMethods[0].last4}</p>
                      <p className="text-sm text-muted-foreground">Expires {finalPaymentMethods[0].expiryMonth}/{finalPaymentMethods[0].expiryYear}</p>
                    </div>
                  </div>
                  <Badge>Primary</Badge>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No payment method on file</p>
              )}
            </div>

            {/* Add New Payment Method */}
            <div className="space-y-4">
              <h4 className="font-medium">Add New Payment Method</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Card Number</label>
                  <input
                    type="text"
                    placeholder="1234 5678 9012 3456"
                    value={paymentMethodForm.cardNumber}
                    onChange={(e) => handlePaymentMethodChange('cardNumber', e.target.value)}
                    className="w-full p-3 border rounded-md"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Cardholder Name</label>
                  <input
                    type="text"
                    placeholder="John Doe"
                    value={paymentMethodForm.cardholderName}
                    onChange={(e) => handlePaymentMethodChange('cardholderName', e.target.value)}
                    className="w-full p-3 border rounded-md"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Expiry Date</label>
                  <input
                    type="text"
                    placeholder="MM/YY"
                    value={paymentMethodForm.expiryDate}
                    onChange={(e) => handlePaymentMethodChange('expiryDate', e.target.value)}
                    className="w-full p-3 border rounded-md"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">CVV</label>
                  <input
                    type="text"
                    placeholder="123"
                    value={paymentMethodForm.cvv}
                    onChange={(e) => handlePaymentMethodChange('cvv', e.target.value)}
                    className="w-full p-3 border rounded-md"
                  />
                </div>
              </div>

              <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                <div className="flex items-start space-x-2">
                  <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h5 className="font-medium text-blue-800">Secure Payment</h5>
                    <p className="text-sm text-blue-700">
                      Your payment information is encrypted and secure. We use industry-standard security measures.
                    </p>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <div className="flex-shrink-0 flex space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsPaymentMethodDialogOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button
              onClick={handlePaymentMethodSubmit}
              disabled={createPaymentMethodMutation.isPending}
              className="flex-1"
            >
              {createPaymentMethodMutation.isPending ? "Adding..." : "Add Payment Method"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Auto-Renewal Settings Dialog */}
      <Dialog open={isAutoRenewalDialogOpen} onOpenChange={setIsAutoRenewalDialogOpen}>
        <DialogContent className="max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Auto-Renewal Settings
            </DialogTitle>
            <DialogDescription>
              Configure your subscription auto-renewal preferences
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-6 pr-2">
            {/* Current Settings */}
            <div className="p-4 border rounded-lg bg-gray-50">
              <h4 className="font-medium mb-3">Current Settings</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto-Renewal Status</span>
                  <Badge variant={currentSubscription?.autoRenew ? "default" : "secondary"}>
                    {currentSubscription?.autoRenew ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Next Renewal Date</span>
                  <span className="text-sm font-medium">{currentSubscription ? formatDate(currentSubscription.endDate) : 'N/A'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Renewal Amount</span>
                  <span className="text-sm font-medium">{currentSubscription ? formatPrice(currentSubscription.totalAmount) : 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Auto-Renewal Options */}
            <div className="space-y-4">
              <h4 className="font-medium">Auto-Renewal Options</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <input type="radio" name="renewal" id="enable" defaultChecked={currentSubscription?.autoRenew} />
                  <label htmlFor="enable" className="flex-1">
                    <div className="font-medium">Enable Auto-Renewal</div>
                    <div className="text-sm text-muted-foreground">Automatically renew your subscription before it expires</div>
                  </label>
                </div>
                <div className="flex items-center space-x-3 p-3 border rounded-lg">
                  <input type="radio" name="renewal" id="disable" defaultChecked={!currentSubscription?.autoRenew} />
                  <label htmlFor="disable" className="flex-1">
                    <div className="font-medium">Disable Auto-Renewal</div>
                    <div className="text-sm text-muted-foreground">Manually renew your subscription when needed</div>
                  </label>
                </div>
              </div>
            </div>

            {/* Notification Settings */}
            <div className="space-y-4">
              <h4 className="font-medium">Renewal Notifications</h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="email-7" defaultChecked />
                  <label htmlFor="email-7" className="text-sm">Email reminder 7 days before renewal</label>
                </div>
                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="email-1" defaultChecked />
                  <label htmlFor="email-1" className="text-sm">Email reminder 1 day before renewal</label>
                </div>
                <div className="flex items-center space-x-3">
                  <input type="checkbox" id="email-success" defaultChecked />
                  <label htmlFor="email-success" className="text-sm">Email confirmation after successful renewal</label>
                </div>
              </div>
            </div>

          </div>
          <div className="flex-shrink-0 flex space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={() => setIsAutoRenewalDialogOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button onClick={async () => {
              try {
                if (currentSubscription) {
                  const response = await apiRequest('PUT', `/api/subscriptions/${currentSubscription.id}/auto-renewal`, {
                    autoRenew: currentSubscription.autoRenew,
                    notifications: {
                      email: true,
                      sms: false,
                      inApp: true
                    }
                  });

                  if (response.ok) {
                    toast({
                      title: "Settings Updated",
                      description: "Your auto-renewal settings have been saved successfully.",
                    });
                    setIsAutoRenewalDialogOpen(false);
                    // Refresh subscription data
                    queryClient.invalidateQueries({ queryKey: ['subscriptions'] });
                  } else {
                    throw new Error('Failed to update settings');
                  }
                }
              } catch (error) {
                toast({
                  title: "Error",
                  description: "Failed to update auto-renewal settings. Please try again.",
                  variant: "destructive"
                });
              }
            }} className="flex-1">
              Save Settings
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Billing History Dialog */}
      <Dialog open={isBillingHistoryDialogOpen} onOpenChange={setIsBillingHistoryDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Billing History
            </DialogTitle>
            <DialogDescription>
              View your complete billing and payment history
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto space-y-4 pr-2">
            {isLoadingBilling ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-2 text-sm text-muted-foreground">Loading billing history...</span>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Billing Summary - Real Data */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        {formatPrice(billingStats.totalPaid)}
                      </p>
                      <p className="text-sm text-muted-foreground">Total Paid</p>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">{billingStats.totalInvoices}</p>
                      <p className="text-sm text-muted-foreground">Invoices</p>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">{billingStats.monthsActive}</p>
                      <p className="text-sm text-muted-foreground">Months Active</p>
                    </div>
                  </Card>
                </div>

                {/* Billing History Table */}
                {finalBillingHistory.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-muted-foreground mb-2">No Billing History</h3>
                    <p className="text-sm text-muted-foreground">You don't have any billing history yet.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="rounded-md border">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b bg-muted/50">
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Subscription ID</th>
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Plan</th>
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Amount</th>
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Status</th>
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Start Date</th>
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">End Date</th>
                              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Auto Renew</th>
                            </tr>
                          </thead>
                          <tbody>
                            {paginatedBillingHistory.map((subscription) => (
                              <tr key={subscription.id} className="border-b transition-colors hover:bg-muted/50">
                                <td className="p-4 align-middle">
                                  <span className="font-mono text-sm">{subscription.id}</span>
                                </td>
                                <td className="p-4 align-middle">
                                  <div>
                                    <span className="text-sm font-medium">{subscription.plan}</span>
                                    <p className="text-xs text-muted-foreground">{subscription.planDescription}</p>
                                  </div>
                                </td>
                                <td className="p-4 align-middle">
                                  <span className="text-sm font-medium">{formatPrice(subscription.amount)}</span>
                                </td>
                                <td className="p-4 align-middle">
                                  <Badge variant={subscription.status === 'Active' || subscription.status === 'Paid' ? 'default' :
                                                subscription.status === 'Cancelled' ? 'destructive' : 'secondary'}>
                                    {subscription.status}
                                  </Badge>
                                </td>
                                <td className="p-4 align-middle">
                                  <span className="text-sm">{formatDate(subscription.startDate)}</span>
                                </td>
                                <td className="p-4 align-middle">
                                  <span className="text-sm">{formatDate(subscription.endDate)}</span>
                                </td>
                                <td className="p-4 align-middle">
                                  <Badge variant={subscription.autoRenew ? 'default' : 'outline'}>
                                    {subscription.autoRenew ? 'Yes' : 'No'}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Pagination for Billing History */}
                    {totalBillingPages > 1 && (
                      <div className="flex items-center justify-between pt-4">
                        <div className="text-sm text-muted-foreground">
                          Showing {billingStartIndex + 1} to {Math.min(billingEndIndex, totalBillingRecords)} of {totalBillingRecords} invoices
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm" onClick={() => setCurrentPage(Math.max(1, currentPage - 1))} disabled={currentPage === 1}>
                            <ChevronLeft className="h-4 w-4" />
                            Previous
                          </Button>
                          <div className="flex items-center space-x-1">
                            {Array.from({ length: totalBillingPages }, (_, i) => i + 1).map((page) => (
                              <Button key={page} variant={currentPage === page ? "default" : "outline"} size="sm" onClick={() => setCurrentPage(page)} className="w-8 h-8 p-0">
                                {page}
                              </Button>
                            ))}
                          </div>
                          <Button variant="outline" size="sm" onClick={() => setCurrentPage(Math.min(totalBillingPages, currentPage + 1))} disabled={currentPage === totalBillingPages}>
                            Next
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="flex-shrink-0 flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={() => setIsBillingHistoryDialogOpen(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
