import pg from 'pg';
import fs from 'fs';
const { Pool } = pg;

const pool = new Pool({
  connectionString: '****************************************************/nemboobill?sslmode=disable'
});

async function runSQL() {
  const client = await pool.connect();
  
  try {
    console.log('Executing SQL...');
    
    // Read and execute the SQL file
    const sql = fs.readFileSync('insert-subscriptions.sql', 'utf8');
    await client.query(sql);
    
    console.log('✓ SQL executed successfully');
    
    // Check results
    const result = await client.query(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE status = 'active') as active,
        COUNT(*) FILTER (WHERE status = 'expired') as expired,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
        COUNT(*) FILTER (WHERE status = 'inactive') as inactive,
        SUM(total_amount) as revenue
      FROM subscriptions
    `);
    
    console.log('📊 Subscription stats:', result.rows[0]);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

runSQL();
