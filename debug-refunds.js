import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Test credentials
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

async function login() {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    const data = await response.json();
    authToken = data.token;
    console.log('✅ Login successful');
    return data;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

async function getPayments() {
  try {
    const response = await fetch(`${BASE_URL}/api/payments`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Get payments failed: ${response.status}`);
    }

    const data = await response.json();
    console.log(`📋 Found ${data.length} payments`);
    
    // Show refund status for each payment
    data.forEach(payment => {
      console.log(`Payment ${payment.orderNumber}: Status=${payment.refundStatus}, Amount=${payment.refundAmount}, Method=${payment.refundMethod}`);
    });
    
    return data;
  } catch (error) {
    console.error('❌ Get payments failed:', error.message);
    return [];
  }
}

async function getRefunds() {
  try {
    const response = await fetch(`${BASE_URL}/api/refunds`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Get refunds failed: ${response.status}`);
    }

    const data = await response.json();
    console.log(`💸 Found ${data.length} refunds`);
    
    // Show details of each refund
    data.forEach(refund => {
      console.log(`Refund ${refund.id}: Order=${refund.orderNumber}, Amount=${refund.amount}, Method=${refund.refundMethod}, Date=${refund.createdAt}`);
    });
    
    return data;
  } catch (error) {
    console.error('❌ Get refunds failed:', error.message);
    return [];
  }
}

async function debugRefunds() {
  console.log('🔍 Starting refund debug...\n');

  try {
    // Step 1: Login
    await login();

    // Step 2: Get payments and their refund status
    console.log('\n📋 Getting payments...');
    const payments = await getPayments();

    // Step 3: Get all refunds
    console.log('\n💸 Getting refunds...');
    const refunds = await getRefunds();

    // Step 4: Cross-reference
    console.log('\n🔍 Cross-referencing payments and refunds...');
    payments.forEach(payment => {
      const paymentRefunds = refunds.filter(refund => refund.orderId === payment.orderId);
      const totalRefunded = paymentRefunds.reduce((sum, refund) => sum + refund.amount, 0);
      
      console.log(`\nPayment ${payment.orderNumber}:`);
      console.log(`  - Order Amount: ${payment.amount}`);
      console.log(`  - Refunds in DB: ${paymentRefunds.length}`);
      console.log(`  - Total Refunded: ${totalRefunded}`);
      console.log(`  - Payment Refund Status: ${payment.refundStatus}`);
      console.log(`  - Payment Refund Amount: ${payment.refundAmount}`);
      
      if (paymentRefunds.length > 0) {
        console.log(`  - Refund Details:`);
        paymentRefunds.forEach(refund => {
          console.log(`    * ${refund.amount} via ${refund.refundMethod} on ${new Date(refund.createdAt).toLocaleString()}`);
        });
      }
    });

    console.log('\n🎉 Debug completed!');

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

// Run the debug
debugRefunds();
