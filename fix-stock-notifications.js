import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function fixStockNotifications() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing Stock Notifications for Your Products...\n');

    // 1. Check current products and their settings
    const productsResult = await client.query(`
      SELECT id, name, quantity, reorder_level, shop_id
      FROM products
      WHERE name IN ('dosa', 'icecream', 'coffee', 'tea')
      ORDER BY name
    `);

    console.log('📋 Current Product Settings:');
    productsResult.rows.forEach(product => {
      console.log(`   ${product.name}:`);
      console.log(`     - Current Stock: ${product.quantity || 0} pcs`);
      console.log(`     - Reorder Level: ${product.reorder_level || 'NOT SET'}`);
      console.log(`     - Shop ID: ${product.shop_id}`);
    });

    if (productsResult.rows.length === 0) {
      console.log('❌ No products found with names: dosa, icecream, coffee, tea');
      console.log('💡 Make sure you have these products in your database.');
      return;
    }

    // 2. Update reorder levels for your products
    console.log('\n🔧 Setting proper reorder levels...');
    
    const reorderLevels = {
      'dosa': 5,      // Minimum 5 pieces
      'icecream': 3,  // Minimum 3 pieces  
      'coffee': 5,    // Minimum 5 pieces
      'tea': 3        // Minimum 3 pieces
    };

    for (const product of productsResult.rows) {
      const newReorderLevel = reorderLevels[product.name] || 5;
      
      await client.query(`
        UPDATE products 
        SET reorder_level = $1 
        WHERE id = $2
      `, [newReorderLevel, product.id]);

      console.log(`   ✅ ${product.name}: Set reorder level to ${newReorderLevel}`);
    }

    // 3. Get shop and user info
    const shopResult = await client.query('SELECT id FROM shops ORDER BY id LIMIT 1');
    const userResult = await client.query('SELECT id FROM users ORDER BY id LIMIT 1');
    
    if (shopResult.rows.length === 0 || userResult.rows.length === 0) {
      console.log('❌ No shop or user found.');
      return;
    }

    const shopId = shopResult.rows[0].id;
    const userId = userResult.rows[0].id;

    // 4. Clear existing stock notifications
    await client.query(`
      DELETE FROM notification_recipients 
      WHERE notification_id IN (
        SELECT id FROM notifications WHERE type = 'stock'
      )
    `);
    await client.query(`DELETE FROM notifications WHERE type = 'stock'`);
    console.log('\n🗑️  Cleared old stock notifications');

    // 5. Create notifications for low stock products
    console.log('\n🔔 Creating stock notifications...');

    // Get updated product data
    const updatedProductsResult = await client.query(`
      SELECT id, name, quantity, reorder_level
      FROM products 
      WHERE name IN ('dosa', 'icecream', 'coffee', 'tea')
      ORDER BY name
    `);

    let notificationCount = 0;

    for (const product of updatedProductsResult.rows) {
      const currentStock = product.quantity || 0;
      const minStock = product.reorder_level || 5;

      // Check if notification should be created
      if (currentStock <= minStock) {
        const priority = currentStock === 0 ? 'urgent' : 'high';
        const title = currentStock === 0 ? 'Out of Stock Alert!' : 'Low Stock Alert';
        const message = currentStock === 0 
          ? `Product "${product.name}" is out of stock! Please reorder immediately.`
          : `Product "${product.name}" is running low (${currentStock} remaining, minimum: ${minStock})`;

        // Create notification
        const notificationResult = await client.query(`
          INSERT INTO notifications (title, message, type, priority, recipient_type, recipient_id, shop_id, data, created_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
          RETURNING id
        `, [
          title,
          message,
          'stock',
          priority,
          'shop',
          shopId,
          shopId,
          JSON.stringify({
            productName: product.name,
            currentStock: currentStock,
            minStock: minStock
          })
        ]);

        const notificationId = notificationResult.rows[0].id;

        // Create recipient record
        await client.query(`
          INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
          VALUES ($1, $2, $3, NOW())
        `, [notificationId, userId, 'unread']);

        notificationCount++;
        console.log(`   🔔 ${title}: ${product.name} (${currentStock}/${minStock}) - ${priority.toUpperCase()}`);
      } else {
        console.log(`   ✅ ${product.name}: Stock OK (${currentStock}/${minStock})`);
      }
    }

    // 6. Create notification settings if they don't exist
    console.log('\n⚙️  Setting up notification preferences...');
    await client.query(`
      INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
      enabled = $4, delivery_methods = $5
    `, [userId, shopId, 'stock', true, JSON.stringify(['in_app'])]);

    // 7. Show final summary
    const totalUnreadResult = await client.query(`
      SELECT COUNT(*) as count
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.status = 'unread' AND n.shop_id = $1 AND nr.user_id = $2
    `, [shopId, userId]);

    console.log(`\n📊 Summary:`);
    console.log(`   - Updated reorder levels for ${productsResult.rows.length} products`);
    console.log(`   - Created ${notificationCount} stock notifications`);
    console.log(`   - Total unread notifications: ${totalUnreadResult.rows[0].count}`);

    console.log('\n🎯 What Should Happen Now:');
    console.log('   1. Refresh your browser');
    console.log(`   2. Notification bell should show red badge with "${totalUnreadResult.rows[0].count}"`);
    console.log('   3. Click the bell to see stock alerts');
    console.log('   4. Go to Inventory → Stock to see updated reorder levels');

    console.log('\n📋 Your Product Status:');
    for (const product of updatedProductsResult.rows) {
      const currentStock = product.quantity || 0;
      const minStock = product.reorder_level || 5;
      const status = currentStock <= minStock ? 
        (currentStock === 0 ? '🔴 OUT OF STOCK' : '🟡 LOW STOCK') : 
        '🟢 OK';
      console.log(`   ${product.name}: ${currentStock}/${minStock} pcs ${status}`);
    }

  } catch (error) {
    console.error('❌ Error fixing stock notifications:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the fix
fixStockNotifications();
