import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    try {
      // Try to parse as JSON first
      const contentType = res.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const errorData = await res.json();
        throw new Error(`${res.status}: ${JSON.stringify(errorData)}`);
      } else {
        // Fall back to text
        const text = await res.text();
        throw new Error(`${res.status}: ${text || res.statusText}`);
      }
    } catch (parseError) {
      throw new Error(`${res.status}: ${res.statusText}`);
    }
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  try {
    // Get auth token from localStorage
    const token = localStorage.getItem("auth_token");

    // Get current shop from localStorage
    const currentShopStr = localStorage.getItem("current_shop");
    const currentShop = currentShopStr ? JSON.parse(currentShopStr) : null;

    // Get current branch from localStorage
    const currentBranchStr = localStorage.getItem("current_branch");
    const currentBranch = currentBranchStr ? JSON.parse(currentBranchStr) : null;

    // Prepare headers
    const headers: Record<string, string> = {};

    if (data) {
      headers["Content-Type"] = "application/json";
    }

    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Add cache-busting headers for order-related requests
    if (url.includes('/api/orders')) {
      headers["Cache-Control"] = "no-cache, no-store, must-revalidate";
      headers["Pragma"] = "no-cache";
      headers["Expires"] = "0";
    }

    // Add shop ID to headers if available
    if (currentShop && currentShop.id) {
      headers["X-Shop-ID"] = currentShop.id.toString();
      console.log(`Adding shop ID to request headers: ${currentShop.id} for ${url}`);
    } else {
      console.warn(`No current shop found in localStorage for request to: ${url}`);
      console.warn(`Current shop from localStorage:`, currentShop);
      console.warn(`Current shop string from localStorage:`, currentShopStr);

      // For critical endpoints that require shop ID, try to get it from the request body
      if (typeof url === 'string' && url.includes('/api/orders') && data && typeof data === 'object') {
        const orderData = data as any;
        if (orderData.order && orderData.order.shopId) {
          headers["X-Shop-ID"] = orderData.order.shopId.toString();
          console.log(`Adding shop ID from request body to headers: ${orderData.order.shopId}`);
        }
      }
    }

    // Add branch ID to headers if available
    if (currentBranch && currentBranch.id) {
      headers["X-Branch-ID"] = currentBranch.id.toString();
      console.log(`Adding branch ID to request headers: ${currentBranch.id}`);
    } else {
      console.log('No current branch found in localStorage for request to: ' + url);

      // For critical endpoints that require branch ID, try to get it from the request body
      if (typeof url === 'string' && url.includes('/api/orders') && data && typeof data === 'object') {
        const orderData = data as any;
        if (orderData.order && orderData.order.branchId) {
          headers["X-Branch-ID"] = orderData.order.branchId.toString();
          console.log(`Adding branch ID from request body to headers: ${orderData.order.branchId}`);
        }
      }
    }

    // Clean up data before sending to API
    let cleanedData = data;
    if (data && typeof data === 'object') {
      // For debugging purposes, log the data before cleaning
      console.log(`API Request (before cleaning): ${method} ${url}`, { data });

      // Function to recursively remove null values from objects
      const removeNulls = (obj: any): any => {
        if (obj === null || obj === undefined) return undefined;
        if (typeof obj !== 'object') return obj;

        if (Array.isArray(obj)) {
          return obj.map(item => removeNulls(item)).filter(item => item !== undefined);
        }

        const result: any = {};
        for (const key in obj) {
          const value = removeNulls(obj[key]);
          if (value !== undefined) {
            result[key] = value;
          }
        }
        return result;
      };

      cleanedData = removeNulls(data);
    }

    const res = await fetch(url, {
      method,
      headers,
      body: cleanedData ? JSON.stringify(cleanedData) : undefined,
      credentials: "include",
    });

    // Don't throw here, let the caller handle the response
    return res;
  } catch (error) {
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await apiRequest("GET", queryKey[0] as string);

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    if (!res.ok) {
      let errorData;
      try {
        errorData = await res.json();
      } catch (e) {
        errorData = { message: `HTTP error ${res.status}` };
      }
      throw new Error(errorData.message || `HTTP error ${res.status}`);
    }

    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: 60000, // Refetch every 60 seconds
      refetchOnWindowFocus: true, // Refetch when window regains focus
      staleTime: 0, // Data is always considered stale immediately
      refetchOnMount: 'always', // Always refetch when component mounts
      retry: 1, // Retry failed requests once
    },
    mutations: {
      retry: 1, // Retry failed mutations once
    },
  },
});

/**
 * Helper function to invalidate and immediately refetch queries
 * Use this after mutations to ensure UI is updated immediately
 */
export const invalidateAndRefetch = async (queryKey: string | string[]) => {
  console.group('Invalidating and Refetching Queries');
  console.log('Original query keys:', Array.isArray(queryKey) ? queryKey : [queryKey]);

  const keys = Array.isArray(queryKey) ? queryKey : [queryKey];

  // Define related query keys for specific data types
  const relatedQueries: Record<string, string[]> = {
    '/api/products': [
      '/api/products',
      '/api/dashboard/top-products',
      '/api/purchases',
      '/api/expenses',
      // Add any other endpoints that might use product data
    ],
    '/api/categories': [
      '/api/categories',
      '/api/products', // Products might need to be refreshed when categories change
    ],
    '/api/customers': [
      '/api/customers',
    ],
    '/api/tables': [
      '/api/tables',
    ],
    '/api/platforms': [
      '/api/platforms',
    ],
    '/api/payment-methods': [
      '/api/payment-methods',
    ],
    '/api/purchases': [
      '/api/purchases',
      '/api/products', // Products might be affected by purchases
    ],
    '/api/expenses': [
      '/api/expenses',
      '/api/products', // Products might be affected by expenses
    ],
    '/api/orders/recent': [
      '/api/orders/recent',
      '/api/orders/all',
      '/api/products',
      '/api/tables',
    ],
    '/api/orders/all': [
      '/api/orders/all',
      '/api/orders/recent',
      '/api/products',
      '/api/tables',
    ],
  };

  // Collect all keys that need to be invalidated
  const keysToInvalidate = new Set<string>();

  for (const key of keys) {
    // Add the original key
    keysToInvalidate.add(key);

    // Add related keys if they exist
    if (relatedQueries[key]) {
      console.log(`Found related queries for ${key}:`, relatedQueries[key]);
      relatedQueries[key].forEach(relatedKey => keysToInvalidate.add(relatedKey));
    }
  }

  console.log('All keys to invalidate:', Array.from(keysToInvalidate));

  // Invalidate all collected keys and force refetch
  const promises = Array.from(keysToInvalidate).map(async (key) => {
    console.log(`Invalidating and refetching: ${key}`);
    // Use fetchQuery to force an immediate refetch
    try {
      // For report queries, use pattern matching to invalidate all variants
      if (key.startsWith('/api/reports/')) {
        console.log(`Invalidating all queries matching pattern: ${key}`);
        await queryClient.invalidateQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' && queryKey[0] === key;
          }
        });

        await queryClient.refetchQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' && queryKey[0] === key;
          },
          type: 'all',
          stale: true,
        });
      } else {
        // First invalidate the query
        await queryClient.invalidateQueries({ queryKey: [key] });

        // Then force an immediate refetch with specific options
        await queryClient.refetchQueries({
          queryKey: [key],
          exact: false,
          type: 'all', // Refetch active and inactive queries
          stale: true, // Only refetch stale queries (which should be all after invalidation)
        });
      }
    } catch (error) {
      console.error(`Error refetching ${key}:`, error);
    }
  });

  await Promise.all(promises);

  console.log('All queries invalidated and refetched');
  console.groupEnd();
};

/**
 * Helper function to log the current state of the query cache
 * This is useful for debugging cache issues
 */
export const logQueryCache = () => {
  const queryCache = queryClient.getQueryCache();
  const queries = queryCache.getAll();

  queries.forEach(query => {
    const queryKey = JSON.stringify(query.queryKey);
    const state = query.state;
  });
};

/**
 * Two-step order creation process
 * 1. Create order without items
 * 2. Add items to the created order
 */
/**
 * Cart management functions
 * These are optional operations that enhance the POS experience but are not critical
 */
export const cartOperations = {
  // Add item to cart
  addToCart: async (productId: number, quantity: number) => {
    try {
      console.log(`Adding product ${productId} to cart with quantity ${quantity}`);

      const response = await apiRequest('POST', '/api/cart/add', {
        item: { productId, quantity }
      });

      if (!response.ok) {
        // Don't throw error, just return a mock response
        return { success: false, productId, quantity };
      }

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        return await response.json();
      } else {
        return { success: false, productId, quantity };
      }
    } catch (error) {
      return { success: false, productId, quantity };
    }
  },

  // Update cart item quantity
  updateCartItem: async (productId: number, quantity: number) => {
    try {
      console.log(`Updating cart item ${productId} to quantity ${quantity}`);

      const response = await apiRequest('POST', '/api/cart/update', {
        item: { productId, quantity }
      });

      if (!response.ok) {
        console.warn(`Cart update operation failed with status ${response.status}. This is not critical for POS functionality.`);
        return { success: false, productId, quantity };
      }

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        return await response.json();
      } else {
        console.warn('Cart update operation returned non-JSON response. This is not critical for POS functionality.');
        return { success: false, productId, quantity };
      }
    } catch (error) {
      console.warn('Cart update operation failed:', error, '. This is not critical for POS functionality.');
      return { success: false, productId, quantity };
    }
  },

  // Remove item from cart
  removeFromCart: async (productId: number) => {
    try {
      console.log(`Removing product ${productId} from cart`);

      const response = await apiRequest('POST', '/api/cart/remove', {
        productId
      });

      if (!response.ok) {
        console.warn(`Cart remove operation failed with status ${response.status}. This is not critical for POS functionality.`);
        return { success: false, productId };
      }

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        return await response.json();
      } else {
        console.warn('Cart remove operation returned non-JSON response. This is not critical for POS functionality.');
        return { success: false, productId };
      }
    } catch (error) {
      console.warn('Cart remove operation failed:', error, '. This is not critical for POS functionality.');
      return { success: false, productId };
    }
  }
};

export const createOrderTwoStep = async (orderData: any, itemsData: any[]) => {
  console.group('Two-Step Order Creation');

  try {
    // Step 1: Create order without items
    console.log('Step 1: Creating order without items');
    console.log('Order data being sent:', JSON.stringify({ order: orderData }, null, 2));

    // Ensure all required fields have the correct types
    const processedOrderData = {
      ...orderData,
      subtotal: Number(orderData.subtotal || 0),
      taxAmount: Number(orderData.taxAmount || 0),
      discountAmount: Number(orderData.discountAmount || 0),
      totalAmount: Number(orderData.totalAmount || 0),
      // Ensure userId is included if available
      ...(orderData.userId ? { userId: Number(orderData.userId) } : {}),
      // Ensure shopId is included
      shopId: Number(orderData.shopId),
      // Include branchId if available
      ...(orderData.branchId ? { branchId: Number(orderData.branchId) } : {}),
      // Include orderNumber only if provided (let server generate if not)
      ...(orderData.orderNumber ? { orderNumber: orderData.orderNumber } : {}),
      // Ensure required string fields have default values
      status: orderData.status || 'pending',
      paymentStatus: orderData.paymentStatus || 'pending',
      notes: orderData.notes || '',
    };

    // Double-check that shopId is present and valid
    if (!processedOrderData.shopId || isNaN(processedOrderData.shopId)) {
      console.error("Missing or invalid shopId in order data:", orderData);
      throw new Error("Shop ID is required for creating an order");
    }

    // Get current branch from localStorage if not provided in order data
    if (!processedOrderData.branchId) {
      const currentBranchStr = localStorage.getItem("current_branch");
      if (currentBranchStr) {
        const currentBranch = JSON.parse(currentBranchStr);
        if (currentBranch && currentBranch.id) {
          processedOrderData.branchId = Number(currentBranch.id);
          console.log(`Added branch ID from localStorage: ${processedOrderData.branchId}`);
        }
      }
    }

    console.log('Processed order data:', JSON.stringify({ order: processedOrderData }, null, 2));

    const orderResponse = await apiRequest('POST', '/api/orders/create', { order: processedOrderData });

    if (!orderResponse.ok) {
      await throwIfResNotOk(orderResponse);
    }

    const createdOrder = await orderResponse.json();
    console.log('Order created successfully:', createdOrder);

    // Step 2: Add items to the created order
    if (itemsData && itemsData.length > 0) {
      console.log(`Step 2: Adding ${itemsData.length} items to order ${createdOrder.id}`);

      // Process items to ensure they have the correct types and all required fields
      const processedItems = itemsData.map(item => ({
        productId: Number(item.productId),
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        orderId: Number(createdOrder.id) // Explicitly add orderId to each item
      }));

      console.log('Processed items data:', JSON.stringify({ items: processedItems }, null, 2));

      const itemsResponse = await apiRequest('POST', `/api/orders/${createdOrder.id}/items`, { items: processedItems });

      if (!itemsResponse.ok) {
        await throwIfResNotOk(itemsResponse);
      }

      const addedItems = await itemsResponse.json();
      console.log('Items added successfully:', addedItems);

      // Return the complete order with items
      return {
        order: createdOrder,
        items: addedItems
      };
    }

    // If no items, just return the order
    return {
      order: createdOrder,
      items: []
    };
  } catch (error) {
    console.error('Error in two-step order creation:', error);
    throw error;
  } finally {
    console.groupEnd();
  }
};

/**
 * Split bill order creation
 * Creates multiple separate orders from a single split bill
 */
export const createSplitBillOrders = async (originalOrderData: any, splits: any[]) => {
  console.group('Split Bill Order Creation');

  try {
    console.log('Creating split bill orders');
    console.log('Original order data:', JSON.stringify(originalOrderData, null, 2));
    console.log('Splits data:', JSON.stringify(splits, null, 2));

    // Process the original order data
    const processedOrderData = {
      ...originalOrderData,
      subtotal: Number(originalOrderData.subtotal || 0),
      taxAmount: Number(originalOrderData.taxAmount || 0),
      discountAmount: Number(originalOrderData.discountAmount || 0),
      totalAmount: Number(originalOrderData.totalAmount || 0),
      shopId: Number(originalOrderData.shopId),
      ...(originalOrderData.branchId ? { branchId: Number(originalOrderData.branchId) } : {}),
      // orderNumber will be generated by the server if not provided
      ...(originalOrderData.orderNumber ? { orderNumber: originalOrderData.orderNumber } : {}),
      status: originalOrderData.status || 'pending',
      paymentStatus: originalOrderData.paymentStatus || 'pending',
      notes: originalOrderData.notes || '',
    };

    // Process splits data
    const processedSplits = splits.map(split => ({
      ...split,
      subtotal: Number(split.subtotal),
      taxAmount: Number(split.taxAmount),
      total: Number(split.total),
      items: split.items.map((item: any) => ({
        ...item,
        productId: Number(item.productId),
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
      })),
    }));

    console.log('Processed order data:', JSON.stringify(processedOrderData, null, 2));
    console.log('Processed splits data:', JSON.stringify(processedSplits, null, 2));

    const response = await apiRequest('POST', '/api/orders/split-bill', {
      originalOrder: processedOrderData,
      splits: processedSplits,
    });

    if (!response.ok) {
      await throwIfResNotOk(response);
    }

    const result = await response.json();
    console.log('Split bill orders created successfully:', result);
    return result;
  } catch (error) {
    console.error('Error creating split bill orders:', error);
    throw error;
  } finally {
    console.groupEnd();
  }
};

/**
 * Table transfer functionality
 * Transfers an order from one table to another
 */
export const transferOrderToTable = async (fromTableId: number, toTableId: number, orderId: number) => {
  console.group('Table Transfer');

  try {
    console.log('Transferring order to different table');
    console.log('Transfer data:', { fromTableId, toTableId, orderId });

    const response = await apiRequest('POST', '/api/orders/transfer-table', {
      fromTableId: Number(fromTableId),
      toTableId: Number(toTableId),
      orderId: Number(orderId),
    });

    if (!response.ok) {
      await throwIfResNotOk(response);
    }

    const result = await response.json();
    console.log('Table transfer completed successfully:', result);
    return result;
  } catch (error) {
    console.error('Error transferring order to different table:', error);
    throw error;
  } finally {
    console.groupEnd();
  }
};

/**
 * Table merge functionality
 * Merges multiple tables into one order
 */
export const mergeTablesOrders = async (primaryTableId: number, secondaryTableIds: number[], orderIds: number[]) => {
  console.group('Table Merge');

  try {
    console.log('Merging tables');
    console.log('Merge data:', { primaryTableId, secondaryTableIds, orderIds });

    const response = await apiRequest('POST', '/api/orders/merge-tables', {
      primaryTableId: Number(primaryTableId),
      secondaryTableIds: secondaryTableIds.map(id => Number(id)),
      orderIds: orderIds.map(id => Number(id)),
    });

    if (!response.ok) {
      await throwIfResNotOk(response);
    }

    const result = await response.json();
    console.log('Table merge completed successfully:', result);
    return result;
  } catch (error) {
    console.error('Error merging tables:', error);
    throw error;
  } finally {
    console.groupEnd();
  }
};