import { Client } from 'pg';

const client = new Client({
  connectionString: '**************************************************/nemboobill?sslmode=disable'
});

async function checkTableOrders() {
  try {
    await client.connect();
    console.log('Connected to database');

    // Get all tables
    const tablesResult = await client.query('SELECT id, name, status FROM tables ORDER BY id');
    console.log('\nAll tables:');
    console.table(tablesResult.rows);

    // Check which tables have orders
    const ordersResult = await client.query(`
      SELECT 
        t.id as table_id,
        t.name as table_name,
        COUNT(o.id) as order_count
      FROM tables t
      LEFT JOIN orders o ON t.id = o.table_id
      GROUP BY t.id, t.name
      ORDER BY t.id
    `);

    console.log('\nTables with order counts:');
    console.table(ordersResult.rows);

    // Find tables without orders
    const tablesWithoutOrders = ordersResult.rows.filter(row => parseInt(row.order_count) === 0);
    console.log('\nTables WITHOUT orders (can be deleted):');
    if (tablesWithoutOrders.length > 0) {
      console.table(tablesWithoutOrders);
    } else {
      console.log('No tables without orders found');
    }

    // Find tables with orders
    const tablesWithOrders = ordersResult.rows.filter(row => parseInt(row.order_count) > 0);
    console.log('\nTables WITH orders (cannot be deleted):');
    if (tablesWithOrders.length > 0) {
      console.table(tablesWithOrders);
    } else {
      console.log('No tables with orders found');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

checkTableOrders();
