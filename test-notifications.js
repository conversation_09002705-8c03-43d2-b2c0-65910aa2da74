import fetch from 'node-fetch';

const API_BASE = 'http://localhost:5000';

// Test notification system
async function testNotificationSystem() {
  console.log('🧪 Testing Notification System...\n');

  try {
    // Test 1: Check if notification endpoints are accessible
    console.log('1. Testing notification endpoints...');
    
    const endpoints = [
      '/api/notifications',
      '/api/notifications/unread-count',
      '/api/notifications/settings'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${API_BASE}${endpoint}`, {
          headers: {
            'Authorization': 'Bearer test-token',
            'X-Shop-ID': '1',
            'X-Branch-ID': '1'
          }
        });
        
        console.log(`   ✅ ${endpoint} - Status: ${response.status}`);
      } catch (error) {
        console.log(`   ❌ ${endpoint} - Error: ${error.message}`);
      }
    }

    // Test 2: Test notification creation endpoints
    console.log('\n2. Testing notification creation...');
    
    const testNotifications = [
      {
        endpoint: '/api/notifications/order',
        data: {
          orderId: 1,
          orderNumber: 'ORD-TEST-001',
          customerName: 'Test Customer'
        }
      },
      {
        endpoint: '/api/notifications/stock',
        data: {
          productName: 'Test Product',
          currentStock: 2,
          minStock: 5
        }
      },
      {
        endpoint: '/api/notifications/marketing',
        data: {
          title: 'Test Promotion',
          message: 'This is a test marketing notification'
        }
      },
      {
        endpoint: '/api/notifications/system',
        data: {
          title: 'Test System Alert',
          message: 'This is a test system notification',
          priority: 'normal'
        }
      }
    ];

    for (const test of testNotifications) {
      try {
        const response = await fetch(`${API_BASE}${test.endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
            'X-Shop-ID': '1',
            'X-Branch-ID': '1'
          },
          body: JSON.stringify(test.data)
        });
        
        console.log(`   ✅ ${test.endpoint} - Status: ${response.status}`);
      } catch (error) {
        console.log(`   ❌ ${test.endpoint} - Error: ${error.message}`);
      }
    }

    // Test 3: Check database tables exist
    console.log('\n3. Testing database connectivity...');
    
    try {
      const response = await fetch(`${API_BASE}/api/notifications`, {
        headers: {
          'Authorization': 'Bearer test-token',
          'X-Shop-ID': '1'
        }
      });
      
      if (response.status === 200 || response.status === 401) {
        console.log('   ✅ Database tables accessible');
      } else {
        console.log(`   ⚠️  Unexpected response: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Database connection error: ${error.message}`);
    }

    console.log('\n🎉 Notification system test completed!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Login to the application');
    console.log('   2. Visit /notifications/test to test the UI');
    console.log('   3. Check the notification bell for real-time updates');
    console.log('   4. Visit /notifications/settings to configure preferences');
    console.log('   5. Create an order in POS to test automatic notifications');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testNotificationSystem();
