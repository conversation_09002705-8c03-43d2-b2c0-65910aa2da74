import fetch from 'node-fetch';

async function testSubscriptionCreation() {
  try {
    console.log('Testing subscription creation...');
    
    // First, let's test getting subscription plans
    console.log('\n1. Testing GET /api/subscription-plans...');
    const plansResponse = await fetch('http://localhost:5000/api/subscription-plans', {
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Plans response status:', plansResponse.status);
    if (!plansResponse.ok) {
      const error = await plansResponse.text();
      console.log('Plans error:', error);
      return;
    }
    
    const plans = await plansResponse.json();
    console.log('Available plans:', plans.length);
    if (plans.length > 0) {
      console.log('First plan:', plans[0]);
    }
    
    // Now test creating a subscription
    console.log('\n2. Testing POST /api/subscriptions...');
    
    if (plans.length === 0) {
      console.log('No plans available to test with');
      return;
    }
    
    const testPlan = plans[0];
    const subscriptionData = {
      planId: testPlan.id,
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      autoRenew: true,
      discountPercent: 0,
      discountAmount: 0,
      totalAmount: testPlan.price
    };
    
    console.log('Subscription data to send:', subscriptionData);
    
    const subscriptionResponse = await fetch('http://localhost:5000/api/subscriptions', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(subscriptionData)
    });
    
    console.log('Subscription response status:', subscriptionResponse.status);
    
    if (subscriptionResponse.ok) {
      const result = await subscriptionResponse.json();
      console.log('Subscription created successfully:', result);
    } else {
      const error = await subscriptionResponse.text();
      console.log('Subscription creation error:', error);
      
      // Try to parse as JSON for better error details
      try {
        const errorJson = JSON.parse(error);
        console.log('Parsed error:', errorJson);
      } catch (e) {
        console.log('Could not parse error as JSON');
      }
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testSubscriptionCreation();
