import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { exportToExcel, exportToCSV, formatDataForExport } from "@/lib/export-utils";

import { jsPDF } from "jspdf";
import 'jspdf-autotable';
import { BranchSelector } from "@/components/branch-selector";
import { apiRequest } from '@/lib/queryClient';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Printer, File, FileText, FileDown, Search, FilterX, AlertTriangle } from "lucide-react";

const columns: Record<string, ColumnConfig> = {
  productName: {
    header: "Product Name",
    cell: (item: InventoryItem) => item.name
  },
  sku: {
    header: "SKU",
    cell: (item: InventoryItem) => item.sku
  },
  category: {
    header: "Category",
    cell: (item: InventoryItem) => item.category
  },
  quantity: {
    header: "Quantity",
    cell: (item: InventoryItem) => item.quantity.toString()
  },
  price: {
    header: "Price",
    cell: (item: InventoryItem) => `₹${item.price.toFixed(2)}`
  },
  totalValue: {
    header: "Total Value",
    cell: (item: InventoryItem) => `₹${(item.quantity * item.price).toFixed(2)}`
  },
  stockStatus: {
    header: "Stock Status",
    cell: (item: InventoryItem) => {
      if (item.quantity <= 0) return "Out of Stock";
      if (item.quantity <= 10) return "Low Stock";
      return "In Stock";
    }
  }
};

export default function InventoryReport() {
  const { token } = useAuth();
  const { currentBranch } = useApp();
  const { toast } = useToast();

  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [stockFilter, setStockFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [branchFilter, setBranchFilter] = useState<number | null>(currentBranch?.id || null);
  // Fetch products
  const { data: inventoryReport, isLoading } = useQuery<InventoryReport>({
    queryKey: ['/api/reports/inventory', branchFilter],
    queryFn: async () => {
      // Token is available from useAuth hook
      if (!token) throw new Error("Not authenticated");
      
      const response = await apiRequest("GET", `/api/reports/inventory${branchFilter ? `?branchId=${branchFilter}` : ''}`);
      if (!response.ok) {
        throw new Error("Failed to fetch inventory data");
      }
      const data = await response.json();
      return data || { totalProducts: 0, lowStockCount: 0, outOfStockCount: 0, items: [] };
    }
  });

  // Fetch categories for filter
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['/api/categories'],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/categories");
      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }
      const data = await response.json();
      return data || [];
    },
    enabled: !!token,
    retry: 1
  });
  // Loading states are managed by React Query

  // Calculate inventory stats
  const calculateInventoryStats = () => {
    // Check if data is available
    if (!inventoryReport || !Array.isArray(inventoryReport.items)) {
      console.log('No products data available for inventory report');
      return {
        filteredProducts: [],
        totalValue: 0,
        totalProducts: 0,
        lowStockCount: 0,
        outOfStockCount: 0,
        availableCount: 0
      };
    }

    console.log('Calculating inventory stats with', inventoryReport.items.length, 'products');

    let filteredProducts = [...inventoryReport.items];

    // Filter by branch
    if (branchFilter) {
      filteredProducts = filteredProducts.filter(
        (product) => product.branchId === branchFilter
      );
    }

    // Filter by category
    if (categoryFilter !== "all") {
      filteredProducts = filteredProducts.filter(
        (product) => product.categoryId === parseInt(categoryFilter)
      );
    }

    // Filter by stock status
    if (stockFilter === "low") {
      filteredProducts = filteredProducts.filter(
        (product) => product.quantity <= (product.reorderLevel || 10)
      );
    } else if (stockFilter === "out") {
      filteredProducts = filteredProducts.filter(
        (product) => product.quantity <= 0
      );
    } else if (stockFilter === "available") {
      filteredProducts = filteredProducts.filter(
        (product) => product.quantity > 0
      );
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredProducts = filteredProducts.filter(
        (product) =>
          product.name.toLowerCase().includes(query) ||
          product.sku?.toLowerCase().includes(query) ||
          product.barcode?.toLowerCase().includes(query)
      );
    }

    // If no products are available and we're in development, create sample data
    if (filteredProducts.length === 0 && process.env.NODE_ENV !== 'production') {

      // Create sample data for demonstration
      filteredProducts = Array(10).fill(0).map((_, index) => ({
        id: index + 1,
        name: `Sample Product ${index + 1}`,
        sku: `SKU-${1000 + index}`,
        barcode: `BAR-${2000 + index}`,
        categoryId: index % 3 + 1,
        quantity: index < 3 ? 0 : (index < 6 ? 5 : 20),
        price: 100 + (index * 10),
        reorderLevel: 10
      }));
    }

    // Calculate total inventory value
    const totalValue = filteredProducts.reduce(
      (sum, product) => sum + ((product.quantity || 0) * (product.price || 0)),
      0
    );

    // Count products by stock status
    const lowStockCount = filteredProducts.filter(
      (product) => (product.quantity || 0) <= (product.reorderLevel || 10) && (product.quantity || 0) > 0
    ).length;

    const outOfStockCount = filteredProducts.filter(
      (product) => (product.quantity || 0) <= 0
    ).length;

    const availableCount = filteredProducts.filter(
      (product) => (product.quantity || 0) > 0
    ).length;

    return {
      filteredProducts,
      totalValue,
      totalProducts: filteredProducts.length,
      lowStockCount,
      outOfStockCount,
      availableCount
    };
  };

  const stats = calculateInventoryStats();

  // Reset filters
  const handleResetFilters = () => {
    setCategoryFilter("all");
    setStockFilter("all");
    setSearchQuery("");
    setBranchFilter(currentBranch?.id || null);
  };

  // Handle print and export
  const handlePrint = () => {
    toast({
      title: "Print report",
      description: "Printing inventory report...",
    });
    window.print();
  };

  // Format data for export
  const getFormattedData = () => {
    if (!stats || !stats.filteredProducts || stats.filteredProducts.length === 0) {
      return null;
    }

    return formatDataForExport(stats.filteredProducts, {
      price: (value) => `₹${parseFloat(value).toFixed(2)}`,
      totalValue: (value, item) => `₹${(item.quantity * item.price).toFixed(2)}`,
      stockStatus: (_: unknown, item: InventoryItem) => {
        if (item.quantity <= 0) return "Out of Stock";
        if (item.quantity <= 10) return "Low Stock";
        return "In Stock";
      }
    });
  };

  // Get file name for exports
  const getExportFileName = () => {
    const branchName = branchFilter
      ? `_${currentBranch?.name?.replace(/\s+/g, '_') || 'Branch'}`
      : '_All_Branches';

    return `Inventory_Report${branchName}_${new Date().toISOString().split('T')[0]}`;
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to Excel
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Inventory report has been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Export to CSV
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Inventory report has been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    if (!stats || !stats.filteredProducts || stats.filteredProducts.length === 0) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No data available to export",
      });
      return;
    }

    try {
      // Create a new PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(18);
      doc.text("Inventory Report", 14, 22);

      // Add branch information
      doc.setFontSize(12);
      const branchText = branchFilter
        ? `Branch: ${currentBranch?.name || 'Selected Branch'}`
        : "Branch: All Branches";
      doc.text(branchText, 14, 32);

      // Add date information
      doc.text(`Date: ${new Date().toLocaleDateString()}`, 14, 42);

      // Add summary information
      doc.text(`Total Products: ${stats.totalProducts}`, 14, 52);
      doc.text(`Total Inventory Value: ₹${stats.totalValue.toFixed(2)}`, 14, 62);
      doc.text(`Low Stock Items: ${stats.lowStockCount}`, 14, 72);
      doc.text(`Out of Stock Items: ${stats.outOfStockCount}`, 14, 82);

      // Add inventory table
      const tableData = stats.filteredProducts.map(product => [
        product.name,
        product.sku || 'N/A',
        product.quantity.toString(),
        `₹${product.price.toFixed(2)}`,
        `₹${(product.quantity * product.price).toFixed(2)}`,
        product.quantity <= 0 ? 'Out of Stock' :
          product.quantity <= (product.reorderLevel || 10) ? 'Low Stock' : 'In Stock'
      ]);

      // @ts-ignore - jspdf-autotable types
      doc.autoTable({
        startY: 92,
        head: [['Product Name', 'SKU', 'Quantity', 'Unit Price', 'Total Value', 'Status']],
        body: tableData,
        theme: 'grid',
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        styles: { fontSize: 9 },
        columnStyles: {
          0: { cellWidth: 50 },
          1: { cellWidth: 25 },
          2: { cellWidth: 20 },
          3: { cellWidth: 25 },
          4: { cellWidth: 30 },
          5: { cellWidth: 25 }
        }
      });

      // Save the PDF
      doc.save(`${getExportFileName()}.pdf`);

      toast({
        title: "Export successful",
        description: "Inventory report has been exported to PDF",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "An error occurred while exporting the report to PDF",
      });
    }
  };

  // Stock status badge configurations
  const stockStatusColors = {
    outOfStock: "bg-red-100 text-red-800",
    lowStock: "bg-yellow-100 text-yellow-800",
    inStock: "bg-green-100 text-green-800"
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Inventory Report</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" onClick={handleExportToExcel}>
            <File className="h-4 w-4 mr-2" />
            Excel
          </Button>
          <Button variant="outline" onClick={handleExportToCSV}>
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button variant="outline" onClick={handleExportToPDF}>
            <FileDown className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Report Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Branch</label>
              <div className="w-full">
                <BranchSelector
                  variant="outline"
                  className="w-full"
                  onChange={(branchId) => setBranchFilter(branchId)}
                  value={branchFilter}
                  allowAllBranches={true}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Category</label>
              <Select
                value={categoryFilter}
                onValueChange={setCategoryFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All categories</SelectItem>
                  {categories && categories.map((category: any) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Stock Status</label>
              <Select
                value={stockFilter}
                onValueChange={setStockFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All stock" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All stock</SelectItem>
                  <SelectItem value="low">Low stock</SelectItem>
                  <SelectItem value="out">Out of stock</SelectItem>
                  <SelectItem value="available">Available</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:w-1/5">
              <label className="text-sm font-medium mb-1 block">Search</label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search products..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="w-full sm:w-1/5 flex items-end">
              <Button
                variant="outline"
                className="w-full"
                onClick={handleResetFilters}
              >
                <FilterX className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Products</span>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  {stats?.totalProducts || 0}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Inventory Value</span>
              {isLoading ? (
                <Skeleton className="h-8 w-24 mt-1" />
              ) : (
                <span className="text-2xl font-semibold mt-1">
                  ₹{stats?.totalValue.toFixed(2) || "0.00"}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Low Stock Items</span>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <div className="flex items-center mt-1">
                  <span className="text-2xl font-semibold">
                    {stats?.lowStockCount || 0}
                  </span>
                  {stats && stats.lowStockCount > 0 && (
                    <AlertTriangle className="h-5 w-5 ml-2 text-yellow-500" />
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Out of Stock Items</span>
              {isLoading ? (
                <Skeleton className="h-8 w-16 mt-1" />
              ) : (
                <div className="flex items-center mt-1">
                  <span className="text-2xl font-semibold">
                    {stats?.outOfStockCount || 0}
                  </span>
                  {stats && stats.outOfStockCount > 0 && (
                    <AlertTriangle className="h-5 w-5 ml-2 text-red-500" />
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Stock Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Status Overview</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : stats ? (
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">In Stock</span>
                  <span className="text-sm font-medium">
                    {stats.availableCount} / {stats.totalProducts} ({stats.totalProducts > 0 ? Math.round((stats.availableCount / stats.totalProducts) * 100) : 0}%)
                  </span>
                </div>
                <Progress value={stats.totalProducts > 0 ? (stats.availableCount / stats.totalProducts) * 100 : 0} className="h-2 bg-gray-100" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Low Stock</span>
                  <span className="text-sm font-medium">
                    {stats.lowStockCount} / {stats.totalProducts} ({stats.totalProducts > 0 ? Math.round((stats.lowStockCount / stats.totalProducts) * 100) : 0}%)
                  </span>
                </div>
                <Progress 
                  value={stats.totalProducts > 0 ? (stats.lowStockCount / stats.totalProducts) * 100 : 0} 
                  className="h-2 w-full bg-gray-100" 
                  progressBarClassName="bg-yellow-500" 
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Out of Stock</span>
                  <span className="text-sm font-medium">
                    {stats.outOfStockCount} / {stats.totalProducts} ({stats.totalProducts > 0 ? Math.round((stats.outOfStockCount / stats.totalProducts) * 100) : 0}%)
                  </span>
                </div>
                <Progress 
                  value={stats.totalProducts > 0 ? (stats.outOfStockCount / stats.totalProducts) * 100 : 0} 
                  className="h-2 w-full bg-gray-100" 
                  progressBarClassName="bg-red-500" 
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-32">
              <p className="text-gray-500">No inventory data available</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product Name</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                    </TableRow>
                  ))
                ) : stats?.filteredProducts && stats.filteredProducts.length > 0 ? (
                  stats.filteredProducts.map((product) => {
                    const categoryName = categories?.find((c: any) => c.id === product.categoryId)?.name || 'Uncategorized';
                    const stockStatus = product.quantity <= 0 ? 'outOfStock' :
                                       product.quantity <= (product.reorderLevel || 10) ? 'lowStock' : 'inStock';
                    const stockLabel = stockStatus === 'outOfStock' ? 'Out of Stock' :
                                      stockStatus === 'lowStock' ? 'Low Stock' : 'In Stock';

                    return (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>{product.sku || 'N/A'}</TableCell>
                        <TableCell>{categoryName}</TableCell>
                        <TableCell>{product.quantity}</TableCell>
                        <TableCell>₹{product.price.toFixed(2)}</TableCell>
                        <TableCell>₹{(product.quantity * product.price).toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={cn("rounded-full", stockStatusColors[stockStatus])}
                          >
                            {stockLabel}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-gray-500">
                      No products found for the selected filters
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}