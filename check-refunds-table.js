import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkRefundsTable() {
  const client = await pool.connect();
  try {
    console.log('Checking refunds table structure...');

    // Check if table exists
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'refunds'
      );
    `);
    
    console.log('Table exists:', tableExists.rows[0].exists);

    if (tableExists.rows[0].exists) {
      // Get table structure
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'refunds' 
        ORDER BY ordinal_position;
      `);
      
      console.log('Table structure:');
      columns.rows.forEach(col => {
        console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });

      // Try a simple select
      const count = await client.query('SELECT COUNT(*) FROM refunds');
      console.log('Current refund count:', count.rows[0].count);
    }

  } catch (error) {
    console.error('Error checking refunds table:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

checkRefundsTable();
