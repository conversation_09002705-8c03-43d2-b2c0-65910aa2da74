import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function fixNotificationRecipients() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing notification recipients - assigning existing notifications to users...\n');

    // Get all existing notifications
    const notificationsResult = await client.query(`
      SELECT id, title, shop_id, created_at
      FROM notifications
      ORDER BY shop_id, created_at DESC
    `);

    // Get all users
    const usersResult = await client.query('SELECT id, name FROM users ORDER BY id');

    console.log(`📧 Found ${notificationsResult.rows.length} notifications`);
    console.log(`👥 Found ${usersResult.rows.length} users`);

    if (notificationsResult.rows.length === 0) {
      console.log('❌ No notifications found to fix');
      return;
    }

    if (usersResult.rows.length === 0) {
      console.log('❌ No users found');
      return;
    }

    console.log('\n🔄 Creating notification recipients...');

    let recipientCount = 0;

    // First, clear existing recipients to avoid conflicts
    await client.query('DELETE FROM notification_recipients');
    console.log('🗑️  Cleared existing recipients');

    // For each notification, create recipients for all users
    for (const notification of notificationsResult.rows) {
      for (const user of usersResult.rows) {
        // Create recipient record
        await client.query(`
          INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
          VALUES ($1, $2, $3, NOW())
        `, [notification.id, user.id, 'unread']);

        recipientCount++;
      }
    }

    console.log(`✅ Created ${recipientCount} notification recipient records`);

    // Create notification settings for all users and shops
    console.log('\n⚙️  Creating notification settings...');
    
    const shopsResult = await client.query('SELECT id FROM shops');
    const notificationTypes = ['stock', 'order', 'system', 'marketing'];
    
    let settingsCount = 0;
    
    for (const user of usersResult.rows) {
      for (const shop of shopsResult.rows) {
        for (const type of notificationTypes) {
          try {
            await client.query(`
              INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
              VALUES ($1, $2, $3, $4, $5)
            `, [user.id, shop.id, type, true, JSON.stringify(['in_app'])]);

            settingsCount++;
          } catch (error) {
            // Ignore duplicate key errors
            if (error.code !== '23505') {
              throw error;
            }
          }
        }
      }
    }

    console.log(`✅ Created ${settingsCount} notification settings`);

    // Verify the fix by checking notifications for each shop
    console.log('\n📊 Verification - Notifications by shop:');
    
    for (const shop of shopsResult.rows) {
      const shopResult = await client.query('SELECT name FROM shops WHERE id = $1', [shop.id]);
      const shopName = shopResult.rows[0]?.name || `Shop ${shop.id}`;
      
      const notificationSummary = await client.query(`
        SELECT 
          COUNT(DISTINCT n.id) as total_notifications,
          COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_notifications
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE n.shop_id = $1
      `, [shop.id]);

      const summary = notificationSummary.rows[0];
      console.log(`   🏪 ${shopName}: ${summary.total_notifications} notifications, ${summary.unread_notifications} unread`);
    }

    // Show summary for each user
    console.log('\n📊 Verification - Notifications by user:');
    
    for (const user of usersResult.rows) {
      const userSummary = await client.query(`
        SELECT COUNT(*) as unread_count
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE nr.user_id = $1 AND nr.status = 'unread'
      `, [user.id]);

      console.log(`   👤 ${user.name}: ${userSummary.rows[0].unread_count} unread notifications`);
    }

    console.log('\n🎉 SUCCESS! All notifications now have recipients');
    
    console.log('\n🎯 What to do now:');
    console.log('   1. Refresh your browser (Ctrl+Shift+R)');
    console.log('   2. Check the notification bell - should show red badge with number');
    console.log('   3. Click the bell to see your notifications');
    console.log('   4. Try marking notifications as read (should work now)');
    console.log('   5. Switch between different shops to see different notifications');

    console.log('\n🔔 Expected Results:');
    console.log('   - Notification bell shows red badge (number of unread notifications)');
    console.log('   - Clicking bell shows list of notifications for current shop');
    console.log('   - Each shop has 5 notifications (stock alerts, orders, welcome messages)');
    console.log('   - Mark-as-read functionality should work correctly');
    console.log('   - Different shops show different notification content');

  } catch (error) {
    console.error('❌ Error fixing notification recipients:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixNotificationRecipients();
