-- Fix Table 3 mapping issue
-- Current issue: Table named "table3" has ID 5, but user wants Table 3 to have ID 3

-- Step 1: Check current table data
SELECT id, name, capacity, status FROM tables ORDER BY id;

-- Step 2: Update table ID 3 to be "Table 3" (this is what user wants)
UPDATE tables SET name = 'Table 3' WHERE id = 3;

-- Step 3: Update table ID 5 (currently "Table 3") to be "Table 5" to avoid confusion
UPDATE tables SET name = 'Table 5' WHERE id = 5;

-- Step 4: Clean up other table names for consistency
UPDATE tables SET name = 'Table 1' WHERE id = 1;
UPDATE tables SET name = 'Table 2' WHERE id = 2;
UPDATE tables SET name = 'Table 4' WHERE id = 4;
UPDATE tables SET name = 'Table 6' WHERE id = 6;
UPDATE tables SET name = 'Table 7' WHERE id = 7;

-- Step 5: Verify the changes
SELECT id, name, capacity, status FROM tables ORDER BY id;
