import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

import {
  Dialog,
  DialogContentRightSlide,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog-right-slide";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Settings, Package, Plus, Minus } from "lucide-react";

interface AdjustStockModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function AdjustStockModal({ open, onOpenChange, onSuccess }: AdjustStockModalProps) {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const { toast } = useToast();

  const [selectedProduct, setSelectedProduct] = useState("");
  const [adjustmentType, setAdjustmentType] = useState<"increase" | "decrease">("increase");
  const [quantity, setQuantity] = useState("");
  const [reason, setReason] = useState("");
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch products
  const { data: productsData, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products', currentShop?.id, currentBranch?.id],
    enabled: !!token && open,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/products");
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      return response.json();
    }
  });

  // Extract products array from the response
  const products = productsData?.products || [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedProduct || !quantity || !reason) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill in all required fields",
      });
      return;
    }

    const adjustmentQuantity = parseInt(quantity);
    if (isNaN(adjustmentQuantity) || adjustmentQuantity <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please enter a valid quantity",
      });
      return;
    }

    // Calculate the actual adjustment (positive for increase, negative for decrease)
    const finalQuantity = adjustmentType === "increase" ? adjustmentQuantity : -adjustmentQuantity;

    setIsSubmitting(true);

    try {
      const response = await apiRequest("PUT", `/api/inventory/products/${selectedProduct}/adjust`, {
        quantity: finalQuantity,
        reason,
        notes
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to adjust stock');
      }

      const result = await response.json();

      toast({
        title: "Stock Adjusted",
        description: `Stock has been ${adjustmentType === "increase" ? "increased" : "decreased"} successfully`,
      });

      // Reset form
      setSelectedProduct("");
      setAdjustmentType("increase");
      setQuantity("");
      setReason("");
      setNotes("");
      
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error adjusting stock:', error);
      toast({
        variant: "destructive",
        title: "Adjustment Failed",
        description: error instanceof Error ? error.message : "Failed to adjust stock",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProductData = Array.isArray(products) ? products.find((p: any) => p.id === parseInt(selectedProduct)) : null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContentRightSlide className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Adjust Stock
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="product">Product *</Label>
            {isLoadingProducts ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a product" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(products) && products.map((product: any) => (
                    <SelectItem key={product.id} value={product.id.toString()}>
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4" />
                        <span>{product.name}</span>
                        <span className="text-sm text-gray-500">
                          (Current: {product.quantity || 0})
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {selectedProductData && (
              <p className="text-sm text-gray-500">
                Current stock: {selectedProductData.quantity || 0} {selectedProductData.unitOfMeasure || 'pcs'}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label>Adjustment Type *</Label>
            <div className="flex gap-2">
              <Button
                type="button"
                variant={adjustmentType === "increase" ? "default" : "outline"}
                onClick={() => setAdjustmentType("increase")}
                className="flex-1"
              >
                <Plus className="h-4 w-4 mr-2" />
                Increase Stock
              </Button>
              <Button
                type="button"
                variant={adjustmentType === "decrease" ? "default" : "outline"}
                onClick={() => setAdjustmentType("decrease")}
                className="flex-1"
              >
                <Minus className="h-4 w-4 mr-2" />
                Decrease Stock
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity *</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              max={adjustmentType === "decrease" ? selectedProductData?.quantity || undefined : undefined}
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              placeholder={`Enter quantity to ${adjustmentType}`}
            />
            {adjustmentType === "decrease" && selectedProductData && (
              <p className="text-sm text-gray-500">
                Maximum decrease: {selectedProductData.quantity || 0}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason *</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select adjustment reason" />
              </SelectTrigger>
              <SelectContent>
                {adjustmentType === "increase" ? (
                  <>
                    <SelectItem value="purchase">New Purchase</SelectItem>
                    <SelectItem value="return">Customer Return</SelectItem>
                    <SelectItem value="found">Stock Found</SelectItem>
                    <SelectItem value="correction">Inventory Correction</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </>
                ) : (
                  <>
                    <SelectItem value="damaged">Damaged Goods</SelectItem>
                    <SelectItem value="expired">Expired Items</SelectItem>
                    <SelectItem value="lost">Lost/Stolen</SelectItem>
                    <SelectItem value="correction">Inventory Correction</SelectItem>
                    <SelectItem value="sample">Sample/Demo</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes (optional)"
              rows={3}
            />
          </div>

          {selectedProductData && quantity && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Preview</h4>
              <div className="text-sm space-y-1">
                <div className="flex justify-between">
                  <span>Current Stock:</span>
                  <span>{selectedProductData.quantity || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>Adjustment:</span>
                  <span className={adjustmentType === "increase" ? "text-green-600" : "text-red-600"}>
                    {adjustmentType === "increase" ? "+" : "-"}{quantity}
                  </span>
                </div>
                <div className="flex justify-between font-medium border-t pt-1">
                  <span>New Stock:</span>
                  <span>
                    {Math.max(0, (selectedProductData.quantity || 0) + 
                      (adjustmentType === "increase" ? parseInt(quantity) : -parseInt(quantity)))}
                  </span>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adjusting..." : "Adjust Stock"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContentRightSlide>
    </Dialog>
  );
}
