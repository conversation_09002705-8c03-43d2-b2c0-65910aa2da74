import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import jwt from 'jsonwebtoken';
import { storage } from './storage';
import { log } from './vite';

// Create a JWT secret
const JWT_SECRET = process.env.JWT_SECRET || "nemboobill-secret-key";

// Verify JWT token
const verifyToken = async (token: string): Promise<any> => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    console.error('Token verification error:', error);
    return null;
  }
};

// Dashboard event types
enum DashboardEvents {
  STATS_UPDATED = 'dashboard:stats_updated',
  TOP_PRODUCTS_UPDATED = 'dashboard:top_products_updated',
  RECENT_ORDERS_UPDATED = 'dashboard:recent_orders_updated',
  REVENUE_DATA_UPDATED = 'dashboard:revenue_data_updated',
  ORDER_DISTRIBUTION_UPDATED = 'dashboard:order_distribution_updated',
}

// POS event types
export enum POSEvents {
  TABLE_STATUS_UPDATED = 'pos:table_status_updated',
  TABLES_UPDATED = 'pos:tables_updated',
}

// Socket.io server instance
let io: SocketIOServer | null = null;

/**
 * Get the Socket.io server instance
 * @returns SocketIOServer instance or null if not initialized
 */
export function getSocketServer(): SocketIOServer | null {
  return io;
}

// Map to store socket connections by user ID, shop ID, and branch ID
const userSockets = new Map<string, Set<string>>();
const shopSockets = new Map<number, Set<string>>();
const branchSockets = new Map<number, Set<string>>();

/**
 * Initialize Socket.io server
 * @param httpServer HTTP server instance
 */
export function initSocketServer(httpServer: HttpServer): SocketIOServer {
  if (io) {
    return io;
  }

  io = new SocketIOServer(httpServer, {
    cors: {
      origin: '*',
      methods: ['GET', 'POST'],
    },
  });

  // Set up connection handler
  io.on('connection', (socket) => {
    log(`Socket connected: ${socket.id}`, 'socket');

    // Handle authentication
    socket.on('authenticate', async (data) => {
      try {
        const { token, shopId } = data;

        if (!token) {
          socket.emit('auth_error', { message: 'No token provided' });
          return;
        }

        // Verify token
        const decoded = await verifyToken(token);

        if (!decoded || !decoded.userId) {
          socket.emit('auth_error', { message: 'Invalid token' });
          return;
        }

        const userId = decoded.userId;

        // Store socket ID for this user
        if (!userSockets.has(userId)) {
          userSockets.set(userId, new Set());
        }
        userSockets.get(userId)?.add(socket.id);

        // Join user-specific room for notifications
        socket.join(`user_${userId}`);

        // If shop ID is provided, store socket ID for this shop
        if (shopId) {
          if (!shopSockets.has(shopId)) {
            shopSockets.set(shopId, new Set());
          }
          shopSockets.get(shopId)?.add(socket.id);

          // Join shop room for notifications
          socket.join(`shop_${shopId}`);
        }

        socket.emit('authenticated', { userId, shopId });
        log(`Socket authenticated: ${socket.id} (User: ${userId}, Shop: ${shopId})`, 'socket');
      } catch (error) {
        console.error('Socket authentication error:', error);
        socket.emit('auth_error', { message: 'Authentication failed' });
      }
    });

    // Handle shop change
    socket.on('set_shop', async (data) => {
      try {
        const { token, shopId } = data;

        if (!token || !shopId) {
          socket.emit('shop_error', { message: 'Invalid shop data' });
          return;
        }

        // Verify token
        const decoded = await verifyToken(token);

        if (!decoded || !decoded.userId) {
          socket.emit('auth_error', { message: 'Invalid token' });
          return;
        }

        const userId = decoded.userId;

        // Leave previous shop rooms
        const rooms = Array.from(socket.rooms);
        rooms.forEach(room => {
          if (room.startsWith('shop_')) {
            socket.leave(room);
          }
        });

        // Remove socket from previous shop
        for (const [shop, sockets] of shopSockets.entries()) {
          if (sockets.has(socket.id)) {
            sockets.delete(socket.id);
            if (sockets.size === 0) {
              shopSockets.delete(shop);
            }
          }
        }

        // Add socket to new shop
        if (!shopSockets.has(shopId)) {
          shopSockets.set(shopId, new Set());
        }
        shopSockets.get(shopId)?.add(socket.id);

        // Join new shop room
        socket.join(`shop_${shopId}`);

        socket.emit('shop_set', { shopId });
        log(`Socket shop set: ${socket.id} (User: ${userId}, Shop: ${shopId})`, 'socket');
      } catch (error) {
        console.error('Socket shop change error:', error);
        socket.emit('shop_error', { message: 'Shop change failed' });
      }
    });

    // Handle branch change
    socket.on('set_branch', async (data) => {
      try {
        const { token, shopId, branchId } = data;

        if (!token || !shopId || !branchId) {
          socket.emit('branch_error', { message: 'Invalid branch data' });
          return;
        }

        // Verify token
        const decoded = await verifyToken(token);

        if (!decoded || !decoded.userId) {
          socket.emit('auth_error', { message: 'Invalid token' });
          return;
        }

        const userId = decoded.userId;

        // Leave previous branch rooms
        const rooms = Array.from(socket.rooms);
        rooms.forEach(room => {
          if (room.startsWith('branch_')) {
            socket.leave(room);
          }
        });

        // Remove socket from previous branch
        for (const [branch, sockets] of branchSockets.entries()) {
          if (sockets.has(socket.id)) {
            sockets.delete(socket.id);
            if (sockets.size === 0) {
              branchSockets.delete(branch);
            }
          }
        }

        // Add socket to new branch
        if (!branchSockets.has(branchId)) {
          branchSockets.set(branchId, new Set());
        }
        branchSockets.get(branchId)?.add(socket.id);

        // Join new branch room
        socket.join(`branch_${branchId}`);

        socket.emit('branch_set', { branchId });
        log(`Socket branch set: ${socket.id} (User: ${userId}, Shop: ${shopId}, Branch: ${branchId})`, 'socket');
      } catch (error) {
        console.error('Socket branch change error:', error);
        socket.emit('branch_error', { message: 'Branch change failed' });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      log(`Socket disconnected: ${socket.id}`, 'socket');

      // Remove socket from user sockets
      for (const [userId, sockets] of userSockets.entries()) {
        if (sockets.has(socket.id)) {
          sockets.delete(socket.id);
          if (sockets.size === 0) {
            userSockets.delete(userId);
          }
        }
      }

      // Remove socket from shop sockets
      for (const [shopId, sockets] of shopSockets.entries()) {
        if (sockets.has(socket.id)) {
          sockets.delete(socket.id);
          if (sockets.size === 0) {
            shopSockets.delete(shopId);
          }
        }
      }

      // Remove socket from branch sockets
      for (const [branchId, sockets] of branchSockets.entries()) {
        if (sockets.has(socket.id)) {
          sockets.delete(socket.id);
          if (sockets.size === 0) {
            branchSockets.delete(branchId);
          }
        }
      }
    });
  });

  return io;
}

/**
 * Emit dashboard stats update to all clients for a specific shop
 * @param shopId Shop ID
 * @param branchId Branch ID (optional)
 */
export async function emitDashboardStatsUpdate(shopId: number, branchId?: number): Promise<void> {
  if (!io) {
    return;
  }

  try {
    // Get dashboard stats
    const stats = await storage.getDashboardStats(shopId, branchId);

    // If branch ID is provided, emit only to sockets for this branch
    if (branchId) {
      const branchSocketIds = branchSockets.get(branchId);

      if (branchSocketIds && branchSocketIds.size > 0) {
        // Emit to all sockets for this branch
        for (const socketId of branchSocketIds) {
          io.to(socketId).emit(DashboardEvents.STATS_UPDATED, stats);
        }
        log(`Emitted dashboard stats update to ${branchSocketIds.size} sockets for branch ${branchId}`, 'socket');
      }
    } else {
      // Otherwise, emit to all sockets for this shop
      const shopSocketIds = shopSockets.get(shopId);

      if (shopSocketIds && shopSocketIds.size > 0) {
        // Emit to all sockets for this shop
        for (const socketId of shopSocketIds) {
          io.to(socketId).emit(DashboardEvents.STATS_UPDATED, stats);
        }
        log(`Emitted dashboard stats update to ${shopSocketIds.size} sockets for shop ${shopId}`, 'socket');
      }
    }
  } catch (error) {
    console.error('Error emitting dashboard stats update:', error);
  }
}

/**
 * Emit top products update to all clients for a specific shop
 * @param shopId Shop ID
 * @param period Period for top products (today, week, month)
 * @param branchId Branch ID (optional)
 */
export async function emitTopProductsUpdate(
  shopId: number,
  period: 'today' | 'week' | 'month' = 'today',
  branchId?: number
): Promise<void> {
  if (!io) {
    return;
  }

  try {
    // Get top products
    const topProducts = await storage.getTopProducts(period, 5, shopId, branchId);

    // If branch ID is provided, emit only to sockets for this branch
    if (branchId) {
      const branchSocketIds = branchSockets.get(branchId);

      if (branchSocketIds && branchSocketIds.size > 0) {
        // Emit to all sockets for this branch
        for (const socketId of branchSocketIds) {
          io.to(socketId).emit(DashboardEvents.TOP_PRODUCTS_UPDATED, { period, products: topProducts });
        }
        log(`Emitted top products update to ${branchSocketIds.size} sockets for branch ${branchId}`, 'socket');
      }
    } else {
      // Otherwise, emit to all sockets for this shop
      const shopSocketIds = shopSockets.get(shopId);

      if (shopSocketIds && shopSocketIds.size > 0) {
        // Emit to all sockets for this shop
        for (const socketId of shopSocketIds) {
          io.to(socketId).emit(DashboardEvents.TOP_PRODUCTS_UPDATED, { period, products: topProducts });
        }
        log(`Emitted top products update to ${shopSocketIds.size} sockets for shop ${shopId}`, 'socket');
      }
    }
  } catch (error) {
    console.error('Error emitting top products update:', error);
  }
}

/**
 * Emit recent orders update to all clients for a specific shop
 * @param shopId Shop ID
 * @param branchId Branch ID (optional)
 */
export async function emitRecentOrdersUpdate(shopId: number, branchId?: number): Promise<void> {
  if (!io) {
    return;
  }

  try {
    // Get recent orders
    const recentOrders = await storage.getRecentOrders(10, shopId, branchId);

    // If branch ID is provided, emit only to sockets for this branch
    if (branchId) {
      const branchSocketIds = branchSockets.get(branchId);

      if (branchSocketIds && branchSocketIds.size > 0) {
        // Emit to all sockets for this branch
        for (const socketId of branchSocketIds) {
          io.to(socketId).emit(DashboardEvents.RECENT_ORDERS_UPDATED, recentOrders);
        }
        log(`Emitted recent orders update to ${branchSocketIds.size} sockets for branch ${branchId}`, 'socket');
      }
    } else {
      // Otherwise, emit to all sockets for this shop
      const shopSocketIds = shopSockets.get(shopId);

      if (shopSocketIds && shopSocketIds.size > 0) {
        // Emit to all sockets for this shop
        for (const socketId of shopSocketIds) {
          io.to(socketId).emit(DashboardEvents.RECENT_ORDERS_UPDATED, recentOrders);
        }
        log(`Emitted recent orders update to ${shopSocketIds.size} sockets for shop ${shopId}`, 'socket');
      }
    }
  } catch (error) {
    console.error('Error emitting recent orders update:', error);
  }
}

/**
 * Emit table status update to all clients for a specific shop
 * @param tableId Table ID that was updated
 * @param status New status of the table
 * @param shopId Shop ID
 * @param branchId Branch ID (optional)
 */
export async function emitTableStatusUpdate(
  tableId: number,
  status: string,
  shopId: number,
  branchId?: number
): Promise<void> {
  if (!io) {
    return;
  }

  try {
    // Create update payload
    const tableUpdate = {
      tableId,
      status,
      timestamp: new Date().toISOString()
    };

    // If branch ID is provided, emit only to sockets for this branch
    if (branchId) {
      const branchSocketIds = branchSockets.get(branchId);

      if (branchSocketIds && branchSocketIds.size > 0) {
        // Emit to all sockets for this branch
        for (const socketId of branchSocketIds) {
          io.to(socketId).emit(POSEvents.TABLE_STATUS_UPDATED, tableUpdate);
        }
        log(`Emitted table status update to ${branchSocketIds.size} sockets for branch ${branchId}`, 'socket');
      }
    } else {
      // Otherwise, emit to all sockets for this shop
      const shopSocketIds = shopSockets.get(shopId);

      if (shopSocketIds && shopSocketIds.size > 0) {
        // Emit to all sockets for this shop
        for (const socketId of shopSocketIds) {
          io.to(socketId).emit(POSEvents.TABLE_STATUS_UPDATED, tableUpdate);
        }
        log(`Emitted table status update to ${shopSocketIds.size} sockets for shop ${shopId}`, 'socket');
      }
    }
  } catch (error) {
    console.error('Error emitting table status update:', error);
  }
}

/**
 * Emit tables update to all clients for a specific shop
 * @param shopId Shop ID
 * @param branchId Branch ID (optional)
 */
export async function emitTablesUpdate(shopId: number, branchId?: number): Promise<void> {
  if (!io) {
    return;
  }

  try {
    // Get all tables for this shop/branch
    const tables = await storage.getAllTables(shopId, branchId);

    // If branch ID is provided, emit only to sockets for this branch
    if (branchId) {
      const branchSocketIds = branchSockets.get(branchId);

      if (branchSocketIds && branchSocketIds.size > 0) {
        // Emit to all sockets for this branch
        for (const socketId of branchSocketIds) {
          io.to(socketId).emit(POSEvents.TABLES_UPDATED, tables);
        }
        log(`Emitted tables update to ${branchSocketIds.size} sockets for branch ${branchId}`, 'socket');
      }
    } else {
      // Otherwise, emit to all sockets for this shop
      const shopSocketIds = shopSockets.get(shopId);

      if (shopSocketIds && shopSocketIds.size > 0) {
        // Emit to all sockets for this shop
        for (const socketId of shopSocketIds) {
          io.to(socketId).emit(POSEvents.TABLES_UPDATED, tables);
        }
        log(`Emitted tables update to ${shopSocketIds.size} sockets for shop ${shopId}`, 'socket');
      }
    }
  } catch (error) {
    console.error('Error emitting tables update:', error);
  }
}
