import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function InventoryDebug() {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const [debugData, setDebugData] = useState<any>(null);

  // Debug stock movements
  const { data: movementsDebug, refetch: refetchMovements } = useQuery({
    queryKey: ['/api/debug/stock-movements', currentShop?.id],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/debug/stock-movements");
      if (!response.ok) {
        throw new Error('Failed to fetch debug movements');
      }
      return response.json();
    },
  });

  // Debug stock transfers
  const { data: transfersDebug, refetch: refetchTransfers } = useQuery({
    queryKey: ['/api/debug/stock-transfers', currentShop?.id],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/debug/stock-transfers");
      if (!response.ok) {
        throw new Error('Failed to fetch debug transfers');
      }
      return response.json();
    },
  });

  // Test regular API endpoints
  const testRegularAPI = async () => {
    try {
      console.log("Testing regular API endpoints...");
      
      // Test movements
      const movementsResponse = await apiRequest("GET", "/api/stock-movements?page=1&pageSize=5");
      const movementsData = await movementsResponse.json();
      
      // Test transfers
      const transfersResponse = await apiRequest("GET", "/api/stock-transfers?page=1&pageSize=5");
      const transfersData = await transfersResponse.json();

      setDebugData({
        movements: movementsData,
        transfers: transfersData,
        timestamp: new Date().toISOString()
      });

      console.log("Regular API test results:", { movementsData, transfersData });
    } catch (error) {
      console.error("Error testing regular API:", error);
      setDebugData({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  };

  const refreshAll = () => {
    refetchMovements();
    refetchTransfers();
    testRegularAPI();
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Inventory Debug</h1>
          <p className="text-gray-500">Debug inventory data filtering issues</p>
        </div>
        <Button onClick={refreshAll}>Refresh All</Button>
      </div>

      {/* Current Context */}
      <Card>
        <CardHeader>
          <CardTitle>Current Context</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Current Shop:</strong>
              <div>ID: {currentShop?.id}</div>
              <div>Name: {currentShop?.name}</div>
            </div>
            <div>
              <strong>Current Branch:</strong>
              <div>ID: {currentBranch?.id}</div>
              <div>Name: {currentBranch?.name}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stock Movements Debug */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Movements Debug</CardTitle>
        </CardHeader>
        <CardContent>
          {movementsDebug ? (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Badge variant="outline">All Movements</Badge>
                  <div className="text-2xl font-bold">{movementsDebug.counts.allMovements}</div>
                </div>
                <div>
                  <Badge variant="default">Shop Movements</Badge>
                  <div className="text-2xl font-bold">{movementsDebug.counts.shopMovements}</div>
                </div>
                <div>
                  <Badge variant="secondary">Shop+Branch Movements</Badge>
                  <div className="text-2xl font-bold">{movementsDebug.counts.shopBranchMovements}</div>
                </div>
              </div>
              
              <div>
                <strong>Branches in Shop:</strong>
                <div className="flex gap-2 mt-2">
                  {movementsDebug.branches.map((branch: any) => (
                    <Badge key={branch.id} variant="outline">
                      {branch.name} (ID: {branch.id})
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <strong>Sample Data:</strong>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(movementsDebug.sampleMovements, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <div>Loading movements debug data...</div>
          )}
        </CardContent>
      </Card>

      {/* Stock Transfers Debug */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Transfers Debug</CardTitle>
        </CardHeader>
        <CardContent>
          {transfersDebug ? (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Badge variant="outline">All Transfers</Badge>
                  <div className="text-2xl font-bold">{transfersDebug.counts.allTransfers}</div>
                </div>
                <div>
                  <Badge variant="default">Shop Transfers</Badge>
                  <div className="text-2xl font-bold">{transfersDebug.counts.shopTransfers}</div>
                </div>
                <div>
                  <Badge variant="secondary">Shop+Branch Transfers</Badge>
                  <div className="text-2xl font-bold">{transfersDebug.counts.shopBranchTransfers}</div>
                </div>
              </div>

              <div>
                <strong>Sample Data:</strong>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(transfersDebug.sampleTransfers, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <div>Loading transfers debug data...</div>
          )}
        </CardContent>
      </Card>

      {/* Regular API Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Regular API Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={testRegularAPI} className="mb-4">Test Regular APIs</Button>
          {debugData ? (
            <div>
              <div className="mb-2">
                <strong>Timestamp:</strong> {debugData.timestamp}
              </div>
              {debugData.error ? (
                <div className="text-red-600">
                  <strong>Error:</strong> {debugData.error}
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <strong>Movements API Response:</strong>
                    <div>Count: {debugData.movements?.movements?.length || 0}</div>
                    <div>Total: {debugData.movements?.pagination?.totalCount || 0}</div>
                  </div>
                  <div>
                    <strong>Transfers API Response:</strong>
                    <div>Count: {debugData.transfers?.transfers?.length || 0}</div>
                    <div>Total: {debugData.transfers?.pagination?.totalCount || 0}</div>
                  </div>
                  <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-64">
                    {JSON.stringify(debugData, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ) : (
            <div>Click "Test Regular APIs" to see results</div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
