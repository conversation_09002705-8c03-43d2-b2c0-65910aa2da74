import { useEffect, useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient, invalidateAndRefetch } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import {
  Store,
  Printer,
  Receipt,
  Percent,
  Package,
  Settings,
  Hash,
  Loader2,
  AlertCircle,
  CheckCircle,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  RotateCcw,
} from "lucide-react";

// Shop settings form schema
const shopSettingsSchema = z.object({
  name: z.string().min(1, "Shop name is required"),
  address: z.string().min(1, "Address is required"),
  phone: z.string()
    .length(10, "Phone number must be exactly 10 digits")
    .regex(/^\d{10}$/, "Phone number must contain only digits"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  taxRegistration: z.string().optional(),
  logo: z.string().optional(),
  currencySymbol: z.string().default("₹"),
  footerText: z.string().optional(),
  receiptTemplate: z.string().optional(),
});

type ShopSettingsFormValues = z.infer<typeof shopSettingsSchema>;

// Tax settings form schema
const taxSettingSchema = z.object({
  name: z.string().min(1, "Tax name is required"),
  rate: z.coerce.number().min(0, "Rate must be a positive number"),
  active: z.boolean().default(true),
});

type TaxSettingFormValues = z.infer<typeof taxSettingSchema>;

// Discount settings form schema
const discountSettingSchema = z.object({
  name: z.string().min(1, "Discount name is required"),
  type: z.enum(["percentage", "fixed"]),
  value: z.coerce.number().min(0, "Value must be a positive number"),
  active: z.boolean().default(true),
});

type DiscountSettingFormValues = z.infer<typeof discountSettingSchema>;

// Order number settings form schema
const orderNumberSettingsSchema = z.object({
  prefix: z.string().min(1, "Prefix is required"),
  suffix: z.string(),
  numberType: z.enum(["sequential", "random", "date_based"]),
  sequentialStart: z.coerce.number().min(1, "Sequential start must be at least 1"),
  dateFormat: z.string(),
  separator: z.string().min(1, "Separator is required"),
  minDigits: z.coerce.number().min(1, "Minimum digits must be at least 1").max(10, "Maximum 10 digits"),
  resetPeriod: z.enum(["never", "daily", "monthly", "yearly"]),
  active: z.boolean().default(true),
});

type OrderNumberSettingsFormValues = z.infer<typeof orderNumberSettingsSchema>;

export default function ShopSettings() {
  const { token } = useAuth();
  const { toast } = useToast();
  const { currentShop, fetchUserShops } = useApp();

  // State for tax settings dialog
  const [isTaxDialogOpen, setIsTaxDialogOpen] = useState(false);
  const [editingTaxId, setEditingTaxId] = useState<number | null>(null);

  // State for discount settings dialog
  const [isDiscountDialogOpen, setIsDiscountDialogOpen] = useState(false);
  const [editingDiscountId, setEditingDiscountId] = useState<number | null>(null);

  // Fetch current shop data
  const { data: shopData, isLoading: isLoadingShopData } = useQuery({
    queryKey: ['/api/shops/current'],
    queryFn: async () => {
      if (!currentShop) return null;
      const response = await apiRequest("GET", `/api/shops/${currentShop.id}`);
      return response.json();
    },
    enabled: !!token && !!currentShop,
  });

  // Fetch tax settings
  const { data: taxSettings, isLoading: isLoadingTaxSettings, refetch: refetchTaxSettings } = useQuery({
    queryKey: ['/api/settings/tax'],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      console.log("Fetching tax settings with token:", token ? "Token exists" : "No token");
      console.log("Current shop when fetching tax settings:", currentShop);

      try {
        const response = await fetch('/api/settings/tax', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-Shop-ID': String(currentShop?.id || '') // Add shop ID to headers
          }
        });

        if (!response.ok) {
          let errorMessage = "Failed to fetch tax settings";
          try {
            const errorData = await response.json();
            console.error("API error response:", errorData);
            errorMessage = errorData.message || errorMessage;
          } catch (e) {
            const errorText = await response.text();
            errorMessage = `${errorMessage}: ${errorText || response.statusText}`;
          }
          console.error(errorMessage);
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Fetched tax settings:", data);

        // If data is not an array, return an empty array
        if (!Array.isArray(data)) {
          console.warn("Tax settings data is not an array:", data);
          return [];
        }

        return data;
      } catch (error) {
        console.error("Error in tax settings query:", error);
        // Return empty array instead of throwing to prevent UI errors
        return [];
      }
    },
    staleTime: 0, // Always consider data stale to ensure fresh data
    retry: 1, // Only retry once to avoid excessive requests on failure
  });

  // Fetch discount settings
  const { data: discountSettings, isLoading: isLoadingDiscountSettings, refetch: refetchDiscountSettings } = useQuery({
    queryKey: ['/api/settings/discount'],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      console.log("Fetching discount settings with token:", token ? "Token exists" : "No token");
      console.log("Current shop when fetching discount settings:", currentShop);

      try {
        const response = await fetch('/api/settings/discount', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-Shop-ID': String(currentShop?.id || '') // Add shop ID to headers
          }
        });

        if (!response.ok) {
          let errorMessage = "Failed to fetch discount settings";
          try {
            const errorData = await response.json();
            console.error("API error response:", errorData);
            errorMessage = errorData.message || errorMessage;
          } catch (e) {
            const errorText = await response.text();
            errorMessage = `${errorMessage}: ${errorText || response.statusText}`;
          }
          console.error(errorMessage);
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Fetched discount settings:", data);

        // If data is not an array, return an empty array
        if (!Array.isArray(data)) {
          console.warn("Discount settings data is not an array:", data);
          return [];
        }

        return data;
      } catch (error) {
        console.error("Error in discount settings query:", error);
        // Return empty array instead of throwing to prevent UI errors
        return [];
      }
    },
    staleTime: 0, // Always consider data stale to ensure fresh data
    retry: 1, // Only retry once to avoid excessive requests on failure
  });

  // Shop settings form
  const form = useForm<ShopSettingsFormValues>({
    resolver: zodResolver(shopSettingsSchema),
    defaultValues: {
      name: "",
      address: "",
      phone: "",
      email: "",
      taxRegistration: "",
      logo: "",
      currencySymbol: "₹",
      footerText: "",
      receiptTemplate: "",
    },
  });

  // Tax settings form
  const taxForm = useForm<TaxSettingFormValues>({
    resolver: zodResolver(taxSettingSchema),
    defaultValues: {
      name: "",
      rate: 0,
      active: true,
    },
  });

  // Discount settings form
  const discountForm = useForm<DiscountSettingFormValues>({
    resolver: zodResolver(discountSettingSchema),
    defaultValues: {
      name: "",
      type: "percentage",
      value: 0,
      active: true,
    },
  });

  // Order number settings form
  const orderNumberForm = useForm<OrderNumberSettingsFormValues>({
    resolver: zodResolver(orderNumberSettingsSchema),
    defaultValues: {
      prefix: "ORD",
      suffix: "",
      numberType: "sequential",
      sequentialStart: 1,
      dateFormat: "",
      separator: "/",
      minDigits: 2,
      resetPeriod: "never",
      active: true,
    },
  });

  // State for order number settings
  const [orderNumberPreview, setOrderNumberPreview] = useState<string>("");

  // Set form values when shop data is loaded
  useEffect(() => {
    if (shopData) {
      form.reset({
        name: shopData.name || "",
        address: shopData.address || "",
        phone: shopData.phone || "",
        email: shopData.email || "",
        taxRegistration: shopData.taxRegistration || "",
        logo: shopData.logo || "",
        currencySymbol: shopData.currencySymbol || "₹",
        footerText: shopData.footerText || "",
        receiptTemplate: shopData.receiptTemplate || "",
      });
    }
  }, [shopData, form]);

  // Reset tax form when dialog opens
  useEffect(() => {
    if (isTaxDialogOpen) {
      if (editingTaxId !== null && taxSettings) {
        const taxToEdit = taxSettings.find(tax => tax.id === editingTaxId);
        if (taxToEdit) {
          taxForm.reset({
            name: taxToEdit.name,
            rate: taxToEdit.rate,
            active: taxToEdit.active,
          });
        }
      } else {
        taxForm.reset({
          name: "",
          rate: 0,
          active: true,
        });
      }
    }
  }, [isTaxDialogOpen, editingTaxId, taxSettings, taxForm]);

  // Reset discount form when dialog opens
  useEffect(() => {
    if (isDiscountDialogOpen) {
      if (editingDiscountId !== null && discountSettings) {
        const discountToEdit = discountSettings.find(discount => discount.id === editingDiscountId);
        if (discountToEdit) {
          discountForm.reset({
            name: discountToEdit.name,
            type: discountToEdit.type as "percentage" | "fixed",
            value: discountToEdit.value,
            active: discountToEdit.active,
          });
        }
      } else {
        discountForm.reset({
          name: "",
          type: "percentage",
          value: 0,
          active: true,
        });
      }
    }
  }, [isDiscountDialogOpen, editingDiscountId, discountSettings, discountForm]);

  // Update shop settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: ShopSettingsFormValues) => {
      if (!currentShop) {
        throw new Error("No shop selected");
      }
      return apiRequest("PATCH", `/api/shops/${currentShop.id}`, data);
    },
    onSuccess: async () => {
      // Invalidate and refetch shop data
      await invalidateAndRefetch([
        `/api/shops/${currentShop?.id}`,
        '/api/shops/current'
      ]);

      // Refresh user shops to update the current shop in the header
      await fetchUserShops();

      toast({
        title: "Settings updated",
        description: "Shop settings have been updated successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to update settings:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update shop settings",
      });
    },
  });

  // Create tax setting mutation
  const createTaxSettingMutation = useMutation({
    mutationFn: async (data: TaxSettingFormValues) => {
      if (!currentShop) {
        throw new Error("No shop selected");
      }

      // Prepare the request payload with explicit shopId
      if (!currentShop.id || typeof currentShop.id !== 'number') {
        throw new Error("Invalid shop ID. Please select a valid shop.");
      }

      // Ensure all fields are properly typed
      const payload = {
        name: data.name,
        rate: Number(data.rate), // Ensure rate is a number
        active: Boolean(data.active),
        shopId: Number(currentShop.id) // Ensure shopId is a number
      };

      console.log("Creating tax setting with payload:", payload);
      console.log("Current shop:", currentShop);
      console.log("Auth token:", token ? "Token exists" : "No token");

      // Include the shop ID in both the request body and headers
      const response = await fetch("/api/settings/tax", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop.id) // Add shop ID to headers
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        let errorMessage = "Failed to create tax setting";
        try {
          const errorData = await response.json();
          console.error("API error response:", errorData);
          if (errorData.errors) {
            errorMessage = `Validation errors: ${JSON.stringify(errorData.errors)}`;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          const errorText = await response.text();
          errorMessage = `${errorMessage}: ${errorText || response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      return response.json();
    },
    onSuccess: async () => {
      // Force refetch to ensure UI updates
      await refetchTaxSettings();
      setIsTaxDialogOpen(false);

      toast({
        title: "Tax setting created",
        description: "Tax setting has been created successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to create tax setting:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create tax setting",
      });
    },
  });

  // Update tax setting mutation
  const updateTaxSettingMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: TaxSettingFormValues }) => {
      // Ensure all fields are properly typed
      const payload = {
        name: data.name,
        rate: Number(data.rate), // Ensure rate is a number
        active: Boolean(data.active)
      };

      console.log("Updating tax setting with payload:", payload);

      const response = await fetch(`/api/settings/tax/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop?.id || '') // Add shop ID to headers
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update tax setting: ${errorText}`);
      }

      return response.json();
    },
    onSuccess: async () => {
      // Force refetch to ensure UI updates
      await refetchTaxSettings();
      setIsTaxDialogOpen(false);
      setEditingTaxId(null);

      toast({
        title: "Tax setting updated",
        description: "Tax setting has been updated successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to update tax setting:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update tax setting",
      });
    },
  });

  // Delete tax setting mutation
  const deleteTaxSettingMutation = useMutation({
    mutationFn: async (id: number) => {
      console.log(`Attempting to delete tax setting with ID: ${id}`);
      console.log(`Current shop ID: ${currentShop?.id}`);

      // First, check if the tax setting exists
      try {
        const checkResponse = await fetch(`/api/settings/tax/${id}`, {
          headers: {
            "Authorization": `Bearer ${token}`,
            "X-Shop-ID": String(currentShop?.id || '')
          }
        });

        if (!checkResponse.ok) {
          console.error(`Tax setting with ID ${id} not found or not accessible`);
          if (checkResponse.status === 404) {
            // If it's already not found, we can consider this a "success" and just refetch
            return { success: true, message: "Tax setting already removed" };
          }
        } else {
          const taxData = await checkResponse.json();
          console.log(`Found tax setting to delete:`, taxData);
        }
      } catch (error) {
        console.error("Error checking tax setting:", error);
      }

      // Proceed with deletion
      const response = await fetch(`/api/settings/tax/${id}`, {
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop?.id || '') // Add shop ID to headers
        }
      });

      if (!response.ok) {
        let errorMessage = `Failed to delete tax setting`;
        try {
          const errorData = await response.json();
          console.error("API error response:", errorData);
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          const errorText = await response.text();
          errorMessage = `${errorMessage}: ${errorText || response.statusText}`;
        }

        // If it's a 404, we can consider this a "success" since the item is already gone
        if (response.status === 404) {
          console.log("Tax setting not found, considering deletion successful");
          return { success: true, message: "Tax setting already removed" };
        }

        throw new Error(errorMessage);
      }

      return response.json();
    },
    onSuccess: async () => {
      // Force refetch to ensure UI updates
      await refetchTaxSettings();

      toast({
        title: "Tax setting deleted",
        description: "Tax setting has been deleted successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to delete tax setting:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete tax setting",
      });
    },
  });

  // Create discount setting mutation
  const createDiscountSettingMutation = useMutation({
    mutationFn: async (data: DiscountSettingFormValues) => {
      if (!currentShop) {
        throw new Error("No shop selected");
      }

      // Prepare the request payload with explicit shopId
      if (!currentShop.id || typeof currentShop.id !== 'number') {
        throw new Error("Invalid shop ID. Please select a valid shop.");
      }

      // Ensure all fields are properly typed
      const payload = {
        name: data.name,
        type: data.type,
        value: Number(data.value), // Ensure value is a number
        active: Boolean(data.active),
        shopId: Number(currentShop.id) // Ensure shopId is a number
      };

      console.log("Creating discount setting with payload:", payload);
      console.log("Current shop:", currentShop);
      console.log("Auth token:", token ? "Token exists" : "No token");

      // Include the shop ID in both the request body and headers
      const response = await fetch("/api/settings/discount", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop.id) // Add shop ID to headers
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        let errorMessage = "Failed to create discount setting";
        try {
          const errorData = await response.json();
          console.error("API error response:", errorData);
          if (errorData.errors) {
            errorMessage = `Validation errors: ${JSON.stringify(errorData.errors)}`;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          const errorText = await response.text();
          errorMessage = `${errorMessage}: ${errorText || response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      return response.json();
    },
    onSuccess: async () => {
      // Force refetch to ensure UI updates
      await refetchDiscountSettings();
      setIsDiscountDialogOpen(false);

      toast({
        title: "Discount setting created",
        description: "Discount setting has been created successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to create discount setting:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create discount setting",
      });
    },
  });

  // Update discount setting mutation
  const updateDiscountSettingMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: DiscountSettingFormValues }) => {
      // Ensure all fields are properly typed
      const payload = {
        name: data.name,
        type: data.type,
        value: Number(data.value), // Ensure value is a number
        active: Boolean(data.active)
      };

      console.log("Updating discount setting with payload:", payload);

      const response = await fetch(`/api/settings/discount/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop?.id || '') // Add shop ID to headers
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update discount setting: ${errorText}`);
      }

      return response.json();
    },
    onSuccess: async () => {
      // Force refetch to ensure UI updates
      await refetchDiscountSettings();
      setIsDiscountDialogOpen(false);
      setEditingDiscountId(null);

      toast({
        title: "Discount setting updated",
        description: "Discount setting has been updated successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to update discount setting:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update discount setting",
      });
    },
  });

  // Delete discount setting mutation
  const deleteDiscountSettingMutation = useMutation({
    mutationFn: async (id: number) => {
      console.log(`Attempting to delete discount setting with ID: ${id}`);
      console.log(`Current shop ID: ${currentShop?.id}`);

      // First, check if the discount setting exists
      try {
        const checkResponse = await fetch(`/api/settings/discount/${id}`, {
          headers: {
            "Authorization": `Bearer ${token}`,
            "X-Shop-ID": String(currentShop?.id || '')
          }
        });

        if (!checkResponse.ok) {
          console.error(`Discount setting with ID ${id} not found or not accessible`);
          if (checkResponse.status === 404) {
            // If it's already not found, we can consider this a "success" and just refetch
            return { success: true, message: "Discount setting already removed" };
          }
        } else {
          const discountData = await checkResponse.json();
          console.log(`Found discount setting to delete:`, discountData);
        }
      } catch (error) {
        console.error("Error checking discount setting:", error);
      }

      // Proceed with deletion
      const response = await fetch(`/api/settings/discount/${id}`, {
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop?.id || '') // Add shop ID to headers
        }
      });

      if (!response.ok) {
        let errorMessage = `Failed to delete discount setting`;
        try {
          const errorData = await response.json();
          console.error("API error response:", errorData);
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          const errorText = await response.text();
          errorMessage = `${errorMessage}: ${errorText || response.statusText}`;
        }

        // If it's a 404, we can consider this a "success" since the item is already gone
        if (response.status === 404) {
          console.log("Discount setting not found, considering deletion successful");
          return { success: true, message: "Discount setting already removed" };
        }

        throw new Error(errorMessage);
      }

      return response.json();
    },
    onSuccess: async () => {
      // Force refetch to ensure UI updates
      await refetchDiscountSettings();

      toast({
        title: "Discount setting deleted",
        description: "Discount setting has been deleted successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to delete discount setting:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete discount setting",
      });
    },
  });

  // Fetch order number settings
  const { data: orderNumberSettings, isLoading: isLoadingOrderNumberSettings, refetch: refetchOrderNumberSettings } = useQuery({
    queryKey: ['/api/settings/order-number', currentShop?.id],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      try {
        const response = await fetch('/api/settings/order-number', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-Shop-ID': String(currentShop?.id || '')
          }
        });

        if (!response.ok) {
          if (response.status === 404) {
            return null; // No settings configured yet
          }
          throw new Error('Failed to fetch order number settings');
        }

        const data = await response.json();
        console.log("Fetched order number settings:", data);
        return data;
      } catch (error) {
        console.error("Error in order number settings query:", error);
        return null;
      }
    },
    staleTime: 0,
    retry: 1,
  });

  // Create/Update order number settings mutation
  const saveOrderNumberSettingsMutation = useMutation({
    mutationFn: async (data: OrderNumberSettingsFormValues) => {
      if (!currentShop) {
        throw new Error("No shop selected");
      }

      const payload = {
        ...data,
        shopId: Number(currentShop.id)
      };

      const method = orderNumberSettings ? "PATCH" : "POST";
      const url = orderNumberSettings
        ? `/api/settings/order-number/${orderNumberSettings.id}`
        : "/api/settings/order-number";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop.id)
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to save order number settings: ${errorText}`);
      }

      return response.json();
    },
    onSuccess: async () => {
      await refetchOrderNumberSettings();

      // Clear all order number related cache completely
      queryClient.removeQueries({ queryKey: ['nextOrderNumber'] });
      queryClient.removeQueries({ queryKey: ['/api/settings/order-number/next'] });

      // Invalidate and refetch immediately
      await queryClient.invalidateQueries({
        queryKey: ['nextOrderNumber'],
        exact: false
      });
      await queryClient.refetchQueries({
        queryKey: ['nextOrderNumber'],
        exact: false
      });

      toast({
        title: "Order number settings saved",
        description: "Order number format has been updated successfully. POS page will reflect changes immediately.",
        variant: "default",
      });
    },
    onError: (error) => {
      console.error("Failed to save order number settings:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to save order number settings",
      });
    },
  });

  // Generate preview mutation
  const generatePreviewMutation = useMutation({
    mutationFn: async (data: OrderNumberSettingsFormValues) => {
      const response = await fetch('/api/settings/order-number/preview', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
          "X-Shop-ID": String(currentShop?.id || '')
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Failed to generate preview');
      }

      const result = await response.json();
      return result.preview;
    },
    onSuccess: (preview) => {
      setOrderNumberPreview(preview);
    },
    onError: (error) => {
      console.error("Failed to generate preview:", error);
      setOrderNumberPreview("Error generating preview");
    },
  });

  // Reset sequence mutation
  const resetSequenceMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/settings/order-number/reset-sequence');
      if (!response.ok) {
        throw new Error('Failed to reset sequence');
      }
      return response.json();
    },
    onSuccess: async (data) => {
      await refetchOrderNumberSettings();

      // Clear all order number related cache completely
      queryClient.removeQueries({ queryKey: ['nextOrderNumber'] });
      queryClient.removeQueries({ queryKey: ['/api/settings/order-number/next'] });

      // Invalidate and refetch immediately
      await queryClient.invalidateQueries({
        queryKey: ['nextOrderNumber'],
        exact: false
      });
      await queryClient.refetchQueries({
        queryKey: ['nextOrderNumber'],
        exact: false
      });

      toast({
        title: "Sequence reset successfully",
        description: `Order number sequence has been reset from ${data.oldSequence} to ${data.newSequence}. Next order will start from ${orderNumberPreview}.`,
        variant: "default",
      });
    },
    onError: (error) => {
      console.error('Error resetting sequence:', error);
      toast({
        title: "Error",
        description: "Failed to reset sequence",
        variant: "destructive",
      });
    },
  });

  // Form submission handlers
  const handleUpdateSettings = (data: ShopSettingsFormValues) => {
    updateSettingsMutation.mutate(data);
  };

  const handleTaxFormSubmit = (data: TaxSettingFormValues) => {
    if (editingTaxId !== null) {
      updateTaxSettingMutation.mutate({ id: editingTaxId, data });
    } else {
      createTaxSettingMutation.mutate(data);
    }
  };

  const handleDeleteTax = (id: number) => {
    if (confirm("Are you sure you want to delete this tax setting?")) {
      // Check if the tax setting exists in our local data
      const taxExists = taxSettings?.some(tax => tax.id === id);
      if (!taxExists) {
        console.warn(`Tax setting with ID ${id} not found in local data`);
        // Refresh the data to ensure UI is up to date
        refetchTaxSettings();
        toast({
          title: "Warning",
          description: "This tax setting may have already been deleted",
          variant: "default",
        });
        return;
      }

      deleteTaxSettingMutation.mutate(id);
    }
  };

  const handleAddTax = () => {
    setEditingTaxId(null);
    setIsTaxDialogOpen(true);
  };

  const handleEditTax = (id: number) => {
    // Check if the tax setting exists in our local data
    const taxExists = taxSettings?.some(tax => tax.id === id);
    if (!taxExists) {
      console.warn(`Tax setting with ID ${id} not found in local data`);
      // Refresh the data to ensure UI is up to date
      refetchTaxSettings();
      toast({
        title: "Warning",
        description: "This tax setting may have been deleted",
        variant: "default",
      });
      return;
    }

    setEditingTaxId(id);
    setIsTaxDialogOpen(true);
  };

  const handleDiscountFormSubmit = (data: DiscountSettingFormValues) => {
    if (editingDiscountId !== null) {
      updateDiscountSettingMutation.mutate({ id: editingDiscountId, data });
    } else {
      createDiscountSettingMutation.mutate(data);
    }
  };

  const handleDeleteDiscount = (id: number) => {
    if (confirm("Are you sure you want to delete this discount setting?")) {
      // Check if the discount setting exists in our local data
      const discountExists = discountSettings?.some(discount => discount.id === id);
      if (!discountExists) {
        console.warn(`Discount setting with ID ${id} not found in local data`);
        // Refresh the data to ensure UI is up to date
        refetchDiscountSettings();
        toast({
          title: "Warning",
          description: "This discount setting may have already been deleted",
          variant: "default",
        });
        return;
      }

      deleteDiscountSettingMutation.mutate(id);
    }
  };

  const handleAddDiscount = () => {
    setEditingDiscountId(null);
    setIsDiscountDialogOpen(true);
  };

  const handleEditDiscount = (id: number) => {
    // Check if the discount setting exists in our local data
    const discountExists = discountSettings?.some(discount => discount.id === id);
    if (!discountExists) {
      console.warn(`Discount setting with ID ${id} not found in local data`);
      // Refresh the data to ensure UI is up to date
      refetchDiscountSettings();
      toast({
        title: "Warning",
        description: "This discount setting may have been deleted",
        variant: "default",
      });
      return;
    }

    setEditingDiscountId(id);
    setIsDiscountDialogOpen(true);
  };

  // Order number settings handlers
  const handleOrderNumberFormSubmit = (data: OrderNumberSettingsFormValues) => {
    saveOrderNumberSettingsMutation.mutate(data);
  };

  const handleGeneratePreview = () => {
    const formData = orderNumberForm.getValues();
    generatePreviewMutation.mutate(formData);
  };

  // Reset order number form when shop changes
  useEffect(() => {
    if (currentShop) {
      // Reset form to default values when shop changes
      orderNumberForm.reset({
        prefix: "ORD",
        suffix: "",
        numberType: "sequential",
        sequentialStart: 1,
        dateFormat: "",
        separator: "/",
        minDigits: 2,
        resetPeriod: "never",
        active: true,
      });
      // Clear preview
      setOrderNumberPreview("ORD/01");
    }
  }, [currentShop?.id, orderNumberForm]);

  // Set order number form values when settings are loaded
  useEffect(() => {
    if (orderNumberSettings) {
      orderNumberForm.reset({
        prefix: orderNumberSettings.prefix || "ORD",
        suffix: orderNumberSettings.suffix || "",
        numberType: orderNumberSettings.numberType || "sequential",
        sequentialStart: orderNumberSettings.sequentialStart || 1,
        dateFormat: orderNumberSettings.dateFormat || "",
        separator: orderNumberSettings.separator || "/",
        minDigits: orderNumberSettings.minDigits || 2,
        resetPeriod: orderNumberSettings.resetPeriod || "never",
        active: orderNumberSettings.active !== false,
      });
      // Generate initial preview
      generatePreviewMutation.mutate(orderNumberForm.getValues());
    }
  }, [orderNumberSettings, orderNumberForm]);

  // Generate preview when form values change
  useEffect(() => {
    const subscription = orderNumberForm.watch(() => {
      const formData = orderNumberForm.getValues();
      generatePreviewMutation.mutate(formData);
    });
    return () => subscription.unsubscribe();
  }, [orderNumberForm]);

  // Loading states
  const isLoading = isLoadingShopData || updateSettingsMutation.isPending;
  const isTaxLoading = isLoadingTaxSettings ||
                      createTaxSettingMutation.isPending ||
                      updateTaxSettingMutation.isPending ||
                      deleteTaxSettingMutation.isPending;
  const isDiscountLoading = isLoadingDiscountSettings ||
                           createDiscountSettingMutation.isPending ||
                           updateDiscountSettingMutation.isPending ||
                           deleteDiscountSettingMutation.isPending;
  const isOrderNumberLoading = isLoadingOrderNumberSettings ||
                               saveOrderNumberSettingsMutation.isPending ||
                               generatePreviewMutation.isPending ||
                               resetSequenceMutation.isPending;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Settings</h1>
        <p className="text-gray-500 mt-1">Manage your shop configuration</p>
      </div>

      <Tabs defaultValue="shop">
        <TabsList className="grid grid-cols-8 sm:w-[800px]">
          <TabsTrigger value="shop" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            <span className="hidden sm:inline">Shop</span>
          </TabsTrigger>
          <TabsTrigger value="printer" className="flex items-center gap-2">
            <Printer className="h-4 w-4" />
            <span className="hidden sm:inline">Printer</span>
          </TabsTrigger>
          <TabsTrigger value="tax" className="flex items-center gap-2">
            <Percent className="h-4 w-4" />
            <span className="hidden sm:inline">Tax</span>
          </TabsTrigger>
          <TabsTrigger value="discount" className="flex items-center gap-2">
            <Percent className="h-4 w-4" />
            <span className="hidden sm:inline">Discount</span>
          </TabsTrigger>
          <TabsTrigger value="order-number" className="flex items-center gap-2">
            <Hash className="h-4 w-4" />
            <span className="hidden sm:inline">Order #</span>
          </TabsTrigger>
          <TabsTrigger value="parcel" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            <span className="hidden sm:inline">Parcel</span>
          </TabsTrigger>
          <TabsTrigger value="receipt" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            <span className="hidden sm:inline">Receipt</span>
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">General</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="shop" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Shop Information</CardTitle>
              <CardDescription>
                Update your shop details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingShopData ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleUpdateSettings)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Shop Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter your shop name" {...field} disabled={isLoading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <Textarea placeholder="Enter your shop address" {...field} disabled={isLoading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter phone number (10 digits)"
                                {...field}
                                disabled={isLoading}
                                type="tel"
                                maxLength={10}
                                onInput={(e) => {
                                  // Allow only numeric input
                                  const target = e.target as HTMLInputElement;
                                  target.value = target.value.replace(/[^0-9]/g, '');
                                  field.onChange(target.value);
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter email address" {...field} disabled={isLoading} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="taxRegistration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tax Registration Number (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter tax registration number" {...field} disabled={isLoading} />
                          </FormControl>
                          <FormDescription>
                            E.g., GST or VAT registration number
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="logo"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo URL (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter logo URL" {...field} disabled={isLoading} />
                          </FormControl>
                          <FormDescription>
                            Enter a URL for your logo image
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="currencySymbol"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Currency Symbol</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="₹"
                              {...field}
                              disabled={isLoading}
                              className="w-24"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="footerText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Receipt Footer Text (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter text to display at the bottom of receipts"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormDescription>
                            This text will appear at the bottom of printed receipts
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end">
                      <Button type="submit" disabled={isLoading || !form.formState.isDirty}>
                        {updateSettingsMutation.isPending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="printer" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Printer Settings</CardTitle>
              <CardDescription>
                Configure your receipt printer settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Printer Connection</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Connection Type</label>
                      <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                        <option value="usb">USB</option>
                        <option value="network">Network/Ethernet</option>
                        <option value="bluetooth">Bluetooth</option>
                      </select>
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Printer Model</label>
                      <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                        <option value="epson">Epson TM Series</option>
                        <option value="star">Star Micronics</option>
                        <option value="bixolon">Bixolon</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Network Printer Settings</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">IP Address</label>
                      <Input placeholder="*************" />
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Port</label>
                      <Input placeholder="9100" />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Print Options</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Auto-print receipts</p>
                        <p className="text-sm text-gray-500">Automatically print receipt when order is completed</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Print order tickets</p>
                        <p className="text-sm text-gray-500">Print kitchen/bar tickets for new orders</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Print logo on receipt</p>
                        <p className="text-sm text-gray-500">Include shop logo on printed receipts</p>
                      </div>
                      <Switch />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button disabled>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tax" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Tax Settings</CardTitle>
                <CardDescription>
                  Configure tax rates and settings
                </CardDescription>
              </div>
              <Button onClick={handleAddTax} disabled={isTaxLoading}>
                <Plus className="h-4 w-4 mr-2" />
                Add Tax
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingTaxSettings ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : taxSettings && taxSettings.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Rate (%)</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {taxSettings.map((tax) => (
                      <TableRow key={tax.id}>
                        <TableCell className="font-medium">{tax.name}</TableCell>
                        <TableCell>{tax.rate}%</TableCell>
                        <TableCell>
                          {tax.active ? (
                            <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                              Inactive
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEditTax(tax.id)}
                              disabled={isTaxLoading}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleDeleteTax(tax.id)}
                              disabled={isTaxLoading}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <p className="text-gray-500">No tax settings found. Add your first tax setting.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tax Settings Sheet */}
          <Sheet open={isTaxDialogOpen} onOpenChange={setIsTaxDialogOpen}>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>
                  {editingTaxId !== null ? "Edit Tax Setting" : "Add Tax Setting"}
                </SheetTitle>
                <SheetDescription>
                  {editingTaxId !== null
                    ? "Update the tax setting details below."
                    : "Add a new tax setting to apply to your sales."}
                </SheetDescription>
              </SheetHeader>

              <Form {...taxForm}>
                <form onSubmit={taxForm.handleSubmit(handleTaxFormSubmit)} className="space-y-4">
                  <FormField
                    control={taxForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., GST, VAT, Sales Tax" {...field} disabled={isTaxLoading} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={taxForm.control}
                    name="rate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Rate (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="e.g., 5, 18, 28"
                            {...field}
                            disabled={isTaxLoading}
                          />
                        </FormControl>
                        <FormDescription>
                          Enter the percentage rate (e.g., 18 for 18%)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={taxForm.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Active</FormLabel>
                          <FormDescription>
                            Inactive taxes won't be applied to sales
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isTaxLoading}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <SheetFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsTaxDialogOpen(false)}
                      disabled={isTaxLoading}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isTaxLoading}>
                      {isTaxLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {editingTaxId !== null ? "Updating..." : "Adding..."}
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          {editingTaxId !== null ? "Update" : "Add"}
                        </>
                      )}
                    </Button>
                  </SheetFooter>
                </form>
              </Form>
            </SheetContent>
          </Sheet>
        </TabsContent>

        <TabsContent value="discount" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Discount Settings</CardTitle>
                <CardDescription>
                  Configure discount rates and settings
                </CardDescription>
              </div>
              <Button onClick={handleAddDiscount} disabled={isDiscountLoading}>
                <Plus className="h-4 w-4 mr-2" />
                Add Discount
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingDiscountSettings ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : discountSettings && discountSettings.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {discountSettings.map((discount) => (
                      <TableRow key={discount.id}>
                        <TableCell className="font-medium">{discount.name}</TableCell>
                        <TableCell>
                          {discount.type === "percentage" ? "Percentage (%)" : "Fixed Amount"}
                        </TableCell>
                        <TableCell>
                          {discount.type === "percentage"
                            ? `${discount.value}%`
                            : `${shopData?.currencySymbol || "₹"}${discount.value.toFixed(2)}`}
                        </TableCell>
                        <TableCell>
                          {discount.active ? (
                            <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                              Inactive
                            </span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEditDiscount(discount.id)}
                              disabled={isDiscountLoading}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleDeleteDiscount(discount.id)}
                              disabled={isDiscountLoading}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <p className="text-gray-500">No discount settings found. Add your first discount setting.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Discount Settings Sheet */}
          <Sheet open={isDiscountDialogOpen} onOpenChange={setIsDiscountDialogOpen}>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>
                  {editingDiscountId !== null ? "Edit Discount Setting" : "Add Discount Setting"}
                </SheetTitle>
                <SheetDescription>
                  {editingDiscountId !== null
                    ? "Update the discount setting details below."
                    : "Add a new discount setting to apply to your sales."}
                </SheetDescription>
              </SheetHeader>

              <Form {...discountForm}>
                <form onSubmit={discountForm.handleSubmit(handleDiscountFormSubmit)} className="space-y-4">
                  <FormField
                    control={discountForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Discount Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Happy Hour, Senior Discount" {...field} disabled={isDiscountLoading} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={discountForm.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Discount Type</FormLabel>
                        <div className="grid grid-cols-2 gap-4">
                          <Button
                            type="button"
                            variant={field.value === "percentage" ? "default" : "outline"}
                            className="w-full"
                            onClick={() => field.onChange("percentage")}
                            disabled={isDiscountLoading}
                          >
                            Percentage (%)
                          </Button>
                          <Button
                            type="button"
                            variant={field.value === "fixed" ? "default" : "outline"}
                            className="w-full"
                            onClick={() => field.onChange("fixed")}
                            disabled={isDiscountLoading}
                          >
                            Fixed Amount
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={discountForm.control}
                    name="value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {discountForm.watch("type") === "percentage"
                            ? "Discount Percentage (%)"
                            : "Discount Amount"}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={discountForm.watch("type") === "percentage"
                              ? "e.g., 10, 15, 20"
                              : "e.g., 50, 100, 200"}
                            {...field}
                            disabled={isDiscountLoading}
                          />
                        </FormControl>
                        <FormDescription>
                          {discountForm.watch("type") === "percentage"
                            ? "Enter the percentage discount (e.g., 10 for 10%)"
                            : "Enter the fixed amount discount"}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={discountForm.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Active</FormLabel>
                          <FormDescription>
                            Inactive discounts won't be available for selection
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isDiscountLoading}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <SheetFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDiscountDialogOpen(false)}
                      disabled={isDiscountLoading}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isDiscountLoading}>
                      {isDiscountLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {editingDiscountId !== null ? "Updating..." : "Adding..."}
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          {editingDiscountId !== null ? "Update" : "Add"}
                        </>
                      )}
                    </Button>
                  </SheetFooter>
                </form>
              </Form>
            </SheetContent>
          </Sheet>
        </TabsContent>

        <TabsContent value="order-number" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Order Number Format</CardTitle>
              <CardDescription>
                Configure how order numbers are generated for your shop
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingOrderNumberSettings ? (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : (
                <Form {...orderNumberForm}>
                  <form onSubmit={orderNumberForm.handleSubmit(handleOrderNumberFormSubmit)} className="space-y-6">
                    {/* Preview Section */}
                    <div className="bg-gray-50 p-4 rounded-lg border">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Preview</h3>
                          <p className="text-xs text-gray-500">How your order numbers will look</p>
                        </div>
                        <div className="text-lg font-mono font-bold text-blue-600">
                          {orderNumberPreview || "Loading..."}
                        </div>
                      </div>
                    </div>

                    {/* Reset Sequence Section */}
                    {orderNumberForm.watch("numberType") === "sequential" && (
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-sm font-medium text-yellow-800">Reset Sequence</h3>
                            <p className="text-xs text-yellow-600">Reset the order number sequence to start from the beginning</p>
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (confirm("Are you sure you want to reset the order number sequence? This will make the next order start from the beginning number.")) {
                                resetSequenceMutation.mutate();
                              }
                            }}
                            disabled={isOrderNumberLoading}
                            className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                          >
                            {resetSequenceMutation.isPending ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Resetting...
                              </>
                            ) : (
                              <>
                                <RotateCcw className="mr-2 h-4 w-4" />
                                Reset to {orderNumberForm.watch("sequentialStart") || 1}
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Basic Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={orderNumberForm.control}
                        name="prefix"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Prefix</FormLabel>
                            <FormControl>
                              <Input placeholder="ORD" {...field} disabled={isOrderNumberLoading} />
                            </FormControl>
                            <FormDescription>Text before the number (e.g., ORD, INV, BILL)</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={orderNumberForm.control}
                        name="suffix"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Suffix</FormLabel>
                            <FormControl>
                              <Input placeholder="Optional suffix" {...field} disabled={isOrderNumberLoading} />
                            </FormControl>
                            <FormDescription>Text after the number (optional)</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={orderNumberForm.control}
                        name="separator"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Separator</FormLabel>
                            <FormControl>
                              <Input placeholder="-" {...field} disabled={isOrderNumberLoading} />
                            </FormControl>
                            <FormDescription>Character between parts (-, _, /, or empty)</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={orderNumberForm.control}
                        name="minDigits"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minimum Digits</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                max="10"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                disabled={isOrderNumberLoading}
                              />
                            </FormControl>
                            <FormDescription>Minimum digits for the number part (1-10)</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Number Type */}
                    <FormField
                      control={orderNumberForm.control}
                      name="numberType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number Type</FormLabel>
                          <FormControl>
                            <select
                              {...field}
                              disabled={isOrderNumberLoading}
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                              <option value="sequential">Sequential (1, 2, 3...)</option>
                              <option value="random">Random Numbers</option>
                              <option value="date_based">Date-based Timestamp</option>
                            </select>
                          </FormControl>
                          <FormDescription>How the number part is generated</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Sequential Settings */}
                    {orderNumberForm.watch("numberType") === "sequential" && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={orderNumberForm.control}
                          name="sequentialStart"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Starting Number</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                  disabled={isOrderNumberLoading}
                                />
                              </FormControl>
                              <FormDescription>Number to start counting from</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={orderNumberForm.control}
                          name="resetPeriod"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Reset Period</FormLabel>
                              <FormControl>
                                <select
                                  {...field}
                                  disabled={isOrderNumberLoading}
                                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                >
                                  <option value="never">Never Reset</option>
                                  <option value="daily">Reset Daily</option>
                                  <option value="monthly">Reset Monthly</option>
                                  <option value="yearly">Reset Yearly</option>
                                </select>
                              </FormControl>
                              <FormDescription>When to reset the counter</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    {/* Date Format */}
                    <FormField
                      control={orderNumberForm.control}
                      name="dateFormat"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date Format (Optional)</FormLabel>
                          <FormControl>
                            <select
                              {...field}
                              disabled={isOrderNumberLoading}
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                              <option value="">No Date</option>
                              <option value="YYYY">Year (2025)</option>
                              <option value="YY">Short Year (25)</option>
                              <option value="MM">Month (01-12)</option>
                              <option value="DD">Day (01-31)</option>
                              <option value="YYYYMM">Year + Month (202501)</option>
                              <option value="YYYYMMDD">Full Date (20250101)</option>
                              <option value="DDMM">Day + Month (0101)</option>
                              <option value="DDMMYY">Day + Month + Year (010125)</option>
                              <option value="MMYY">Month + Year (0125)</option>
                            </select>
                          </FormControl>
                          <FormDescription>Include date information in order numbers</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Active Toggle */}
                    <FormField
                      control={orderNumberForm.control}
                      name="active"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Enable Custom Format</FormLabel>
                            <FormDescription>
                              Use this custom format for new orders. If disabled, the default format (ORD-XXXXX) will be used.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={isOrderNumberLoading}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleGeneratePreview}
                        disabled={isOrderNumberLoading}
                      >
                        Refresh Preview
                      </Button>
                      <Button type="submit" disabled={isOrderNumberLoading || !orderNumberForm.formState.isDirty}>
                        {saveOrderNumberSettingsMutation.isPending ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" />
                            Save Settings
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parcel" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Parcel Settings</CardTitle>
              <CardDescription>
                Configure takeaway and delivery settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Takeaway Options</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Enable Takeaway</p>
                        <p className="text-sm text-gray-500">Allow customers to place takeaway orders</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Takeaway Packaging Fee</label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">
                          {shopData?.currencySymbol || "₹"}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Additional fee for takeaway packaging (leave empty for no fee)
                      </p>
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Estimated Pickup Time</label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          placeholder="15"
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">minutes</span>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Delivery Options</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Enable Delivery</p>
                        <p className="text-sm text-gray-500">Allow customers to place delivery orders</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Delivery Radius</label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          placeholder="5"
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">kilometers</span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Maximum distance for delivery
                      </p>
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Minimum Order Value for Delivery</label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="100.00"
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">
                          {shopData?.currencySymbol || "₹"}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Delivery Fee</label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="40.00"
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">
                          {shopData?.currencySymbol || "₹"}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Estimated Delivery Time</label>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          placeholder="30"
                          className="w-32"
                        />
                        <span className="text-sm text-gray-500">minutes</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button disabled>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="receipt" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Receipt Template</CardTitle>
              <CardDescription>
                Customize your receipt layout and content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Receipt Header</h3>
                  <div className="space-y-4">
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Header Text</label>
                      <Textarea
                        placeholder="Enter text to display at the top of receipts"
                        className="min-h-[100px]"
                        value={form.watch("name")}
                        disabled
                      />
                      <p className="text-xs text-gray-500">
                        This will display your shop name at the top of the receipt
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Logo</p>
                        <p className="text-sm text-gray-500">Display shop logo at the top of receipts</p>
                      </div>
                      <Switch />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Receipt Content</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Order Number</p>
                        <p className="text-sm text-gray-500">Display order number on receipt</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Date and Time</p>
                        <p className="text-sm text-gray-500">Display order date and time</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Server Name</p>
                        <p className="text-sm text-gray-500">Display name of server/cashier</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Item Prices</p>
                        <p className="text-sm text-gray-500">Display individual item prices</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Show Tax Breakdown</p>
                        <p className="text-sm text-gray-500">Display detailed tax information</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Receipt Footer</h3>
                  <div className="flex flex-col space-y-1.5">
                    <label className="text-sm font-medium">Footer Text</label>
                    <Textarea
                      placeholder="Enter text to display at the bottom of receipts"
                      className="min-h-[100px]"
                      value={form.watch("footerText") || "Thank you for your business!"}
                    />
                    <p className="text-xs text-gray-500">
                      This will display at the bottom of the receipt (e.g., thank you message, return policy)
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button disabled>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="general" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Configure general application settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Application Theme</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                      <span className="text-sm font-medium mb-1">Light</span>
                      <span className="text-xs text-gray-500">Default light theme</span>
                    </Button>
                    <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                      <span className="text-sm font-medium mb-1">Dark</span>
                      <span className="text-xs text-gray-500">Dark mode (coming soon)</span>
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Date & Time Format</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Date Format</label>
                      <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </select>
                    </div>
                    <div className="flex flex-col space-y-1.5">
                      <label className="text-sm font-medium">Time Format</label>
                      <select className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                        <option value="12">12-hour (AM/PM)</option>
                        <option value="24">24-hour</option>
                      </select>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Notifications</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Order Notifications</p>
                        <p className="text-sm text-gray-500">Receive notifications for new orders</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Low Stock Alerts</p>
                        <p className="text-sm text-gray-500">Get notified when products are running low</p>
                      </div>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Daily Summary</p>
                        <p className="text-sm text-gray-500">Receive daily sales summary</p>
                      </div>
                      <Switch />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button disabled>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
