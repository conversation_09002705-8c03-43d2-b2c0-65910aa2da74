import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Search, X, Plus, ShoppingCart } from 'lucide-react';
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/context/auth-context';
import { useApp } from '@/context/app-context';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';

interface Product {
  id: number;
  name: string;
  price: number;
  category?: string;
  categoryName?: string;
  imageUrl?: string;
  quantity?: number;
  barcode?: string;
}

interface QuickSearchProps {
  onProductSelect?: (product: Product) => void;
  className?: string;
  triggerClassName?: string;
  showTrigger?: boolean;
}

export function QuickSearch({
  onProductSelect,
  className,
  triggerClassName,
  showTrigger = true
}: QuickSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const [isOpen, setIsOpen] = useState(false);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only set debounced query if search has at least 2 characters
      if (searchQuery.trim().length >= 2) {
        setDebouncedSearchQuery(searchQuery);
      } else {
        setDebouncedSearchQuery('');
      }
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch products based on debounced search query
  const { data: products, isLoading, error } = useQuery({
    queryKey: ['products-search', debouncedSearchQuery, currentShop?.id, currentBranch?.id],
    queryFn: async () => {
      // Only fetch if search query is not empty
      if (!debouncedSearchQuery.trim() || !token || !currentShop) {
        return [];
      }

      try {
        console.log('Searching products with query:', debouncedSearchQuery);

        // Fetch all products from the API
        const response = await apiRequest('GET', '/api/products');
        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }

        const allProducts: Product[] = await response.json();
        console.log('Fetched products:', allProducts.length);

        // Filter products based on search query
        const filteredProducts = allProducts.filter(product => {
          const searchLower = debouncedSearchQuery.toLowerCase();
          return (
            product.name.toLowerCase().includes(searchLower) ||
            (product.categoryName && product.categoryName.toLowerCase().includes(searchLower)) ||
            (product.barcode && product.barcode.toLowerCase().includes(searchLower))
          );
        });

        console.log('Filtered products:', filteredProducts.length);
        return filteredProducts.slice(0, 20); // Limit to 20 results for performance
      } catch (error) {
        console.error('Error fetching products:', error);
        throw error;
      }
    },
    enabled: !!debouncedSearchQuery.trim() && !!token && !!currentShop,
    staleTime: 30000, // Cache for 30 seconds
  });

  const handleSelectProduct = (product: Product) => {
    if (onProductSelect) {
      onProductSelect(product);
      setSearchQuery('');
      setIsOpen(false);

      // Prevent automatic scrolling by maintaining current scroll position
      const currentScrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      setTimeout(() => {
        window.scrollTo(0, currentScrollTop);
      }, 0);
    }
  };

  const searchContent = (
    <Card className={cn("w-full shadow-lg", className)}>
      <CardHeader className="p-4 pb-0">
        <CardTitle className="text-lg font-medium">Quick Product Search</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search by name, category, or barcode..."
            className="pl-9 pr-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            autoComplete="off"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 rounded-full p-0"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="max-h-[300px] overflow-y-auto rounded-md border">
          {(isLoading || (searchQuery !== debouncedSearchQuery && searchQuery.trim())) ? (
            <div className="p-4 space-y-3">
              <div className="text-center text-sm text-muted-foreground">
                Searching products...
              </div>
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[150px]" />
                    <Skeleton className="h-4 w-[100px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="p-4 text-center">
              <div className="text-sm text-red-600 mb-2">
                Error loading products
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSearchQuery(searchQuery + ' ')} // Trigger refetch
              >
                Try Again
              </Button>
            </div>
          ) : products && products.length > 0 ? (
            <div className="divide-y">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-3 hover:bg-muted/50 cursor-pointer"
                  onClick={() => handleSelectProduct(product)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-md bg-muted flex items-center justify-center">
                      {product.imageUrl ? (
                        <img
                          src={product.imageUrl}
                          alt={product.name}
                          className="h-full w-full object-cover rounded-md"
                        />
                      ) : (
                        <ShoppingCart className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-muted-foreground">
                          ${product.price.toFixed(2)}
                        </p>
                        {(product.categoryName || product.category) && (
                          <Badge variant="outline" className="text-xs">
                            {product.categoryName || product.category}
                          </Badge>
                        )}
                        {product.quantity !== undefined && (
                          <Badge variant={product.quantity > 0 ? "default" : "destructive"} className="text-xs">
                            Stock: {product.quantity}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button size="icon" variant="ghost" className="h-8 w-8">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : searchQuery.trim().length >= 2 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No products found matching "{searchQuery}"
            </div>
          ) : searchQuery.trim().length === 1 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Type at least 2 characters to search
            </div>
          ) : (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Start typing to search for products
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (!showTrigger) {
    return searchContent;
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className={triggerClassName}>
          <Search className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="p-0 sm:max-w-[400px] z-50">
        <SheetHeader className="px-4 pt-4">
          <SheetTitle>Quick Product Search</SheetTitle>
        </SheetHeader>
        <div className="h-full overflow-y-auto">
          {searchContent}
        </div>
      </SheetContent>
    </Sheet>
  );
}
