import { useState } from "react";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function TestHeaders() {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const [testResult, setTestResult] = useState<any>(null);

  const testHeaders = async () => {
    try {
      console.log("Testing headers with current context:", {
        shopId: currentShop?.id,
        shopName: currentShop?.name,
        branchId: currentBranch?.id,
        branchName: currentBranch?.name
      });

      const response = await fetch('/api/test-headers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || '',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Test headers response:", data);
      setTestResult(data);
    } catch (error) {
      console.error("Error testing headers:", error);
      setTestResult({ error: error.message });
    }
  };

  const testStockMovements = async () => {
    try {
      console.log("Testing stock movements API with current context:", {
        shopId: currentShop?.id,
        shopName: currentShop?.name,
        branchId: currentBranch?.id,
        branchName: currentBranch?.name
      });

      const response = await fetch('/api/stock-movements?page=1&pageSize=5', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || '',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log("Stock movements response:", data);
      setTestResult({ stockMovements: data });
    } catch (error) {
      console.error("Error testing stock movements:", error);
      setTestResult({ error: error.message });
    }
  };

  const testStockTransfers = async () => {
    try {
      console.log("Testing stock transfers API with current context:", {
        shopId: currentShop?.id,
        shopName: currentShop?.name,
        branchId: currentBranch?.id,
        branchName: currentBranch?.name
      });

      const response = await fetch('/api/stock-transfers?page=1&pageSize=5', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || '',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log("Stock transfers response:", data);
      setTestResult({ stockTransfers: data });
    } catch (error) {
      console.error("Error testing stock transfers:", error);
      setTestResult({ error: error.message });
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h1 className="text-2xl font-bold">Headers Test</h1>
        <p className="text-gray-500">Test if headers are being sent correctly</p>
      </div>

      {/* Current Context */}
      <Card>
        <CardHeader>
          <CardTitle>Current Context</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Current Shop:</strong>
              <div>ID: {currentShop?.id || 'None'}</div>
              <div>Name: {currentShop?.name || 'None'}</div>
            </div>
            <div>
              <strong>Current Branch:</strong>
              <div>ID: {currentBranch?.id || 'None'}</div>
              <div>Name: {currentBranch?.name || 'None'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Buttons */}
      <Card>
        <CardHeader>
          <CardTitle>Test APIs</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={testHeaders}>Test Headers Endpoint</Button>
            <Button onClick={testStockMovements}>Test Stock Movements</Button>
            <Button onClick={testStockTransfers}>Test Stock Transfers</Button>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResult && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(testResult, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
