import pg from 'pg';
const { Pool } = pg;

const pool = new Pool({
  connectionString: '****************************************************/nemboobill?sslmode=disable'
});

async function addMoreSubscriptions() {
  const client = await pool.connect();
  
  try {
    console.log('Adding more subscription data...');
    
    // Get all shops and users
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    const usersResult = await client.query('SELECT id, name FROM users ORDER BY id');
    const plansResult = await client.query('SELECT id, name, price FROM subscription_plans ORDER BY price');
    
    console.log(`Found ${shopsResult.rows.length} shops, ${usersResult.rows.length} users, ${plansResult.rows.length} plans`);
    
    if (shopsResult.rows.length === 0 || usersResult.rows.length === 0 || plansResult.rows.length === 0) {
      console.log('Missing required data. Cannot create subscriptions.');
      return;
    }
    
    // Check current subscription count
    const currentCount = await client.query('SELECT COUNT(*) FROM subscriptions');
    console.log(`Current subscription count: ${currentCount.rows[0].count}`);
    
    // Create more diverse subscriptions
    const subscriptionsToAdd = [];
    
    // For each shop, create different subscription scenarios
    for (let i = 0; i < Math.min(shopsResult.rows.length, 5); i++) {
      const shop = shopsResult.rows[i];
      const user = usersResult.rows[0]; // Use first user as creator
      
      // Scenario 1: Active Basic subscription
      subscriptionsToAdd.push({
        shopId: shop.id,
        planId: plansResult.rows[0].id, // Basic plan
        status: 'active',
        startDate: new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)), // Started 30 days ago
        endDate: new Date(Date.now() + (335 * 24 * 60 * 60 * 1000)), // Ends in 335 days
        autoRenew: true,
        discountPercent: 0,
        discountAmount: 0,
        totalAmount: plansResult.rows[0].price,
        createdBy: user.id
      });
      
      // Scenario 2: Active Professional subscription with discount
      if (plansResult.rows.length > 1) {
        subscriptionsToAdd.push({
          shopId: shop.id,
          planId: plansResult.rows[1].id, // Professional plan
          status: 'active',
          startDate: new Date(Date.now() - (60 * 24 * 60 * 60 * 1000)), // Started 60 days ago
          endDate: new Date(Date.now() + (305 * 24 * 60 * 60 * 1000)), // Ends in 305 days
          autoRenew: true,
          discountPercent: 15,
          discountAmount: plansResult.rows[1].price * 0.15,
          totalAmount: plansResult.rows[1].price * 0.85,
          createdBy: user.id
        });
      }
      
      // Scenario 3: Expired subscription
      subscriptionsToAdd.push({
        shopId: shop.id,
        planId: plansResult.rows[0].id, // Basic plan
        status: 'expired',
        startDate: new Date(Date.now() - (400 * 24 * 60 * 60 * 1000)), // Started 400 days ago
        endDate: new Date(Date.now() - (35 * 24 * 60 * 60 * 1000)), // Expired 35 days ago
        autoRenew: false,
        discountPercent: 0,
        discountAmount: 0,
        totalAmount: plansResult.rows[0].price,
        createdBy: user.id
      });
      
      // Scenario 4: Cancelled subscription
      if (plansResult.rows.length > 2) {
        subscriptionsToAdd.push({
          shopId: shop.id,
          planId: plansResult.rows[2].id, // Enterprise plan
          status: 'cancelled',
          startDate: new Date(Date.now() - (90 * 24 * 60 * 60 * 1000)), // Started 90 days ago
          endDate: new Date(Date.now() + (275 * 24 * 60 * 60 * 1000)), // Would end in 275 days
          autoRenew: false,
          discountPercent: 20,
          discountAmount: plansResult.rows[2].price * 0.20,
          totalAmount: plansResult.rows[2].price * 0.80,
          createdBy: user.id
        });
      }
      
      // Scenario 5: Inactive subscription
      subscriptionsToAdd.push({
        shopId: shop.id,
        planId: plansResult.rows[0].id, // Basic plan
        status: 'inactive',
        startDate: new Date(Date.now() - (15 * 24 * 60 * 60 * 1000)), // Started 15 days ago
        endDate: new Date(Date.now() + (350 * 24 * 60 * 60 * 1000)), // Ends in 350 days
        autoRenew: true,
        discountPercent: 5,
        discountAmount: plansResult.rows[0].price * 0.05,
        totalAmount: plansResult.rows[0].price * 0.95,
        createdBy: user.id
      });
    }
    
    // Insert the subscriptions
    let insertedCount = 0;
    for (const subscription of subscriptionsToAdd) {
      try {
        // Check if a similar subscription already exists for this shop
        const existingCheck = await client.query(
          'SELECT id FROM subscriptions WHERE shop_id = $1 AND plan_id = $2 AND status = $3 LIMIT 1',
          [subscription.shopId, subscription.planId, subscription.status]
        );
        
        if (existingCheck.rows.length === 0) {
          const result = await client.query(`
            INSERT INTO subscriptions (
              shop_id, plan_id, status, start_date, end_date, 
              auto_renew, discount_percent, discount_amount, total_amount, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id, shop_id, plan_id, status, total_amount;
          `, [
            subscription.shopId,
            subscription.planId,
            subscription.status,
            subscription.startDate,
            subscription.endDate,
            subscription.autoRenew,
            subscription.discountPercent,
            subscription.discountAmount,
            subscription.totalAmount,
            subscription.createdBy
          ]);
          
          console.log(`✓ Created subscription for shop ${subscription.shopId}:`, result.rows[0]);
          insertedCount++;
        } else {
          console.log(`- Skipped duplicate subscription for shop ${subscription.shopId} (${subscription.status})`);
        }
      } catch (error) {
        console.error(`Error creating subscription for shop ${subscription.shopId}:`, error.message);
      }
    }
    
    // Show final statistics
    const finalStats = await client.query(`
      SELECT 
        COUNT(*) as total_subscriptions,
        COUNT(*) FILTER (WHERE status = 'active') as active_subscriptions,
        COUNT(*) FILTER (WHERE status = 'expired') as expired_subscriptions,
        COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_subscriptions,
        COUNT(*) FILTER (WHERE status = 'inactive') as inactive_subscriptions,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(SUM(total_amount) FILTER (WHERE status = 'active'), 0) as active_revenue
      FROM subscriptions;
    `);
    
    console.log(`\n✓ Added ${insertedCount} new subscriptions`);
    console.log('📊 Final subscription statistics:', finalStats.rows[0]);
    console.log('More subscription data added successfully!');
    
  } catch (error) {
    console.error('Error adding subscription data:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addMoreSubscriptions();
