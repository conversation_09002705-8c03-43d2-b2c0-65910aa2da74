import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { useFreshData, forceRefreshPage } from "@/hooks/use-fresh-data";
import { exportToExcel, exportToCSV } from "@/lib/export-utils";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { clearInventoryCache } from "@/utils/cacheUtils";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  TrendingUp,
  TrendingDown,
  Search,
  RefreshCw,
  FileDown,
  ArrowUp,
  ArrowDown,
  ArrowRightLeft,
  Settings,
  ChevronDown
} from "lucide-react";

export default function StockMovements() {
  const { token } = useAuth();
  const { currentBranch, currentShop } = useApp();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [movementTypeFilter, setMovementTypeFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);

  // Use custom hook to ensure fresh data
  useFreshData(['/api/stock-movements'], []);

  // Reset all local state when component mounts
  useEffect(() => {
    console.log("Stock Movements component mounted - resetting state");
    setSearchQuery("");
    setMovementTypeFilter("all");
    setDateFilter("all");
    setPage(1);
  }, []);

  // Clear cache and reset state when shop changes
  useEffect(() => {
    if (currentShop) {
      console.log("Shop changed in Stock Movements - clearing cache and resetting state", { shopId: currentShop.id, shopName: currentShop.name });

      // Reset all local state
      setSearchQuery("");
      setMovementTypeFilter("all");
      setDateFilter("all");
      setPage(1);

      // Clear cache for stock movements when shop changes
      const clearShopCache = async () => {
        // Clear ALL queries to ensure no stale data
        console.log("Clearing ALL query cache to prevent cross-shop data contamination");
        queryClient.clear();

        // Also specifically target stock movements
        await queryClient.removeQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   queryKey[0].includes('/api/stock-movements');
          }
        });

        await queryClient.invalidateQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   queryKey[0].includes('/api/stock-movements');
          }
        });
      };

      clearShopCache();
    }
  }, [currentShop?.id]); // Watch for shop ID changes

  // Fetch stock movements from backend
  const { data: movementsData, isLoading: movementsLoading, refetch: refetchMovements } = useQuery({
    queryKey: ['/api/stock-movements', currentShop?.id, currentBranch?.id, page, pageSize, movementTypeFilter],
    enabled: !!token && !!currentShop && !!currentShop.id && currentShop.id > 0,
    staleTime: 0, // Always consider data stale to ensure fresh data on shop change
    gcTime: 0, // Don't cache to prevent cross-shop data contamination
    refetchOnMount: true, // Refetch on mount to ensure fresh data
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnReconnect: false, // Don't refetch on reconnect
    refetchInterval: false, // Disable automatic refetching
    queryFn: async () => {
      console.log("Fetching stock movements data...", {
        shopId: currentShop?.id,
        shopName: currentShop?.name,
        branchId: currentBranch?.id,
        page,
        pageSize,
        movementTypeFilter
      });

      // Ensure we have a valid shop ID before making the request
      if (!currentShop?.id || currentShop.id <= 0) {
        console.error("No valid current shop ID available for stock movements request", {
          currentShop,
          shopId: currentShop?.id
        });
        throw new Error('Valid shop context is required');
      }

      console.log("Making stock movements request with shop context:", {
        shopId: currentShop.id,
        shopName: currentShop.name,
        branchId: currentBranch?.id,
        branchName: currentBranch?.name
      });

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      if (movementTypeFilter !== "all") {
        params.append('movementType', movementTypeFilter);
      }

      // Add shop ID as a query parameter as well for extra validation
      params.append('shopId', currentShop.id.toString());

      const headers = {
        'Authorization': `Bearer ${token}`,
        'X-Shop-ID': currentShop.id.toString(),
        'X-Branch-ID': currentBranch?.id?.toString() || '',
        'Content-Type': 'application/json'
      };

      console.log("Request headers being sent:", headers);
      console.log("Request URL:", `/api/stock-movements?${params}`);

      const response = await fetch(`/api/stock-movements?${params}`, { headers });
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Stock movements API error:", response.status, errorText);
        throw new Error(`Failed to fetch stock movements: ${response.status} ${errorText}`);
      }
      const data = await response.json();
      console.log("Stock movements data fetched for shop:", currentShop?.name, data);

      // Additional client-side validation to ensure data belongs to current shop
      if (data.movements) {
        const validMovements = data.movements.filter((movement: any) => {
          // Check if movement belongs to current shop through branch
          if (movement.branch && movement.branch.shopId !== currentShop.id) {
            console.warn("Filtering out movement from different shop:", movement.id, movement.branch.shopId);
            return false;
          }
          return true;
        });

        if (validMovements.length !== data.movements.length) {
          console.warn(`Filtered out ${data.movements.length - validMovements.length} movements from other shops`);
          data.movements = validMovements;
          data.pagination.totalCount = validMovements.length;
          data.pagination.totalPages = Math.ceil(validMovements.length / pageSize);
        }
      }

      return data;
    },
  });

  // Extract movements from API response
  const movements = movementsData?.movements || [];
  const pagination = movementsData?.pagination || {};

  // Filter movements (client-side search and date filter, movement type filter handled by API)
  const filteredMovements = movements.filter((movement) => {
    let matches = true;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      matches = matches && (
        (movement.product?.name || '').toLowerCase().includes(query) ||
        (movement.referenceId?.toString() || '').toLowerCase().includes(query) ||
        (movement.reason || '').toLowerCase().includes(query)
      );
    }

    // Date filter (client-side)
    if (dateFilter === "today") {
      const today = new Date().toDateString();
      matches = matches && new Date(movement.createdAt).toDateString() === today;
    } else if (dateFilter === "week") {
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      matches = matches && new Date(movement.createdAt) >= weekAgo;
    } else if (dateFilter === "month") {
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      matches = matches && new Date(movement.createdAt) >= monthAgo;
    }

    return matches;
  });

  const getMovementInfo = (movementType: string, quantity: number) => {
    switch (movementType) {
      case 'in':
        return { 
          label: 'Stock In', 
          variant: 'default' as const, 
          icon: ArrowUp,
          color: 'text-green-600',
          sign: '+'
        };
      case 'out':
        return { 
          label: 'Stock Out', 
          variant: 'secondary' as const, 
          icon: ArrowDown,
          color: 'text-red-600',
          sign: '-'
        };
      case 'transfer':
        return { 
          label: 'Transfer', 
          variant: 'default' as const, 
          icon: ArrowRightLeft,
          color: 'text-blue-600',
          sign: quantity > 0 ? '+' : '-'
        };
      case 'adjustment':
        return { 
          label: 'Adjustment', 
          variant: 'secondary' as const, 
          icon: Settings,
          color: quantity > 0 ? 'text-green-600' : 'text-red-600',
          sign: quantity > 0 ? '+' : ''
        };
      default:
        return { 
          label: 'Unknown', 
          variant: 'secondary' as const, 
          icon: Settings,
          color: 'text-gray-600',
          sign: ''
        };
    }
  };

  // Format data for export
  const getFormattedData = () => {
    if (!filteredMovements || filteredMovements.length === 0) {
      return null;
    }

    return filteredMovements.map((movement: any) => {
      const movementInfo = getMovementInfo(movement.movementType, movement.quantity);
      
      return {
        'Product Name': movement.productName,
        'Movement Type': movementInfo.label,
        'Quantity': `${movementInfo.sign}${Math.abs(movement.quantity)}`,
        'Reference Type': movement.referenceType.charAt(0).toUpperCase() + movement.referenceType.slice(1),
        'Reference ID': movement.referenceId,
        'Reason': movement.reason,
        'Date': new Date(movement.createdAt).toLocaleDateString(),
        'Time': new Date(movement.createdAt).toLocaleTimeString(),
        'Created By': movement.createdBy
      };
    });
  };

  // Generate export filename
  const getExportFileName = () => {
    const date = new Date().toISOString().split('T')[0];
    const branchName = currentBranch?.name || 'All_Branches';
    return `Stock_Movements_${branchName}_${date}`;
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No movement data available to export",
      });
      return;
    }

    try {
      const success = exportToExcel(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Stock movements have been exported to Excel",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the movements",
      });
    }
  };

  // Handle export to CSV
  const handleExportToCSV = () => {
    const formattedData = getFormattedData();

    if (!formattedData) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "No movement data available to export",
      });
      return;
    }

    try {
      const success = exportToCSV(
        formattedData,
        getExportFileName()
      );

      if (success) {
        toast({
          title: "Export successful",
          description: "Stock movements have been exported to CSV",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the movements",
      });
    }
  };

  const handleRefresh = async () => {
    try {
      console.log("Manual refresh triggered - clearing cache and refetching");

      // Reset filters to initial state
      setSearchQuery("");
      setMovementTypeFilter("all");
      setDateFilter("all");
      setPage(1);

      // Clear only stock movement related cache entries
      await queryClient.removeQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) && queryKey.length > 0 &&
                 typeof queryKey[0] === 'string' &&
                 queryKey[0].includes('/api/stock-movements');
        }
      });

      // Invalidate to force immediate refetch
      await queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) && queryKey.length > 0 &&
                 typeof queryKey[0] === 'string' &&
                 queryKey[0].includes('/api/stock-movements');
        }
      });

      // Refetch the data
      await refetchMovements();

      toast({
        title: "Refreshed",
        description: "Movement data has been refreshed",
      });
    } catch (error) {
      console.error("Refresh error:", error);
      toast({
        variant: "destructive",
        title: "Refresh failed",
        description: "Failed to refresh movement data",
      });
    }
  };

  // Handle movement type filter change
  const handleMovementTypeFilterChange = (value: string) => {
    setMovementTypeFilter(value);
    setPage(1); // Reset to first page when filter changes
  };

  // Calculate stats
  const totalMovements = filteredMovements.length;
  const stockInCount = filteredMovements.filter(m => m.movementType === 'in').length;
  const stockOutCount = filteredMovements.filter(m => m.movementType === 'out').length;
  const adjustmentCount = filteredMovements.filter(m => m.movementType === 'adjustment').length;
  const transferCount = filteredMovements.filter(m => m.movementType === 'transfer').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Stock Movements</h1>
          <p className="text-gray-500">Track all stock movement activities</p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <FileDown className="h-4 w-4 mr-2" />
                Export
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleExportToExcel}>
                Export to Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportToCSV}>
                Export to CSV
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Movements</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMovements}</div>
            <p className="text-xs text-muted-foreground">
              All movements
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock In</CardTitle>
            <ArrowUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stockInCount}</div>
            <p className="text-xs text-muted-foreground">
              Incoming stock
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Out</CardTitle>
            <ArrowDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stockOutCount}</div>
            <p className="text-xs text-muted-foreground">
              Outgoing stock
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transfers</CardTitle>
            <ArrowRightLeft className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{transferCount}</div>
            <p className="text-xs text-muted-foreground">
              Stock transfers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Movements Table */}
      <Card>
        <CardHeader>
          <CardTitle>Movement History</CardTitle>
        </CardHeader>
        <CardContent className="mt-3">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search movements..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={movementTypeFilter} onValueChange={handleMovementTypeFilterChange}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="in">Stock In</SelectItem>
                <SelectItem value="out">Stock Out</SelectItem>
                <SelectItem value="transfer">Transfer</SelectItem>
                <SelectItem value="adjustment">Adjustment</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All Dates" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Dates</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Movement Type</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Created By</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {movementsLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-40" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredMovements.length > 0 ? (
                  filteredMovements.map((movement) => {
                    const movementInfo = getMovementInfo(movement.movementType, movement.quantity);

                    return (
                      <TableRow key={movement.id}>
                        <TableCell className="font-medium">
                          {movement.product?.name || 'Unknown Product'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={movementInfo.variant} className="flex items-center gap-1 w-fit">
                            <movementInfo.icon className="h-3 w-3" />
                            {movementInfo.label}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className={`font-medium ${movementInfo.color}`}>
                            {movementInfo.sign}{Math.abs(movement.quantity)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">
                              {movement.referenceType === 'transfer' ? `TRF-${movement.referenceId?.toString().padStart(3, '0')}` : movement.referenceId || 'N/A'}
                            </div>
                            <div className="text-gray-500 capitalize">{movement.referenceType}</div>
                          </div>
                        </TableCell>
                        <TableCell>{movement.reason || 'No reason provided'}</TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{new Date(movement.createdAt).toLocaleDateString()}</div>
                            <div className="text-gray-500">
                              {new Date(movement.createdAt).toLocaleTimeString()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>User {movement.createdBy}</TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <TrendingUp className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No movements found</p>
                        <p className="text-sm text-gray-400">
                          {searchQuery ? 'Try adjusting your search' : 'No movements available'}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{' '}
                {pagination.totalCount} movements
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={!pagination.hasPreviousPage}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
