import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  Plus,
  Minus,
  Trash2,
  Receipt,
  Save,
  Printer,
  ChevronRight,
  ChevronLeft,
  Clock,
  FileEdit,
  XCircle,
  MessageSquare,
  Users,
} from "lucide-react";
import { SplitBillModal } from "./split-bill-modal";

// CartItem Component
interface CartItemProps {
  item: OrderItem;
  index: number;
  onUpdateQuantity: (index: number, newQuantity: number) => void;
  onRemoveItem: (index: number) => void;
  onUpdateNotes: (index: number, notes: string) => void;
}

function CartItem({ item, index, onUpdateQuantity, onRemoveItem, onUpdateNotes }: CartItemProps) {
  const [showNotes, setShowNotes] = useState(false);
  const [notesValue, setNotesValue] = useState(item.notes || "");

  const handleNotesChange = (value: string) => {
    setNotesValue(value);
    onUpdateNotes(index, value);
  };

  return (
    <div className="pb-3 border-b last:border-0">
      {/* Main item row */}
      <div className="flex items-start justify-between">
        <div className="flex-1 pr-4">
          <p className="font-medium">{item.productName}</p>
          <p className="text-sm text-muted-foreground">
            ₹{item.unitPrice.toFixed(2)} each
          </p>
          {item.notes && (
            <p className="text-xs text-blue-600 mt-1 italic">
              Note: {item.notes}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 rounded-full"
            onClick={() => onUpdateQuantity(index, item.quantity - 1)}
            disabled={item.quantity <= 1}
          >
            <Minus className="h-3 w-3" />
          </Button>
          <span className="w-8 text-center font-medium">
            {item.quantity}
          </span>
          <Button
            variant="outline"
            size="icon"
            className="h-7 w-7 rounded-full"
            onClick={() => onUpdateQuantity(index, item.quantity + 1)}
          >
            <Plus className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-7 w-7",
              showNotes ? "bg-blue-100 text-blue-600" : "text-muted-foreground"
            )}
            onClick={() => setShowNotes(!showNotes)}
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 text-destructive hover:text-destructive hover:bg-destructive/10"
            onClick={() => onRemoveItem(index)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Notes input */}
      {showNotes && (
        <div className="mt-3">
          <Textarea
            placeholder="Add notes for this item..."
            value={notesValue}
            onChange={(e) => handleNotesChange(e.target.value)}
            className="min-h-[60px] text-sm"
          />
        </div>
      )}
    </div>
  );
}

// Define OrderItem interface
export interface OrderItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

interface PersonSplit {
  id: string;
  name: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  total: number;
}

interface CartSidebarProps {
  items: OrderItem[];
  onUpdateQuantity: (index: number, newQuantity: number) => void;
  onRemoveItem: (index: number) => void;
  onUpdateNotes: (index: number, notes: string) => void;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  discountValue: string;
  onDiscountChange: (value: string) => void;
  discountType: "percentage" | "fixed";
  onDiscountTypeChange: (type: "percentage" | "fixed") => void;
  onSubmitOrder: (options: {
    saveOnly?: boolean;
    printReceipt?: boolean;
    kotOnly?: boolean;
    printKOT?: boolean;
    draft?: boolean;
    hold?: boolean;
    cancel?: boolean;
    splitBills?: PersonSplit[];
  }) => void;
  isProcessing: boolean;
  className?: string;
  isCollapsible?: boolean;
  orderType?: string; // Add order type to determine if split bill is available
}

export function CartSidebar({
  items,
  onUpdateQuantity,
  onRemoveItem,
  onUpdateNotes,
  subtotal,
  taxAmount,
  discountAmount,
  totalAmount,
  discountValue,
  onDiscountChange,
  discountType,
  onDiscountTypeChange,
  onSubmitOrder,
  isProcessing,
  className,
  isCollapsible = false,
  orderType,
}: CartSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isSplitBillModalOpen, setIsSplitBillModalOpen] = useState(false);

  // Check if split bill is available (only for dine-in orders with items)
  const isSplitBillAvailable = orderType === "dine_in" && items.length > 0;

  const handleSplitBillConfirm = (splits: PersonSplit[]) => {
    onSubmitOrder({ splitBills: splits });
    setIsSplitBillModalOpen(false);
  };

  if (isCollapsible && isCollapsed) {
    return (
      <div className="fixed right-0 top-1/2 -translate-y-1/2 z-10">
        <Button
          variant="default"
          size="sm"
          className="h-32 rounded-l-md rounded-r-none"
          onClick={() => setIsCollapsed(false)}
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="rotate-90 ml-1">Cart ({items.length})</span>
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col bg-white h-full", className)}>
      {/* Cart Header */}
      <div className="p-4 border-b flex justify-between items-center flex-shrink-0">
        <h2 className="text-lg font-bold">Order Summary</h2>
        <div className="flex items-center">
          {items.length > 0 && (
            <Badge variant="secondary" className="mr-2">
              {items.length} {items.length === 1 ? "item" : "items"}
            </Badge>
          )}
          {isCollapsible && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8"
              onClick={() => setIsCollapsed(true)}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Cart Items - Scrollable */}
      <div className="flex-1 overflow-y-auto px-4 py-2">
        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <p>Your cart is empty</p>
            <p className="text-sm mt-2">Add items to create an order</p>
          </div>
        ) : (
          <div className="space-y-4 pb-4">
            {items.map((item, index) => (
              <CartItem
                key={`${item.productId}-${index}`}
                item={item}
                index={index}
                onUpdateQuantity={onUpdateQuantity}
                onRemoveItem={onRemoveItem}
                onUpdateNotes={onUpdateNotes}
              />
            ))}
          </div>
        )}
      </div>

      {/* Fixed Bottom Section - Order Totals and Action Buttons */}
      <div className="flex-shrink-0 bg-white border-t">
        {/* Order Totals - Always Visible */}
        {items.length > 0 && (
          <div className="px-4 py-3">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Subtotal</span>
                <span>₹{subtotal.toFixed(2)}</span>
              </div>

              {/* Discount Input */}
              <div className="flex items-center space-x-2">
                <span className="text-muted-foreground">Discount</span>
                <div className="flex-1 flex space-x-2">
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={discountValue}
                    onChange={(e) => onDiscountChange(e.target.value)}
                    className="w-20 text-right"
                  />
                  <select
                    value={discountType}
                    onChange={(e) =>
                      onDiscountTypeChange(
                        e.target.value as "percentage" | "fixed"
                      )
                    }
                    className="h-9 rounded-md border border-input px-3"
                  >
                    <option value="percentage">%</option>
                    <option value="fixed">₹</option>
                  </select>
                </div>
                <span>-₹{discountAmount.toFixed(2)}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground">Tax</span>
                <span>₹{taxAmount.toFixed(2)}</span>
              </div>

              <Separator />

              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>₹{totalAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons - Always Visible */}
        <div className="p-2 border-t space-y-1 pos-cart-actions">
        {/* Draft, Hold, Cancel Buttons */}
        <div className="grid grid-cols-3 gap-2">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onSubmitOrder({ draft: true })}
            disabled={isProcessing || items.length === 0}
          >
            <FileEdit className="mr-2 h-4 w-4" />
            Draft
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onSubmitOrder({ hold: true })}
            disabled={isProcessing || items.length === 0}
          >
            <Clock className="mr-2 h-4 w-4" />
            Hold
          </Button>
          <Button
            variant="outline"
            className="w-full bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700"
            onClick={() => onSubmitOrder({ cancel: true })}
            disabled={isProcessing || items.length === 0}
          >
            <XCircle className="mr-2 h-4 w-4" />
            Cancel
          </Button>
        </div>

        {/* KOT Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onSubmitOrder({ kotOnly: true })}
            disabled={isProcessing || items.length === 0}
          >
            <Receipt className="mr-2 h-4 w-4" />
            KOT
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onSubmitOrder({ kotOnly: true, printKOT: true })}
            disabled={isProcessing || items.length === 0}
          >
            <Receipt className="mr-2 h-4 w-4" />
            KOT & Print
          </Button>
        </div>

        {/* Split Bill Button (only for dine-in orders) */}
        {isSplitBillAvailable && (
          <div>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setIsSplitBillModalOpen(true)}
              disabled={isProcessing || items.length === 0}
            >
              <Users className="mr-2 h-4 w-4" />
              Split Bill
            </Button>
          </div>
        )}

        {/* Save Buttons - At Bottom */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onSubmitOrder({ saveOnly: true })}
            disabled={isProcessing || items.length === 0}
          >
            <Save className="mr-2 h-4 w-4" />
            Save
          </Button>
          <Button
            className="w-full"
            onClick={() => onSubmitOrder({ saveOnly: true, printReceipt: true })}
            disabled={isProcessing || items.length === 0}
          >
            <Printer className="mr-2 h-4 w-4" />
            Save & Print
          </Button>
        </div>
      </div>
      </div>

      {/* Split Bill Modal */}
      <SplitBillModal
        isOpen={isSplitBillModalOpen}
        onClose={() => setIsSplitBillModalOpen(false)}
        orderItems={items}
        subtotal={subtotal}
        taxAmount={taxAmount}
        totalAmount={totalAmount}
        onSplitConfirm={handleSplitBillConfirm}
      />
    </div>
  );
}
