import { QueryClient } from '@tanstack/react-query';

/**
 * Utility functions for managing React Query cache
 */

/**
 * Aggressively clear all inventory-related cache
 */
export const clearInventoryCache = (queryClient: QueryClient) => {
  console.log('Clearing all inventory-related cache...');
  
  // Clear specific inventory endpoints
  const inventoryEndpoints = [
    '/api/stock-transfers',
    '/api/stock-transfers/stats',
    '/api/stock-movements',
    '/api/inventory',
    '/api/products',
    '/api/branches'
  ];

  inventoryEndpoints.forEach(endpoint => {
    queryClient.removeQueries({ queryKey: [endpoint] });
    queryClient.invalidateQueries({ queryKey: [endpoint] });
  });

  // Clear any query that contains inventory-related keywords
  queryClient.removeQueries({ 
    predicate: (query) => {
      const queryKey = query.queryKey[0];
      if (typeof queryKey === 'string') {
        return queryKey.includes('stock') || 
               queryKey.includes('inventory') || 
               queryKey.includes('product') || 
               queryKey.includes('transfer') || 
               queryKey.includes('movement');
      }
      return false;
    }
  });

  // Force garbage collection of cache
  queryClient.getQueryCache().clear();
  
  console.log('Inventory cache cleared successfully');
};

/**
 * Clear all cache (nuclear option)
 */
export const clearAllCache = (queryClient: QueryClient) => {
  console.log('Clearing ALL cache (nuclear option)...');
  queryClient.clear();
  queryClient.getQueryCache().clear();
  queryClient.getMutationCache().clear();
  console.log('All cache cleared successfully');
};

/**
 * Generate a fresh query key with timestamp to prevent caching
 */
export const getFreshQueryKey = (baseKey: string, ...params: any[]) => {
  return [baseKey, ...params, Date.now()];
};

/**
 * Generate URL with cache-busting parameter
 */
export const getCacheBustingUrl = (url: string) => {
  if (typeof url !== 'string') {
    console.warn('getCacheBustingUrl received non-string URL:', url);
    return url || '';
  }
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}_t=${Date.now()}`;
};
