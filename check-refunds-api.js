import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkRefundsData() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking refunds table and data...\n');

    // Check if table exists and get structure
    const tableInfo = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'refunds' 
      ORDER BY ordinal_position;
    `);
    
    console.log('📋 Refunds table structure:');
    tableInfo.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // Check current refund count and data
    const countResult = await client.query('SELECT COUNT(*) FROM refunds');
    console.log(`\n💰 Total refunds in database: ${countResult.rows[0].count}`);

    if (parseInt(countResult.rows[0].count) > 0) {
      // Get all refunds
      const refundsResult = await client.query(`
        SELECT r.*, o.order_number 
        FROM refunds r 
        LEFT JOIN orders o ON r.order_id = o.id 
        ORDER BY r.created_at DESC 
        LIMIT 10
      `);
      
      console.log('\n📊 Recent refunds:');
      refundsResult.rows.forEach(refund => {
        console.log(`  ID: ${refund.id}, Order: ${refund.order_number || refund.order_id}, Amount: ${refund.amount}, Method: ${refund.refund_method}, Date: ${refund.created_at}`);
      });
    }

    // Check if there are any orders that could be refunded
    const ordersResult = await client.query(`
      SELECT COUNT(*) as total_orders,
             COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_orders,
             COUNT(CASE WHEN status = 'cancelled' AND payment_status = 'paid' THEN 1 END) as refundable_orders
      FROM orders
    `);
    
    console.log('\n📋 Orders summary:');
    const orderStats = ordersResult.rows[0];
    console.log(`  Total orders: ${orderStats.total_orders}`);
    console.log(`  Paid orders: ${orderStats.paid_orders}`);
    console.log(`  Refundable orders (cancelled + paid): ${orderStats.refundable_orders}`);

    // Get some sample refundable orders
    if (parseInt(orderStats.refundable_orders) > 0) {
      const refundableOrders = await client.query(`
        SELECT id, order_number, total_amount, payment_method, status, payment_status, created_at
        FROM orders 
        WHERE status = 'cancelled' AND payment_status = 'paid'
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      console.log('\n🎯 Sample refundable orders:');
      refundableOrders.rows.forEach(order => {
        console.log(`  ${order.order_number}: ₹${order.total_amount} (${order.payment_method}) - ${order.status}/${order.payment_status}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking refunds data:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

checkRefundsData();
