// Test script to verify the duplicate data fix
import { Storage } from './server/storage.js';

async function testDuplicateFix() {
  console.log('Testing duplicate data fix...');
  
  try {
    const storage = new Storage();
    
    // Test payment methods
    console.log('\n=== Testing Payment Methods ===');
    const paymentMethods = await storage.getAllPaymentMethods(undefined, 1, undefined);
    console.log('Payment methods count:', paymentMethods.length);
    console.log('Payment methods:', paymentMethods.map(pm => ({ id: pm.id, name: pm.name })));
    
    // Check for duplicates
    const paymentMethodNames = paymentMethods.map(pm => pm.name);
    const uniquePaymentMethodNames = [...new Set(paymentMethodNames)];
    console.log('Unique payment method names:', uniquePaymentMethodNames.length);
    console.log('Total payment method names:', paymentMethodNames.length);
    
    if (uniquePaymentMethodNames.length !== paymentMethodNames.length) {
      console.log('❌ DUPLICATE PAYMENT METHODS FOUND!');
      const duplicates = paymentMethodNames.filter((name, index) => paymentMethodNames.indexOf(name) !== index);
      console.log('Duplicates:', duplicates);
    } else {
      console.log('✅ No duplicate payment methods found');
    }
    
    // Test online platforms
    console.log('\n=== Testing Online Platforms ===');
    const platforms = await storage.getAllOnlinePlatforms(1, undefined);
    console.log('Platforms count:', platforms.length);
    console.log('Platforms:', platforms.map(p => ({ id: p.id, name: p.name })));
    
    // Check for duplicates
    const platformNames = platforms.map(p => p.name);
    const uniquePlatformNames = [...new Set(platformNames)];
    console.log('Unique platform names:', uniquePlatformNames.length);
    console.log('Total platform names:', platformNames.length);
    
    if (uniquePlatformNames.length !== platformNames.length) {
      console.log('❌ DUPLICATE PLATFORMS FOUND!');
      const duplicates = platformNames.filter((name, index) => platformNames.indexOf(name) !== index);
      console.log('Duplicates:', duplicates);
    } else {
      console.log('✅ No duplicate platforms found');
    }
    
    console.log('\n=== Test completed ===');
    
  } catch (error) {
    console.error('Error during test:', error);
  }
}

testDuplicateFix();
