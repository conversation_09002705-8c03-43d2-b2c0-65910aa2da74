import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import {
  ShoppingCart,
  BarChart3,
  Users,
  CreditCard,
  Package,
  TrendingUp,
  Menu,
  X,
  ArrowRight,
  CheckCircle,
  Star,
  Sparkles,
  Zap,
  Globe,
  Mail,
  Phone,
  MapPin,
  Send,
  MessageSquare,
  Clock,
  Shield,
  Award,
  Smartphone,
  Wifi,
  Database,
  Settings,
  FileText,
  PieChart,
  Target,
  Layers,
  Headphones,
  Lock,
  Truck,
  Calendar,
  DollarSign,
  Monitor,
  Cloud,
  Repeat,
  AlertTriangle,
  CheckSquare,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  Github,
  ExternalLink,
  Heart,
  Download,
  BookOpen,
  HelpCircle,
  Briefcase,
  Building,
  Users2,
  Newspaper,
  Code,
  Smartphone as MobileIcon
} from "lucide-react";

export default function LandingPage() {
  const [location, setLocation] = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const { toast } = useToast();

  // Ensure we're on the landing page route
  React.useEffect(() => {
    if (location === '/' || location === '/home' || location === '/welcome') {
      // Update URL to show /landing for consistency
      window.history.replaceState(null, '', '/landing');
    }
  }, [location]);

  // Animation on mount
  useEffect(() => {
    // Small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    // Auto-rotate features
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 6);
    }, 3000);

    return () => {
      clearTimeout(timer);
      clearInterval(interval);
    };
  }, []);

  // Mouse tracking for parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Contact form handlers
  const handleContactFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast({
        title: "Message sent successfully!",
        description: "We'll get back to you within 24 hours.",
      });

      // Reset form
      setContactForm({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      toast({
        title: "Error sending message",
        description: "Please try again later or contact us directly.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const featureCategories = [
    {
      category: "Core Operations",
      description: "Essential tools to run your restaurant efficiently",
      features: [
        {
          icon: <ShoppingCart className="w-6 h-6" />,
          title: "Advanced POS System",
          description: "Complete point-of-sale with table management, takeaway, and online orders",
          highlights: ["Multi-device support", "Offline mode", "Custom layouts", "Quick ordering"],
          color: "from-blue-600 to-cyan-600"
        },
        {
          icon: <Package className="w-6 h-6" />,
          title: "Smart Inventory",
          description: "AI-powered inventory management with automated reordering and waste tracking",
          highlights: ["Real-time tracking", "Auto-reorder", "Waste analytics", "Supplier management"],
          color: "from-green-600 to-emerald-600"
        },
        {
          icon: <Users className="w-6 h-6" />,
          title: "Customer Management",
          description: "Build lasting relationships with comprehensive customer profiles and loyalty programs",
          highlights: ["Customer profiles", "Loyalty programs", "Order history", "Preferences tracking"],
          color: "from-purple-600 to-pink-600"
        }
      ]
    },
    {
      category: "Business Intelligence",
      description: "Data-driven insights to grow your business",
      features: [
        {
          icon: <BarChart3 className="w-6 h-6" />,
          title: "Advanced Analytics",
          description: "Comprehensive reporting with sales analytics, trends, and business insights",
          highlights: ["Real-time dashboards", "Custom reports", "Trend analysis", "Performance metrics"],
          color: "from-orange-600 to-red-600"
        },
        {
          icon: <PieChart className="w-6 h-6" />,
          title: "Financial Reports",
          description: "Detailed financial reporting with profit analysis and cost management",
          highlights: ["P&L statements", "Cost analysis", "Tax reports", "Budget tracking"],
          color: "from-indigo-600 to-blue-600"
        },
        {
          icon: <Target className="w-6 h-6" />,
          title: "Performance Tracking",
          description: "Monitor KPIs, staff performance, and operational efficiency metrics",
          highlights: ["KPI monitoring", "Staff analytics", "Efficiency metrics", "Goal tracking"],
          color: "from-teal-600 to-green-600"
        }
      ]
    },
    {
      category: "Technology & Integration",
      description: "Modern technology stack for seamless operations",
      features: [
        {
          icon: <CreditCard className="w-6 h-6" />,
          title: "Payment Processing",
          description: "Accept all payment methods with secure, fast transaction processing",
          highlights: ["Multiple payment methods", "Secure processing", "Split bills", "Contactless payments"],
          color: "from-yellow-600 to-orange-600"
        },
        {
          icon: <Smartphone className="w-6 h-6" />,
          title: "Mobile Apps",
          description: "Native mobile apps for staff and customers with offline capabilities",
          highlights: ["iOS & Android apps", "Offline mode", "Push notifications", "Mobile ordering"],
          color: "from-pink-600 to-rose-600"
        },
        {
          icon: <Cloud className="w-6 h-6" />,
          title: "Cloud Infrastructure",
          description: "Reliable cloud-based system with automatic backups and 99.9% uptime",
          highlights: ["99.9% uptime", "Auto backups", "Scalable infrastructure", "Global CDN"],
          color: "from-cyan-600 to-blue-600"
        }
      ]
    }
  ];

  const stats = [
    { label: "Total Collections", value: "₹24.5M", color: "text-blue-600" },
    { label: "Pending Collections", value: "₹3.8M", color: "text-orange-600" },
    { label: "Active Agents", value: "42", color: "text-green-600" },
    { label: "Total Customers", value: "1,248", color: "text-purple-600" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Debug Info */}
      <div className="fixed top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded text-sm z-50 space-y-1">
        <div>URL: {location}</div>
        <div className={`transition-all duration-500 ${isVisible ? 'text-green-300' : 'text-red-300'}`}>
          Animations: {isVisible ? '✅ Active' : '❌ Loading'}
        </div>
        <div className="animate-pulse">🎬 Live</div>
      </div>

      {/* Navigation Header */}
      <nav className={`bg-white/90 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50 transition-all duration-700 ease-out ${
        isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className={`flex items-center space-x-3 transition-all duration-700 delay-200 ease-out ${
              isVisible ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'
            }`}>
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center transform hover:scale-110 hover:rotate-6 transition-all duration-300 shadow-lg hover:shadow-xl">
                <ShoppingCart className="w-5 h-5 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-300">
                NembooBill
              </span>
            </div>

            {/* Desktop Navigation */}
            <div className={`hidden md:flex items-center space-x-8 transition-all duration-700 delay-300 ${
              isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'
            }`}>
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-all duration-300 hover:scale-105 relative group">
                Features
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-all duration-300 hover:scale-105 relative group">
                Pricing
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <a href="#contact" className="text-gray-600 hover:text-gray-900 transition-all duration-300 hover:scale-105 relative group">
                Contact
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
              </a>
              <Button
                variant="ghost"
                onClick={() => setLocation('/login')}
                className="text-gray-600 hover:text-gray-900 hover:scale-105 transition-all duration-300"
              >
                Login
              </Button>
              <Button
                onClick={() => setLocation('/register')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 hover:scale-105 hover:shadow-lg transform transition-all duration-300 relative overflow-hidden group"
              >
                <span className="relative z-10">Get Started</span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200">
              <div className="flex flex-col space-y-4">
                <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Features
                </a>
                <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Pricing
                </a>
                <a href="#contact" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Contact
                </a>
                <Button 
                  variant="ghost" 
                  onClick={() => setLocation('/login')}
                  className="justify-start text-gray-600 hover:text-gray-900"
                >
                  Login
                </Button>
                <Button 
                  onClick={() => setLocation('/register')}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  Get Started
                </Button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div
            className="absolute top-20 left-10 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl animate-pulse"
            style={{
              transform: `translate(${mousePosition.x * 0.02}px, ${mousePosition.y * 0.02}px)`,
            }}
          ></div>
          <div
            className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/10 rounded-full blur-3xl animate-pulse"
            style={{
              transform: `translate(${mousePosition.x * -0.02}px, ${mousePosition.y * -0.02}px)`,
            }}
          ></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <Sparkles className="w-6 h-6 text-blue-400/20 animate-spin" style={{ animationDuration: '8s' }} />
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className={`space-y-8 transition-all duration-1000 delay-500 ease-out ${
              isVisible ? 'translate-x-0 opacity-100' : '-translate-x-20 opacity-0'
            }`}>
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium hover:bg-blue-200 transition-all duration-300 hover:scale-105 cursor-pointer group animate-pulse">
                <CheckCircle className="w-4 h-4 mr-2 group-hover:rotate-45 transition-transform duration-300 animate-spin" style={{ animationDuration: '3s' }} />
                Restaurant Management Simplified
                <Sparkles className="w-4 h-4 ml-2 animate-bounce" />
              </div>

              <div className="space-y-6">
                <h1 className={`text-5xl lg:text-6xl font-bold leading-tight transition-all duration-1000 delay-700 ease-out ${
                  isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
                }`}>
                  Complete Restaurant
                  <br />
                  Management
                  <br />
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-purple-600 hover:to-blue-600 transition-all duration-500 animate-pulse">
                    Made Simple
                  </span>
                </h1>

                <p className={`text-xl text-gray-600 leading-relaxed max-w-lg transition-all duration-700 delay-1300 ${
                  isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
                }`}>
                  NembooBill is a comprehensive SaaS platform for restaurants to manage
                  orders, inventory, payments, and customer relationships with ease and efficiency.
                </p>
              </div>

              <div className={`flex flex-col sm:flex-row gap-4 transition-all duration-1000 delay-1000 ease-out ${
                isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
              }`}>
                <Button
                  size="lg"
                  onClick={() => setLocation('/register')}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-8 py-4 hover:scale-105 hover:shadow-xl transform transition-all duration-300 group animate-pulse shadow-lg"
                  style={{ animationDuration: '2s' }}
                >
                  <span className="flex items-center">
                    ✨ Get Started
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-2 transition-transform duration-300 animate-bounce" />
                  </span>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  onClick={() => setLocation('/login')}
                  className="text-lg px-8 py-4 border-2 hover:scale-105 hover:shadow-lg transform transition-all duration-300 hover:border-blue-400 hover:text-blue-600"
                >
                  Login to Dashboard
                </Button>
              </div>
            </div>

            {/* Right Content - Dashboard Preview */}
            <div className={`relative transition-all duration-1000 delay-700 ${
              isVisible ? 'translate-x-0 opacity-100' : 'translate-x-20 opacity-0'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-3xl blur-3xl animate-pulse"></div>
              <Card className="relative bg-white/90 backdrop-blur-sm shadow-2xl border-0 overflow-hidden hover:shadow-3xl hover:scale-105 transition-all duration-500 group">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4 group-hover:from-purple-600 group-hover:to-blue-600 transition-all duration-500">
                  <div className="flex items-center justify-between text-white">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center hover:rotate-12 transition-transform duration-300">
                        <ShoppingCart className="w-5 h-5" />
                      </div>
                      <span className="font-semibold">NembooBill</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                    </div>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <div className="space-y-6">
                    <div className="animate-fade-in">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">Dashboard</h3>
                      <p className="text-sm text-gray-500">Welcome back, Restaurant Owner!</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {stats.map((stat, index) => (
                        <div
                          key={index}
                          className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-all duration-300 hover:scale-105 hover:shadow-md cursor-pointer group"
                          style={{ animationDelay: `${index * 200}ms` }}
                        >
                          <div className="flex items-center space-x-2 mb-2">
                            <div className={`w-3 h-3 rounded-full transition-all duration-300 group-hover:scale-125 ${
                              stat.color === 'text-blue-600' ? 'bg-blue-600' :
                              stat.color === 'text-orange-600' ? 'bg-orange-600' :
                              stat.color === 'text-green-600' ? 'bg-green-600' :
                              'bg-purple-600'
                            }`}></div>
                            <span className="text-xs text-gray-600 group-hover:text-gray-800 transition-colors duration-300">{stat.label}</span>
                          </div>
                          <div className={`text-lg font-bold transition-all duration-300 group-hover:scale-110 ${stat.color}`}>
                            {stat.value}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 hover:from-purple-50 hover:to-blue-50 transition-all duration-500">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium text-gray-700 flex items-center">
                          Collection Trends
                          <TrendingUp className="w-4 h-4 ml-2 text-green-600 animate-bounce" />
                        </span>
                        <span className="text-xs text-gray-500">Last 30 Days</span>
                      </div>
                      <div className="flex items-end justify-between space-x-1 h-16 bg-white/50 rounded p-2">
                        {[40, 65, 45, 80, 55, 90, 70, 85, 60, 95, 75, 88].map((height, index) => {
                          const barHeight = Math.max(height * 0.6, 8);
                          return (
                            <div
                              key={index}
                              className="bg-gradient-to-t from-blue-600 to-purple-600 rounded-sm w-3 hover:from-purple-600 hover:to-blue-600 transition-all duration-500 hover:scale-110 cursor-pointer animate-grow-up"
                              style={{
                                height: `${barHeight}px`,
                                minHeight: '8px',
                                animationDelay: `${index * 100}ms`,
                                animationDuration: '1s'
                              }}
                              title={`Day ${index + 1}: ${height}%`}
                            ></div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <Sparkles className="w-12 h-12 text-blue-400/20 animate-spin" style={{ animationDuration: '12s' }} />
          </div>
          {/* Floating particles */}
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="particle bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${Math.random() * 8 + 4}px`,
                height: `${Math.random() * 8 + 4}px`,
                animationDelay: `${Math.random() * 8}s`,
                animationDuration: `${Math.random() * 4 + 6}s`
              }}
            />
          ))}
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6 hover:bg-blue-200 transition-all duration-300 hover:scale-105 cursor-pointer group">
              <Zap className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              Powerful Features
              <Sparkles className="w-4 h-4 ml-2 animate-bounce" />
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
              Everything you need to run your restaurant
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From advanced POS systems to AI-powered analytics, NembooBill provides a comprehensive suite of tools
              designed to streamline operations, boost efficiency, and drive growth for restaurants of all sizes.
            </p>
          </div>

          {/* Feature Categories */}
          <div className="space-y-20">
            {featureCategories.map((category, categoryIndex) => (
              <div key={categoryIndex} className="space-y-12">
                {/* Category Header */}
                <div className="text-center">
                  <h3 className="text-3xl font-bold text-gray-900 mb-4">
                    {category.category}
                  </h3>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    {category.description}
                  </p>
                </div>

                {/* Features Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {category.features.map((feature, featureIndex) => (
                    <Card
                      key={featureIndex}
                      className="group relative bg-white/80 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 cursor-pointer overflow-hidden"
                      onMouseEnter={() => setActiveFeature(categoryIndex * 3 + featureIndex)}
                      style={{
                        animationDelay: `${featureIndex * 200}ms`,
                      }}
                    >
                      {/* Gradient overlay */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

                      <CardContent className="p-8 relative z-10">
                        {/* Icon */}
                        <div className={`w-16 h-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center text-white mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg group-hover:shadow-xl`}>
                          {feature.icon}
                        </div>

                        {/* Title & Description */}
                        <h4 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                          {feature.title}
                        </h4>
                        <p className="text-gray-600 leading-relaxed mb-6 group-hover:text-gray-700 transition-colors duration-300">
                          {feature.description}
                        </p>

                        {/* Feature Highlights */}
                        <div className="space-y-2">
                          {feature.highlights.map((highlight, highlightIndex) => (
                            <div key={highlightIndex} className="flex items-center text-sm text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
                              <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                              <span>{highlight}</span>
                            </div>
                          ))}
                        </div>

                        {/* Hover effect indicator */}
                        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${feature.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left`}></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Feature Benefits Section */}
          <div className="mt-20 pt-16 border-t border-gray-200">
            <div className="text-center mb-16">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose NembooBill?
              </h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Join thousands of successful restaurants that have transformed their operations with our platform
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: <Shield className="w-8 h-8" />,
                  title: "Enterprise Security",
                  description: "Bank-level security with end-to-end encryption",
                  stat: "99.9%",
                  statLabel: "Uptime"
                },
                {
                  icon: <Headphones className="w-8 h-8" />,
                  title: "24/7 Support",
                  description: "Round-the-clock customer support and assistance",
                  stat: "<2min",
                  statLabel: "Response Time"
                },
                {
                  icon: <Repeat className="w-8 h-8" />,
                  title: "Easy Integration",
                  description: "Seamless integration with existing systems",
                  stat: "50+",
                  statLabel: "Integrations"
                },
                {
                  icon: <TrendingUp className="w-8 h-8" />,
                  title: "Proven Results",
                  description: "Average 30% increase in operational efficiency",
                  stat: "30%",
                  statLabel: "Efficiency Boost"
                }
              ].map((benefit, index) => (
                <Card key={index} className="text-center bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
                  <CardContent className="p-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center text-white mx-auto mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300">
                      {benefit.icon}
                    </div>
                    <div className="text-3xl font-bold text-blue-600 mb-2 group-hover:scale-110 transition-transform duration-300">
                      {benefit.stat}
                    </div>
                    <div className="text-sm text-gray-500 mb-4">{benefit.statLabel}</div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                      {benefit.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-20 text-center">
            <Card className="bg-gradient-to-r from-blue-600 to-purple-600 border-0 shadow-2xl overflow-hidden">
              <CardContent className="p-12 relative">
                {/* Background decoration */}
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

                <div className="relative z-10">
                  <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                    Ready to transform your restaurant?
                  </h3>
                  <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Join over 10,000 restaurants worldwide that trust NembooBill to power their operations
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      size="lg"
                      onClick={() => setLocation('/register')}
                      className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4 font-semibold hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      <Sparkles className="w-5 h-5 mr-2" />
                      Start Free Trial
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </Button>
                    <Button
                      size="lg"
                      variant="outline"
                      onClick={() => setLocation('/login')}
                      className="border-2 border-white/30 text-white hover:bg-white/10 text-lg px-8 py-4 font-semibold hover:scale-105 transform transition-all duration-300 backdrop-blur-sm"
                    >
                      <Calendar className="w-5 h-5 mr-2" />
                      Schedule Demo
                    </Button>
                  </div>
                  <div className="mt-6 text-sm text-blue-200">
                    ✨ No credit card required • 🚀 Setup in minutes • 📞 Free support included
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Trusted by restaurants worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our customers have to say about NembooBill
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "Restaurant Owner",
                restaurant: "The Golden Spoon",
                content: "NembooBill transformed how we manage our restaurant. The POS system is intuitive and the reporting features help us make better business decisions.",
                rating: 5
              },
              {
                name: "Michael Chen",
                role: "Operations Manager",
                restaurant: "Spice Garden",
                content: "The inventory management feature alone has saved us thousands. We never run out of ingredients and waste has decreased significantly.",
                rating: 5
              },
              {
                name: "Emily Rodriguez",
                role: "Franchise Owner",
                restaurant: "Pizza Corner",
                content: "Managing multiple locations is now effortless. The centralized dashboard gives me complete visibility across all our restaurants.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg bg-white">
                <CardContent className="p-8">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-6 italic">
                    "{testimonial.content}"
                  </p>
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.role}</div>
                    <div className="text-sm text-blue-600">{testimonial.restaurant}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 right-10 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-10 w-80 h-80 bg-purple-400/5 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
            <DollarSign className="w-16 h-16 text-green-400/10 animate-pulse" />
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium mb-6 hover:bg-green-200 transition-all duration-300 hover:scale-105 cursor-pointer group">
              <DollarSign className="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-300" />
              Transparent Pricing
              <Sparkles className="w-4 h-4 ml-2 animate-bounce" />
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 bg-gradient-to-r from-gray-900 via-blue-800 to-green-800 bg-clip-text text-transparent">
              Simple, transparent pricing
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              Choose the perfect plan for your restaurant. All plans include our core features with no hidden fees.
              Start with a 14-day free trial, no credit card required.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center space-x-4 mb-12">
              <span className={`text-lg font-medium transition-colors duration-300 ${billingCycle === 'monthly' ? 'text-gray-900' : 'text-gray-500'}`}>
                Monthly
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
                className={`relative inline-flex h-8 w-16 items-center rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  billingCycle === 'yearly' ? 'bg-gradient-to-r from-blue-600 to-purple-600' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-6 w-6 transform rounded-full bg-white transition-transform duration-300 ${
                    billingCycle === 'yearly' ? 'translate-x-9' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`text-lg font-medium transition-colors duration-300 ${billingCycle === 'yearly' ? 'text-gray-900' : 'text-gray-500'}`}>
                Yearly
              </span>
              {billingCycle === 'yearly' && (
                <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full animate-pulse">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Save 20%
                </span>
              )}
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {[
              {
                name: "Starter",
                description: "Perfect for small cafes and food trucks",
                monthlyPrice: 999,
                yearlyPrice: 799,
                currency: "₹",
                icon: <ShoppingCart className="w-8 h-8" />,
                color: "from-green-600 to-emerald-600",
                features: [
                  "Up to 5 tables",
                  "Basic POS system",
                  "Inventory tracking",
                  "Basic reports",
                  "Email support",
                  "Mobile app access",
                  "Payment processing"
                ],
                limits: {
                  tables: "5 tables",
                  orders: "500 orders/month",
                  users: "2 users"
                },
                popular: false,
                recommended: false
              },
              {
                name: "Professional",
                description: "Best for growing restaurants",
                monthlyPrice: 2499,
                yearlyPrice: 1999,
                currency: "₹",
                icon: <TrendingUp className="w-8 h-8" />,
                color: "from-blue-600 to-purple-600",
                features: [
                  "Unlimited tables",
                  "Advanced POS system",
                  "Full inventory management",
                  "Advanced analytics",
                  "Customer management",
                  "Priority support",
                  "Staff management",
                  "Kitchen display system",
                  "Online ordering"
                ],
                limits: {
                  tables: "Unlimited",
                  orders: "5,000 orders/month",
                  users: "10 users"
                },
                popular: true,
                recommended: false
              },
              {
                name: "Enterprise",
                description: "For multi-location restaurants",
                monthlyPrice: 4999,
                yearlyPrice: 3999,
                currency: "₹",
                icon: <Building className="w-8 h-8" />,
                color: "from-purple-600 to-pink-600",
                features: [
                  "Multiple locations",
                  "Advanced reporting",
                  "Custom integrations",
                  "Dedicated support",
                  "API access",
                  "White-label options",
                  "Advanced security",
                  "Custom workflows",
                  "Training & onboarding"
                ],
                limits: {
                  tables: "Unlimited",
                  orders: "Unlimited",
                  users: "Unlimited"
                },
                popular: false,
                recommended: true
              },
              {
                name: "Custom",
                description: "Tailored solutions for large enterprises",
                monthlyPrice: null,
                yearlyPrice: null,
                currency: "",
                icon: <Settings className="w-8 h-8" />,
                color: "from-gray-600 to-gray-800",
                features: [
                  "Everything in Enterprise",
                  "Custom development",
                  "Dedicated infrastructure",
                  "24/7 phone support",
                  "SLA guarantees",
                  "Custom integrations",
                  "Advanced security",
                  "Compliance support",
                  "Priority feature requests"
                ],
                limits: {
                  tables: "Unlimited",
                  orders: "Unlimited",
                  users: "Unlimited"
                },
                popular: false,
                recommended: false
              }
            ].map((plan, index) => {
              const currentPrice = billingCycle === 'yearly' ? plan.yearlyPrice : plan.monthlyPrice;
              const isSelected = selectedPlan === plan.name;

              return (
                <Card
                  key={index}
                  className={`relative border-2 transition-all duration-500 hover:scale-105 cursor-pointer group ${
                    plan.popular
                      ? 'border-blue-500 shadow-2xl bg-gradient-to-br from-blue-50 to-purple-50'
                      : plan.recommended
                      ? 'border-purple-500 shadow-xl bg-gradient-to-br from-purple-50 to-pink-50'
                      : isSelected
                      ? 'border-green-500 shadow-xl bg-gradient-to-br from-green-50 to-emerald-50'
                      : 'border-gray-200 hover:border-gray-300 hover:shadow-xl bg-white'
                  }`}
                  onClick={() => setSelectedPlan(plan.name)}
                >
                  {/* Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                      <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                        <Star className="w-4 h-4 inline mr-1" />
                        Most Popular
                      </span>
                    </div>
                  )}

                  {/* Recommended Badge */}
                  {plan.recommended && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                      <span className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                        <Award className="w-4 h-4 inline mr-1" />
                        Recommended
                      </span>
                    </div>
                  )}

                  <CardContent className="p-8">
                    {/* Plan Header */}
                    <div className="text-center mb-8">
                      <div className={`w-16 h-16 bg-gradient-to-br ${plan.color} rounded-2xl flex items-center justify-center text-white mx-auto mb-4 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg`}>
                        {plan.icon}
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">{plan.description}</p>
                    </div>

                    {/* Pricing */}
                    <div className="text-center mb-8">
                      {currentPrice ? (
                        <>
                          <div className="flex items-baseline justify-center mb-2">
                            <span className="text-4xl font-bold text-gray-900">{plan.currency}{currentPrice.toLocaleString()}</span>
                            <span className="text-gray-600 ml-1">/{billingCycle === 'yearly' ? 'year' : 'month'}</span>
                          </div>
                          {billingCycle === 'yearly' && plan.monthlyPrice && (
                            <div className="text-sm text-gray-500">
                              <span className="line-through">{plan.currency}{plan.monthlyPrice.toLocaleString()}/month</span>
                              <span className="text-green-600 font-medium ml-2">Save 20%</span>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-center">
                          <span className="text-3xl font-bold text-gray-900">Custom</span>
                          <p className="text-gray-600 text-sm mt-1">Contact us for pricing</p>
                        </div>
                      )}
                    </div>

                    {/* Usage Limits */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-6">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">Usage Limits</h4>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Tables:</span>
                          <span className="font-medium">{plan.limits.tables}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Orders:</span>
                          <span className="font-medium">{plan.limits.orders}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Users:</span>
                          <span className="font-medium">{plan.limits.users}</span>
                        </div>
                      </div>
                    </div>

                    {/* Features List */}
                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-600 text-sm leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {/* CTA Button */}
                    <div className="space-y-4">
                      {currentPrice ? (
                        <Button
                          className={`w-full py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg ${
                            plan.popular
                              ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                              : plan.recommended
                              ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white'
                              : isSelected
                              ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white'
                              : 'bg-gray-900 hover:bg-gray-800 text-white'
                          }`}
                          onClick={() => setLocation('/register')}
                        >
                          <Sparkles className="w-4 h-4 mr-2" />
                          Start Free Trial
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          className="w-full py-3 font-semibold border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 hover:scale-105"
                          onClick={() => setLocation('#contact')}
                        >
                          <MessageSquare className="w-4 h-4 mr-2" />
                          Contact Sales
                        </Button>
                      )}

                      {currentPrice && (
                        <p className="text-xs text-gray-500 text-center">
                          14-day free trial • No credit card required
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Additional Information */}
          <div className="mt-20 space-y-16">
            {/* Feature Comparison */}
            <div className="text-center">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Compare Plans
              </h3>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                All plans include our core features. Choose based on your restaurant size and needs.
              </p>

              <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Features</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Starter</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 bg-blue-100">
                          Professional
                          <span className="block text-xs text-blue-600 font-normal">Most Popular</span>
                        </th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Enterprise</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Custom</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {[
                        { feature: "POS System", starter: "Basic", professional: "Advanced", enterprise: "Advanced", custom: "Custom" },
                        { feature: "Tables", starter: "5", professional: "Unlimited", enterprise: "Unlimited", custom: "Unlimited" },
                        { feature: "Inventory Management", starter: "Basic", professional: "Full", enterprise: "Advanced", custom: "Custom" },
                        { feature: "Analytics & Reports", starter: "Basic", professional: "Advanced", enterprise: "Enterprise", custom: "Custom" },
                        { feature: "Customer Management", starter: "❌", professional: "✅", enterprise: "✅", custom: "✅" },
                        { feature: "Multi-location Support", starter: "❌", professional: "❌", enterprise: "✅", custom: "✅" },
                        { feature: "API Access", starter: "❌", professional: "❌", enterprise: "✅", custom: "✅" },
                        { feature: "Custom Integrations", starter: "❌", professional: "❌", enterprise: "✅", custom: "✅" },
                        { feature: "Support", starter: "Email", professional: "Priority", enterprise: "Dedicated", custom: "24/7 Phone" }
                      ].map((row, index) => (
                        <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">{row.feature}</td>
                          <td className="px-6 py-4 text-sm text-gray-600 text-center">{row.starter}</td>
                          <td className="px-6 py-4 text-sm text-gray-600 text-center bg-blue-50">{row.professional}</td>
                          <td className="px-6 py-4 text-sm text-gray-600 text-center">{row.enterprise}</td>
                          <td className="px-6 py-4 text-sm text-gray-600 text-center">{row.custom}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* FAQ Section */}
            <div className="text-center">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h3>
              <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
                Got questions? We've got answers. Here are some common questions about our pricing.
              </p>

              <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
                {[
                  {
                    question: "Can I change plans anytime?",
                    answer: "Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate the billing."
                  },
                  {
                    question: "Is there a setup fee?",
                    answer: "No setup fees, ever. We believe in transparent pricing with no hidden costs or surprise charges."
                  },
                  {
                    question: "What payment methods do you accept?",
                    answer: "We accept all major credit cards, debit cards, UPI, and bank transfers. All payments are processed securely."
                  },
                  {
                    question: "Do you offer refunds?",
                    answer: "Yes, we offer a 30-day money-back guarantee. If you're not satisfied, we'll refund your payment in full."
                  },
                  {
                    question: "Is my data secure?",
                    answer: "Absolutely. We use bank-level encryption and are SOC 2 compliant. Your data is backed up daily and stored securely."
                  },
                  {
                    question: "Can I get a demo before purchasing?",
                    answer: "Of course! We offer free demos and a 14-day trial. No credit card required to get started."
                  }
                ].map((faq, index) => (
                  <Card key={index} className="p-6 hover:shadow-lg transition-all duration-300 hover:scale-105 border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-3 flex items-start">
                      <HelpCircle className="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                      {faq.question}
                    </h4>
                    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                  </Card>
                ))}
              </div>
            </div>

            {/* Final CTA */}
            <div className="text-center">
              <Card className="bg-gradient-to-r from-blue-600 to-purple-600 border-0 shadow-2xl overflow-hidden max-w-4xl mx-auto">
                <CardContent className="p-12 relative">
                  {/* Background decoration */}
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
                  <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>

                  <div className="relative z-10">
                    <h3 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                      Ready to get started?
                    </h3>
                    <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                      Join over 10,000 restaurants worldwide. Start your free trial today and see the difference NembooBill can make.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        size="lg"
                        onClick={() => setLocation('/register')}
                        className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4 font-semibold hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl"
                      >
                        <Sparkles className="w-5 h-5 mr-2" />
                        Start Free Trial
                        <ArrowRight className="w-5 h-5 ml-2" />
                      </Button>
                      <Button
                        size="lg"
                        variant="outline"
                        onClick={() => setLocation('#contact')}
                        className="border-2 border-white/30 text-white hover:bg-white/10 text-lg px-8 py-4 font-semibold hover:scale-105 transform transition-all duration-300 backdrop-blur-sm"
                      >
                        <Calendar className="w-5 h-5 mr-2" />
                        Schedule Demo
                      </Button>
                    </div>
                    <div className="mt-6 text-sm text-blue-200">
                      ✨ 14-day free trial • 💳 No credit card required • 🚀 Setup in minutes
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <Sparkles className="w-8 h-8 text-blue-400/20 animate-spin" style={{ animationDuration: '8s' }} />
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 text-blue-200 rounded-full text-sm font-medium mb-6 animate-pulse">
              <MessageSquare className="w-4 h-4 mr-2" />
              Get in Touch
              <Sparkles className="w-4 h-4 ml-2 animate-bounce" />
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Ready to transform your restaurant?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Join thousands of restaurants already using NembooBill to streamline their operations.
              Get started today or reach out to learn more about our solutions.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-start">
            {/* Contact Form */}
            <Card className="bg-white/10 backdrop-blur-md border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-white flex items-center">
                  <Send className="w-6 h-6 mr-3 text-blue-400" />
                  Send us a message
                </CardTitle>
                <p className="text-gray-300">
                  Fill out the form below and we'll get back to you within 24 hours
                </p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleContactFormSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-white font-medium">
                        Full Name *
                      </Label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={contactForm.name}
                        onChange={handleContactFormChange}
                        className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-blue-400/50"
                        placeholder="Your full name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white font-medium">
                        Email Address *
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={contactForm.email}
                        onChange={handleContactFormChange}
                        className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-blue-400/50"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-white font-medium">
                        Phone Number
                      </Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={contactForm.phone}
                        onChange={handleContactFormChange}
                        className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-blue-400/50"
                        placeholder="+****************"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="subject" className="text-white font-medium">
                        Subject *
                      </Label>
                      <Input
                        id="subject"
                        name="subject"
                        type="text"
                        required
                        value={contactForm.subject}
                        onChange={handleContactFormChange}
                        className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-blue-400/50"
                        placeholder="How can we help you?"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-white font-medium">
                      Message *
                    </Label>
                    <Textarea
                      id="message"
                      name="message"
                      required
                      rows={5}
                      value={contactForm.message}
                      onChange={handleContactFormChange}
                      className="bg-white/10 border-white/30 text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-blue-400/50 resize-none"
                      placeholder="Tell us about your restaurant and how we can help..."
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg py-3 font-semibold hover:scale-105 transform transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        Send Message
                        <Send className="w-5 h-5 ml-2" />
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information & CTA */}
            <div className="space-y-8">
              {/* Contact Info Cards */}
              <div className="grid gap-6">
                <Card className="bg-white/10 backdrop-blur-md border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 group">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Mail className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">Email Us</h3>
                        <p className="text-gray-300 text-sm mb-2">Get in touch via email</p>
                        <a
                          href="mailto:<EMAIL>"
                          className="text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium"
                        >
                          <EMAIL>
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/10 backdrop-blur-md border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 group">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Phone className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">Call Us</h3>
                        <p className="text-gray-300 text-sm mb-2">Speak with our team</p>
                        <a
                          href="tel:+1234567890"
                          className="text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium"
                        >
                          +1 (234) 567-890
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/10 backdrop-blur-md border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 group">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <MapPin className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">Visit Us</h3>
                        <p className="text-gray-300 text-sm mb-2">Our office location</p>
                        <p className="text-blue-400 font-medium">
                          123 Business District<br />
                          Tech City, TC 12345
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Business Hours & Features */}
              <Card className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-md border-white/20 shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <Clock className="w-6 h-6 text-blue-400 mr-3" />
                    <h3 className="text-lg font-semibold text-white">Business Hours</h3>
                  </div>
                  <div className="space-y-2 text-gray-300">
                    <div className="flex justify-between">
                      <span>Monday - Friday</span>
                      <span className="text-blue-400 font-medium">9:00 AM - 6:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Saturday</span>
                      <span className="text-blue-400 font-medium">10:00 AM - 4:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sunday</span>
                      <span className="text-gray-400">Closed</span>
                    </div>
                  </div>

                  <div className="border-t border-white/20 mt-6 pt-6">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="group">
                        <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                          <Shield className="w-5 h-5 text-green-400" />
                        </div>
                        <p className="text-xs text-gray-300">Secure & Reliable</p>
                      </div>
                      <div className="group">
                        <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                          <Clock className="w-5 h-5 text-blue-400" />
                        </div>
                        <p className="text-xs text-gray-300">24/7 Support</p>
                      </div>
                      <div className="group">
                        <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-300">
                          <Award className="w-5 h-5 text-purple-400" />
                        </div>
                        <p className="text-xs text-gray-300">Award Winning</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Buttons */}
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-gray-300 mb-6">
                    Ready to get started? Choose your preferred way to begin your journey with NembooBill.
                  </p>
                </div>

                <div className="grid gap-4">
                  <Button
                    size="lg"
                    onClick={() => setLocation('/register')}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg py-4 font-semibold hover:scale-105 transform transition-all duration-300 shadow-xl hover:shadow-2xl group"
                  >
                    <div className="flex items-center justify-center">
                      <Sparkles className="w-5 h-5 mr-2 group-hover:rotate-45 transition-transform duration-300" />
                      Start Free Trial
                      <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-2 transition-transform duration-300" />
                    </div>
                  </Button>

                  <Button
                    size="lg"
                    variant="outline"
                    onClick={() => setLocation('/login')}
                    className="w-full border-2 border-white/30 text-white hover:bg-white hover:text-gray-900 text-lg py-4 font-semibold hover:scale-105 transform transition-all duration-300 backdrop-blur-sm group"
                  >
                    <div className="flex items-center justify-center">
                      <MessageSquare className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                      Schedule Demo
                    </div>
                  </Button>
                </div>

                <div className="text-center pt-4">
                  <p className="text-sm text-gray-400">
                    🔒 No credit card required • ✨ Setup in minutes • 🚀 Cancel anytime
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-16 pt-12 border-t border-white/20">
            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div className="group">
                <div className="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                  10,000+
                </div>
                <div className="text-gray-300 text-sm">Happy Restaurants</div>
              </div>
              <div className="group">
                <div className="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                  99.9%
                </div>
                <div className="text-gray-300 text-sm">Uptime Guarantee</div>
              </div>
              <div className="group">
                <div className="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                  24/7
                </div>
                <div className="text-gray-300 text-sm">Customer Support</div>
              </div>
              <div className="group">
                <div className="text-3xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                  &lt; 24h
                </div>
                <div className="text-gray-300 text-sm">Response Time</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-96 h-96 bg-blue-600/5 rounded-full blur-3xl -translate-x-48 -translate-y-48"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-purple-600/5 rounded-full blur-3xl translate-x-40 translate-y-40"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <Sparkles className="w-16 h-16 text-blue-400/10 animate-spin" style={{ animationDuration: '20s' }} />
          </div>
        </div>

        <div className="relative z-10">
          {/* Main Footer Content */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="grid lg:grid-cols-5 md:grid-cols-3 gap-8">
              {/* Company Info */}
              <div className="lg:col-span-2">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-300">
                    <ShoppingCart className="w-7 h-7 text-white" />
                  </div>
                  <div>
                    <span className="text-3xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                      NembooBill
                    </span>
                    <p className="text-sm text-gray-400">Restaurant Management Platform</p>
                  </div>
                </div>

                <p className="text-gray-300 leading-relaxed mb-6 max-w-md">
                  Empowering restaurants worldwide with cutting-edge technology. From small cafes to enterprise chains,
                  we provide the tools you need to succeed in the modern food service industry.
                </p>

                {/* Social Media Links */}
                <div className="space-y-4">
                  <h5 className="text-lg font-semibold text-white">Follow Us</h5>
                  <div className="flex space-x-4">
                    {[
                      { icon: <Facebook className="w-5 h-5" />, href: "#", label: "Facebook", color: "hover:bg-blue-600" },
                      { icon: <Twitter className="w-5 h-5" />, href: "#", label: "Twitter", color: "hover:bg-sky-500" },
                      { icon: <Instagram className="w-5 h-5" />, href: "#", label: "Instagram", color: "hover:bg-pink-600" },
                      { icon: <Linkedin className="w-5 h-5" />, href: "#", label: "LinkedIn", color: "hover:bg-blue-700" },
                      { icon: <Youtube className="w-5 h-5" />, href: "#", label: "YouTube", color: "hover:bg-red-600" },
                      { icon: <Github className="w-5 h-5" />, href: "#", label: "GitHub", color: "hover:bg-gray-700" }
                    ].map((social, index) => (
                      <a
                        key={index}
                        href={social.href}
                        aria-label={social.label}
                        className={`w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 transition-all duration-300 hover:text-white hover:scale-110 hover:shadow-lg ${social.color}`}
                      >
                        {social.icon}
                      </a>
                    ))}
                  </div>
                </div>
              </div>

              {/* Product Links */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-6 flex items-center">
                  <Package className="w-5 h-5 mr-2 text-blue-400" />
                  Product
                </h4>
                <ul className="space-y-3">
                  {[
                    { name: "Features", href: "#features", icon: <Zap className="w-4 h-4" /> },
                    { name: "Pricing", href: "#pricing", icon: <DollarSign className="w-4 h-4" /> },
                    { name: "Integrations", href: "#", icon: <Layers className="w-4 h-4" /> },
                    { name: "API Documentation", href: "#", icon: <Code className="w-4 h-4" /> },
                    { name: "Mobile Apps", href: "#", icon: <MobileIcon className="w-4 h-4" /> },
                    { name: "Security", href: "#", icon: <Shield className="w-4 h-4" /> }
                  ].map((item, index) => (
                    <li key={index}>
                      <a
                        href={item.href}
                        className="flex items-center text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 group"
                      >
                        <span className="text-blue-400 mr-2 group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </span>
                        {item.name}
                        <ExternalLink className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Company Links */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-6 flex items-center">
                  <Building className="w-5 h-5 mr-2 text-purple-400" />
                  Company
                </h4>
                <ul className="space-y-3">
                  {[
                    { name: "About Us", href: "#", icon: <Users2 className="w-4 h-4" /> },
                    { name: "Blog", href: "#", icon: <Newspaper className="w-4 h-4" /> },
                    { name: "Careers", href: "#", icon: <Briefcase className="w-4 h-4" /> },
                    { name: "Contact", href: "#contact", icon: <Mail className="w-4 h-4" /> },
                    { name: "Press Kit", href: "#", icon: <Download className="w-4 h-4" /> },
                    { name: "Partners", href: "#", icon: <Users className="w-4 h-4" /> }
                  ].map((item, index) => (
                    <li key={index}>
                      <a
                        href={item.href}
                        className="flex items-center text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 group"
                      >
                        <span className="text-purple-400 mr-2 group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </span>
                        {item.name}
                        <ExternalLink className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Support & Resources */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-6 flex items-center">
                  <Headphones className="w-5 h-5 mr-2 text-green-400" />
                  Support
                </h4>
                <ul className="space-y-3">
                  {[
                    { name: "Help Center", href: "#", icon: <HelpCircle className="w-4 h-4" /> },
                    { name: "Documentation", href: "#", icon: <BookOpen className="w-4 h-4" /> },
                    { name: "System Status", href: "#", icon: <Monitor className="w-4 h-4" /> },
                    { name: "Privacy Policy", href: "#", icon: <FileText className="w-4 h-4" /> },
                    { name: "Terms of Service", href: "#", icon: <FileText className="w-4 h-4" /> },
                    { name: "Cookie Policy", href: "#", icon: <Settings className="w-4 h-4" /> }
                  ].map((item, index) => (
                    <li key={index}>
                      <a
                        href={item.href}
                        className="flex items-center text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 group"
                      >
                        <span className="text-green-400 mr-2 group-hover:scale-110 transition-transform duration-300">
                          {item.icon}
                        </span>
                        {item.name}
                        <ExternalLink className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Newsletter Section */}
          <div className="border-t border-gray-700 pt-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2 flex items-center">
                    <Mail className="w-6 h-6 mr-3 text-blue-400" />
                    Stay Updated
                  </h3>
                  <p className="text-gray-300">
                    Get the latest updates, tips, and insights delivered to your inbox. Join thousands of restaurant owners who trust our newsletter.
                  </p>
                </div>
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Input
                      type="email"
                      placeholder="Enter your email address"
                      className="flex-1 bg-gray-800 border-gray-600 text-white placeholder:text-gray-400 focus:border-blue-400 focus:ring-blue-400/50"
                    />
                    <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-6 py-2 font-semibold hover:scale-105 transform transition-all duration-300">
                      <Send className="w-4 h-4 mr-2" />
                      Subscribe
                    </Button>
                  </div>
                  <p className="text-xs text-gray-400">
                    By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Info Bar */}
          <div className="border-t border-gray-700 pt-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid md:grid-cols-3 gap-6 text-center md:text-left">
                <div className="flex items-center justify-center md:justify-start space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <Mail className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Email us</p>
                    <a href="mailto:<EMAIL>" className="text-white hover:text-blue-400 transition-colors duration-300 font-medium">
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-center justify-center md:justify-start space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg flex items-center justify-center">
                    <Phone className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Call us</p>
                    <a href="tel:+1234567890" className="text-white hover:text-blue-400 transition-colors duration-300 font-medium">
                      +1 (234) 567-890
                    </a>
                  </div>
                </div>

                <div className="flex items-center justify-center md:justify-start space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Visit us</p>
                    <p className="text-white font-medium">
                      123 Business District, Tech City
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Copyright Section */}
          <div className="border-t border-gray-700 mt-12 pt-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div className="flex items-center space-x-4">
                  <p className="text-gray-400 text-sm">
                    &copy; 2024 NembooBill. All rights reserved.
                  </p>
                  <div className="hidden md:flex items-center space-x-1 text-gray-400 text-sm">
                    <span>Made with</span>
                    <Heart className="w-4 h-4 text-red-500 animate-pulse" />
                    <span>for restaurants worldwide</span>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <a href="#" className="hover:text-white transition-colors duration-300">Privacy</a>
                    <span>•</span>
                    <a href="#" className="hover:text-white transition-colors duration-300">Terms</a>
                    <span>•</span>
                    <a href="#" className="hover:text-white transition-colors duration-300">Cookies</a>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Globe className="w-4 h-4 text-gray-400" />
                    <select className="bg-transparent text-gray-400 text-sm border-none focus:outline-none hover:text-white transition-colors duration-300 cursor-pointer">
                      <option value="en">English</option>
                      <option value="es">Español</option>
                      <option value="fr">Français</option>
                      <option value="de">Deutsch</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Trust Badges */}
              <div className="mt-8 pt-6 border-t border-gray-800">
                <div className="flex flex-wrap justify-center items-center space-x-8 space-y-4">
                  <div className="flex items-center space-x-2 text-gray-400 text-sm">
                    <Shield className="w-5 h-5 text-green-400" />
                    <span>SOC 2 Compliant</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-400 text-sm">
                    <Lock className="w-5 h-5 text-blue-400" />
                    <span>256-bit SSL Encryption</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-400 text-sm">
                    <Award className="w-5 h-5 text-yellow-400" />
                    <span>ISO 27001 Certified</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-400 text-sm">
                    <Cloud className="w-5 h-5 text-purple-400" />
                    <span>99.9% Uptime SLA</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Floating Action Button */}
      <div className="fixed bottom-8 right-8 z-50">
        <div className="relative">
          {/* Pulsing ring effect */}
          <div className="absolute inset-0 w-16 h-16 bg-blue-400 rounded-full animate-ping opacity-20"></div>
          <div className="absolute inset-0 w-16 h-16 bg-purple-400 rounded-full animate-pulse opacity-30"></div>

          <Button
            onClick={() => setLocation('/register')}
            className="relative w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-purple-600 hover:to-blue-600 shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 animate-bounce group"
            style={{ animationDuration: '3s' }}
          >
            <Zap className="w-6 h-6 text-white group-hover:rotate-45 transition-transform duration-300 animate-pulse" />
          </Button>
        </div>
      </div>

      {/* Animated cursor follower */}
      <div
        className="fixed w-4 h-4 bg-blue-400/30 rounded-full pointer-events-none z-40 transition-all duration-300 ease-out"
        style={{
          left: `${mousePosition.x}%`,
          top: `${mousePosition.y}%`,
          transform: 'translate(-50%, -50%)',
        }}
      />
    </div>
  );
}
