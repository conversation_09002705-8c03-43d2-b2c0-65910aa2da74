import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function testStockNotifications() {
  const client = await pool.connect();
  try {
    console.log('📦 Testing Stock Notification System...\n');

    // 1. Check current products and their stock levels
    const productsResult = await client.query(`
      SELECT id, name, quantity, reorder_level, unit_of_measure 
      FROM products 
      ORDER BY id
    `);

    console.log('📋 Current Products:');
    productsResult.rows.forEach(product => {
      const status = product.quantity <= (product.reorder_level || 10) ? '🔴 LOW' : '🟢 OK';
      console.log(`   ${product.name}: ${product.quantity || 0} ${product.unit_of_measure || 'pcs'} (Min: ${product.reorder_level || 10}) ${status}`);
    });

    // 2. Create test products with different stock scenarios
    console.log('\n🧪 Creating test products for stock notifications...');

    const testProducts = [
      {
        name: 'Coffee Beans Premium',
        quantity: 2,        // Current stock: 2
        reorderLevel: 10,   // Minimum: 10 → Will trigger notification
        price: 25.00,
        unitOfMeasure: 'kg'
      },
      {
        name: 'Milk Cartons',
        quantity: 1,        // Current stock: 1  
        reorderLevel: 5,    // Minimum: 5 → Will trigger notification
        price: 3.50,
        unitOfMeasure: 'pcs'
      },
      {
        name: 'Sugar Packets',
        quantity: 0,        // Current stock: 0
        reorderLevel: 20,   // Minimum: 20 → Will trigger URGENT notification
        price: 0.50,
        unitOfMeasure: 'pcs'
      },
      {
        name: 'Paper Cups',
        quantity: 50,       // Current stock: 50
        reorderLevel: 100,  // Minimum: 100 → Will trigger notification
        price: 0.25,
        unitOfMeasure: 'pcs'
      },
      {
        name: 'Tea Bags',
        quantity: 15,       // Current stock: 15
        reorderLevel: 10,   // Minimum: 10 → Will NOT trigger (15 > 10)
        price: 1.00,
        unitOfMeasure: 'pcs'
      }
    ];

    // Get shop and user info
    const shopResult = await client.query('SELECT id FROM shops ORDER BY id LIMIT 1');
    const userResult = await client.query('SELECT id FROM users ORDER BY id LIMIT 1');
    
    if (shopResult.rows.length === 0 || userResult.rows.length === 0) {
      console.log('❌ No shop or user found. Please ensure you have shops and users set up.');
      return;
    }

    const shopId = shopResult.rows[0].id;
    const userId = userResult.rows[0].id;

    // Clear existing test products
    await client.query(`DELETE FROM products WHERE name LIKE '%Test%' OR name IN ('Coffee Beans Premium', 'Milk Cartons', 'Sugar Packets', 'Paper Cups', 'Tea Bags')`);

    // Create test products
    for (const product of testProducts) {
      await client.query(`
        INSERT INTO products (name, quantity, reorder_level, price, unit_of_measure, shop_id, created_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        product.name,
        product.quantity,
        product.reorderLevel,
        product.price,
        product.unitOfMeasure,
        shopId,
        userId
      ]);

      console.log(`   ✅ Created: ${product.name} (${product.quantity}/${product.reorderLevel})`);
    }

    // 3. Create stock notifications for low stock items
    console.log('\n🔔 Creating stock notifications...');

    for (const product of testProducts) {
      if (product.quantity <= product.reorderLevel) {
        const priority = product.quantity === 0 ? 'urgent' : 'high';
        const title = product.quantity === 0 ? 'Out of Stock Alert!' : 'Low Stock Alert';
        const message = product.quantity === 0 
          ? `Product "${product.name}" is out of stock! Please reorder immediately.`
          : `Product "${product.name}" is running low (${product.quantity} remaining, minimum: ${product.reorderLevel})`;

        // Create notification
        const notificationResult = await client.query(`
          INSERT INTO notifications (title, message, type, priority, recipient_type, recipient_id, shop_id, created_by, data)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING id
        `, [
          title,
          message,
          'stock',
          priority,
          'shop',
          shopId,
          shopId,
          userId,
          JSON.stringify({
            productName: product.name,
            currentStock: product.quantity,
            minStock: product.reorderLevel,
            unitOfMeasure: product.unitOfMeasure
          })
        ]);

        const notificationId = notificationResult.rows[0].id;

        // Create recipient record
        await client.query(`
          INSERT INTO notification_recipients (notification_id, user_id, status)
          VALUES ($1, $2, $3)
        `, [notificationId, userId, 'unread']);

        console.log(`   🔔 ${title}: ${product.name} (${product.quantity}/${product.reorderLevel}) - Priority: ${priority.toUpperCase()}`);
      } else {
        console.log(`   ✅ ${product.name}: Stock OK (${product.quantity}/${product.reorderLevel})`);
      }
    }

    // 4. Show summary
    const notificationCount = await client.query(`
      SELECT COUNT(*) as count
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE n.type = 'stock' AND nr.status = 'unread' AND n.shop_id = $1
    `, [shopId]);

    console.log(`\n📊 Summary:`);
    console.log(`   - Created ${testProducts.length} test products`);
    console.log(`   - Generated ${notificationCount.rows[0].count} stock notifications`);
    console.log(`   - Products that will trigger notifications:`);
    
    testProducts.forEach(product => {
      if (product.quantity <= product.reorderLevel) {
        const status = product.quantity === 0 ? 'OUT OF STOCK' : 'LOW STOCK';
        console.log(`     • ${product.name}: ${product.quantity}/${product.reorderLevel} ${product.unitOfMeasure} (${status})`);
      }
    });

    console.log('\n🎯 How to Test:');
    console.log('   1. Refresh your browser');
    console.log('   2. Check the notification bell - should show red badge');
    console.log('   3. Go to Inventory → Stock Management to see the products');
    console.log('   4. Try adjusting stock levels to trigger more notifications');

    console.log('\n📋 Stock Notification Rules:');
    console.log('   • Notification appears when: Current Stock ≤ Minimum Stock');
    console.log('   • Priority "HIGH" when: Stock is low but > 0');
    console.log('   • Priority "URGENT" when: Stock = 0 (out of stock)');
    console.log('   • Default minimum stock: 10 pieces (if not set)');

  } catch (error) {
    console.error('❌ Error testing stock notifications:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the test
testStockNotifications();
