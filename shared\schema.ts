import {
  pgTable,
  text,
  serial,
  integer,
  boolean,
  timestamp,
  doublePrecision,
  unique,
  jsonb
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Role definitions
export const roles = pgTable("roles", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  permissions: jsonb("permissions").notNull().$type<string[]>()
});

export const insertRoleSchema = createInsertSchema(roles).pick({
  name: true,
  permissions: true
});

export type InsertRole = z.infer<typeof insertRoleSchema>;
export type Role = typeof roles.$inferSelect;

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  roleId: integer("role_id").references(() => roles.id),
  active: boolean("active").default(true).notNull(),
  otp: text("otp"),
  otpExpiry: timestamp("otp_expiry"),
  lastLogin: timestamp("last_login")
});

export const insertUserSchema = createInsertSchema(users).pick({
  name: true,
  username: true,
  email: true,
  password: true,
  roleId: true,
  active: true,
  lastLogin: true
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Shops table
export const shops = pgTable("shops", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  shopType: text("shop_type").notNull(), // restaurant, retail, service, etc.
  taxRegistration: text("tax_registration"),
  logo: text("logo"),
  currencySymbol: text("currency_symbol").default("₹").notNull(),
  footerText: text("footer_text"),
  receiptTemplate: text("receipt_template"),
  accessCode: text("access_code").notNull().unique(), // Code for users to join this shop
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: integer("created_by").references(() => users.id).notNull()
});

export const insertShopSchema = createInsertSchema(shops).omit({
  id: true,
  createdAt: true
});

export type InsertShop = z.infer<typeof insertShopSchema>;
export type Shop = typeof shops.$inferSelect;

// User-Shop relationship table
export const userShops = pgTable("user_shops", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  role: text("role").default("member").notNull(), // owner, admin, member, etc.
  joinedAt: timestamp("joined_at").defaultNow().notNull()
});

export const insertUserShopSchema = createInsertSchema(userShops).omit({
  id: true,
  joinedAt: true
});

export type InsertUserShop = z.infer<typeof insertUserShopSchema>;
export type UserShop = typeof userShops.$inferSelect;

// Branches table
export const branches = pgTable("branches", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  isMainBranch: boolean("is_main_branch").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: integer("created_by").references(() => users.id).notNull()
});

export const insertBranchSchema = createInsertSchema(branches).omit({
  id: true,
  createdAt: true
});

export type InsertBranch = z.infer<typeof insertBranchSchema>;
export type Branch = typeof branches.$inferSelect;

// User-Branch relationship table
export const userBranches = pgTable("user_branches", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id).notNull(),
  role: text("role").default("staff").notNull(), // manager, staff, etc.
  joinedAt: timestamp("joined_at").defaultNow().notNull()
});

export const insertUserBranchSchema = createInsertSchema(userBranches).omit({
  id: true,
  joinedAt: true
});

export type InsertUserBranch = z.infer<typeof insertUserBranchSchema>;
export type UserBranch = typeof userBranches.$inferSelect;

// Keep the old shop_settings table for backward compatibility
export const shopSettings = pgTable("shop_settings", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  taxRegistration: text("tax_registration"),
  logo: text("logo"),
  currencySymbol: text("currency_symbol").default("₹").notNull(),
  footerText: text("footer_text"),
  receiptTemplate: text("receipt_template")
});

export const insertShopSettingsSchema = createInsertSchema(shopSettings).omit({
  id: true
});

export type InsertShopSettings = z.infer<typeof insertShopSettingsSchema>;
export type ShopSettings = typeof shopSettings.$inferSelect;

// Categories table
export const categories = pgTable("categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  shopId: integer("shop_id").references(() => shops.id),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertCategorySchema = createInsertSchema(categories).pick({
  name: true,
  description: true,
  shopId: true,
  branchId: true
});

export type InsertCategory = z.infer<typeof insertCategorySchema>;
export type Category = typeof categories.$inferSelect;

// Products table
export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  price: doublePrecision("price").notNull(),
  categoryId: integer("category_id").references(() => categories.id),
  image: text("image"),
  barcode: text("barcode"),
  sku: text("sku"),
  taxIncluded: boolean("tax_included").default(false),
  taxRate: doublePrecision("tax_rate"),
  active: boolean("active").default(true).notNull(),
  quantity: integer("quantity").default(0).notNull(),
  reorderLevel: integer("reorder_level").default(10).notNull(),
  minStock: integer("min_stock").default(5).notNull(),
  maxStock: integer("max_stock").default(1000).notNull(),
  unitOfMeasure: text("unit_of_measure").default("pcs").notNull(),
  shopId: integer("shop_id").references(() => shops.id),
  branchId: integer("branch_id").references(() => branches.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertProductSchema = createInsertSchema(products).pick({
  name: true,
  description: true,
  price: true,
  categoryId: true,
  image: true,
  barcode: true,
  sku: true,
  taxIncluded: true,
  taxRate: true,
  active: true,
  quantity: true,
  reorderLevel: true,
  minStock: true,
  maxStock: true,
  unitOfMeasure: true,
  shopId: true,
  branchId: true
});

// Product Branch Prices table for branch-specific pricing
export const productBranchPrices = pgTable("product_branch_prices", {
  id: serial("id").primaryKey(),
  productId: integer("product_id").references(() => products.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id).notNull(),
  price: doublePrecision("price").notNull(),
  active: boolean("active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertProductBranchPriceSchema = createInsertSchema(productBranchPrices).pick({
  productId: true,
  branchId: true,
  price: true,
  active: true
});

export type InsertProduct = z.infer<typeof insertProductSchema>;
export type Product = typeof products.$inferSelect;

export type InsertProductBranchPrice = z.infer<typeof insertProductBranchPriceSchema>;
export type ProductBranchPrice = typeof productBranchPrices.$inferSelect;

// Tables for restaurant
export const tables = pgTable("tables", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  capacity: integer("capacity").notNull(),
  status: text("status").default("available").notNull(), // available, occupied, reserved, billing
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertTableSchema = createInsertSchema(tables).pick({
  name: true,
  capacity: true,
  status: true,
  shopId: true,
  branchId: true
});

export type InsertTable = z.infer<typeof insertTableSchema>;
export type Table = typeof tables.$inferSelect;

// Customers
export const customers = pgTable("customers", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  address: text("address"),
  gstNumber: text("gst_number"),
  loyaltyPoints: integer("loyalty_points").default(0).notNull(),
  loyaltyTier: text("loyalty_tier").default("standard").notNull(), // standard, silver, gold, platinum
  lastVisitDate: timestamp("last_visit_date"),
  campaignOptIn: boolean("campaign_opt_in").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertCustomerSchema = createInsertSchema(customers).pick({
  name: true,
  phone: true,
  email: true,
  address: true,
  gstNumber: true,
  loyaltyPoints: true,
  loyaltyTier: true,
  lastVisitDate: true,
  campaignOptIn: true,
  shopId: true,
  branchId: true
});

export type InsertCustomer = z.infer<typeof insertCustomerSchema>;
export type Customer = typeof customers.$inferSelect;

// Online platforms (Swiggy, Zomato, etc.)
export const onlinePlatforms = pgTable("online_platforms", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  commissionPercent: doublePrecision("commission_percent").default(0).notNull(),
  active: boolean("active").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertOnlinePlatformSchema = createInsertSchema(onlinePlatforms).pick({
  name: true,
  commissionPercent: true,
  active: true,
  shopId: true,
  branchId: true
});

export type InsertOnlinePlatform = z.infer<typeof insertOnlinePlatformSchema>;
export type OnlinePlatform = typeof onlinePlatforms.$inferSelect;

// Orders table
export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  orderNumber: text("order_number").notNull().unique(),
  orderType: text("order_type").notNull(), // dine_in, takeaway, eat_and_pay, online
  status: text("status").notNull(), // pending, preparing, completed, cancelled, draft, hold
  tableId: integer("table_id").references(() => tables.id),
  customerId: integer("customer_id").references(() => customers.id),
  platformId: integer("platform_id").references(() => onlinePlatforms.id),
  numberOfPersons: integer("number_of_persons"), // Number of persons for dine-in orders
  subtotal: doublePrecision("subtotal").notNull(),
  taxAmount: doublePrecision("tax_amount").notNull(),
  taxRate: doublePrecision("tax_rate").default(0), // Effective tax rate for this order
  discountAmount: doublePrecision("discount_amount").default(0).notNull(),
  totalAmount: doublePrecision("total_amount").notNull(),
  paymentMethod: text("payment_method"), // cash, card, upi, etc.
  paymentStatus: text("payment_status").notNull(), // paid, pending
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertOrderSchema = createInsertSchema(orders).omit({
  id: true,
  createdAt: true
}).extend({
  orderNumber: z.string().optional() // Make orderNumber optional for server generation
});

export type InsertOrder = z.infer<typeof insertOrderSchema>;
export type Order = typeof orders.$inferSelect;

// Order items
export const orderItems = pgTable("order_items", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").references(() => orders.id).notNull(),
  productId: integer("product_id").references(() => products.id).notNull(),
  quantity: integer("quantity").notNull(),
  unitPrice: doublePrecision("unit_price").notNull(),
  totalPrice: doublePrecision("total_price").notNull(),
  notes: text("notes")
});

export const insertOrderItemSchema = createInsertSchema(orderItems).omit({
  id: true
}).extend({
  orderId: z.number().optional() // Make orderId optional for server generation
});

export type InsertOrderItem = z.infer<typeof insertOrderItemSchema>;
export type OrderItem = typeof orderItems.$inferSelect;

// Expenses
export const expenses = pgTable("expenses", {
  id: serial("id").primaryKey(),
  category: text("category").notNull(), // rent, utilities, salary, supplies, etc.
  amount: doublePrecision("amount").notNull(),
  description: text("description"),
  date: timestamp("date").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

// Create base schema
const baseExpenseSchema = createInsertSchema(expenses).omit({
  id: true
});

// Extend the schema to handle string dates
export const insertExpenseSchema = baseExpenseSchema.extend({
  date: z.string().or(z.date()).transform((val) => {
    if (typeof val === 'string') {
      return new Date(val);
    }
    return val;
  }),
  shopId: z.number().optional(),
  branchId: z.number().optional()
});

export type InsertExpense = z.infer<typeof insertExpenseSchema>;
export type Expense = typeof expenses.$inferSelect;

// Purchases
export const purchases = pgTable("purchases", {
  id: serial("id").primaryKey(),
  supplierName: text("supplier_name").notNull(),
  invoiceNumber: text("invoice_number"),
  amount: doublePrecision("amount").notNull(),
  date: timestamp("date").defaultNow().notNull(),
  notes: text("notes"),
  userId: integer("user_id").references(() => users.id).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

// Create base schema
const basePurchaseSchema = createInsertSchema(purchases).omit({
  id: true
});

// Extend the schema to handle string dates
export const insertPurchaseSchema = basePurchaseSchema.extend({
  date: z.string().or(z.date()).transform((val) => {
    if (typeof val === 'string') {
      return new Date(val);
    }
    return val;
  }),
  shopId: z.number().optional(),
  branchId: z.number().optional()
});

export type InsertPurchase = z.infer<typeof insertPurchaseSchema>;
export type Purchase = typeof purchases.$inferSelect;

// Tax settings
export const taxSettings = pgTable("tax_settings", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  rate: doublePrecision("rate").notNull(),
  active: boolean("active").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertTaxSettingSchema = createInsertSchema(taxSettings).pick({
  name: true,
  rate: true,
  active: true,
  shopId: true,
  branchId: true
});

export type InsertTaxSetting = z.infer<typeof insertTaxSettingSchema>;
export type TaxSetting = typeof taxSettings.$inferSelect;

// Discount settings
export const discountSettings = pgTable("discount_settings", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // percentage, fixed
  value: doublePrecision("value").notNull(),
  active: boolean("active").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertDiscountSettingSchema = createInsertSchema(discountSettings).pick({
  name: true,
  type: true,
  value: true,
  active: true,
  shopId: true,
  branchId: true
});

export type InsertDiscountSetting = z.infer<typeof insertDiscountSettingSchema>;
export type DiscountSetting = typeof discountSettings.$inferSelect;

// Payment methods
export const paymentMethods = pgTable("payment_methods", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  active: boolean("active").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertPaymentMethodSchema = createInsertSchema(paymentMethods).pick({
  name: true,
  description: true,
  active: true,
  shopId: true,
  branchId: true
});

export type InsertPaymentMethod = z.infer<typeof insertPaymentMethodSchema>;
export type PaymentMethod = typeof paymentMethods.$inferSelect;

// Printer settings
export const printerSettings = pgTable("printer_settings", {
  id: serial("id").primaryKey(),
  connectionType: text("connection_type").notNull(), // usb, network, bluetooth
  printerModel: text("printer_model"),
  ipAddress: text("ip_address"), // For network printers
  port: text("port"), // For network printers
  autoPrintReceipts: boolean("auto_print_receipts").default(true).notNull(),
  printOrderTickets: boolean("print_order_tickets").default(true).notNull(),
  printLogo: boolean("print_logo").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertPrinterSettingSchema = createInsertSchema(printerSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertPrinterSetting = z.infer<typeof insertPrinterSettingSchema>;
export type PrinterSetting = typeof printerSettings.$inferSelect;

// User preferences
export const userPreferences = pgTable("user_preferences", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  language: text("language").default("en").notNull(),
  theme: text("theme").default("light").notNull(),
  notificationsEnabled: boolean("notifications_enabled").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertUserPreferenceSchema = createInsertSchema(userPreferences).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertUserPreference = z.infer<typeof insertUserPreferenceSchema>;
export type UserPreference = typeof userPreferences.$inferSelect;

// Rounding settings
export const roundingSettings = pgTable("rounding_settings", {
  id: serial("id").primaryKey(),
  type: text("type").notNull(), // up, down, nearest, none
  precision: doublePrecision("precision").default(1.0).notNull(), // 0.5, 1.0, etc.
  active: boolean("active").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertRoundingSettingSchema = createInsertSchema(roundingSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertRoundingSetting = z.infer<typeof insertRoundingSettingSchema>;
export type RoundingSetting = typeof roundingSettings.$inferSelect;

// Stock Movements table
export const stockMovements = pgTable("stock_movements", {
  id: serial("id").primaryKey(),
  productId: integer("product_id").references(() => products.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id),
  movementType: text("movement_type").notNull(), // 'in', 'out', 'transfer', 'adjustment'
  quantity: integer("quantity").notNull(),
  referenceType: text("reference_type"), // 'order', 'purchase', 'transfer', 'adjustment'
  referenceId: integer("reference_id"),
  reason: text("reason"),
  notes: text("notes"),
  createdBy: integer("created_by").references(() => users.id),
  createdAt: timestamp("created_at").defaultNow().notNull()
});

export const insertStockMovementSchema = createInsertSchema(stockMovements).omit({
  id: true
});

export type InsertStockMovement = z.infer<typeof insertStockMovementSchema>;
export type StockMovement = typeof stockMovements.$inferSelect;

// Stock Transfers table
export const stockTransfers = pgTable("stock_transfers", {
  id: serial("id").primaryKey(),
  fromBranchId: integer("from_branch_id").references(() => branches.id).notNull(),
  toBranchId: integer("to_branch_id").references(() => branches.id).notNull(),
  status: text("status").default("pending").notNull(), // 'pending', 'in_transit', 'completed', 'cancelled'
  transferDate: timestamp("transfer_date").defaultNow(),
  notes: text("notes"),
  createdBy: integer("created_by").references(() => users.id).notNull(),
  approvedBy: integer("approved_by").references(() => users.id),
  completedAt: timestamp("completed_at"),
  createdAt: timestamp("created_at").defaultNow().notNull()
});

export const insertStockTransferSchema = createInsertSchema(stockTransfers).omit({
  id: true
});

export type InsertStockTransfer = z.infer<typeof insertStockTransferSchema>;
export type StockTransfer = typeof stockTransfers.$inferSelect;

// Stock Transfer Items table
export const stockTransferItems = pgTable("stock_transfer_items", {
  id: serial("id").primaryKey(),
  transferId: integer("transfer_id").references(() => stockTransfers.id).notNull(),
  productId: integer("product_id").references(() => products.id).notNull(),
  quantity: integer("quantity").notNull(),
  unitPrice: doublePrecision("unit_price"),
  notes: text("notes")
});

export const insertStockTransferItemSchema = createInsertSchema(stockTransferItems).omit({
  id: true
});

export type InsertStockTransferItem = z.infer<typeof insertStockTransferItemSchema>;
export type StockTransferItem = typeof stockTransferItems.$inferSelect;

// Refunds table
export const refunds = pgTable("refunds", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").references(() => orders.id).notNull(),
  amount: doublePrecision("amount").notNull(),
  reason: text("reason").notNull(),
  refundMethod: text("refund_method").notNull(), // cash, card, upi, etc.
  status: text("status").default("completed").notNull(), // completed, pending, failed
  createdAt: timestamp("created_at").defaultNow().notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id)
});

export const insertRefundSchema = createInsertSchema(refunds).omit({
  id: true,
  createdAt: true
});

export type InsertRefund = z.infer<typeof insertRefundSchema>;
export type Refund = typeof refunds.$inferSelect;

// Order Number Settings table
export const orderNumberSettings = pgTable("order_number_settings", {
  id: serial("id").primaryKey(),
  prefix: text("prefix").default("ORD").notNull(), // e.g., "ORD", "INV", "BILL"
  suffix: text("suffix").default("").notNull(), // e.g., "", "-A", "-MAIN"
  numberType: text("number_type").default("sequential").notNull(), // sequential, random, date_based
  sequentialStart: integer("sequential_start").default(1).notNull(), // Starting number for sequential
  currentSequence: integer("current_sequence").default(1).notNull(), // Current sequence number
  dateFormat: text("date_format").default("").notNull(), // e.g., "YYYY", "YYYYMM", "YYYYMMDD", "DDMMYY"
  separator: text("separator").default("-").notNull(), // e.g., "-", "_", "/", ""
  minDigits: integer("min_digits").default(2).notNull(), // Minimum digits for number part
  resetPeriod: text("reset_period").default("never").notNull(), // never, daily, monthly, yearly
  lastResetDate: timestamp("last_reset_date"),
  active: boolean("active").default(true).notNull(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  branchId: integer("branch_id").references(() => branches.id),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertOrderNumberSettingsSchema = createInsertSchema(orderNumberSettings).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertOrderNumberSettings = z.infer<typeof insertOrderNumberSettingsSchema>;
export type OrderNumberSettings = typeof orderNumberSettings.$inferSelect;

// Subscription Plans table
export const subscriptionPlans = pgTable("subscription_plans", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  price: doublePrecision("price").notNull(),
  billingCycle: text("billing_cycle").notNull(),
  features: jsonb("features").notNull().$type<string[]>(),
  maxBranches: integer("max_branches").default(1).notNull(),
  maxUsers: integer("max_users").default(5).notNull(),
  maxProducts: integer("max_products").default(100).notNull(),
  maxOrders: integer("max_orders").default(1000).notNull(),
  storageLimit: integer("storage_limit").default(1024).notNull(),
  supportLevel: text("support_level").default("basic").notNull(),
  active: boolean("active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertSubscriptionPlanSchema = createInsertSchema(subscriptionPlans).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertSubscriptionPlan = z.infer<typeof insertSubscriptionPlanSchema>;
export type SubscriptionPlan = typeof subscriptionPlans.$inferSelect;

// Shop Subscriptions table
export const subscriptions = pgTable("subscriptions", {
  id: serial("id").primaryKey(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  planId: integer("plan_id").references(() => subscriptionPlans.id).notNull(),
  status: text("status").default("active").notNull(), // active, inactive, cancelled, expired
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  autoRenew: boolean("auto_renew").default(true).notNull(),
  discountPercent: doublePrecision("discount_percent").default(0).notNull(),
  discountAmount: doublePrecision("discount_amount").default(0).notNull(),
  totalAmount: doublePrecision("total_amount").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: integer("created_by").references(() => users.id).notNull()
});

export const insertSubscriptionSchema = createInsertSchema(subscriptions).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertSubscription = z.infer<typeof insertSubscriptionSchema>;
export type Subscription = typeof subscriptions.$inferSelect;

// Subscription Payment Methods table
export const subscriptionPaymentMethods = pgTable("subscription_payment_methods", {
  id: serial("id").primaryKey(),
  shopId: integer("shop_id").references(() => shops.id).notNull(),
  cardholderName: text("cardholder_name").notNull(),
  cardNumber: text("card_number").notNull(), // Last 4 digits only for security
  expiryMonth: text("expiry_month").notNull(),
  expiryYear: text("expiry_year").notNull(),
  cardType: text("card_type"), // visa, mastercard, etc.
  isDefault: boolean("is_default").default(true).notNull(),
  active: boolean("active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertSubscriptionPaymentMethodSchema = createInsertSchema(subscriptionPaymentMethods).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertSubscriptionPaymentMethod = z.infer<typeof insertSubscriptionPaymentMethodSchema>;
export type SubscriptionPaymentMethod = typeof subscriptionPaymentMethods.$inferSelect;

// Notifications table
export const notifications = pgTable("notifications", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  message: text("message").notNull(),
  type: text("type").notNull(), // 'order', 'stock', 'marketing', 'system'
  priority: text("priority").default("normal").notNull(), // 'low', 'normal', 'high', 'urgent'
  status: text("status").default("unread").notNull(), // 'unread', 'read', 'archived'
  recipientType: text("recipient_type").notNull(), // 'user', 'role', 'shop', 'branch'
  recipientId: integer("recipient_id"),
  shopId: integer("shop_id").references(() => shops.id),
  branchId: integer("branch_id").references(() => branches.id),
  data: jsonb("data").$type<Record<string, any>>(),
  scheduledAt: timestamp("scheduled_at"),
  expiresAt: timestamp("expires_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: integer("created_by").references(() => users.id)
});

// Notification Recipients table
export const notificationRecipients = pgTable("notification_recipients", {
  id: serial("id").primaryKey(),
  notificationId: integer("notification_id").references(() => notifications.id, { onDelete: "cascade" }).notNull(),
  userId: integer("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  status: text("status").default("unread").notNull(), // 'unread', 'read', 'archived'
  readAt: timestamp("read_at"),
  deliveredAt: timestamp("delivered_at").defaultNow()
});

// Notification Settings table
export const notificationSettings = pgTable("notification_settings", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id, { onDelete: "cascade" }).notNull(),
  shopId: integer("shop_id").references(() => shops.id),
  notificationType: text("notification_type").notNull(),
  enabled: boolean("enabled").default(true).notNull(),
  deliveryMethods: jsonb("delivery_methods").default('["in_app"]').$type<string[]>(),
  settings: jsonb("settings").default('{}').$type<Record<string, any>>(),
}, (table) => ({
  uniqueUserShopType: unique().on(table.userId, table.shopId, table.notificationType)
}));

export const insertNotificationSchema = createInsertSchema(notifications).omit({
  id: true,
  createdAt: true
});

export const insertNotificationRecipientSchema = createInsertSchema(notificationRecipients).omit({
  id: true,
  deliveredAt: true
});

export const insertNotificationSettingSchema = createInsertSchema(notificationSettings).omit({
  id: true
});

export type Notification = typeof notifications.$inferSelect;
export type InsertNotification = typeof notifications.$inferInsert;
export type NotificationRecipient = typeof notificationRecipients.$inferSelect;
export type InsertNotificationRecipient = typeof notificationRecipients.$inferInsert;
export type NotificationSetting = typeof notificationSettings.$inferSelect;
export type InsertNotificationSetting = typeof notificationSettings.$inferInsert;

// Support Tickets table
export const supportTickets = pgTable("support_tickets", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  priority: text("priority").default("medium").notNull(), // low, medium, high
  category: text("category").default("general").notNull(), // technical, billing, feature, general
  status: text("status").default("open").notNull(), // open, in_progress, resolved, closed
  userId: integer("user_id").references(() => users.id), // Optional - for authenticated users
  shopId: integer("shop_id").references(() => shops.id), // Optional - if user is associated with a shop
  branchId: integer("branch_id").references(() => branches.id), // Optional - if user is associated with a branch
  assignedTo: integer("assigned_to").references(() => users.id), // Support agent assigned to ticket
  resolution: text("resolution"), // Resolution notes
  resolvedAt: timestamp("resolved_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const insertSupportTicketSchema = createInsertSchema(supportTickets).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  resolvedAt: true
});

export type InsertSupportTicket = z.infer<typeof insertSupportTicketSchema>;
export type SupportTicket = typeof supportTickets.$inferSelect;
