import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Calculator as <PERSON><PERSON>tor<PERSON><PERSON>, ScanLine, Search, Wifi, ArrowLeft, Server, Database, TrendingUp, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Calculator } from '@/components/utilities/calculator';
import { BarcodeScanner } from '@/components/utilities/barcode-scanner';
import { QuickSearch } from '@/components/utilities/quick-search';
import { NetworkStatus } from '@/components/utilities/network-status';
import { SystemInfo } from '@/components/utilities/system-info';
import { DatabaseStats } from '@/components/utilities/database-stats';
import { BusinessAnalytics } from '@/components/utilities/business-analytics';
import { HealthCheck } from '@/components/utilities/health-check';
import { useToast } from '@/hooks/use-toast';
import { useNetworkStatus } from '@/context/network-status-context';
import { useSelectedProducts } from '@/hooks/use-selected-products';
import { SelectedProductsWidget } from '@/components/selected-products-widget-fixed';
import { useAuth } from '@/context/auth-context';
import { useApp } from '@/context/app-context';
import { apiRequest, createOrderTwoStep, queryClient, invalidateAndRefetch } from '@/lib/queryClient';

export default function UtilitiesPage() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('calculator');
  const [systemTab, setSystemTab] = useState('health-check');
  const { token, user } = useAuth();
  const { currentShop, currentBranch } = useApp();

  // Use the global selected products hook
  const {
    selectedProducts,
    addProduct,
    removeProduct,
    clearAllProducts,
    getTotalValue,
    getTotalCount
  } = useSelectedProducts();

  // Try to use network status, but provide a fallback if context is not available
  let isOnline = navigator.onLine;
  try {
    const networkStatus = useNetworkStatus();
    isOnline = networkStatus.isOnline;
  } catch (error) {
    // If NetworkStatusProvider is not available, use default values
  }

  const handleCalculatorResult = (result: string) => {
    toast({
      title: 'Calculator Result',
      description: `Result: ${result}`,
    });
  };

  const handleBarcodeScan = async (barcode: string) => {
    console.log('Barcode scanned:', barcode);

    try {
      // Search for product using the barcode API
      const response = await apiRequest('GET', `/api/products/barcode/${encodeURIComponent(barcode)}`);

      if (response.ok) {
        const product = await response.json();
        console.log('Product found:', product);

        // Add the product to selected products
        addProduct(product);

        toast({
          title: 'Product Found! ✅',
          description: `${product.name} - ₹${product.price} added to selection`,
          className: "bg-green-50 border-green-200 text-green-800",
        });
      } else if (response.status === 404) {
        toast({
          title: 'Product Not Found',
          description: `No product found with barcode: ${barcode}. Try creating test products first.`,
          variant: "destructive",
        });
      } else {
        throw new Error('Failed to search product');
      }
    } catch (error) {
      console.error('Error searching product by barcode:', error);
      toast({
        title: 'Search Error',
        description: `Failed to search for barcode: ${barcode}`,
        variant: "destructive",
      });
    }
  };

  const createTestProducts = async () => {
    try {
      const response = await apiRequest('POST', '/api/products/create-test-products');

      if (response.ok) {
        const result = await response.json();
        toast({
          title: 'Test Products Created! ✅',
          description: `Created ${result.products.length} test products with barcodes`,
          className: "bg-green-50 border-green-200 text-green-800",
        });
      } else {
        throw new Error('Failed to create test products');
      }
    } catch (error) {
      console.error('Error creating test products:', error);
      toast({
        title: 'Error',
        description: 'Failed to create test products',
        variant: "destructive",
      });
    }
  };

  const handleProductSelect = (product: any) => {
    addProduct(product);
  };

  // Handle table assignment and navigate to orders page
  const handleTableAssign = async (tableId: number, products: any[]) => {
    console.log(`🚀🚀🚀 UTILITIES PAGE - QUICK PRODUCT SEARCH - Assigning ${products.length} products to table ${tableId}:`, products);
    console.log(`🚀🚀🚀 UTILITIES PAGE - This is the NEW CODE that should create NEW orders!`);

    // Show immediate alert to confirm function is called
    alert(`🚀 UTILITIES PAGE: Creating NEW order for table ${tableId} with ${products.length} products!`);

    try {
      // Validate required data
      if (!token || !currentShop || !user) {
        toast({
          title: "Error",
          description: "Authentication or shop information missing. Please refresh and try again.",
          variant: "destructive",
        });
        return;
      }

      // ALWAYS CREATE NEW ORDER FOR QUICK PRODUCT SEARCH
      console.log(`🆕 CREATING NEW ORDER for table ${tableId} (Quick Product Search ALWAYS creates new orders)`);

      // Get user ID directly from localStorage (more reliable)
      const currentUserStr = localStorage.getItem("current_user");
      let userId = null;

      if (currentUserStr) {
        try {
          const currentUser = JSON.parse(currentUserStr);
          userId = currentUser?.id;
        } catch (error) {
          console.error("Error parsing current_user from localStorage:", error);
        }
      }

      // Fallback to auth context if localStorage doesn't work
      if (!userId) {
        userId = user?.id;
      }



      if (!userId || !currentShop?.id) {
        console.error("Missing required data:", { userId, shopId: currentShop?.id });
        throw new Error("Missing user ID or shop ID");
      }

      // Ensure we have valid numbers for userId and shopId
      const validUserId = Number(userId);
      const validShopId = Number(currentShop.id);

      if (!validUserId || !validShopId) {
        console.error("Invalid user ID or shop ID:", { validUserId, validShopId });
        throw new Error(`Invalid user ID (${validUserId}) or shop ID (${validShopId})`);
      }

      const subtotal = products.reduce((sum, p) => sum + p.price, 0);
      const taxAmount = subtotal * 0.05; // 5% tax
      const totalAmount = subtotal + taxAmount;

      const orderData = {
        orderType: "dine_in",
        status: "pending",
        tableId: Number(tableId),
        userId: validUserId,
        shopId: validShopId,
        branchId: currentBranch?.id ? Number(currentBranch.id) : null,
        numberOfPersons: 2, // Default value
        subtotal: Number(subtotal.toFixed(2)),
        taxAmount: Number(taxAmount.toFixed(2)),
        discountAmount: 0,
        totalAmount: Number(totalAmount.toFixed(2)),
        paymentMethod: "cash",
        paymentStatus: "pending",
        notes: `Order created from Quick Product Search`
      };

      // Prepare items for the order
      const orderItems = products.map(product => ({
        productId: product.id,
        quantity: 1,
        unitPrice: product.price,
        totalPrice: product.price,
        notes: `Added from Quick Product Search`
      }));

      console.log("🆕 Creating NEW order with data:", orderData);
      console.log("🆕 Creating NEW order with items:", orderItems);

      // Use the two-step order creation process (same as POS)
      const result = await createOrderTwoStep(orderData, orderItems);

      const orderId = result.order.id;
      const orderNumber = result.order.orderNumber;
      console.log(`✅ Created NEW order: ${orderNumber} (ID: ${orderId})`);

      // CRITICAL: Invalidate all order-related cache to ensure new order appears
      console.log('🔄 Invalidating cache to ensure new order appears...');
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/orders/all',
        '/api/orders/status-summary',
        '/api/products',
        '/api/tables'
      ]);

      // Clear selected products after successful operation
      clearAllProducts();

      toast({
        title: "New Order Created! ✅",
        description: `Order ${orderNumber} created for Table ${tableId} with ${products.length} items`,
        className: "bg-green-50 border-green-200 text-green-800",
      });

      // Force complete page reload to orders page
      setTimeout(() => {
        console.log('🚀 Redirecting to orders page...');
        window.location.href = '/billing/orders?refresh=true&order=' + orderNumber;
      }, 1000);

    } catch (error) {
      console.error("❌ Error creating new order:", error);
      toast({
        title: "Error",
        description: "Failed to create new order. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container px-4 py-6 md:px-6 md:py-8">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={() => setLocation('/dashboard')}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Utilities & Tools</h1>
        </div>

        {!isOnline && (
          <div className="flex items-center">
            <Wifi className="mr-1 h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">Offline</span>
          </div>
        )}
      </div>

      {/* Quick Access Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setSystemTab('health-check')}>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5 text-green-500" />
              Health Check
            </CardTitle>
            <CardDescription>
              Monitor system health and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Real-time system monitoring and diagnostics
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setSystemTab('system-info')}>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Server className="mr-2 h-5 w-5 text-blue-500" />
              System Info
            </CardTitle>
            <CardDescription>
              Server specifications and resource usage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              CPU, memory, and system information
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setSystemTab('database-stats')}>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Database className="mr-2 h-5 w-5 text-purple-500" />
              Database Stats
            </CardTitle>
            <CardDescription>
              Database table statistics and metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Record counts and database insights
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setSystemTab('analytics')}>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-orange-500" />
              Analytics
            </CardTitle>
            <CardDescription>
              Business insights and performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Revenue trends and business analytics
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Basic Utilities */}
      <div className="mt-8">
        <h2 className="mb-4 text-xl font-semibold">Basic Utilities</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <CalculatorIcon className="mr-2 h-5 w-5" />
                Calculator
              </CardTitle>
              <CardDescription>
                Perform quick calculations for orders and expenses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Calculator
                showTrigger={false}
                onResultSelect={handleCalculatorResult}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <ScanLine className="mr-2 h-5 w-5" />
                  Barcode Scanner
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={createTestProducts}
                  className="text-xs"
                >
                  Create Test Products
                </Button>
              </CardTitle>
              <CardDescription>
                Scan product barcodes to quickly add items to orders. Create test products first if needed.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BarcodeScanner
                showTrigger={false}
                onScan={handleBarcodeScan}
              />
            </CardContent>
          </Card>

          <Card className="md:col-span-2 lg:col-span-1">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center">
                    <Search className="mr-2 h-5 w-5" />
                    Quick Product Search
                  </CardTitle>
                  <CardDescription>
                    Quickly find products by name, category, or barcode
                  </CardDescription>
                </div>

              </div>
            </CardHeader>
            <CardContent>
              <QuickSearch
                showTrigger={false}
                onProductSelect={handleProductSelect}
              />
            </CardContent>
          </Card>

          {/* Selected Products Display */}
          <SelectedProductsWidget
            className="md:col-span-2 lg:col-span-3"
            showTableAssignment={true}
            onTableAssign={handleTableAssign}
          />



          {/* Test Navigation Card */}
          <Card className="md:col-span-2 lg:col-span-1 cursor-pointer hover:shadow-md transition-shadow" onClick={() => setLocation('/test-selected-products')}>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <ArrowLeft className="mr-2 h-5 w-5 text-blue-500" />
                Test Persistence
              </CardTitle>
              <CardDescription>
                Test that selected products persist across page navigation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click here to navigate to test page and verify selected products remain visible
              </p>
            </CardContent>
          </Card>

          <Card className="md:col-span-2 lg:col-span-3">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center">
                <Wifi className="mr-2 h-5 w-5" />
                Network Status & Offline Mode
              </CardTitle>
              <CardDescription>
                Monitor connection status and manage offline operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NetworkStatus showTrigger={false} />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* System Utilities */}
      <div className="mt-8">
        <h2 className="mb-4 text-xl font-semibold">System Utilities</h2>

        <Card>
          <CardHeader>
            <CardTitle>System Monitoring & Analytics</CardTitle>
            <CardDescription>
              Real-time system information, database statistics, and business analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue={systemTab} onValueChange={setSystemTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="health-check">Health Check</TabsTrigger>
                <TabsTrigger value="system-info">System Info</TabsTrigger>
                <TabsTrigger value="database-stats">Database</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
              </TabsList>
              <TabsContent value="health-check" className="mt-6">
                <HealthCheck />
              </TabsContent>
              <TabsContent value="system-info" className="mt-6">
                <SystemInfo />
              </TabsContent>
              <TabsContent value="database-stats" className="mt-6">
                <DatabaseStats />
              </TabsContent>
              <TabsContent value="analytics" className="mt-6">
                <BusinessAnalytics />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <h2 className="mb-4 text-xl font-semibold">Mobile Utilities</h2>

        <Card>
          <CardHeader>
            <CardTitle>Mobile-Optimized Tools</CardTitle>
            <CardDescription>
              Access these utilities from any screen using the floating button in the mobile view
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="calculator">Calculator</TabsTrigger>
                <TabsTrigger value="barcode">Barcode</TabsTrigger>
                <TabsTrigger value="search">Search</TabsTrigger>
                <TabsTrigger value="network">Network</TabsTrigger>
              </TabsList>
              <TabsContent value="calculator" className="mt-4">
                <div className="rounded-lg border p-4">
                  <p className="mb-4 text-sm text-muted-foreground">
                    The calculator is accessible from any screen via the floating utility button.
                    It provides basic arithmetic operations and can transfer results to input fields.
                  </p>
                  <div className="flex justify-center">
                    <img
                      src="/assets/calculator-mobile.png"
                      alt="Calculator on mobile"
                      className="h-48 rounded-md object-contain shadow-md"
                      onError={(e) => {
                        e.currentTarget.src = 'https://placehold.co/300x200?text=Calculator+Preview';
                      }}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="barcode" className="mt-4">
                <div className="rounded-lg border p-4">
                  <p className="mb-4 text-sm text-muted-foreground">
                    The barcode scanner uses your device's camera to scan product barcodes.
                    Scanned products can be quickly added to orders or looked up in inventory.
                  </p>
                  <div className="flex justify-center">
                    <img
                      src="/assets/barcode-mobile.png"
                      alt="Barcode scanner on mobile"
                      className="h-48 rounded-md object-contain shadow-md"
                      onError={(e) => {
                        e.currentTarget.src = 'https://placehold.co/300x200?text=Barcode+Scanner+Preview';
                      }}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="search" className="mt-4">
                <div className="rounded-lg border p-4">
                  <p className="mb-4 text-sm text-muted-foreground">
                    Quick product search allows you to find products instantly by name or category.
                    Results update as you type and products can be added directly to orders.
                  </p>
                  <div className="flex justify-center">
                    <img
                      src="/assets/search-mobile.png"
                      alt="Product search on mobile"
                      className="h-48 rounded-md object-contain shadow-md"
                      onError={(e) => {
                        e.currentTarget.src = 'https://placehold.co/300x200?text=Search+Preview';
                      }}
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="network" className="mt-4">
                <div className="rounded-lg border p-4">
                  <p className="mb-4 text-sm text-muted-foreground">
                    The network status indicator shows your current connection status.
                    When offline, it tracks pending operations and syncs them when you're back online.
                  </p>
                  <div className="flex justify-center">
                    <img
                      src="/assets/network-mobile.png"
                      alt="Network status on mobile"
                      className="h-48 rounded-md object-contain shadow-md"
                      onError={(e) => {
                        e.currentTarget.src = 'https://placehold.co/300x200?text=Network+Status+Preview';
                      }}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
