import { useState } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Utensils, Package, ShoppingBag, Globe } from "lucide-react";

export type OrderType = "dine_in" | "takeaway" | "eat_and_pay" | "online";

interface OrderTypeSelectorProps {
  value: OrderType;
  onChange: (value: OrderType) => void;
  className?: string;
  variant?: "horizontal" | "vertical" | "icons";
}

export function OrderTypeSelector({
  value,
  onChange,
  className,
  variant = "horizontal"
}: OrderTypeSelectorProps) {
  const orderTypes = [
    {
      id: "dine_in",
      label: "Table",
      icon: Utensils,
      description: "For customers dining in the restaurant",
    },
    {
      id: "takeaway",
      label: "Parcel",
      icon: Package,
      description: "For takeaway orders",
    },
    {
      id: "eat_and_pay",
      label: "Eat & Pay",
      icon: ShoppingBag,
      description: "For home delivery orders",
    },
    {
      id: "online",
      label: "Online",
      icon: Globe,
      description: "For orders placed online",
    },
  ];

  if (variant === "icons") {
    return (
      <div className={cn("flex space-x-2", className)}>
        {orderTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = value === type.id;
          return (
            <Button
              key={type.id}
              variant="outline"
              size="icon"
              className={cn(
                "h-10 w-10 rounded-full border-2 transition-all duration-200",
                isSelected
                  ? "bg-white text-[#007BFF] border-white shadow-lg hover:bg-gray-50 hover:text-[#0056b3]"
                  : "bg-transparent text-white border-white/30 hover:bg-white/10 hover:border-white/60 hover:text-white"
              )}
              onClick={() => onChange(type.id as OrderType)}
              title={type.label}
            >
              <Icon className="h-5 w-5" />
            </Button>
          );
        })}
      </div>
    );
  }

  if (variant === "vertical") {
    return (
      <div className={cn("flex flex-col space-y-2", className)}>
        {orderTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = value === type.id;
          return (
            <Button
              key={type.id}
              variant="outline"
              className={cn(
                "justify-start border-2 transition-all duration-200 font-medium",
                isSelected
                  ? "bg-white text-[#007BFF] border-white shadow-lg hover:bg-gray-50 hover:text-[#0056b3]"
                  : "bg-transparent text-white border-white/30 hover:bg-white/10 hover:border-white/60 hover:text-white"
              )}
              onClick={() => onChange(type.id as OrderType)}
            >
              <Icon className="mr-2 h-4 w-4" />
              {type.label}
            </Button>
          );
        })}
      </div>
    );
  }

  // Default horizontal layout
  return (
    <div className={cn("flex space-x-2", className)}>
      {orderTypes.map((type) => {
        const Icon = type.icon;
        const isSelected = value === type.id;
        return (
          <Button
            key={type.id}
            variant="outline"
            className={cn(
              "flex-1 transition-all duration-200 border-2 font-medium",
              isSelected
                ? "bg-white text-[#007BFF] border-white shadow-lg hover:bg-gray-50 hover:text-[#0056b3]"
                : "bg-transparent text-white border-white/30 hover:bg-white/10 hover:border-white/60 hover:text-white"
            )}
            onClick={() => onChange(type.id as OrderType)}
          >
            <Icon className="mr-2 h-4 w-4" />
            {type.label}
          </Button>
        );
      })}
    </div>
  );
}
