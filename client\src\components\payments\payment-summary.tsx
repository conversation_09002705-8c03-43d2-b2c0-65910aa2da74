import React, { useState, useEffect, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { CreditCard, Banknote, Smartphone, PieChart, BarChart } from "lucide-react";
import {
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  Legend,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid
} from 'recharts';

interface PaymentSummaryProps {
  period?: string;
  className?: string;
}

interface PaymentStats {
  totalAmount: number;
  paymentsByMethod: Record<string, number>;
  methodCounts: Record<string, number>;
}

export function PaymentSummary({ period = "week", className }: PaymentSummaryProps) {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const [chartType, setChartType] = useState<string>("pie");

  // Fetch payment stats
  const { data: paymentStats, isLoading, error } = useQuery<PaymentStats>({
    queryKey: ['/api/payments/stats', period],
    enabled: !!token && !!currentShop,
    retry: 1,
    queryFn: async () => {
      const params = new URLSearchParams({ period });
      const response = await apiRequest('GET', `/api/payments/stats?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch payment stats');
      }
      return response.json();
    },
    onError: (error) => {
      console.error('Payment stats query error:', error);
    },
    onSuccess: (data) => {
      console.log('Payment stats query success:', data);
    }
  });

  // Debug logging
  useEffect(() => {
    console.log('=== PaymentSummary Debug ===');
    console.log('paymentStats:', paymentStats);
    console.log('isLoading:', isLoading);
    console.log('error:', error);
    console.log('token exists:', !!token);
    console.log('currentShop:', currentShop);
    console.log('query enabled:', !!token && !!currentShop);
    console.log('===========================');
  }, [paymentStats, isLoading, error, token, currentShop]);

  // Prepare chart data with fallback sample data
  const chartData = useMemo(() => {
    if (paymentStats && Object.keys(paymentStats.paymentsByMethod).length > 0) {
      return Object.entries(paymentStats.paymentsByMethod).map(([method, amount]) => ({
        name: method.charAt(0).toUpperCase() + method.slice(1),
        value: amount,
        percentage: paymentStats.totalAmount > 0 ? Math.round((amount / paymentStats.totalAmount) * 100) : 0
      }));
    }

    // Fallback sample data for demonstration
    return [
      { name: 'Cash', value: 15000, percentage: 60 },
      { name: 'Card', value: 7500, percentage: 30 },
      { name: 'UPI', value: 2500, percentage: 10 }
    ];
  }, [paymentStats]);

  // Payment method colors
  const paymentMethodColors: Record<string, string> = {
    cash: "bg-green-100 text-green-800",
    card: "bg-blue-100 text-blue-800",
    upi: "bg-purple-100 text-purple-800",
    default: "bg-gray-100 text-gray-800",
  };

  // Payment method icons
  const getPaymentMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case 'cash':
        return <Banknote className="h-3 w-3 mr-1 inline" />;
      case 'card':
        return <CreditCard className="h-3 w-3 mr-1 inline" />;
      case 'upi':
        return <Smartphone className="h-3 w-3 mr-1 inline" />;
      default:
        return null;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle>Payment Method Distribution</CardTitle>
        <Tabs value={chartType} className="w-[180px]">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="pie" onClick={() => setChartType("pie")}>
              Pie
            </TabsTrigger>
            <TabsTrigger value="bar" onClick={() => setChartType("bar")}>
              Bar
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-[300px] flex items-center justify-center">
            <Skeleton className="h-full w-full rounded-md" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Show sample data notice if no real data */}
            {(!paymentStats || Object.keys(paymentStats.paymentsByMethod).length === 0) && (
              <div className="text-center text-sm text-muted-foreground bg-muted/30 p-2 rounded">
                📊 Showing sample data - No payment records found for the selected period
                {error && <><br/>Error: {error.message}</>}
              </div>
            )}

            <div className="h-[250px] w-full">
              {chartType === "pie" ? (
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      nameKey="name"
                    >
                      {chartData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.name.toLowerCase() === 'cash' ? '#10b981' :
                            entry.name.toLowerCase() === 'card' ? '#3b82f6' :
                            entry.name.toLowerCase() === 'upi' ? '#8b5cf6' : '#6b7280'
                          }
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: any, name: any) => [
                        `₹${value.toLocaleString()}`,
                        name
                      ]}
                    />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `₹${value.toLocaleString()}`} />
                    <Tooltip
                      formatter={(value: any, name: any) => [
                        `₹${value.toLocaleString()}`,
                        'Amount'
                      ]}
                    />
                    <Bar
                      dataKey="value"
                      fill="#3b82f6"
                      radius={[4, 4, 0, 0]}
                    />
                  </RechartsBarChart>
                </ResponsiveContainer>
              )}
            </div>

            {/* Payment method summary */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {chartData.map((item, index) => (
                <div key={item.name} className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className={`w-4 h-4 rounded-full mx-auto mb-2 ${
                    item.name.toLowerCase() === 'cash' ? 'bg-green-500' :
                    item.name.toLowerCase() === 'card' ? 'bg-blue-500' :
                    item.name.toLowerCase() === 'upi' ? 'bg-purple-500' : 'bg-gray-500'
                  }`} />
                  <div className="text-sm font-medium">{item.name}</div>
                  <div className="text-xs text-muted-foreground">₹{item.value.toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">{item.percentage}%</div>
                </div>
              ))}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(paymentStats.paymentsByMethod).map(([method, amount]) => {
                const percentage = (amount / paymentStats.totalAmount) * 100;
                const count = paymentStats.methodCounts[method] || 0;

                return (
                  <Card key={method} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <Badge
                          variant="outline"
                          className={`${paymentMethodColors[method] || paymentMethodColors.default} px-2 py-1`}
                        >
                          {getPaymentMethodIcon(method)}
                          {method.charAt(0).toUpperCase() + method.slice(1)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {count} {count === 1 ? 'transaction' : 'transactions'}
                        </span>
                      </div>
                      <div className="mt-2">
                        <p className="text-2xl font-bold">₹{amount.toFixed(2)}</p>
                        <p className="text-xs text-muted-foreground">{percentage.toFixed(2)}% of total</p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
