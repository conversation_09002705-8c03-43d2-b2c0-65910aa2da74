import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Server, Cpu, HardDrive, Clock, Activity } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface SystemInfo {
  server: {
    platform: string;
    architecture: string;
    nodeVersion: string;
    uptime: number;
    memory: {
      total: number;
      free: number;
      used: number;
      processUsed: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
      };
    };
    cpu: {
      model: string;
      cores: number;
      loadAverage: number[];
    };
  };
  application: {
    name: string;
    version: string;
    environment: string;
    startTime: string;
  };
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
};

export function SystemInfo() {
  const { data: systemInfo, isLoading, error } = useQuery<SystemInfo>({
    queryKey: ['/api/utilities/system-info'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="mr-2 h-5 w-5" />
            System Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !systemInfo) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="mr-2 h-5 w-5" />
            System Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Failed to load system information
          </div>
        </CardContent>
      </Card>
    );
  }

  const memoryUsagePercent = (systemInfo.server.memory.used / systemInfo.server.memory.total) * 100;
  const heapUsagePercent = (systemInfo.server.memory.processUsed.heapUsed / systemInfo.server.memory.processUsed.heapTotal) * 100;

  return (
    <div className="space-y-6">
      {/* Application Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            Application Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Application</p>
              <p className="text-2xl font-bold">{systemInfo.application.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Version</p>
              <Badge variant="secondary">{systemInfo.application.version}</Badge>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Environment</p>
              <Badge variant={systemInfo.application.environment === 'production' ? 'default' : 'outline'}>
                {systemInfo.application.environment}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Started</p>
              <p className="text-sm text-muted-foreground">
                {new Date(systemInfo.application.startTime).toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Server Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="mr-2 h-5 w-5" />
            Server Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Platform</p>
              <p className="text-sm">{systemInfo.server.platform} ({systemInfo.server.architecture})</p>
            </div>
            <div>
              <p className="text-sm font-medium">Node.js Version</p>
              <p className="text-sm">{systemInfo.server.nodeVersion}</p>
            </div>
          </div>
          <div>
            <p className="text-sm font-medium flex items-center">
              <Clock className="mr-1 h-4 w-4" />
              Uptime
            </p>
            <p className="text-lg font-semibold">{formatUptime(systemInfo.server.uptime)}</p>
          </div>
        </CardContent>
      </Card>

      {/* CPU Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Cpu className="mr-2 h-5 w-5" />
            CPU Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium">Model</p>
            <p className="text-sm">{systemInfo.server.cpu.model}</p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Cores</p>
              <p className="text-lg font-semibold">{systemInfo.server.cpu.cores}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Load Average</p>
              <p className="text-sm">
                {systemInfo.server.cpu.loadAverage.map(load => load.toFixed(2)).join(', ')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Memory Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <HardDrive className="mr-2 h-5 w-5" />
            Memory Usage
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <p className="text-sm font-medium">System Memory</p>
              <p className="text-sm text-muted-foreground">
                {formatBytes(systemInfo.server.memory.used)} / {formatBytes(systemInfo.server.memory.total)}
              </p>
            </div>
            <Progress value={memoryUsagePercent} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {memoryUsagePercent.toFixed(1)}% used
            </p>
          </div>
          
          <Separator />
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <p className="text-sm font-medium">Process Heap</p>
              <p className="text-sm text-muted-foreground">
                {formatBytes(systemInfo.server.memory.processUsed.heapUsed)} / {formatBytes(systemInfo.server.memory.processUsed.heapTotal)}
              </p>
            </div>
            <Progress value={heapUsagePercent} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {heapUsagePercent.toFixed(1)}% used
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4 pt-2">
            <div>
              <p className="text-sm font-medium">RSS</p>
              <p className="text-sm">{formatBytes(systemInfo.server.memory.processUsed.rss)}</p>
            </div>
            <div>
              <p className="text-sm font-medium">External</p>
              <p className="text-sm">{formatBytes(systemInfo.server.memory.processUsed.external)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
