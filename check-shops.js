// Script to check shops and assign admin user to a shop
import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

async function main() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    console.log('Connected to database successfully');
    
    // Check shops
    const shopsResult = await client.query('SELECT * FROM shops ORDER BY id;');
    console.log('Shops in database:', shopsResult.rows);

    // Check users
    const usersResult = await client.query('SELECT id, username, name FROM users ORDER BY id;');
    console.log('Users in database:', usersResult.rows);

    // Check user-shop relationships
    const userShopsResult = await client.query('SELECT * FROM user_shops ORDER BY id;');
    console.log('User-shop relationships:', userShopsResult.rows);

    // Check branches
    const branchesResult = await client.query('SELECT * FROM branches ORDER BY id;');
    console.log('Branches in database:', branchesResult.rows);

    // Check user-branch relationships
    const userBranchesResult = await client.query('SELECT * FROM user_branches ORDER BY id;');
    console.log('User-branch relationships:', userBranchesResult.rows);

    // If admin user (id=1) is not assigned to any shop, assign to first shop
    const adminUserId = 1;
    const adminShopCheck = await client.query('SELECT * FROM user_shops WHERE user_id = $1', [adminUserId]);
    
    if (adminShopCheck.rows.length === 0 && shopsResult.rows.length > 0) {
      const firstShopId = shopsResult.rows[0].id;
      console.log(`Assigning admin user (${adminUserId}) to first shop (${firstShopId})`);
      
      await client.query(
        'INSERT INTO user_shops (user_id, shop_id, role) VALUES ($1, $2, $3)',
        [adminUserId, firstShopId, 'owner']
      );
      
      console.log('Admin user assigned to shop successfully');

      // Also assign to first branch if exists
      if (branchesResult.rows.length > 0) {
        const firstBranchId = branchesResult.rows[0].id;
        const adminBranchCheck = await client.query('SELECT * FROM user_branches WHERE user_id = $1', [adminUserId]);
        
        if (adminBranchCheck.rows.length === 0) {
          console.log(`Assigning admin user (${adminUserId}) to first branch (${firstBranchId})`);
          
          await client.query(
            'INSERT INTO user_branches (user_id, branch_id, role) VALUES ($1, $2, $3)',
            [adminUserId, firstBranchId, 'manager']
          );
          
          console.log('Admin user assigned to branch successfully');
        }
      }
    } else {
      console.log('Admin user already assigned to shop or no shops exist');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

main();
