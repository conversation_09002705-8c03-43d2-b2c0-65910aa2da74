// Very basic API test
console.log('Starting basic API test...');

const testAPI = async () => {
  try {
    console.log('Testing localhost:5000...');
    
    const response = await fetch('http://localhost:5000/api/subscription-plans', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token',
        'X-Shop-ID': '1'
      }
    });
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success! Data:', data);
    } else {
      const error = await response.text();
      console.log('❌ Error:', error);
    }
    
  } catch (err) {
    console.log('❌ Network error:', err.message);
  }
};

testAPI();
