import { storage } from "./storage";
import { z } from "zod";
import { eq, and, gte, lte } from "drizzle-orm";
import { orders } from "@shared/schema";

// Define payment schema
export const paymentSchema = z.object({
  orderId: z.number().min(1, "Order ID is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  method: z.string().min(1, "Payment method is required"),
  status: z.string().default("completed"),
});

// Define refund schema
export const refundSchema = z.object({
  orderId: z.number().min(1, "Order ID is required"),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  reason: z.string().min(1, "Reason is required"),
  refundMethod: z.enum(["same", "cash", "upi", "card"]),
});

export interface Payment {
  id: number;
  orderId: number;
  orderNumber: string;
  amount: number;
  method: string;
  status: string;
  orderStatus: string;
  refundStatus: 'not_refunded' | 'refunded' | 'partial_refund';
  refundAmount: number;
  refundMethod?: string;
  createdAt: string;
  customerId?: number;
  customerName?: string;
}

export interface Refund {
  id: number;
  paymentId: number;
  orderId: number;
  orderNumber: string;
  amount: number;
  reason: string;
  status: string;
  refundMethod?: string;
  createdAt: string;
}

export interface PaymentStats {
  totalAmount: number;
  paymentsByMethod: Record<string, number>;
  methodCounts: Record<string, number>;
}

export class PaymentService {
  private storage: any;

  constructor(storage: any) {
    this.storage = storage;
  }

  // Get all payments
  async getAllPayments(
    startDate?: Date,
    endDate?: Date,
    shopId?: number,
    branchId?: number
  ): Promise<Payment[]> {
    try {
      // Get orders with payment information
      const allOrders = await storage.getAllOrders(startDate, endDate, shopId, branchId);

      // Map orders to payments with refund information from database
      const payments: Payment[] = await Promise.all(
        allOrders
          .filter(order => order.paymentMethod && order.paymentStatus) // Only include orders with payment info
          .map(async order => {
            // Get refunds for this order from database
            const orderRefunds = await storage.getRefundsByOrderId(order.id);
            const refundAmount = orderRefunds.reduce((sum, refund) => sum + refund.amount, 0);

            let refundStatus: 'not_refunded' | 'refunded' | 'partial_refund' = 'not_refunded';
            if (refundAmount >= order.totalAmount) {
              refundStatus = 'refunded';
            } else if (refundAmount > 0) {
              refundStatus = 'partial_refund';
            }

            const refundMethod = orderRefunds.length > 0 ? orderRefunds[0].refundMethod : undefined;

            return {
              id: order.id,
              orderId: order.id,
              orderNumber: order.orderNumber,
              amount: order.totalAmount,
              method: order.paymentMethod || 'unknown',
              status: order.paymentStatus,
              orderStatus: order.status,
              refundStatus,
              refundAmount,
              refundMethod,
              createdAt: order.createdAt?.toISOString() || new Date().toISOString(),
              customerId: order.customerId || undefined,
              customerName: order.customer?.name,
            };
          })
      );

      return payments;
    } catch (error) {
      console.error('Error getting payments:', error);
      throw new Error('Failed to retrieve payments');
    }
  }

  // Get payment by ID
  async getPaymentById(id: number): Promise<Payment | null> {
    try {
      const order = await storage.getOrderById(id);

      if (!order || !order.paymentMethod) {
        return null;
      }

      // Get refunds for this order from database
      const orderRefunds = await storage.getRefundsByOrderId(order.id);
      const refundAmount = orderRefunds.reduce((sum, refund) => sum + refund.amount, 0);

      let refundStatus: 'not_refunded' | 'refunded' | 'partial_refund' = 'not_refunded';
      if (refundAmount >= order.totalAmount) {
        refundStatus = 'refunded';
      } else if (refundAmount > 0) {
        refundStatus = 'partial_refund';
      }

      const refundMethod = orderRefunds.length > 0 ? orderRefunds[0].refundMethod : undefined;

      return {
        id: order.id,
        orderId: order.id,
        orderNumber: order.orderNumber,
        amount: order.totalAmount,
        method: order.paymentMethod,
        status: order.paymentStatus,
        orderStatus: order.status,
        refundStatus,
        refundAmount,
        refundMethod,
        createdAt: order.createdAt?.toISOString() || new Date().toISOString(),
        customerId: order.customerId || undefined,
        customerName: order.customer?.name,
      };
    } catch (error) {
      console.error('Error getting payment by ID:', error);
      throw new Error('Failed to retrieve payment');
    }
  }

  // Process a refund
  async processRefund(refundData: z.infer<typeof refundSchema>): Promise<Refund> {
    try {
      // Get the order
      const order = await storage.getOrderById(refundData.orderId);
      
      if (!order) {
        throw new Error('Order not found');
      }
      
      if (order.paymentStatus !== 'paid') {
        throw new Error('Cannot refund an unpaid order');
      }

      if (order.status !== 'cancelled') {
        throw new Error('Can only refund cancelled orders');
      }
      
      // Check existing refunds for this order
      const existingRefunds = await storage.getRefundsByOrderId(order.id);
      const totalRefunded = existingRefunds.reduce((sum, refund) => sum + refund.amount, 0);

      if (totalRefunded + refundData.amount > order.totalAmount) {
        throw new Error('Refund amount cannot exceed the remaining order total');
      }

      // Determine actual refund method
      const actualRefundMethod = refundData.refundMethod === 'same'
        ? order.paymentMethod
        : refundData.refundMethod;

      // Create refund record in database
      const refundRecord = await storage.createRefund({
        orderId: order.id,
        amount: refundData.amount,
        reason: refundData.reason,
        refundMethod: actualRefundMethod || 'cash',
        status: 'completed',
        userId: order.userId, // Use the order's user ID
        shopId: order.shopId,
        branchId: order.branchId
      });

      // Return refund in the expected format
      const refund: Refund = {
        id: refundRecord.id,
        paymentId: order.id,
        orderId: order.id,
        orderNumber: order.orderNumber,
        amount: refundRecord.amount,
        reason: refundRecord.reason,
        status: refundRecord.status,
        refundMethod: refundRecord.refundMethod,
        createdAt: refundRecord.createdAt.toISOString(),
      };

      console.log('Refund processed and stored in database:', refund);
      return refund;
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  // Get all refunds
  async getAllRefunds(
    startDate?: Date,
    endDate?: Date,
    shopId?: number,
    branchId?: number
  ): Promise<Refund[]> {
    try {
      console.log(`PaymentService.getAllRefunds called with: startDate=${startDate}, endDate=${endDate}, shopId=${shopId}, branchId=${branchId}`);

      // Get refunds from database
      const dbRefunds = await storage.getAllRefunds(startDate, endDate, shopId, branchId);
      console.log(`Storage returned ${dbRefunds.length} refunds`);

      // Convert database refunds to the expected format
      const refunds: Refund[] = await Promise.all(
        dbRefunds.map(async (dbRefund) => {
          // Get order information for the refund
          const order = await storage.getOrderById(dbRefund.orderId);

          return {
            id: dbRefund.id,
            paymentId: dbRefund.orderId, // Using orderId as paymentId since they're the same
            orderId: dbRefund.orderId,
            orderNumber: order?.orderNumber || `ORD-${dbRefund.orderId}`,
            amount: dbRefund.amount,
            reason: dbRefund.reason,
            status: dbRefund.status,
            refundMethod: dbRefund.refundMethod,
            createdAt: dbRefund.createdAt.toISOString(),
          };
        })
      );

      console.log(`PaymentService returning ${refunds.length} formatted refunds`);
      return refunds;
    } catch (error) {
      console.error('Error getting all refunds:', error);
      throw new Error('Failed to retrieve refunds');
    }
  }

  // Get payment statistics
  async getPaymentStats(
    period: string = 'week',
    shopId?: number,
    branchId?: number
  ): Promise<PaymentStats> {
    try {
      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        default:
          startDate.setDate(startDate.getDate() - 7);
      }
      
      // Get payments for the period
      const payments = await this.getAllPayments(startDate, endDate, shopId, branchId);
      
      // Calculate statistics
      const stats: PaymentStats = {
        totalAmount: 0,
        paymentsByMethod: {},
        methodCounts: {},
      };
      
      payments.forEach(payment => {
        if (payment.status === 'completed' || payment.status === 'paid') {
          // Add to total
          stats.totalAmount += payment.amount;
          
          // Add to payment method totals
          const method = payment.method.toLowerCase();
          stats.paymentsByMethod[method] = (stats.paymentsByMethod[method] || 0) + payment.amount;
          stats.methodCounts[method] = (stats.methodCounts[method] || 0) + 1;
        }
      });
      
      return stats;
    } catch (error) {
      console.error('Error getting payment stats:', error);
      throw new Error('Failed to retrieve payment statistics');
    }
  }
}
