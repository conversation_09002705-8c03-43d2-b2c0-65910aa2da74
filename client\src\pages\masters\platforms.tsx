import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest, queryClient, invalidateAndRefetch, logQueryCache } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/auth-context";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Search, Plus, Edit, AlertCircle, Loader2, Percent, Trash2 } from "lucide-react";

// Form schema
const platformSchema = z.object({
  name: z.string().min(1, "Name is required"),
  commissionPercent: z.coerce.number().min(0, "Commission must be at least 0").max(100, "Commission cannot exceed 100%"),
  active: z.boolean().default(true),
});

type PlatformFormValues = z.infer<typeof platformSchema>;

// Define Platform interface
interface Platform {
  id: number;
  name: string;
  commissionPercent: number;
  active: boolean;
  shopId: number;
}

export default function OnlinePlatforms() {
  const { token } = useAuth();
  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null);
  const [platformToDelete, setPlatformToDelete] = useState<Platform | null>(null);
  const [error, setError] = useState<string | null>(null);

  // State to force component re-render
  const [refreshKey, setRefreshKey] = useState(0);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Form
  const form = useForm<PlatformFormValues>({
    resolver: zodResolver(platformSchema),
    defaultValues: {
      name: "",
      commissionPercent: 0,
      active: true,
    },
  });

  const editForm = useForm<PlatformFormValues>({
    resolver: zodResolver(platformSchema),
    defaultValues: {
      name: "",
      commissionPercent: 0,
      active: true,
    },
  });

  // Fetch platforms
  const { data: platforms, isLoading: isLoadingPlatforms, refetch: refetchPlatforms } = useQuery<Platform[]>({
    queryKey: ['/api/platforms', refreshKey], // Add refreshKey to force refetch when it changes
    enabled: !!token,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Force a refetch when component mounts
  useEffect(() => {
    if (token) {
      console.log("Platforms component mounted - forcing data refresh");
      // Force a refetch to ensure we have the latest data
      queryClient.removeQueries({ queryKey: ['/api/platforms'] });
      refetchPlatforms();

      // Log the query cache state for debugging
      logQueryCache();
    }
  }, [token, refetchPlatforms]);

  // Function to force a refresh of the platforms data
  const forceRefresh = () => {
    console.log("Forcing refresh of platforms data");
    setRefreshKey(prevKey => prevKey + 1);
    queryClient.removeQueries({ queryKey: ['/api/platforms'] });
    setTimeout(() => {
      refetchPlatforms();
    }, 100); // Small delay to ensure query is removed before refetching
  };

  // Log when platforms data changes
  useEffect(() => {
    console.log("Platforms data changed:", platforms);
  }, [platforms]);

  // Reset to page 1 when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Filter platforms
  const filteredPlatforms = platforms?.filter((platform: Platform) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase();
    return platform.name.toLowerCase().includes(search);
  });

  // Pagination helper interface
  interface PaginatedData<T> {
    data: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  // Pagination helper function
  const paginateData = (data: Platform[] | undefined, page: number, size: number): PaginatedData<Platform> => {
    if (!data || !Array.isArray(data)) return { data: [], total: 0, page, pageSize: size, totalPages: 0 };

    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const paginatedData = data.slice(startIndex, endIndex);
    const total = data.length;
    const totalPages = Math.ceil(total / size);

    return {
      data: paginatedData,
      total,
      page,
      pageSize: size,
      totalPages
    };
  };

  // Paginate filtered platforms
  const paginatedData = paginateData(filteredPlatforms, currentPage, pageSize);
  const displayedPlatforms = paginatedData.data;

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size change
  const handlePageSizeChange = (size: string) => {
    setPageSize(parseInt(size));
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Create platform mutation
  const createPlatformMutation = useMutation({
    mutationFn: async (data: PlatformFormValues) => {
      console.log("Creating platform with data:", data);
      const response = await apiRequest("POST", "/api/platforms", data);
      console.log("Create platform response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Create platform error:", errorText);
        throw new Error(errorText);
      }
      return response.json();
    },
    onSuccess: async (responseData) => {
      console.log("Platform created successfully:", responseData);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/platforms');

      // Force a refresh of the platforms data
      forceRefresh();

      setIsAddDialogOpen(false);
      form.reset();

      toast({
        title: "Success",
        description: "Online platform created successfully",
      });
    },
    onError: (err: any) => {
      console.error("Error creating platform:", err);
      setError("Failed to create online platform");

      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create online platform",
      });
    },
  });

  // Update platform mutation
  const updatePlatformMutation = useMutation({
    mutationFn: async (data: PlatformFormValues & { id: number }) => {
      const { id, ...updateData } = data;
      console.log(`Updating platform ${id} with data:`, updateData);

      try {
        // First, try to get the platform to ensure it exists
        const checkResponse = await apiRequest("GET", `/api/platforms/${id}`);
        if (!checkResponse.ok) {
          console.error(`Platform with ID ${id} not found`);
          throw new Error(`Platform with ID ${id} not found`);
        }

        // Then update the platform
        const response = await apiRequest("PATCH", `/api/platforms/${id}`, updateData);
        console.log("Update platform response:", response);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Update platform error:", errorText);
          throw new Error(errorText);
        }

        return response.json();
      } catch (error) {
        console.error("Error in update platform mutation:", error);
        throw error;
      }
    },
    onSuccess: async (responseData) => {
      console.log("Platform updated successfully:", responseData);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/platforms');

      // Force a refresh of the platforms data
      forceRefresh();

      // Reset form and close modal
      editForm.reset({
        name: "",
        commissionPercent: 0,
        active: true,
      });
      setSelectedPlatform(null);
      setError(null);
      setIsEditDialogOpen(false);

      toast({
        title: "Success",
        description: "Online platform updated successfully",
      });
    },
    onError: (err: any) => {
      console.error("Error updating platform:", err);
      setError("Failed to update online platform");

      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update online platform",
      });
    },
  });

  // Delete platform mutation
  const deletePlatformMutation = useMutation({
    mutationFn: async (platformId: number) => {
      const response = await apiRequest("DELETE", `/api/platforms/${platformId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete platform");
      }
      return response.json();
    },
    onSuccess: async () => {
      // Invalidate and refetch platforms
      await invalidateAndRefetch('/api/platforms');

      setIsDeleteDialogOpen(false);
      setPlatformToDelete(null);

      toast({
        title: "Success",
        description: "Platform deleted successfully",
      });

      // Force a refresh of the platforms data
      forceRefresh();
    },
    onError: (err: any) => {
      console.error("Error deleting platform:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to delete platform";

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    },
  });

  // Set form values for edit
  const setFormForEdit = (platform: Platform) => {
    editForm.reset({
      name: platform.name,
      commissionPercent: platform.commissionPercent,
      active: platform.active,
    });
  };

  // Form submission handlers
  const handleCreatePlatform = (data: PlatformFormValues) => {
    console.log("Creating platform with data:", data);
    createPlatformMutation.mutate(data);
  };

  const handleUpdatePlatform = (data: PlatformFormValues) => {
    if (!selectedPlatform) {
      console.error("Cannot update platform: No platform selected");
      setError("No platform selected");
      return;
    }

    console.log("Updating platform with data:", data);
    console.log("Selected platform ID:", selectedPlatform.id);

    try {
      updatePlatformMutation.mutate({
        id: selectedPlatform.id,
        ...data,
      });
    } catch (error) {
      console.error("Error in handleUpdatePlatform:", error);
      setError("Failed to update platform");
    }
  };

  const handleDeletePlatform = async () => {
    if (!platformToDelete) return;

    try {
      await deletePlatformMutation.mutateAsync(platformToDelete.id);
    } catch (error) {
      // Error handling is done in mutation's onError
      console.error("Failed to delete platform:", error);
    }
  };

  // Loading state
  const isLoading =
    isLoadingPlatforms ||
    createPlatformMutation.isPending ||
    updatePlatformMutation.isPending ||
    deletePlatformMutation.isPending;



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Online Platforms</h1>
        <Sheet open={isAddDialogOpen} onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            // If opening add modal, close edit modal if it's open
            if (open && isEditDialogOpen) {
              setIsEditDialogOpen(false);
              setSelectedPlatform(null);
            }
            // If closing, reset the form
            if (!open) {
              form.reset();
              setError(null);
            }
          }}>
          <SheetTrigger asChild>
            <Button onClick={() => {
              // Close edit modal if it's open when clicking Add Online Platform
              if (isEditDialogOpen) {
                setIsEditDialogOpen(false);
                setSelectedPlatform(null);
              }
            }}>
              <Plus className="h-4 w-4 mr-2" />
              Add Platform
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <SheetHeader>
              <SheetTitle>Add New Online Platform</SheetTitle>
              <SheetDescription>
                Add a new online delivery platform
              </SheetDescription>
            </SheetHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleCreatePlatform)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Platform Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter platform name" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="commissionPercent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commission Percentage</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type="number"
                            placeholder="Enter commission percentage"
                            {...field}
                            disabled={isLoading}
                          />
                          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <Percent className="h-4 w-4 text-gray-500" />
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          Enable or disable this platform
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <SheetFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </Button>
                </SheetFooter>
              </form>
            </Form>
          </SheetContent>
        </Sheet>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Online Platform List</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {/* Search */}
          <div className="flex mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search platforms..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Platforms Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingPlatforms ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : displayedPlatforms && displayedPlatforms.length > 0 ? (
                  displayedPlatforms.map((platform: Platform) => (
                    <TableRow key={platform.id}>
                      <TableCell className="font-medium">{platform.name}</TableCell>
                      <TableCell>{platform.commissionPercent}%</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          platform.active
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          {platform.active ? "Active" : "Inactive"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedPlatform(platform);
                              setFormForEdit(platform);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            disabled={deletePlatformMutation.isPending}
                            onClick={() => {
                              setPlatformToDelete(platform);
                              setIsDeleteDialogOpen(true);
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            {deletePlatformMutation.isPending && platformToDelete?.id === platform.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      No online platforms found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {paginatedData.totalPages > 1 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium">Rows per page</p>
                <Select
                  value={pageSize.toString()}
                  onValueChange={handlePageSizeChange}
                >
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder={pageSize.toString()} />
                  </SelectTrigger>
                  <SelectContent side="top">
                    {[5, 10, 20, 30, 40, 50].map((size) => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-6 lg:space-x-8">
                <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                  Page {currentPage} of {paginatedData.totalPages}
                </div>
                <div className="flex items-center space-x-2">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {
                        // Show pages around current page
                        let pageNum;
                        if (paginatedData.totalPages <= 5) {
                          // If 5 or fewer pages, show all
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          // If near start, show first 5 pages
                          pageNum = i + 1;
                        } else if (currentPage >= paginatedData.totalPages - 2) {
                          // If near end, show last 5 pages
                          pageNum = paginatedData.totalPages - 4 + i;
                        } else {
                          // Otherwise show 2 before and 2 after current page
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <PaginationItem key={pageNum}>
                            <PaginationLink
                              isActive={currentPage === pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className="cursor-pointer"
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      })}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(Math.min(paginatedData.totalPages, currentPage + 1))}
                          className={currentPage === paginatedData.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              </div>
            </div>
          )}

          {/* Results summary */}
          <div className="text-xs text-muted-foreground">
            Showing {Math.min((currentPage - 1) * pageSize + 1, paginatedData.total)} to{" "}
            {Math.min(currentPage * pageSize, paginatedData.total)} of {paginatedData.total} results
          </div>
        </CardContent>
      </Card>

      {/* Edit Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={(open) => {
        console.log(`Edit sheet onOpenChange: ${open}`);

        // If closing edit modal, reset selected platform and form
        if (!open) {
          console.log("Closing edit modal, resetting form and selected platform");
          setSelectedPlatform(null);
          editForm.reset({
            name: "",
            commissionPercent: 0,
            active: true,
          });
          setError(null);
        }

        // If opening edit modal, close add modal if it's open
        if (open && isAddDialogOpen) {
          console.log("Opening edit modal, closing add modal");
          setIsAddDialogOpen(false);
          form.reset();
        }

        // Finally set the dialog state
        setIsEditDialogOpen(open);
      }}>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Edit Online Platform</SheetTitle>
            <SheetDescription>
              Update online platform details
            </SheetDescription>
          </SheetHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdatePlatform)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Platform Name</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="commissionPercent"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Commission Percentage</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          {...field}
                          disabled={isLoading}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                          <Percent className="h-4 w-4 text-gray-500" />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Enable or disable this platform
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <SheetFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Platform</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the platform "{platformToDelete?.name}"?
              This action cannot be undone and may affect orders using this platform.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deletePlatformMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePlatform}
              disabled={deletePlatformMutation.isPending}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {deletePlatformMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
