const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function fixTable3Final() {
  const client = await pool.connect();
  try {
    console.log('🔍 Current table data:');
    const before = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(before.rows);
    
    console.log('\n🔧 Fixing Table 3 mapping...');
    
    // The issue: Table ID 5 has name "table3" but user wants Table 3 to have ID 3
    // Solution: Swap the names so Table 3 has ID 3
    
    // Step 1: Update table ID 3 to be "Table 3"
    await client.query("UPDATE tables SET name = 'Table 3' WHERE id = 3");
    console.log('✅ Updated table ID 3 to "Table 3"');
    
    // Step 2: Update table ID 5 to be "Table 5" (remove the confusing "table3" name)
    await client.query("UPDATE tables SET name = 'Table 5' WHERE id = 5");
    console.log('✅ Updated table ID 5 to "Table 5"');
    
    console.log('\n📋 Updated table data:');
    const after = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(after.rows);
    
    // Verify the fix
    const table3 = after.rows.find(row => row.id === 3);
    if (table3 && table3.name === 'Table 3') {
      console.log('\n🎉 SUCCESS! Table ID 3 now has name "Table 3"');
      console.log('✅ When you select "Table 3", it will now use ID 3');
    } else {
      console.log('\n❌ FAILED! Table ID 3 still has wrong name');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixTable3Final();
