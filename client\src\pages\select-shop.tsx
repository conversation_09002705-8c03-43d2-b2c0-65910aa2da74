import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2, Plus, LogIn, Store, Building, ShoppingBag, Grid3X3, List, Phone, Mail, MapPin } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { Shop } from "@shared/schema";

type ViewMode = 'grid' | 'list';

export default function SelectShop() {
  const { user, token } = useAuth();
  const { setCurrentShop, switchShop } = useApp();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [shops, setShops] = useState<Shop[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>(() => {
    const saved = localStorage.getItem('select-shop-view-mode');
    return (saved as ViewMode) || 'grid';
  });

  useEffect(() => {
    // Fetch user's shops
    const fetchShops = async () => {
      setIsLoading(true);
      try {
        const response = await apiRequest("GET", "/api/shops");
        const shopsData = await response.json();
        setShops(shopsData);
      } catch (error: any) {
        console.error("Error fetching shops:", error);
        setError("Failed to load shops");
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to load shops",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      fetchShops();
    }
  }, [token, toast]);

  const handleSelectShop = async (shop: Shop) => {
    console.log(`Selecting shop: ${shop.name} (ID: ${shop.id})`);

    try {
      // Use switchShop instead of setCurrentShop to ensure all data is refreshed
      await switchShop(shop.id);

      // Redirect to dashboard
      setLocation("/dashboard");
    } catch (error) {
      console.error("Error selecting shop:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to select shop. Please try again.",
      });
    }
  };

  const handleCreateShop = () => {
    setLocation("/create-shop");
  };

  const handleJoinShop = () => {
    setLocation("/join-shop");
  };

  // Handle view mode change
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    localStorage.setItem('select-shop-view-mode', mode);
  };

  // Get shop type icon
  const getShopIcon = (type: string) => {
    switch (type) {
      case 'restaurant':
        return <Store className="h-6 w-6" />;
      case 'retail':
        return <ShoppingBag className="h-6 w-6" />;
      default:
        return <Building className="h-6 w-6" />;
    }
  };

  // Grid View Component
  const GridView = ({ shops }: { shops: Shop[] }) => (
    <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6 gap-8 lg:gap-10">
      {shops.map((shop) => (
        <Card
          key={shop.id}
          className="group cursor-pointer bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.03] hover:bg-white rounded-3xl overflow-hidden"
        >
          <CardHeader className="pb-6 p-8">
            <div className="flex items-center gap-5">
              <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 group-hover:from-blue-100 group-hover:to-purple-100 transition-colors shadow-lg">
                {getShopIcon(shop.shopType)}
              </div>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-2xl font-bold text-gray-900 truncate mb-2">
                  {shop.name}
                </CardTitle>
                <CardDescription className="text-base text-gray-600 flex items-center gap-2">
                  <MapPin className="h-4 w-4 flex-shrink-0" />
                  <span className="truncate">{shop.address}</span>
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pb-8 px-8 space-y-4">
            <div className="flex items-center justify-between text-base">
              <span className="text-gray-500 font-semibold">Type:</span>
              <span className="capitalize font-bold text-gray-900 bg-blue-50 px-4 py-2 rounded-xl text-sm">{shop.shopType}</span>
            </div>
            <div className="flex items-center justify-between text-base">
              <span className="text-gray-500 flex items-center gap-2 font-semibold">
                <Phone className="h-4 w-4" />
                Phone:
              </span>
              <span className="font-bold text-gray-900">{shop.phone}</span>
            </div>
            {shop.email && (
              <div className="flex items-center justify-between text-base">
                <span className="text-gray-500 flex items-center gap-2 font-semibold">
                  <Mail className="h-4 w-4" />
                  Email:
                </span>
                <span className="font-bold text-gray-900 truncate">{shop.email}</span>
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0 px-8 pb-8">
            <Button
              onClick={() => handleSelectShop(shop)}
              size="lg"
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 font-bold text-lg py-4"
            >
              Select Shop
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  // List View Component
  const ListView = ({ shops }: { shops: Shop[] }) => (
    <div className="w-full bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100">
      <div className="w-full">
        <table className="w-full table-fixed">
          <thead className="bg-gradient-to-r from-blue-50 to-purple-50 border-b-2 border-gray-200">
            <tr>
              <th className="w-1/5 px-4 py-3 text-left text-sm font-semibold text-gray-900">Shop</th>
              <th className="w-1/6 px-4 py-3 text-left text-sm font-semibold text-gray-900">Type</th>
              <th className="w-1/4 px-4 py-3 text-left text-sm font-semibold text-gray-900 hidden md:table-cell">Address</th>
              <th className="w-1/4 px-4 py-3 text-left text-sm font-semibold text-gray-900 hidden lg:table-cell">Contact</th>
              <th className="w-1/6 px-4 py-3 text-center text-sm font-semibold text-gray-900">Action</th>
            </tr>
          </thead>
          <tbody className="divide-y-2 divide-gray-200">
            {shops.map((shop, index) => (
              <tr
                key={shop.id}
                className={`hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-colors duration-300 ${
                  index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                }`}
              >
                <td className="px-4 py-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-blue-50 to-purple-50 shadow-md">
                      {getShopIcon(shop.shopType)}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="font-semibold text-gray-900 truncate text-sm">{shop.name}</div>
                      <div className="text-xs text-gray-500 md:hidden flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3 flex-shrink-0" />
                        <span className="truncate">{shop.address}</span>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 capitalize">
                    {shop.shopType}
                  </span>
                </td>
                <td className="px-4 py-4 hidden md:table-cell">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <MapPin className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate font-medium">{shop.address}</span>
                  </div>
                </td>
                <td className="px-4 py-4 hidden lg:table-cell">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <Phone className="h-3 w-3 flex-shrink-0" />
                      <span className="font-medium">{shop.phone}</span>
                    </div>
                    {shop.email && (
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Mail className="h-3 w-3 flex-shrink-0" />
                        <span className="truncate font-medium">{shop.email}</span>
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-4 py-4 text-center">
                  <Button
                    onClick={() => handleSelectShop(shop)}
                    size="sm"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 font-semibold px-4 py-2 text-sm"
                  >
                    Select
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="h-screen w-full bg-gradient-to-br from-blue-50/30 via-white to-purple-50/30 overflow-y-auto overflow-x-hidden">
      <div className="w-full min-h-full px-4 sm:px-6 lg:px-8 xl:px-12 py-8 max-w-full">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3 lg:mb-0">Select a Shop</h1>

          {shops.length > 0 && (
            <div className="flex items-center gap-4">
              <span className="text-lg text-gray-600 hidden lg:inline font-semibold">View:</span>
              <ToggleGroup
                type="single"
                value={viewMode}
                onValueChange={(value) => value && handleViewModeChange(value as ViewMode)}
                className="bg-white shadow-xl border-2 rounded-2xl p-2"
              >
                <ToggleGroupItem
                  value="grid"
                  aria-label="Grid view"
                  className="data-[state=on]:bg-blue-100 data-[state=on]:text-blue-700 px-6 py-3 rounded-xl transition-all duration-300 text-base font-semibold"
                >
                  <Grid3X3 className="h-5 w-5" />
                  <span className="hidden sm:inline ml-3">Grid</span>
                </ToggleGroupItem>
                <ToggleGroupItem
                  value="list"
                  aria-label="List view"
                  className="data-[state=on]:bg-blue-100 data-[state=on]:text-blue-700 px-6 py-3 rounded-xl transition-all duration-300 text-base font-semibold"
                >
                  <List className="h-5 w-5" />
                  <span className="hidden sm:inline ml-3">List</span>
                </ToggleGroupItem>
              </ToggleGroup>
            </div>
          )}
        </div>

        {error && (
          <Alert variant="destructive" className="mb-8 w-full">
            <AlertCircle className="h-5 w-5" />
            <AlertDescription className="text-lg">{error}</AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center min-h-[70vh] w-full">
            <div className="text-center">
              <Loader2 className="h-16 w-16 animate-spin text-blue-600 mx-auto mb-6" />
              <p className="text-gray-600 font-semibold text-xl">Loading your shops...</p>
            </div>
          </div>
        ) : shops.length === 0 ? (
          <div className="flex items-center justify-center min-h-[70vh] w-full">
            <div className="text-center p-20 bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl border border-blue-100 shadow-2xl w-full max-w-4xl">
              <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-xl">
                <Store className="h-16 w-16 text-blue-600" />
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">No Shops Found</h2>
              <p className="text-gray-600 mb-12 text-xl lg:text-2xl leading-relaxed max-w-2xl mx-auto">
                You don't have any shops yet. Get started by creating a new shop or joining an existing one to begin your journey.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  onClick={handleCreateShop}
                  size="lg"
                  className="flex items-center bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 font-semibold px-12 py-4 text-lg"
                >
                  <Plus className="mr-3 h-6 w-6" />
                  Create New Shop
                </Button>
                <Button
                  onClick={handleJoinShop}
                  variant="outline"
                  size="lg"
                  className="flex items-center hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-300 font-semibold px-12 py-4 text-lg border-2"
                >
                  <LogIn className="mr-3 h-6 w-6" />
                  Join Existing Shop
                </Button>
              </div>
            </div>
          </div>
      ) : (
        <div className="w-full space-y-16">
          {/* Shops Display */}
          <div className="w-full">
            {viewMode === 'grid' ? (
              <GridView shops={shops} />
            ) : (
              <ListView shops={shops} />
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-8 justify-center pt-16 border-t-2 border-gray-200">
            <Button
              onClick={handleCreateShop}
              size="lg"
              className="flex items-center bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 font-bold px-12 py-5 text-xl"
            >
              <Plus className="mr-4 h-6 w-6" />
              Create New Shop
            </Button>
            <Button
              onClick={handleJoinShop}
              variant="outline"
              size="lg"
              className="flex items-center hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300 transition-all duration-300 font-bold px-12 py-5 text-xl border-3"
            >
              <LogIn className="mr-4 h-6 w-6" />
              Join Existing Shop
            </Button>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
