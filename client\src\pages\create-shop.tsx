import { useState } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/context/auth-context";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2, ArrowLeft } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { apiRequest } from "@/lib/queryClient";

// Shop creation form schema
const formSchema = z.object({
  name: z.string().min(1, "Shop name is required"),
  address: z.string().min(1, "Address is required"),
  phone: z.string()
    .length(10, "Phone number must be exactly 10 digits")
    .regex(/^\d{10}$/, "Phone number must contain only digits"),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  shopType: z.string().min(1, "Shop type is required"),
  taxRegistration: z.string().optional().or(z.literal("")),
  currencySymbol: z.string().default("₹"),
  accessCode: z.string().optional(), // Optional access code, will be generated on server if not provided
});

type FormValues = z.infer<typeof formSchema>;

export default function CreateShop() {
  const { token } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      address: "",
      phone: "",
      email: "",
      shopType: "",
      taxRegistration: "",
      currencySymbol: "₹",
      accessCode: "", // Empty string for optional access code
    },
  });

  async function onSubmit(data: FormValues) {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiRequest("POST", "/api/shops", data);
      const shop = await response.json();

      toast({
        title: "Shop created successfully",
        description: `Your shop "${shop.name}" has been created.`,
      });

      // Redirect to dashboard
      setLocation("/dashboard");
    } catch (error: any) {
      console.error("Shop creation error:", error);

      let errorMessage = "Failed to create shop";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);

      toast({
        variant: "destructive",
        title: "Shop creation failed",
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="bg-gradient-to-br from-indigo-100 via-purple-50 to-cyan-100">
      {/* Ultra Compact Header */}
      <div className="flex items-center justify-between px-6 py-1 bg-white/90 backdrop-blur-md border-b border-indigo-200 shadow-sm">
        <div>
          <h1 className="text-base font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">Create New Shop</h1>
          <p className="text-gray-600 text-xs">Enter your shop details to get started</p>
        </div>
        <Button
          variant="outline"
          onClick={() => setLocation("/dashboard")}
          className="flex items-center gap-1 hover:bg-indigo-50 hover:border-indigo-300 h-6 text-xs px-2 border-indigo-200"
        >
          <ArrowLeft className="h-3 w-3" />
          Back
        </Button>
      </div>

      {/* Minimal Form Card */}
      <div className="px-2 py-1">
        <div className="w-full max-w-6xl mx-auto bg-white/95 backdrop-blur-sm rounded-lg shadow-xl border border-white/50 p-4">
          {error && (
            <Alert variant="destructive" className="mb-4 border-red-200 bg-red-50/80">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">{error}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
              {/* Minimal Wide Grid - 2 Fields Per Row */}
              <div className="grid grid-cols-2 gap-2.5">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Shop Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter shop name"
                            {...field}
                            disabled={isLoading}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shopType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Shop Type *</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm">
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="restaurant">Restaurant</SelectItem>
                            <SelectItem value="retail">Retail Store</SelectItem>
                            <SelectItem value="cafe">Cafe</SelectItem>
                            <SelectItem value="grocery">Grocery Store</SelectItem>
                            <SelectItem value="service">Service Business</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Address *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter address"
                            {...field}
                            disabled={isLoading}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Phone *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter phone number (10 digits)"
                            {...field}
                            disabled={isLoading}
                            type="tel"
                            maxLength={10}
                            onInput={(e) => {
                              // Allow only numeric input
                              const target = e.target as HTMLInputElement;
                              target.value = target.value.replace(/[^0-9]/g, '');
                              field.onChange(target.value);
                            }}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Email</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter email address"
                            {...field}
                            disabled={isLoading}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currencySymbol"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Currency</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="₹"
                            {...field}
                            disabled={isLoading}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxRegistration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Tax Registration</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter tax registration"
                            {...field}
                            disabled={isLoading}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="accessCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs font-semibold text-gray-700">Access Code</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Auto-generated"
                            {...field}
                            disabled={isLoading}
                            className="h-7 text-xs border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 bg-white/80 backdrop-blur-sm"
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
              </div>

              {/* Minimal Info Panel */}
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded p-1 mt-1">
                <div className="flex items-center gap-1">
                  <AlertCircle className="h-3 w-3 text-indigo-600 flex-shrink-0" />
                  <div className="text-xs text-indigo-800">
                    <span className="text-red-500">*</span> Required fields • Access code auto-generated if empty
                  </div>
                </div>
              </div>

              {/* Minimal Submit Section */}
              <div className="flex items-center justify-end gap-2 pt-1 border-t border-indigo-100 pb-0 mt-1">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setLocation("/dashboard")}
                  disabled={isLoading}
                  className="px-3 h-7 text-xs border-gray-300 hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="px-4 h-7 text-xs bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 hover:from-indigo-700 hover:via-purple-700 hover:to-cyan-700 shadow-lg hover:shadow-xl transition-all duration-300 text-white font-semibold"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Shop"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
