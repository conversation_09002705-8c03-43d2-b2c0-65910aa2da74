// Load environment variables directly in this file
import dotenv from "dotenv";
import path from "path";

// Load .env file from the project root
const envPath = path.resolve(process.cwd(), '.env');
dotenv.config({ path: envPath });

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Check if DATABASE_URL is defined
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is not defined');
}

// Create PostgreSQL client with connection options
const client = postgres(process.env.DATABASE_URL, {
  connect_timeout: 10, // 10 seconds timeout
  idle_timeout: 20,
  max_lifetime: 60 * 30, // 30 minutes
  max: 10, // Maximum number of connections
  onnotice: () => {}, // Disable notices
});

// Create Drizzle ORM instance
const db = drizzle(client);

// Test database connection
async function testConnection() {
  try {
    await client`SELECT 1`;
    console.log('✅ Connected to PostgreSQL database successfully');
    console.log('📍 Database URL:', process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':****@')); // Hide password in logs
  } catch (error) {
    console.error('❌ Failed to connect to PostgreSQL database:');
    console.error('Error:', error instanceof Error ? error.message : error);
    console.error('Database URL:', process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':****@'));

    if (error instanceof Error && error.message.includes('ETIMEDOUT')) {
      console.error('\n🔧 Connection timeout detected. Possible solutions:');
      console.error('1. Check if the database server is running');
      console.error('2. Verify the IP address and port are correct');
      console.error('3. Check firewall settings');
      console.error('4. Consider using a local PostgreSQL database for development');
    }

    // Don't throw error to allow server to start, but log the issue
    console.error('\n⚠️  Server will start but database operations will fail until connection is restored');
  }
}

// Test connection on startup
testConnection();

// Export the database
export { db };
