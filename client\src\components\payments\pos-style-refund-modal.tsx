import React, { useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Printer, Download, Share, Loader2, ArrowLeftRight, CreditCard, Banknote, Smartphone, Receipt } from 'lucide-react';
import { useReactToPrint } from 'react-to-print';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { useApp } from '@/context/app-context';
import { UseFormReturn } from 'react-hook-form';

interface Payment {
  id: number;
  orderId: number;
  orderNumber: string;
  amount: number;
  method: string;
  status: string;
  orderStatus: string;
  refundStatus: 'not_refunded' | 'refunded' | 'partial_refund';
  refundAmount: number;
  refundMethod?: string;
  createdAt: string;
  customerId?: number;
  customerName?: string;
}

interface RefundFormValues {
  orderId: number;
  amount: number;
  reason: string;
  refundMethod: "same" | "cash" | "upi" | "card";
}

interface PosStyleRefundModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: RefundFormValues) => void;
  payment: Payment | null;
  form: UseFormReturn<RefundFormValues>;
  isProcessing: boolean;
}

export function PosStyleRefundModal({
  isOpen,
  onClose,
  onSubmit,
  payment,
  form,
  isProcessing
}: PosStyleRefundModalProps) {
  const { currentShop } = useApp();
  const refundReceiptRef = useRef<HTMLDivElement>(null);

  // Handle print receipt
  const handlePrint = useReactToPrint({
    content: () => refundReceiptRef.current,
    documentTitle: payment ? `Refund-Receipt-${payment.orderNumber}` : 'Refund-Receipt',
  });

  // Handle export to PDF
  const handleExportPDF = async () => {
    if (!refundReceiptRef.current) return;

    const canvas = await html2canvas(refundReceiptRef.current, {
      scale: 2,
      logging: false,
      useCORS: true,
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: [80, 200], // Receipt width: 80mm (standard thermal receipt width)
    });

    // Calculate the height based on the aspect ratio
    const imgWidth = 80;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
    pdf.save(`Refund-Receipt-${payment.orderNumber}.pdf`);
  };

  // Handle share receipt (mobile only)
  const handleShareReceipt = async () => {
    if (!refundReceiptRef.current || !navigator.share) return;

    try {
      const canvas = await html2canvas(refundReceiptRef.current, {
        scale: 2,
        logging: false,
        useCORS: true,
      });

      canvas.toBlob(async (blob) => {
        if (!blob) return;

        const file = new File([blob], `Refund-Receipt-${payment.orderNumber}.png`, { type: 'image/png' });

        try {
          await navigator.share({
            title: `Refund Receipt #${payment.orderNumber}`,
            text: 'Your refund receipt from NembooBill',
            files: [file],
          });
        } catch (error) {
          console.error('Error sharing refund receipt:', error);
        }
      });
    } catch (error) {
      console.error('Error creating refund receipt image:', error);
    }
  };

  const handleFormSubmit = (data: RefundFormValues) => {
    onSubmit(data);
  };

  // Return early if no payment, but after all hooks have been called
  if (!payment) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-center justify-center">
            <ArrowLeftRight className="mr-2 h-5 w-5" />
            Process Refund
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Order Number:</span>
              <span className="font-semibold">{payment.orderNumber}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Original Amount:</span>
              <span className="font-semibold">₹{payment.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Payment Method:</span>
              <Badge variant="outline" className={
                payment.method === 'cash' ? 'bg-green-100 text-green-800' :
                payment.method === 'card' ? 'bg-blue-100 text-blue-800' :
                payment.method === 'upi' ? 'bg-purple-100 text-purple-800' :
                'bg-gray-100 text-gray-800'
              }>
                {payment.method === 'cash' ? <Banknote className="h-3 w-3 mr-1 inline" /> :
                 payment.method === 'card' ? <CreditCard className="h-3 w-3 mr-1 inline" /> :
                 payment.method === 'upi' ? <Smartphone className="h-3 w-3 mr-1 inline" /> : null}
                {payment.method.charAt(0).toUpperCase() + payment.method.slice(1)}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">Customer:</span>
              <span className="font-medium">{payment.customerName || 'Walk-in'}</span>
            </div>
          </div>

          {/* Refund Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Refund Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        max={payment.amount}
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        className="text-lg font-semibold"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Refund</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter reason for refund"
                        {...field}
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="refundMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Refund Method</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select refund method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="same">
                          <div className="flex items-center">
                            {payment.method === 'cash' ? <Banknote className="h-4 w-4 mr-2" /> :
                             payment.method === 'card' ? <CreditCard className="h-4 w-4 mr-2" /> :
                             payment.method === 'upi' ? <Smartphone className="h-4 w-4 mr-2" /> : null}
                            Same as Payment ({payment.method.charAt(0).toUpperCase() + payment.method.slice(1)})
                          </div>
                        </SelectItem>
                        <SelectItem value="cash">
                          <div className="flex items-center">
                            <Banknote className="h-4 w-4 mr-2" />
                            Cash
                          </div>
                        </SelectItem>
                        <SelectItem value="upi">
                          <div className="flex items-center">
                            <Smartphone className="h-4 w-4 mr-2" />
                            UPI
                          </div>
                        </SelectItem>
                        <SelectItem value="card">
                          <div className="flex items-center">
                            <CreditCard className="h-4 w-4 mr-2" />
                            Card
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Receipt Preview */}
              <div className="border rounded-lg p-4 bg-white" ref={refundReceiptRef}>
                {/* Shop Header */}
                <div className="text-center mb-3">
                  <h3 className="font-bold text-base">{currentShop?.name || 'NembooBill'}</h3>
                  <p className="text-xs text-gray-600">{currentShop?.address || ''}</p>
                  <p className="text-xs text-gray-600">{currentShop?.phone || ''}</p>
                </div>

                <Separator className="my-2" />

                {/* Refund Header */}
                <div className="text-center mb-3">
                  <h4 className="font-bold text-sm">REFUND RECEIPT</h4>
                  <p className="text-xs text-gray-500">
                    {new Date().toLocaleString()}
                  </p>
                </div>

                {/* Order Details */}
                <div className="space-y-1 mb-3 text-xs">
                  <div className="flex justify-between">
                    <span>Order:</span>
                    <span className="font-medium">{payment.orderNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Original Amount:</span>
                    <span>₹{payment.amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Payment Method:</span>
                    <span className="capitalize">{payment.method}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Customer:</span>
                    <span>{payment.customerName || 'Walk-in'}</span>
                  </div>
                </div>

                <Separator className="my-2" />

                {/* Refund Details */}
                <div className="space-y-1 mb-3 text-xs">
                  <div className="flex justify-between font-medium">
                    <span>Refund Amount:</span>
                    <span>₹{form.watch('amount')?.toFixed(2) || '0.00'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Refund Method:</span>
                    <span className="capitalize">
                      {form.watch('refundMethod') === 'same'
                        ? payment.method
                        : form.watch('refundMethod') || 'N/A'}
                    </span>
                  </div>
                  <div className="text-xs">
                    <span>Reason: </span>
                    <span>{form.watch('reason') || 'N/A'}</span>
                  </div>
                </div>

                <Separator className="my-2" />

                {/* Footer */}
                <div className="text-center text-xs text-gray-500">
                  <p>Thank you for your business!</p>
                </div>
              </div>

              {/* Action Buttons - POS Style */}
              <div className="flex flex-col space-y-3">
                {/* Print/Save/Share Buttons */}
                <div className="flex justify-center space-x-2">
                  <Button 
                    type="button"
                    onClick={handlePrint} 
                    size="sm" 
                    variant="outline"
                    className="flex items-center"
                  >
                    <Printer className="mr-2 h-4 w-4" />
                    Print
                  </Button>
                  <Button 
                    type="button"
                    onClick={handleExportPDF} 
                    size="sm" 
                    variant="outline"
                    className="flex items-center"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Save PDF
                  </Button>
                  {navigator.share && (
                    <Button 
                      type="button"
                      onClick={handleShareReceipt} 
                      size="sm" 
                      variant="outline"
                      className="flex items-center"
                    >
                      <Share className="mr-2 h-4 w-4" />
                      Share
                    </Button>
                  )}
                </div>

                {/* Main Action Buttons */}
                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    className="flex-1"
                    disabled={isProcessing}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isProcessing}
                    className="flex-1"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Receipt className="mr-2 h-4 w-4" />
                        Process Refund
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
