import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { 
  BookOpen, 
  Search, 
  Play, 
  Code, 
  CreditCard, 
  Settings, 
  Users, 
  BarChart3,
  FileText,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Info,
  Lightbulb,
  Download,
  Video
} from "lucide-react";
import jsPDF from 'jspdf';

export default function SubscriptionDocs() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeSection, setActiveSection] = useState("getting-started");

  const downloadPDF = () => {
    try {
      // Create a new jsPDF instance
      const doc = new jsPDF();

      // Set font and title
      doc.setFontSize(20);
      doc.setFont('helvetica', 'bold');
      doc.text('NembooBill Subscription Documentation', 20, 30);

      // Add subtitle
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text('Complete guide to managing your NembooBill subscription', 20, 45);

      // Add generation date
      doc.setFontSize(10);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 55);

      let yPosition = 75;

      // Getting Started Section
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Getting Started with Subscriptions', 20, yPosition);
      yPosition += 15;

      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');
      const gettingStartedText = [
        'Welcome to NembooBill Subscriptions',
        'Our subscription system gives you access to premium features, unlimited storage, and priority support.',
        '',
        'What is a NembooBill Subscription?',
        'A NembooBill subscription is a premium service that unlocks advanced features for your business:',
        '• Unlimited invoice generation',
        '• Advanced reporting and analytics',
        '• Priority customer support',
        '• Cloud storage and backup',
        '• Multi-user access',
        '• Custom branding options'
      ];

      gettingStartedText.forEach(line => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }
        doc.text(line, 20, yPosition);
        yPosition += 7;
      });

      yPosition += 10;

      // How to Subscribe Section
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('How to Subscribe', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');
      const subscribeSteps = [
        '1. Navigate to the Subscription page in your dashboard',
        '2. Choose a plan that fits your business needs',
        '3. Enter your payment information',
        '4. Complete the subscription process',
        '5. Start enjoying premium features immediately'
      ];

      subscribeSteps.forEach(step => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }
        doc.text(step, 20, yPosition);
        yPosition += 7;
      });

      yPosition += 10;

      // Billing & Payments Section
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Billing & Payments', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');
      const billingText = [
        '• Manage your billing information',
        '• View payment history',
        '• Update payment methods',
        '• Handle subscription renewals'
      ];

      billingText.forEach(item => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }
        doc.text(item, 20, yPosition);
        yPosition += 7;
      });

      yPosition += 10;

      // Subscription Plans Section
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Subscription Plans', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');
      const plansText = [
        '• Basic Plan: Essential features for small businesses',
        '• Professional Plan: Advanced features for growing businesses',
        '• Enterprise Plan: Full feature set for large organizations'
      ];

      plansText.forEach(plan => {
        if (yPosition > 270) {
          doc.addPage();
          yPosition = 20;
        }
        doc.text(plan, 20, yPosition);
        yPosition += 7;
      });

      // Add footer
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(`Page ${i} of ${pageCount}`, 20, 285);
        doc.text('NembooBill - POS & Billing System', 150, 285);
      }

      // Save the PDF
      doc.save('NembooBill-Subscription-Documentation.pdf');

    } catch (error) {
      console.error('Error generating PDF:', error);
      // Fallback to text download if PDF generation fails
      const textContent = `
NembooBill Subscription Documentation

Getting Started with Subscriptions
Learn how to set up and manage your NembooBill subscription

Welcome to NembooBill Subscriptions
Our subscription system gives you access to premium features, unlimited storage, and priority support.

What is a NembooBill Subscription?
A NembooBill subscription is a premium service that unlocks advanced features for your business:
- Unlimited invoice generation
- Advanced reporting and analytics
- Priority customer support
- Cloud storage and backup
- Multi-user access
- Custom branding options

How to Subscribe
1. Navigate to the Subscription page in your dashboard
2. Choose a plan that fits your business needs
3. Enter your payment information
4. Complete the subscription process
5. Start enjoying premium features immediately

For more information, visit our website or contact support.
Generated on ${new Date().toLocaleDateString()}
      `;

      const blob = new Blob([textContent], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'NembooBill-Subscription-Documentation.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }
  };
  const [filteredSections, setFilteredSections] = useState<string[]>([]);

  const documentationSections = [
    {
      id: "getting-started",
      title: "Getting Started",
      icon: <BookOpen className="h-4 w-4" />,
      description: "Learn the basics of subscription management"
    },
    {
      id: "billing",
      title: "Billing & Payments",
      icon: <CreditCard className="h-4 w-4" />,
      description: "Manage billing, payments, and invoices"
    },
    {
      id: "plans",
      title: "Subscription Plans",
      icon: <Settings className="h-4 w-4" />,
      description: "Understanding and managing subscription plans"
    },
    {
      id: "users",
      title: "User Management",
      icon: <Users className="h-4 w-4" />,
      description: "Managing users and permissions"
    },
    {
      id: "analytics",
      title: "Analytics & Reports",
      icon: <BarChart3 className="h-4 w-4" />,
      description: "Understanding subscription analytics"
    },
    {
      id: "api",
      title: "API Reference",
      icon: <Code className="h-4 w-4" />,
      description: "Developer documentation and API endpoints"
    },
    {
      id: "videos",
      title: "Video Tutorials",
      icon: <Video className="h-4 w-4" />,
      description: "Step-by-step video guides and tutorials"
    }
  ];

  const quickLinks = [
    { title: "How to Subscribe", section: "getting-started", icon: <CreditCard className="h-4 w-4" /> },
    { title: "Billing Issues", section: "billing", icon: <AlertCircle className="h-4 w-4" /> },
    { title: "Plan Comparison", section: "plans", icon: <BarChart3 className="h-4 w-4" /> },
    { title: "API Keys", section: "api", icon: <Code className="h-4 w-4" /> },
    { title: "Payment Methods", section: "billing", icon: <CreditCard className="h-4 w-4" /> },
    { title: "User Management", section: "users", icon: <Users className="h-4 w-4" /> }
  ];

  // Search functionality
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === "") {
      setFilteredSections([]);
      return;
    }

    const searchTerms = query.toLowerCase().split(" ");
    const matchingSections = documentationSections.filter(section => {
      const searchText = `${section.title} ${section.description}`.toLowerCase();
      return searchTerms.some(term => searchText.includes(term));
    });

    setFilteredSections(matchingSections.map(s => s.id));
  };

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold">Subscription Documentation</h1>
          <p className="text-gray-500">Complete guide to managing your NembooBill subscription</p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={downloadPDF}>
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setActiveSection("videos")}
          >
            <Video className="h-4 w-4 mr-2" />
            Video Tutorials
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search documentation..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          {searchQuery && filteredSections.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">Search Results:</p>
              <div className="space-y-2">
                {filteredSections.map(sectionId => {
                  const section = documentationSections.find(s => s.id === sectionId);
                  return section ? (
                    <Button
                      key={sectionId}
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveSection(sectionId);
                        setSearchQuery("");
                        setFilteredSections([]);
                      }}
                    >
                      {section.icon}
                      <span className="ml-2">{section.title}</span>
                    </Button>
                  ) : null;
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Links */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Quick Links
          </CardTitle>
          <CardDescription>Jump to commonly accessed topics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {quickLinks.map((link, index) => (
              <Button
                key={index}
                variant="outline"
                className="justify-start h-auto p-3"
                onClick={() => setActiveSection(link.section)}
              >
                {link.icon}
                <span className="ml-2 text-sm">{link.title}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Documentation */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-3">
          <Card className="h-fit sticky top-6">
          <CardHeader>
            <CardTitle className="text-lg">Documentation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-1 p-4">
            {documentationSections.map((section) => (
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "ghost"}
                className="w-full justify-start h-auto p-3 text-left hover:bg-accent"
                onClick={() => setActiveSection(section.id)}
              >
                <div className="flex items-start space-x-3 w-full overflow-hidden">
                  <div className="flex-shrink-0 mt-1">
                    {section.icon}
                  </div>
                  <div className="flex-1 min-w-0 overflow-hidden">
                    <div className="font-medium text-sm mb-1">{section.title}</div>
                    <div className="text-xs text-muted-foreground leading-relaxed break-words">
                      {section.description}
                    </div>
                  </div>
                </div>
              </Button>
            ))}
          </CardContent>
        </Card>
        </div>

        {/* Content Area */}
        <div className="lg:col-span-9">
          <Tabs value={activeSection} onValueChange={setActiveSection}>
            <TabsContent value="getting-started">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Getting Started with Subscriptions
                  </CardTitle>
                  <CardDescription>
                    Learn how to set up and manage your NembooBill subscription
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-800">Welcome to NembooBill Subscriptions</h4>
                        <p className="text-sm text-blue-700 mt-1">
                          Our subscription system gives you access to premium features, unlimited storage, and priority support.
                        </p>
                      </div>
                    </div>
                  </div>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="what-is-subscription">
                      <AccordionTrigger>What is a NembooBill Subscription?</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <p>A NembooBill subscription gives you access to:</p>
                          <ul className="list-disc pl-6 space-y-1">
                            <li>Advanced POS features and customizations</li>
                            <li>Unlimited product catalog and inventory management</li>
                            <li>Multi-branch and multi-user support</li>
                            <li>Advanced reporting and analytics</li>
                            <li>Priority customer support</li>
                            <li>Regular feature updates and new modules</li>
                          </ul>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="how-to-subscribe">
                      <AccordionTrigger>How to Subscribe</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="flex items-start space-x-3">
                            <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">1</div>
                            <div>
                              <h5 className="font-medium">Choose Your Plan</h5>
                              <p className="text-sm text-gray-600">Go to Subscription → Plans and select the plan that fits your needs.</p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-3">
                            <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">2</div>
                            <div>
                              <h5 className="font-medium">Add Payment Method</h5>
                              <p className="text-sm text-gray-600">Add your credit card or preferred payment method securely.</p>
                            </div>
                          </div>
                          <div className="flex items-start space-x-3">
                            <div className="bg-blue-100 text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium">3</div>
                            <div>
                              <h5 className="font-medium">Confirm Subscription</h5>
                              <p className="text-sm text-gray-600">Review your selection and confirm to activate your subscription.</p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="first-steps">
                      <AccordionTrigger>First Steps After Subscribing</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-3">
                          <p>Once you've subscribed, here's what to do next:</p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="border rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="font-medium text-sm">Set Up Your Shop</span>
                              </div>
                              <p className="text-xs text-gray-600">Configure your shop settings, add your logo, and customize your receipt template.</p>
                            </div>
                            <div className="border rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="font-medium text-sm">Add Products</span>
                              </div>
                              <p className="text-xs text-gray-600">Import or manually add your product catalog with categories and pricing.</p>
                            </div>
                            <div className="border rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="font-medium text-sm">Invite Team Members</span>
                              </div>
                              <p className="text-xs text-gray-600">Add staff members and assign appropriate roles and permissions.</p>
                            </div>
                            <div className="border rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-2">
                                <CheckCircle className="h-4 w-4 text-green-500" />
                                <span className="font-medium text-sm">Test Your POS</span>
                              </div>
                              <p className="text-xs text-gray-600">Create a few test orders to familiarize yourself with the POS system.</p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="billing">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Billing & Payments
                  </CardTitle>
                  <CardDescription>
                    Manage your billing information, payment methods, and invoices
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="payment-methods">
                      <AccordionTrigger>Managing Payment Methods</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <p>You can add multiple payment methods to your account:</p>
                          <div className="bg-gray-50 rounded-lg p-4">
                            <h5 className="font-medium mb-2">Supported Payment Methods:</h5>
                            <ul className="list-disc pl-6 space-y-1 text-sm">
                              <li>Credit Cards (Visa, MasterCard, American Express)</li>
                              <li>Debit Cards</li>
                              <li>Bank Transfers</li>
                              <li>Digital Wallets (PayPal, Google Pay)</li>
                            </ul>
                          </div>
                          <div className="space-y-2">
                            <h5 className="font-medium">To add a payment method:</h5>
                            <ol className="list-decimal pl-6 space-y-1 text-sm">
                              <li>Go to Subscription → Billing</li>
                              <li>Click "Add Payment Method"</li>
                              <li>Enter your payment details securely</li>
                              <li>Set as default if desired</li>
                            </ol>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="billing-cycle">
                      <AccordionTrigger>Understanding Billing Cycles</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <p>NembooBill offers flexible billing cycles:</p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium text-green-600 mb-2">Monthly Billing</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Billed every 30 days</li>
                                <li>• Lower commitment</li>
                                <li>• Easy to upgrade/downgrade</li>
                                <li>• Perfect for testing</li>
                              </ul>
                            </div>
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium text-blue-600 mb-2">Annual Billing</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Billed once per year</li>
                                <li>• 2 months free (16% savings)</li>
                                <li>• Best value for money</li>
                                <li>• Priority support included</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="invoices">
                      <AccordionTrigger>Invoices and Receipts</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <p>All your billing information is available in your account:</p>
                          <div className="space-y-3">
                            <div className="flex items-start space-x-3">
                              <FileText className="h-5 w-5 text-blue-500 mt-0.5" />
                              <div>
                                <h5 className="font-medium">Download Invoices</h5>
                                <p className="text-sm text-gray-600">Access and download all your invoices from the Billing History section.</p>
                              </div>
                            </div>
                            <div className="flex items-start space-x-3">
                              <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
                              <div>
                                <h5 className="font-medium">Payment Failures</h5>
                                <p className="text-sm text-gray-600">If a payment fails, you'll receive an email notification with instructions to update your payment method.</p>
                              </div>
                            </div>
                            <div className="flex items-start space-x-3">
                              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                              <div>
                                <h5 className="font-medium">Auto-Renewal</h5>
                                <p className="text-sm text-gray-600">Subscriptions auto-renew by default. You can disable this in your billing settings.</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="plans">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Subscription Plans
                  </CardTitle>
                  <CardDescription>
                    Compare plans and understand what's included in each tier
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="border rounded-lg p-4">
                      <div className="text-center mb-4">
                        <h3 className="text-lg font-semibold">Basic</h3>
                        <p className="text-2xl font-bold text-blue-600">₹999/month</p>
                        <p className="text-sm text-gray-500">Perfect for small businesses</p>
                      </div>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Up to 1,000 products</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Single location</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Basic reporting</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Email support</li>
                      </ul>
                    </div>
                    <div className="border-2 border-blue-500 rounded-lg p-4 relative">
                      <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">Most Popular</Badge>
                      <div className="text-center mb-4">
                        <h3 className="text-lg font-semibold">Professional</h3>
                        <p className="text-2xl font-bold text-blue-600">₹1,999/month</p>
                        <p className="text-sm text-gray-500">For growing businesses</p>
                      </div>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Unlimited products</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Up to 5 locations</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Advanced reporting</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Priority support</li>
                      </ul>
                    </div>
                    <div className="border rounded-lg p-4">
                      <div className="text-center mb-4">
                        <h3 className="text-lg font-semibold">Enterprise</h3>
                        <p className="text-2xl font-bold text-blue-600">₹4,999/month</p>
                        <p className="text-sm text-gray-500">For large enterprises</p>
                      </div>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Unlimited everything</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Unlimited locations</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />Custom reporting</li>
                        <li className="flex items-center"><CheckCircle className="h-4 w-4 text-green-500 mr-2" />24/7 phone support</li>
                      </ul>
                    </div>
                  </div>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="upgrade-downgrade">
                      <AccordionTrigger>Upgrading or Downgrading Plans</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <p>You can change your plan at any time:</p>
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h5 className="font-medium text-green-800 mb-2">Upgrading</h5>
                            <ul className="text-sm text-green-700 space-y-1">
                              <li>• Takes effect immediately</li>
                              <li>• Prorated billing for the current period</li>
                              <li>• Access to new features right away</li>
                            </ul>
                          </div>
                          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                            <h5 className="font-medium text-orange-800 mb-2">Downgrading</h5>
                            <ul className="text-sm text-orange-700 space-y-1">
                              <li>• Takes effect at the end of current billing period</li>
                              <li>• No immediate loss of features</li>
                              <li>• Credit applied to next billing cycle</li>
                            </ul>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="users">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    User Management
                  </CardTitle>
                  <CardDescription>
                    Managing users, roles, and permissions in your subscription
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="user-limits">
                      <AccordionTrigger>User Limits by Plan</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="border rounded-lg p-4 text-center">
                              <h4 className="font-medium text-blue-600 mb-2">Basic Plan</h4>
                              <p className="text-2xl font-bold">3 Users</p>
                              <p className="text-sm text-gray-500">Owner + 2 staff members</p>
                            </div>
                            <div className="border rounded-lg p-4 text-center">
                              <h4 className="font-medium text-blue-600 mb-2">Professional Plan</h4>
                              <p className="text-2xl font-bold">15 Users</p>
                              <p className="text-sm text-gray-500">Perfect for growing teams</p>
                            </div>
                            <div className="border rounded-lg p-4 text-center">
                              <h4 className="font-medium text-blue-600 mb-2">Enterprise Plan</h4>
                              <p className="text-2xl font-bold">Unlimited</p>
                              <p className="text-sm text-gray-500">No user restrictions</p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="user-roles">
                      <AccordionTrigger>User Roles and Permissions</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="space-y-4">
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium text-purple-600 mb-2">Owner</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Full access to all features</li>
                                <li>• Manage subscription and billing</li>
                                <li>• Add/remove users and locations</li>
                                <li>• Access to all reports and analytics</li>
                              </ul>
                            </div>
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium text-blue-600 mb-2">Manager</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Manage products and inventory</li>
                                <li>• View sales reports</li>
                                <li>• Manage staff schedules</li>
                                <li>• Process refunds and exchanges</li>
                              </ul>
                            </div>
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium text-green-600 mb-2">Cashier</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Process sales transactions</li>
                                <li>• View product information</li>
                                <li>• Handle customer inquiries</li>
                                <li>• Limited reporting access</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Analytics & Reports
                  </CardTitle>
                  <CardDescription>
                    Understanding your subscription usage and analytics
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="usage-tracking">
                      <AccordionTrigger>Usage Tracking</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <p>Monitor your subscription usage across different metrics:</p>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="text-center p-3 border rounded-lg">
                              <Users className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                              <h5 className="font-medium">Active Users</h5>
                              <p className="text-sm text-gray-500">Track user activity</p>
                            </div>
                            <div className="text-center p-3 border rounded-lg">
                              <BarChart3 className="h-8 w-8 text-green-500 mx-auto mb-2" />
                              <h5 className="font-medium">Transactions</h5>
                              <p className="text-sm text-gray-500">Monthly transaction count</p>
                            </div>
                            <div className="text-center p-3 border rounded-lg">
                              <Settings className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                              <h5 className="font-medium">Storage</h5>
                              <p className="text-sm text-gray-500">Data storage usage</p>
                            </div>
                            <div className="text-center p-3 border rounded-lg">
                              <Code className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                              <h5 className="font-medium">API Calls</h5>
                              <p className="text-sm text-gray-500">API usage tracking</p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="reports">
                      <AccordionTrigger>Available Reports</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium mb-2">Subscription Health</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Usage vs. plan limits</li>
                                <li>• Feature utilization</li>
                                <li>• Performance metrics</li>
                              </ul>
                            </div>
                            <div className="border rounded-lg p-4">
                              <h5 className="font-medium mb-2">Billing Analytics</h5>
                              <ul className="text-sm space-y-1">
                                <li>• Payment history</li>
                                <li>• Cost per transaction</li>
                                <li>• ROI calculations</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="api">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    API Reference
                  </CardTitle>
                  <CardDescription>
                    Developer documentation for subscription-related API endpoints
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Base URL</h4>
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">https://api.nemboobill.com/v1</code>
                  </div>

                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="auth">
                      <AccordionTrigger>Authentication</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <p>All API requests require authentication using your API key:</p>
                          <div className="bg-gray-900 text-gray-100 rounded-lg p-4 text-sm">
                            <div className="text-green-400 mb-2"># Example request</div>
                            <div>curl -H "Authorization: Bearer YOUR_API_KEY" \</div>
                            <div className="ml-4">https://api.nemboobill.com/v1/subscriptions</div>
                          </div>
                          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div className="flex items-start space-x-2">
                              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                              <div>
                                <h5 className="font-medium text-yellow-800">API Key Security</h5>
                                <p className="text-sm text-yellow-700">Keep your API keys secure and never expose them in client-side code.</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="endpoints">
                      <AccordionTrigger>Subscription Endpoints</AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-6">
                          <div className="border rounded-lg p-4">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge variant="outline" className="text-green-600 border-green-600">GET</Badge>
                              <code className="text-sm">/api/subscriptions</code>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">Get all subscriptions for the authenticated shop</p>
                            <div className="bg-gray-900 text-gray-100 rounded p-3 text-xs">
                              <div className="text-green-400 mb-1"># Response</div>
                              <div>{"{"}</div>
                              <div className="ml-2">"subscriptions": [</div>
                              <div className="ml-4">{"{"}</div>
                              <div className="ml-6">"id": 1,</div>
                              <div className="ml-6">"planId": 2,</div>
                              <div className="ml-6">"status": "active",</div>
                              <div className="ml-6">"startDate": "2024-01-01",</div>
                              <div className="ml-6">"endDate": "2024-12-31"</div>
                              <div className="ml-4">{"}"}</div>
                              <div className="ml-2">]</div>
                              <div>{"}"}</div>
                            </div>
                          </div>

                          <div className="border rounded-lg p-4">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge variant="outline" className="text-blue-600 border-blue-600">POST</Badge>
                              <code className="text-sm">/api/subscriptions</code>
                            </div>
                            <p className="text-sm text-gray-600 mb-3">Create a new subscription</p>
                            <div className="bg-gray-900 text-gray-100 rounded p-3 text-xs">
                              <div className="text-green-400 mb-1"># Request Body</div>
                              <div>{"{"}</div>
                              <div className="ml-2">"planId": 2,</div>
                              <div className="ml-2">"paymentMethodId": 1,</div>
                              <div className="ml-2">"autoRenew": true</div>
                              <div>{"}"}</div>
                            </div>
                          </div>

                          <div className="border rounded-lg p-4">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge variant="outline" className="text-green-600 border-green-600">GET</Badge>
                              <code className="text-sm">/api/subscription-plans</code>
                            </div>
                            <p className="text-sm text-gray-600">Get all available subscription plans</p>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="videos">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Video className="h-5 w-5" />
                    Video Tutorials
                  </CardTitle>
                  <CardDescription>
                    Step-by-step video guides to help you get the most out of your subscription
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="border rounded-lg p-4">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                        <Play className="h-12 w-12 text-gray-400" />
                      </div>
                      <h4 className="font-medium mb-2">Getting Started with NembooBill</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Learn the basics of setting up your subscription and configuring your first shop.
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">Duration: 5:30</span>
                        <Button size="sm">
                          <Play className="h-4 w-4 mr-2" />
                          Watch
                        </Button>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                        <Play className="h-12 w-12 text-gray-400" />
                      </div>
                      <h4 className="font-medium mb-2">Managing Your Subscription</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        How to upgrade, downgrade, and manage your billing information.
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">Duration: 8:15</span>
                        <Button size="sm">
                          <Play className="h-4 w-4 mr-2" />
                          Watch
                        </Button>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                        <Play className="h-12 w-12 text-gray-400" />
                      </div>
                      <h4 className="font-medium mb-2">Adding Payment Methods</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Step-by-step guide to adding and managing your payment methods securely.
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">Duration: 3:45</span>
                        <Button size="sm">
                          <Play className="h-4 w-4 mr-2" />
                          Watch
                        </Button>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                        <Play className="h-12 w-12 text-gray-400" />
                      </div>
                      <h4 className="font-medium mb-2">Understanding Usage Analytics</h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Learn how to read your usage reports and optimize your subscription.
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">Duration: 6:20</span>
                        <Button size="sm">
                          <Play className="h-4 w-4 mr-2" />
                          Watch
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-800">Need a Specific Tutorial?</h4>
                        <p className="text-sm text-blue-700 mt-1">
                          Can't find what you're looking for? Contact our support team to request a specific tutorial or guide.
                        </p>
                        <Button size="sm" variant="outline" className="mt-3">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Request Tutorial
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
