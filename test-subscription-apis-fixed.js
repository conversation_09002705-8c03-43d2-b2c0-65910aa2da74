// Test subscription APIs with proper validation
console.log('🧪 Testing Subscription APIs with validation fixes...\n');

const testSubscriptionAPIs = async () => {
  const baseURL = 'http://localhost:5000';
  const headers = {
    'Authorization': 'Bearer test-token',
    'X-Shop-ID': '1',
    'Content-Type': 'application/json'
  };

  try {
    console.log('1. Testing subscription plans...');
    const plansResponse = await fetch(`${baseURL}/api/subscription-plans`, { headers });
    console.log('   Plans status:', plansResponse.status);
    if (plansResponse.ok) {
      const plans = await plansResponse.json();
      console.log('   ✅ Plans loaded:', plans.length, 'plans');
    } else {
      console.log('   ❌ Plans error:', await plansResponse.text());
    }

    console.log('\n2. Testing subscriptions...');
    const subsResponse = await fetch(`${baseURL}/api/subscriptions`, { headers });
    console.log('   Subscriptions status:', subsResponse.status);
    if (subsResponse.ok) {
      const subs = await subsResponse.json();
      console.log('   ✅ Subscriptions loaded:', subs.length, 'subscriptions');
    } else {
      console.log('   ❌ Subscriptions error:', await subsResponse.text());
    }

    console.log('\n3. Testing subscription stats...');
    const statsResponse = await fetch(`${baseURL}/api/subscriptions/stats`, { headers });
    console.log('   Stats status:', statsResponse.status);
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('   ✅ Stats loaded:', JSON.stringify(stats, null, 2));
    } else {
      console.log('   ❌ Stats error:', await statsResponse.text());
    }

    console.log('\n4. Testing subscription usage...');
    const usageResponse = await fetch(`${baseURL}/api/subscriptions/usage`, { headers });
    console.log('   Usage status:', usageResponse.status);
    if (usageResponse.ok) {
      const usage = await usageResponse.json();
      console.log('   ✅ Usage loaded successfully');
    } else {
      const error = await usageResponse.text();
      console.log('   ❌ Usage error:', error);
    }

    console.log('\n5. Testing billing history...');
    const historyResponse = await fetch(`${baseURL}/api/subscriptions/billing-history`, { headers });
    console.log('   History status:', historyResponse.status);
    if (historyResponse.ok) {
      const history = await historyResponse.json();
      console.log('   ✅ Billing history loaded:', history.length, 'records');
    } else {
      console.log('   ❌ History error:', await historyResponse.text());
    }

    console.log('\n6. Testing subscription payment methods...');
    const paymentResponse = await fetch(`${baseURL}/api/payment-methods/subscription`, { headers });
    console.log('   Payment methods status:', paymentResponse.status);
    if (paymentResponse.ok) {
      const methods = await paymentResponse.json();
      console.log('   ✅ Payment methods loaded:', methods.length, 'methods');
    } else {
      console.log('   ❌ Payment methods error:', await paymentResponse.text());
    }

    console.log('\n🎉 All subscription API tests completed!');

  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
};

testSubscriptionAPIs();
