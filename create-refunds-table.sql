-- Drop existing refunds table if it exists
DROP TABLE IF EXISTS refunds;

-- Create refunds table with correct structure
CREATE TABLE refunds (
  id SERIAL PRIMARY KEY,
  order_id INTEGER NOT NULL REFERENCES orders(id),
  amount DOUBLE PRECISION NOT NULL,
  reason TEXT NOT NULL,
  refund_method TEXT NOT NULL,
  status TEXT DEFAULT 'completed' NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  user_id INTEGER NOT NULL REFERENCES users(id),
  shop_id INTEGER NOT NULL REFERENCES shops(id),
  branch_id INTEGER REFERENCES branches(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_refunds_order_id ON refunds(order_id);
CREATE INDEX IF NOT EXISTS idx_refunds_shop_id ON refunds(shop_id);
CREATE INDEX IF NOT EXISTS idx_refunds_branch_id ON refunds(branch_id);
CREATE INDEX IF NOT EXISTS idx_refunds_created_at ON refunds(created_at);
CREATE INDEX IF NOT EXISTS idx_refunds_status ON refunds(status);
