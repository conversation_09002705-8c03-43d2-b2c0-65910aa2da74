import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Client } = pg;

async function fixSubscriptionData() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
  });

  try {
    await client.connect();
    console.log('✓ Connected to database');

    // Check if subscription_plans table exists
    const plansTableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'subscription_plans'
      );
    `);

    if (!plansTableCheck.rows[0].exists) {
      console.log('Creating subscription_plans table...');
      await client.query(`
        CREATE TABLE subscription_plans (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          price DOUBLE PRECISION NOT NULL,
          billing_cycle TEXT NOT NULL,
          features J<PERSON>NB NOT NULL,
          max_branches INTEGER DEFAULT 1 NOT NULL,
          max_users INTEGER DEFAULT 5 NOT NULL,
          max_products INTEGER DEFAULT 100 NOT NULL,
          max_orders INTEGER DEFAULT 1000 NOT NULL,
          storage_limit INTEGER DEFAULT 1024 NOT NULL,
          support_level TEXT DEFAULT 'basic' NOT NULL,
          active BOOLEAN DEFAULT true NOT NULL,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP DEFAULT NOW() NOT NULL
        );
      `);
      console.log('✓ Created subscription_plans table');
    }

    // Check if subscriptions table exists
    const subscriptionsTableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'subscriptions'
      );
    `);

    if (!subscriptionsTableCheck.rows[0].exists) {
      console.log('Creating subscriptions table...');
      await client.query(`
        CREATE TABLE subscriptions (
          id SERIAL PRIMARY KEY,
          shop_id INTEGER NOT NULL REFERENCES shops(id),
          plan_id INTEGER NOT NULL REFERENCES subscription_plans(id),
          status TEXT DEFAULT 'active' NOT NULL,
          start_date TIMESTAMP NOT NULL,
          end_date TIMESTAMP NOT NULL,
          auto_renew BOOLEAN DEFAULT true NOT NULL,
          discount_percent DOUBLE PRECISION DEFAULT 0 NOT NULL,
          discount_amount DOUBLE PRECISION DEFAULT 0 NOT NULL,
          total_amount DOUBLE PRECISION NOT NULL,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
          created_by INTEGER NOT NULL REFERENCES users(id)
        );
      `);
      console.log('✓ Created subscriptions table');
    }

    // Check if subscription plans exist
    const existingPlans = await client.query('SELECT COUNT(*) FROM subscription_plans');
    const planCount = parseInt(existingPlans.rows[0].count);
    
    if (planCount === 0) {
      console.log('Inserting sample subscription plans...');
      
      const plans = [
        {
          name: 'Basic',
          description: 'Perfect for small businesses just getting started',
          price: 29.99,
          billing_cycle: 'monthly',
          features: JSON.stringify([
            'Up to 1 branch',
            'Up to 5 users',
            'Up to 100 products',
            'Up to 1,000 orders/month',
            '1GB storage',
            'Basic support',
            'Standard reporting'
          ]),
          max_branches: 1,
          max_users: 5,
          max_products: 100,
          max_orders: 1000,
          storage_limit: 1024,
          support_level: 'basic'
        },
        {
          name: 'Professional',
          description: 'Ideal for growing businesses with multiple locations',
          price: 79.99,
          billing_cycle: 'monthly',
          features: JSON.stringify([
            'Up to 3 branches',
            'Up to 15 users',
            'Up to 500 products',
            'Up to 5,000 orders/month',
            '5GB storage',
            'Priority support',
            'Advanced reporting',
            'Inventory management',
            'Customer loyalty program'
          ]),
          max_branches: 3,
          max_users: 15,
          max_products: 500,
          max_orders: 5000,
          storage_limit: 5120,
          support_level: 'priority'
        },
        {
          name: 'Enterprise',
          description: 'For large businesses with complex requirements',
          price: 199.99,
          billing_cycle: 'monthly',
          features: JSON.stringify([
            'Unlimited branches',
            'Unlimited users',
            'Unlimited products',
            'Unlimited orders',
            '50GB storage',
            '24/7 premium support',
            'Custom reporting',
            'Advanced analytics',
            'Multi-location management',
            'API access',
            'Custom integrations'
          ]),
          max_branches: 999,
          max_users: 999,
          max_products: 999999,
          max_orders: 999999,
          storage_limit: 51200,
          support_level: 'premium'
        }
      ];

      for (const plan of plans) {
        await client.query(`
          INSERT INTO subscription_plans (
            name, description, price, billing_cycle, features,
            max_branches, max_users, max_products, max_orders,
            storage_limit, support_level
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        `, [
          plan.name, plan.description, plan.price, plan.billing_cycle,
          plan.features, plan.max_branches, plan.max_users, plan.max_products,
          plan.max_orders, plan.storage_limit, plan.support_level
        ]);
      }
      
      console.log('✓ Inserted 3 subscription plans');
    } else {
      console.log(`✓ Found ${planCount} existing subscription plans`);
    }

    // Get first shop and user for sample subscriptions
    const shops = await client.query('SELECT id FROM shops LIMIT 1');
    const users = await client.query('SELECT id FROM users LIMIT 1');
    const plans = await client.query('SELECT id, price FROM subscription_plans ORDER BY price LIMIT 3');

    if (shops.rows.length > 0 && users.rows.length > 0 && plans.rows.length > 0) {
      const shopId = shops.rows[0].id;
      const userId = users.rows[0].id;

      // Check existing subscriptions for this shop
      const existingSubscriptions = await client.query(
        'SELECT COUNT(*) FROM subscriptions WHERE shop_id = $1', 
        [shopId]
      );
      
      const subscriptionCount = parseInt(existingSubscriptions.rows[0].count);
      
      if (subscriptionCount === 0) {
        console.log('Creating sample subscriptions...');
        
        // Create an active subscription
        const startDate = new Date();
        const endDate = new Date();
        endDate.setFullYear(endDate.getFullYear() + 1);
        
        await client.query(`
          INSERT INTO subscriptions (
            shop_id, plan_id, status, start_date, end_date,
            auto_renew, discount_percent, discount_amount, total_amount, created_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
          shopId, plans.rows[0].id, 'active', startDate, endDate,
          true, 0, 0, plans.rows[0].price, userId
        ]);
        
        console.log('✓ Created sample active subscription');
        
        // Create an expired subscription for history
        const expiredStart = new Date();
        expiredStart.setFullYear(expiredStart.getFullYear() - 1);
        const expiredEnd = new Date();
        expiredEnd.setMonth(expiredEnd.getMonth() - 1);
        
        await client.query(`
          INSERT INTO subscriptions (
            shop_id, plan_id, status, start_date, end_date,
            auto_renew, discount_percent, discount_amount, total_amount, created_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
          shopId, plans.rows[1]?.id || plans.rows[0].id, 'expired', expiredStart, expiredEnd,
          false, 10, plans.rows[1]?.price * 0.1 || plans.rows[0].price * 0.1, 
          plans.rows[1]?.price * 0.9 || plans.rows[0].price * 0.9, userId
        ]);
        
        console.log('✓ Created sample expired subscription');
      } else {
        console.log(`✓ Found ${subscriptionCount} existing subscriptions for shop ${shopId}`);
      }
    }

    // Final verification
    const finalPlansCount = await client.query('SELECT COUNT(*) FROM subscription_plans WHERE active = true');
    const finalSubscriptionsCount = await client.query('SELECT COUNT(*) FROM subscriptions');
    
    console.log('\n📊 Final Status:');
    console.log(`✓ Active subscription plans: ${finalPlansCount.rows[0].count}`);
    console.log(`✓ Total subscriptions: ${finalSubscriptionsCount.rows[0].count}`);
    console.log('\n✅ Subscription data fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing subscription data:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the fix
fixSubscriptionData().catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
