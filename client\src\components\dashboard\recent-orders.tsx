import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Eye, Printer, RefreshCw, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useSocketEvent } from "@/context/socket-context";
import { DashboardEvents } from "@/lib/socket";
import { Link } from "wouter";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter
} from "@/components/ui/sheet";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

const orderTypeIcons = {
  dine_in: "utensils",
  takeaway: "shopping-bag",
  eat_and_pay: "credit-card",
  online: "motorcycle"
};

const orderTypeColors = {
  dine_in: "bg-blue-100 text-blue-800",
  takeaway: "bg-yellow-100 text-yellow-800",
  eat_and_pay: "bg-green-100 text-green-800",
  online: "bg-purple-100 text-purple-800"
};

const orderStatusColors = {
  pending: "bg-blue-100 text-blue-800",
  preparing: "bg-yellow-100 text-yellow-800",
  ready: "bg-orange-100 text-orange-800",
  completed: "bg-green-100 text-green-800",
  cancelled: "bg-red-100 text-red-800"
};

interface RecentOrdersProps {
  refreshInterval?: number;
  socketEnabled?: boolean;
}

export default function RecentOrders({
  refreshInterval = 30000,
  socketEnabled = false
}: RecentOrdersProps) {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const { toast } = useToast();
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [viewOrderDetails, setViewOrderDetails] = useState<any>(null);
  const [isViewSheetOpen, setIsViewSheetOpen] = useState<boolean>(false);

  // Fetch recent orders from API
  const {
    data: orders,
    isLoading,
    error,
    refetch: refetchOrders
  } = useQuery({
    queryKey: ['/api/orders/recent', 10],
    enabled: !!token && !!currentShop,
    refetchInterval: refreshInterval,
  });

  // Set up socket event listener for real-time updates
  useSocketEvent(DashboardEvents.RECENT_ORDERS_UPDATED, (newOrders) => {
    console.log('Received real-time recent orders update:', newOrders);
    // The query client will automatically update the cache with the new data
    refetchOrders();
  });

  // Handle manual refresh
  const handleRefresh = () => {
    if (!isRefreshing) {
      setIsRefreshing(true);
      refetchOrders().then(() => {
        setLastUpdated(new Date());
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      });
    }
  };

  // Update last updated time when orders change
  useEffect(() => {
    if (orders) {
      setLastUpdated(new Date());
    }
  }, [orders]);

  // Format the last updated time
  const formatLastUpdated = () => {
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) {
      return `${diffSec}s ago`;
    } else if (diffSec < 3600) {
      return `${Math.floor(diffSec / 60)}m ago`;
    } else {
      return lastUpdated.toLocaleTimeString();
    }
  };

  // Handle view order
  const handleViewOrder = async (orderId: number) => {
    if (!token || !currentShop) return;

    try {
      // Fetch the complete order data
      const response = await apiRequest("GET", `/api/orders/${orderId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch order details");
      }
      const orderData = await response.json();

      // Set the order details and open the sheet
      setViewOrderDetails(orderData);
      setIsViewSheetOpen(true);
    } catch (error) {
      console.error("Error fetching order details:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch order details",
      });
    }
  };

  // Handle print order
  const handlePrintOrder = async (order: any) => {
    if (!token || !currentShop) return;

    try {
      // Get the full order details if not already available
      let orderData = order;

      // Check if we have the complete order data structure
      if (!orderData.items || !orderData.order) {
        // If we have a nested structure with order property, use that
        if (orderData.order && !orderData.items) {
          // We need to fetch the complete order data
          const response = await apiRequest("GET", `/api/orders/${orderData.order.id}`);
          if (!response.ok) {
            throw new Error("Failed to fetch order details for printing");
          }
          orderData = await response.json();
        }
        // If we just have a simple order object, fetch the complete data
        else if (orderData.id) {
          const response = await apiRequest("GET", `/api/orders/${orderData.id}`);
          if (!response.ok) {
            throw new Error("Failed to fetch order details for printing");
          }
          orderData = await response.json();
        }
      }

      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error("Could not open print window. Please check your popup blocker settings.");
      }

      // Generate receipt HTML
      const receiptHtml = generateReceiptHtml(orderData, currentShop);

      // Write the receipt HTML to the new window
      printWindow.document.write(receiptHtml);
      printWindow.document.close();

      // Wait for resources to load then print
      printWindow.onload = () => {
        printWindow.print();
        // Close the window after printing (optional)
        // printWindow.onafterprint = () => printWindow.close();
      };

      toast({
        title: "Print order",
        description: `Printing order #${order.orderNumber}`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Print Failed",
        description: "Failed to print the order receipt",
      });
    }
  };

  // Generate HTML for the receipt
  const generateReceiptHtml = (orderData: any, shop: any) => {
    const { order, items } = orderData;
    const date = new Date(order.createdAt).toLocaleString();

    // Calculate totals
    const subtotal = order.subtotal || items.reduce((sum: number, item: any) => sum + Number(item.totalPrice), 0);
    const tax = order.taxAmount || 0;
    const discount = order.discountAmount || 0;
    const total = order.totalAmount || (subtotal + tax - discount);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt - Order #${order.orderNumber}</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 0;
            width: 80mm; /* Standard receipt width */
            margin: 0 auto;
          }
          .receipt {
            padding: 10px;
          }
          .header {
            text-align: center;
            margin-bottom: 10px;
          }
          .shop-name {
            font-size: 18px;
            font-weight: bold;
          }
          .shop-address, .shop-contact {
            font-size: 12px;
          }
          .order-info {
            margin: 10px 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 5px 0;
          }
          .order-number, .order-date, .order-type, .order-status {
            font-size: 12px;
          }
          .items {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
          }
          .items th, .items td {
            font-size: 12px;
            text-align: left;
            padding: 3px 0;
          }
          .items th {
            border-bottom: 1px solid #000;
          }
          .totals {
            margin: 10px 0;
            border-top: 1px dashed #000;
            padding-top: 5px;
          }
          .total-row {
            font-size: 12px;
            margin: 3px 0;
          }
          .grand-total {
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
          }
          .footer {
            text-align: center;
            margin-top: 10px;
            font-size: 12px;
            border-top: 1px dashed #000;
            padding-top: 5px;
          }
          @media print {
            body {
              width: 80mm;
            }
          }
        </style>
      </head>
      <body>
        <div class="receipt">
          <div class="header">
            <div class="shop-name">${shop.name || 'Nemboo Billing'}</div>
            <div class="shop-address">${shop.address || ''}</div>
            <div class="shop-contact">${shop.phone || ''}</div>
          </div>

          <div class="order-info">
            <div class="order-number">Order #: ${order.orderNumber}</div>
            <div class="order-date">Date: ${date}</div>
            <div class="order-type">Type: ${order.orderType === 'dine_in' ? 'Dine-in' :
                                          order.orderType === 'takeaway' ? 'Takeaway' :
                                          order.orderType === 'eat_and_pay' ? 'Eat & Pay' : 'Online'}</div>
            <div class="order-status">Status: ${order.status}</div>
            ${order.orderType === 'dine_in' && order.tableId ?
              `<div class="table">Table: ${order.tableName || order.tableId}</div>` :
              (order.orderType === 'takeaway' || order.orderType === 'eat_and_pay') && order.customerId ? `<div class="customer">Customer ID: ${order.customerId}</div>` :
              order.orderType === 'online' && order.platformId ? `<div class="platform">Platform ID: ${order.platformId}</div>` : ''}
          </div>

          <table class="items">
            <thead>
              <tr>
                <th>Item</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              ${items.map((item: any) => `
                <tr>
                  <td>${item.productName || `Product ${item.productId}`}</td>
                  <td>${item.quantity}</td>
                  <td>₹${item.unitPrice}</td>
                  <td>₹${item.totalPrice}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="totals" style ="margin-top:100px;">
            <div class="total-row">Subtotal: ₹${subtotal}</div>
            <div class="total-row">Tax: ₹${tax}</div>
            <div class="total-row">Discount: ₹${discount}</div>
            <div class="grand-total">Total: ₹${total}</div>
            <div class="total-row">Payment Method: ${order.paymentMethod || 'Cash'}</div>
          </div>

          <div class="footer">
            <div>Thank you for your business!</div>
            <div>${shop.footerText || ''}</div>
          </div>
        </div>
        <script>
          // Auto-print when loaded
          window.onload = function() {
            window.print();
          }
        </script>
      </body>
      </html>
    `;
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">Failed to load recent orders.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="border-t-4 border-t-secondary/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <CardTitle>Recent Orders</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "ml-2 text-muted-foreground hover:text-primary",
                isRefreshing && "animate-spin text-primary"
              )}
              onClick={handleRefresh}
              disabled={isRefreshing}
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <span className="text-xs text-muted-foreground ml-2">
              Updated {formatLastUpdated()}
            </span>
          </div>
          <Link href="/billing/orders">
            <Button variant="link" size="sm" className="font-medium text-secondary hover:text-secondary/80">
              View All
            </Button>
          </Link>
        </CardHeader>
        <CardContent className="mt-3">
          <div className="rounded-md border shadow-sm">
            <Table>
              <TableHeader className="bg-muted/50">
                <TableRow className="hover:bg-muted/70">
                  <TableHead className="font-semibold">Order ID</TableHead>
                  <TableHead className="font-semibold">Type</TableHead>
                  <TableHead className="font-semibold">Customer</TableHead>
                  <TableHead className="hidden sm:table-cell font-semibold">Items</TableHead>
                  <TableHead className="font-semibold">Amount</TableHead>
                  <TableHead className="font-semibold">Status</TableHead>
                  <TableHead className="text-right font-semibold">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(4).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell className="hidden sm:table-cell"><Skeleton className="h-4 w-12" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : orders?.length > 0 ? (
                  orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">#{order.orderNumber}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={cn("rounded-full py-1", orderTypeColors[order.orderType as keyof typeof orderTypeColors])}>
                          <span className="mr-1">
                            <i className={`fas fa-${orderTypeIcons[order.orderType as keyof typeof orderTypeIcons]}`}></i>
                          </span>
                          {order.orderType === 'dine_in' ? 'Dine-in' :
                           order.orderType === 'takeaway' ? 'Takeaway' :
                           order.orderType === 'eat_and_pay' ? 'Eat & Pay' : 'Online'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {order.orderType === 'dine_in' && order.tableId ?
                          (order.tableName ? order.tableName : `Table ${order.tableId}`) :
                         (order.orderType === 'takeaway' || order.orderType === 'eat_and_pay') && order.customerId ? "Customer" :
                         order.orderType === 'online' && order.platformId ? "Platform" :
                         order.tableId ?
                          (order.tableName ? order.tableName : `Table ${order.tableId}`) :
                         order.customerId ? "Customer" :
                         order.platformId ? "Platform" : "-"}
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">-</TableCell>
                      <TableCell>₹{order.totalAmount}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={cn("rounded-full", orderStatusColors[order.status as keyof typeof orderStatusColors])}>
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="mr-1"
                          onClick={() => handleViewOrder(order.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click event
                            handlePrintOrder(order);
                          }}
                        >
                          <Printer className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      No recent orders found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* View Order Sheet */}
      <Sheet open={isViewSheetOpen} onOpenChange={setIsViewSheetOpen}>
        <SheetContent className="sm:max-w-md md:max-w-lg overflow-y-auto" side="right">
          {viewOrderDetails ? (
            <>
              <SheetHeader className="mb-4">
                <SheetTitle>
                  Order #{viewOrderDetails.order.orderNumber}
                </SheetTitle>
              </SheetHeader>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Order Type</h4>
                    <p className="text-sm">
                      {viewOrderDetails.order.orderType === 'dine_in' ? 'Dine-in' :
                       viewOrderDetails.order.orderType === 'takeaway' ? 'Takeaway' :
                       viewOrderDetails.order.orderType === 'eat_and_pay' ? 'Eat & Pay' : 'Online'}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Status</h4>
                    <Badge variant="outline" className={cn("rounded-full",
                      orderStatusColors[viewOrderDetails.order.status as keyof typeof orderStatusColors])}>
                      {viewOrderDetails.order.status}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Date</h4>
                    <p className="text-sm">
                      {new Date(viewOrderDetails.order.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground">Customer/Table</h4>
                    <p className="text-sm">
                      {viewOrderDetails.order.orderType === 'dine_in' && viewOrderDetails.order.tableId ?
                        (viewOrderDetails.order.tableName ? viewOrderDetails.order.tableName : `Table ${viewOrderDetails.order.tableId}`) :
                       (viewOrderDetails.order.orderType === 'takeaway' || viewOrderDetails.order.orderType === 'eat_and_pay') && viewOrderDetails.order.customerId ? "Customer" :
                       viewOrderDetails.order.orderType === 'online' && viewOrderDetails.order.platformId ? "Platform" :
                       viewOrderDetails.order.tableId ?
                        (viewOrderDetails.order.tableName ? viewOrderDetails.order.tableName : `Table ${viewOrderDetails.order.tableId}`) :
                       viewOrderDetails.order.customerId ? "Customer" :
                       viewOrderDetails.order.platformId ? "Platform" : "-"}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Items</h4>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Item</TableHead>
                          <TableHead>Qty</TableHead>
                          <TableHead>Price</TableHead>
                          <TableHead>Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {viewOrderDetails.items.map((item: any) => (
                          <TableRow key={item.id}>
                            <TableCell>{item.productName || `Product ${item.productId}`}</TableCell>
                            <TableCell>{item.quantity}</TableCell>
                            <TableCell>₹{item.unitPrice}</TableCell>
                            <TableCell>₹{item.totalPrice}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Subtotal:</span>
                    <span className="text-sm">₹{viewOrderDetails.order.subtotal}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Tax:</span>
                    <span className="text-sm">₹{viewOrderDetails.order.taxAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Discount:</span>
                    <span className="text-sm">₹{viewOrderDetails.order.discountAmount}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Total:</span>
                    <span>₹{viewOrderDetails.order.totalAmount}</span>
                  </div>
                </div>

                {viewOrderDetails.order.notes && (
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-1">Notes</h4>
                    <p className="text-sm">{viewOrderDetails.order.notes}</p>
                  </div>
                )}
              </div>

              <SheetFooter className="flex flex-row gap-2 mt-6">
                <Button
                  variant="secondary"
                  onClick={() => handlePrintOrder(viewOrderDetails)}
                  className="flex-1"
                >
                  <Printer className="h-4 w-4 mr-2" />
                  Print Receipt
                </Button>
              </SheetFooter>
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p>Loading order details...</p>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </>
  );
}
