import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users } from "lucide-react";
import { cn } from "@/lib/utils";

// Define Table interface
interface Table {
  id: number;
  name: string;
  capacity: number;
  status: "available" | "occupied" | "reserved" | "billing";
  shopId: number;
}

interface TableGridProps {
  tables: Table[];
  isLoading: boolean;
  onTableSelect: (table: Table) => void;
  selectedTableId?: number | null;
}

export function TableGrid({ tables, isLoading, onTableSelect, selectedTableId }: TableGridProps) {
  // Status colors for visual indicators
  const statusColors = {
    available: "bg-green-100 text-green-800 border-green-300",
    occupied: "bg-red-100 text-red-800 border-red-300",
    reserved: "bg-yellow-100 text-yellow-800 border-yellow-300",
    billing: "bg-blue-100 text-blue-800 border-blue-300",
  };

  // Status icons for visual indicators
  const statusIcons = {
    available: "🟢",
    occupied: "🔴",
    reserved: "🟡",
    billing: "🔵",
  };

  if (isLoading) {
    // Show skeleton loading state
    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4">
        {Array(10).fill(0).map((_, index) => (
          <Card key={index} className="h-24 animate-pulse bg-gray-100 flex items-center justify-center">
            <div className="w-full h-full flex flex-col items-center justify-center">
              <div className="h-4 w-16 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 w-10 bg-gray-200 rounded"></div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (!tables || tables.length === 0) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">No tables available. Create tables in the Tables section.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4">
      {tables.map((table) => (
        <Card
          key={table.id}
          className={cn(
            "h-24 cursor-pointer transition-all duration-200 hover:shadow-md border-2",
            table.id === selectedTableId ? "ring-2 ring-primary ring-offset-2" : "",
            statusColors[table.status as keyof typeof statusColors]
          )}
          onClick={() => {
            console.log('TableGrid: Table clicked:', table);
            console.log('TableGrid: Current selectedTableId:', selectedTableId);
            onTableSelect(table);
          }}
        >
          <div className="w-full h-full flex flex-col items-center justify-center p-2 relative">
            <span className="absolute top-1 right-1 text-xs">{statusIcons[table.status as keyof typeof statusIcons]}</span>
            <h3 className="font-bold text-lg">{table.name}</h3>
            <div className="flex items-center mt-1">
              <Users className="h-3 w-3 mr-1" />
              <span className="text-xs">{table.capacity}</span>
            </div>
            <Badge 
              variant="outline" 
              className={cn(
                "mt-1 text-xs",
                table.status === "available" ? "bg-green-100 text-green-800" :
                table.status === "occupied" ? "bg-red-100 text-red-800" :
                table.status === "reserved" ? "bg-yellow-100 text-yellow-800" :
                "bg-blue-100 text-blue-800"
              )}
            >
              {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
            </Badge>
          </div>
        </Card>
      ))}
    </div>
  );
}
