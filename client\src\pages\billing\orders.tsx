import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation, Link } from "wouter";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest, queryClient, invalidateAndRefetch } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Eye,
  Printer,
  PlusCircle,
  Search,
  Calendar,
  FileText,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import OrderStatusSummary from "@/components/billing/order-status-summary";

// Status badge configurations
const orderTypeColors = {
  dine_in: "bg-gradient-to-r from-primary/20 to-primary/10 text-primary font-medium shadow-sm",
  takeaway: "bg-gradient-to-r from-secondary/30 to-secondary/20 text-secondary-foreground font-medium shadow-sm",
  eat_and_pay: "bg-gradient-to-r from-green-500/20 to-green-400/10 text-green-700 font-medium shadow-sm",
  online: "bg-gradient-to-r from-accent/30 to-accent/20 text-accent-foreground font-medium shadow-sm"
};

const orderStatusColors = {
  pending: "bg-gradient-to-r from-info/20 to-info/10 text-info font-medium shadow-sm",
  preparing: "bg-gradient-to-r from-warning/30 to-warning/20 text-warning-foreground font-medium shadow-sm",
  ready: "bg-gradient-to-r from-accent/30 to-accent/20 text-accent-foreground font-medium shadow-sm",
  completed: "bg-gradient-to-r from-success/20 to-success/10 text-success font-medium shadow-sm",
  cancelled: "bg-gradient-to-r from-destructive/20 to-destructive/10 text-destructive font-medium shadow-sm"
};

export default function Orders() {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [viewOrderDetails, setViewOrderDetails] = useState<any | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number>(30000); // 30 seconds by default

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // Add timestamp to force cache refresh
  const [refreshTimestamp, setRefreshTimestamp] = useState(Date.now());
  const [forceDirectFetch, setForceDirectFetch] = useState(false);

  // Fetch all orders with pagination and status filter
  const { data: ordersData, isLoading, error, refetch } = useQuery({
    queryKey: ['/api/orders/all', currentPage, pageSize, statusFilter, refreshTimestamp],
    queryFn: async () => {
      console.log('Fetching orders with fresh request...');

      // Create a completely fresh request with cache-busting
      const url = `/api/orders/all?page=${currentPage}&pageSize=${pageSize}&status=${statusFilter}&_t=${Date.now()}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem("auth_token")}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        cache: 'no-store' // Force no caching
      });

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }
      return response.json();
    },
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache data
    refetchOnMount: true, // Always refetch on mount
    refetchOnWindowFocus: false, // Don't refetch on window focus to avoid conflicts
  });

  // Force refetch when component mounts (especially when coming from POS)
  useEffect(() => {
    if (token && currentShop) {
      console.log('🚀 Orders page mounted - checking for fresh order creation');

      // Check URL parameters for refresh
      const urlParams = new URLSearchParams(window.location.search);
      const shouldRefresh = urlParams.get('refresh');
      const orderFromUrl = urlParams.get('order');

      // Check for forced reload flag first
      const forceReloadFlag = localStorage.getItem('forceOrdersReload');
      const freshOrderFlag = localStorage.getItem('freshOrderCreated');
      const quickSearchFlag = localStorage.getItem('quickSearchOrderCreated');
      const justCreatedFlag = localStorage.getItem('justCreatedOrder');

      if (shouldRefresh || forceReloadFlag || justCreatedFlag) {
        console.log('🚀 REFRESH DETECTED - Page was reloaded from Quick Product Search');
        console.log('🚀 Refresh triggers:', { shouldRefresh, forceReloadFlag, justCreatedFlag, orderFromUrl });

        // Get the new order info
        const newOrderNumber = localStorage.getItem('newOrderCreated') || orderFromUrl;
        const orderCreationTime = localStorage.getItem('orderCreationTime');

        console.log('🚀 New order details:', { newOrderNumber, orderCreationTime });

        // Clear the flags
        localStorage.removeItem('forceOrdersReload');
        localStorage.removeItem('freshOrderCreated');
        localStorage.removeItem('quickSearchOrderCreated');
        localStorage.removeItem('newOrderCreated');
        localStorage.removeItem('orderCreationTime');
        localStorage.removeItem('justCreatedOrder');

        // Clean up URL
        if (shouldRefresh || orderFromUrl) {
          window.history.replaceState({}, '', '/billing/orders');
        }

        // Show success message
        if (newOrderNumber) {
          toast({
            title: "Order Created Successfully! ✅",
            description: `Order ${newOrderNumber} was created via Quick Product Search and should appear below`,
            className: "bg-green-50 border-green-200 text-green-800",
          });
        }

        // NUCLEAR OPTION: Clear everything and force fresh data
        console.log('🚀 Clearing all cache and forcing fresh data...');
        queryClient.clear();
        setRefreshTimestamp(Date.now());

        // Force immediate refetch with no delay
        setTimeout(async () => {
          try {
            console.log('🚀 Fetching completely fresh data...');
            await fetchOrdersDirectly();
            console.log('✅ Fresh data loaded successfully');

            // Double-check: refetch again after a short delay
            setTimeout(async () => {
              try {
                console.log('🚀 Double-checking with second fetch...');
                await fetchOrdersDirectly();
                console.log('✅ Second fetch completed');
              } catch (error) {
                console.error('❌ Second fetch failed:', error);
              }
            }, 1000);

          } catch (error) {
            console.error('❌ Error fetching fresh data after reload:', error);
            refetch();
          }
        }, 100);

        return;
      }

      if (freshOrderFlag || quickSearchFlag) {
        console.log('🚀 Fresh order detected - forcing complete refresh');

        // Clear the flags
        localStorage.removeItem('freshOrderCreated');
        if (quickSearchFlag) {
          try {
            const quickSearchData = JSON.parse(quickSearchFlag);
            console.log('Quick search order detected:', quickSearchData);
            localStorage.removeItem('quickSearchOrderCreated');
          } catch (e) {
            console.error('Error parsing quick search flag:', e);
            localStorage.removeItem('quickSearchOrderCreated');
          }
        }

        // Nuclear option: clear everything and force fresh fetch
        queryClient.clear();
        setRefreshTimestamp(Date.now());

        // Use direct fetch to bypass all caching with a longer delay for quick search
        const delay = quickSearchFlag ? 1000 : 500;
        setTimeout(async () => {
          try {
            console.log('🚀 Attempting direct fetch...');
            await fetchOrdersDirectly();
            console.log('✅ Direct fetch completed successfully');
          } catch (error) {
            console.error('❌ Direct fetch failed, falling back to refetch:', error);
            refetch();
          }
        }, delay);
      } else {
        console.log('Normal page load - standard refresh');
        // Update timestamp to force cache refresh
        setRefreshTimestamp(Date.now());
        // Invalidate all order-related queries to ensure fresh data
        queryClient.invalidateQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' && queryKey[0].includes('/api/orders');
          }
        });
        // Force immediate refetch
        refetch();
      }
    }
  }, [token, currentShop, refetch]); // Only run when these dependencies change

  // Add periodic check for new orders (especially from quick search)
  useEffect(() => {
    if (!token || !currentShop) return;

    const checkForNewOrders = () => {
      const quickSearchFlag = localStorage.getItem('quickSearchOrderCreated');
      if (quickSearchFlag) {
        try {
          const quickSearchData = JSON.parse(quickSearchFlag);
          const now = Date.now();

          // If the quick search flag is less than 30 seconds old, force refresh
          if (now - quickSearchData.timestamp < 30000) {
            console.log('🚀 Periodic check: Quick search order detected, refreshing...');
            setRefreshTimestamp(Date.now());
            refetch();
            localStorage.removeItem('quickSearchOrderCreated');
          } else if (now - quickSearchData.timestamp > 30000) {
            // If it's been more than 30 seconds and still not showing, force page reload
            console.log('🚀 Quick search order still not visible after 30 seconds, forcing page reload...');
            localStorage.removeItem('quickSearchOrderCreated');
            window.location.reload();
          }
        } catch (e) {
          localStorage.removeItem('quickSearchOrderCreated');
        }
      }
    };

    // Check every 3 seconds for new orders (more frequent)
    const interval = setInterval(checkForNewOrders, 3000);

    return () => clearInterval(interval);
  }, [token, currentShop, refetch]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Direct fetch function that bypasses React Query
  const fetchOrdersDirectly = async () => {
    try {
      console.log('🚀 Fetching orders directly, bypassing ALL cache...');

      // Use multiple cache-busting parameters
      const cacheBuster = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const url = `/api/orders/all?page=${currentPage}&pageSize=${pageSize}&status=${statusFilter}&_t=${cacheBuster}&_cb=${refreshTimestamp}&_force=true&_nocache=${Math.random()}`;

      console.log('🚀 Direct fetch URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem("auth_token")}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();
      console.log('✅ Direct fetch successful:', {
        total: data.total,
        ordersCount: data.orders?.length,
        firstOrder: data.orders?.[0]?.orderNumber,
        timestamp: new Date().toISOString()
      });

      // Manually update the query cache with fresh data
      queryClient.setQueryData(['/api/orders/all', currentPage, pageSize, statusFilter, refreshTimestamp], data);

      // Force trigger a re-render by updating the refresh timestamp
      setRefreshTimestamp(Date.now());

      return data;
    } catch (error) {
      console.error('❌ Direct fetch failed:', error);
      throw error;
    }
  };

  const handleManualRefresh = async () => {
    console.log('Manual refresh triggered');
    setRefreshTimestamp(Date.now());
    queryClient.clear();

    try {
      await fetchOrdersDirectly();
      toast({
        title: "Refreshed",
        description: "Order list has been refreshed with fresh data",
      });
    } catch (error) {
      console.error('Manual refresh failed:', error);
      refetch(); // Fallback to normal refetch
    }
  };

  // Add a nuclear refresh option for debugging
  const handleNuclearRefresh = async () => {
    console.log('Nuclear refresh triggered - clearing everything and reloading page');

    // Clear all localStorage flags
    localStorage.removeItem('freshOrderCreated');
    localStorage.removeItem('quickSearchOrderCreated');

    // Clear all React Query cache
    queryClient.clear();

    // Force page reload
    window.location.reload();
  };

  // Handle page size change
  const handlePageSizeChange = (size: string) => {
    setPageSize(parseInt(size));
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Update order status mutation
  const updateOrderStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }) => {
      return apiRequest("PATCH", `/api/orders/${id}/status`, { status });
    },
    onSuccess: async () => {
      // Immediately invalidate and refetch orders data
      await invalidateAndRefetch('/api/orders/all');

      toast({
        title: "Status updated",
        description: "Order status has been updated successfully",
      });

      // Close order details dialog
      setViewOrderDetails(null);
    },
    onError: (error) => {
      console.error("Failed to update order status:", error);
      toast({
        variant: "destructive",
        title: "Update failed",
        description: "Failed to update order status",
      });
    },
  });

  // Filter orders based on search term only (status filtering is done on the server)
  const filteredOrders = ordersData?.orders?.filter(order => {
    // Filter by search term
    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
      return (
        order.orderNumber.toLowerCase().includes(searchTermLower) ||
        order.orderType.toLowerCase().includes(searchTermLower)
      );
    }

    return true;
  });

  // Handle view order details
  const handleViewOrder = async (orderId: number) => {
    if (!token || !currentShop) return;

    try {
      const response = await apiRequest("GET", `/api/orders/${orderId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch order details");
      }

      const data = await response.json();
      setViewOrderDetails(data);
    } catch (error) {
      console.error("Error fetching order details:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch order details",
      });
    }
  };

  // Refresh order details
  const refreshOrderDetails = async () => {
    if (!viewOrderDetails || !token || !currentShop) return;

    try {
      const response = await apiRequest("GET", `/api/orders/${viewOrderDetails.order.id}`);

      if (!response.ok) {
        throw new Error("Failed to refresh order details");
      }

      const data = await response.json();
      setViewOrderDetails(data);

      toast({
        title: "Refreshed",
        description: "Order details have been refreshed",
      });
    } catch (error) {
      console.error("Error refreshing order details:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to refresh order details",
      });
    }
  };

  // Handle update order status
  const handleUpdateStatus = (status: string) => {
    if (!viewOrderDetails) return;

    updateOrderStatusMutation.mutate({
      id: viewOrderDetails.order.id,
      status,
    });
  };

  // Handle print order with browser print functionality
  const handlePrintOrder = async (order: any) => {
    if (!token || !currentShop) return;

    try {
      // Get the full order details if not already available
      let orderData = order;

      // Check if we have the complete order data structure
      if (!orderData.items || !orderData.order) {
        // If we have a nested structure with order property, use that
        if (orderData.order && !orderData.items) {
          // We need to fetch the complete order data
          const response = await apiRequest("GET", `/api/orders/${orderData.order.id}`);
          if (!response.ok) {
            throw new Error("Failed to fetch order details for printing");
          }
          orderData = await response.json();
        }
        // If we just have a simple order object, fetch the complete data
        else if (orderData.id) {
          const response = await apiRequest("GET", `/api/orders/${orderData.id}`);
          if (!response.ok) {
            throw new Error("Failed to fetch order details for printing");
          }
          orderData = await response.json();
        }
      }

      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error("Could not open print window. Please check your popup blocker settings.");
      }

      // Generate receipt HTML
      const receiptHtml = generateReceiptHtml(orderData, currentShop);

      // Write the receipt HTML to the new window
      printWindow.document.write(receiptHtml);
      printWindow.document.close();

      // Wait for resources to load then print
      printWindow.onload = () => {
        printWindow.print();
        // Close the window after printing (optional)
        // printWindow.onafterprint = () => printWindow.close();
      };

      toast({
        title: "Print order",
        description: `Printing order #${order.orderNumber}`,
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Print Failed",
        description: "Failed to print the order receipt",
      });
    }
  };

  // Generate HTML for the receipt
  const generateReceiptHtml = (orderData: any, shop: any) => {
    const { order, items } = orderData;
    const date = new Date(order.createdAt).toLocaleString();

    // Calculate totals
    const subtotal = order.subtotal || items.reduce((sum: number, item: any) => sum + Number(item.totalPrice), 0);
    const tax = order.taxAmount || 0;
    const discount = order.discountAmount || 0;
    const total = order.totalAmount || (subtotal + tax - discount);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt - Order #${order.orderNumber}</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 0;
            width: 80mm; /* Standard receipt width */
            margin: 0 auto;
          }
          .receipt {
            padding: 10px;
          }
          .header {
            text-align: center;
            margin-bottom: 10px;
          }
          .shop-name {
            font-size: 18px;
            font-weight: bold;
          }
          .shop-address, .shop-contact {
            font-size: 12px;
          }
          .order-info {
            margin: 10px 0;
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 5px 0;
          }
          .order-number, .order-date, .order-type, .order-status {
            font-size: 12px;
          }
          .items {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
          }
          .items th {
            text-align: left;
            font-size: 12px;
            border-bottom: 1px solid #000;
          }
          .items td {
            font-size: 12px;
            padding: 3px 0;
          }
          .totals {
            margin: 10px 0;
            text-align: right;
          }
          .total-row {
            font-size: 12px;
            margin: 2px 0;
          }
          .grand-total {
            font-size: 14px;
            font-weight: bold;
            border-top: 1px solid #000;
            padding-top: 5px;
          }
          .footer {
            text-align: center;
            margin-top: 10px;
            font-size: 12px;
            border-top: 1px dashed #000;
            padding-top: 10px;
          }
          @media print {
            body {
              width: 80mm;
              margin: 0;
              padding: 0;
            }
            .receipt {
              width: 100%;
            }
          }
        </style>
      </head>
      <body>
        <div class="receipt">
          <div class="header">
            <div class="shop-name">${shop.name || 'Nemboo Billing'}</div>
            <div class="shop-address">${shop.address || ''}</div>
            <div class="shop-contact">${shop.phone || ''}</div>
          </div>

          <div class="order-info">
            <div class="order-number">Order #: ${order.orderNumber}</div>
            <div class="order-date">Date: ${date}</div>
            <div class="order-type">Type: ${order.orderType === 'dine_in' ? 'Dine-in' :
                                          order.orderType === 'takeaway' ? 'Takeaway' :
                                          order.orderType === 'eat_and_pay' ? 'Eat & Pay' : 'Online'}</div>
            <div class="order-status">Status: ${order.status}</div>
            ${order.orderType === 'dine_in' && order.tableId ?
              `<div class="table">Table: ${order.tableName || order.tableId}</div>` :
              (order.orderType === 'takeaway' || order.orderType === 'eat_and_pay') && order.customerId ? `<div class="customer">Customer ID: ${order.customerId}</div>` :
              order.orderType === 'online' && order.platformId ? `<div class="platform">Platform ID: ${order.platformId}</div>` : ''}
          </div>

          <table class="items">
            <thead>
              <tr>
                <th>Item</th>
                <th>Qty</th>
                <th>Price</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              ${items.map((item: any) => `
                <tr>
                  <td>${item.productName || `Product ${item.productId}`}</td>
                  <td>${item.quantity}</td>
                  <td>₹${item.unitPrice}</td>
                  <td>₹${item.totalPrice}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="totals">
            <div class="total-row">Subtotal: ₹${subtotal}</div>
            <div class="total-row">Tax: ₹${tax}</div>
            <div class="total-row">Discount: ₹${discount}</div>
            <div class="grand-total">Total: ₹${total}</div>
            <div class="total-row">Payment Method: ${order.paymentMethod || 'Cash'}</div>
          </div>

          <div class="footer">
            <div>Thank you for your business!</div>
            <div>${shop.footerText || ''}</div>
          </div>
        </div>
        <script>
          // Auto-print when loaded
          window.onload = function() {
            window.print();
          }
        </script>
      </body>
      </html>
    `;
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Orders</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleManualRefresh}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={handleNuclearRefresh}
            className="text-red-600 border-red-200 hover:bg-red-50"
          >
            🚀 Nuclear Refresh
          </Button>
          <Button onClick={() => setLocation("/billing/pos")}>
            <PlusCircle className="h-4 w-4 mr-2" />
            New Order
          </Button>
        </div>
      </div>



      {/* Quick Product Search Success Indicator */}
      {(localStorage.getItem('newOrderCreated') || new URLSearchParams(window.location.search).get('order')) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-green-600 text-xl">✅</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">
                  Quick Product Search Order Created
                </h3>
                <div className="mt-1 text-sm text-green-700">
                  Order {localStorage.getItem('newOrderCreated') || new URLSearchParams(window.location.search).get('order')} was successfully created and should appear below.
                  {ordersData?.orders?.length > 0 && (
                    <span className="ml-2 font-medium">
                      {ordersData.orders.find(o => o.orderNumber === (localStorage.getItem('newOrderCreated') || new URLSearchParams(window.location.search).get('order')))
                        ? '🎉 Found in list!'
                        : '⏳ Loading...'}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                localStorage.removeItem('newOrderCreated');
                window.history.replaceState({}, '', '/billing/orders');
                setRefreshTimestamp(Date.now());
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </Button>
          </div>
        </div>
      )}

      {/* Order Status Summary */}
      <OrderStatusSummary
        refreshInterval={30000}
        onRefresh={() => refetch()}
      />

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>All Orders</CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search orders..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  // Only reset page if we're clearing the search or if there's a significant change
                  if (e.target.value === '' || (searchTerm.length > 2 && e.target.value.length <= 2)) {
                    setCurrentPage(1);
                  }
                }}
              />
            </div>

            <Select
              value={statusFilter}
              onValueChange={(value) => {
                setStatusFilter(value);
                setCurrentPage(1); // Reset to first page when changing status filter
              }}
            >
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Orders</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="preparing">Preparing</SelectItem>
                <SelectItem value="ready">Ready</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={pageSize.toString()}
              onValueChange={handlePageSizeChange}
            >
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue placeholder="Items per page" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="25">25 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
                <SelectItem value="100">100 per page</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Orders Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Customer/Table</TableHead>
                  <TableHead className="hidden sm:table-cell">Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell className="hidden sm:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-red-500">
                      Failed to load orders. Please try again.
                    </TableCell>
                  </TableRow>
                ) : filteredOrders && filteredOrders.length > 0 ? (
                  filteredOrders.map((order: any) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">#{order.orderNumber}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={cn("rounded-full py-1", orderTypeColors[order.orderType as keyof typeof orderTypeColors])}>
                          {order.orderType === 'dine_in' ? 'Dine-in' :
                           order.orderType === 'takeaway' ? 'Takeaway' :
                           order.orderType === 'eat_and_pay' ? 'Eat & Pay' : 'Online'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {order.orderType === 'dine_in' && order.tableId ?
                          (order.tableName ? order.tableName : `Table ${order.tableId}`) :
                         (order.orderType === 'takeaway' || order.orderType === 'eat_and_pay') && order.customerId ? "Customer" :
                         order.orderType === 'online' && order.platformId ? "Platform" :
                         order.tableId ?
                          (order.tableName ? order.tableName : `Table ${order.tableId}`) :
                         order.customerId ? "Customer" :
                         order.platformId ? "Platform" : "-"}
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>₹{order.totalAmount}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={cn("rounded-full", orderStatusColors[order.status as keyof typeof orderStatusColors])}>
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewOrder(order.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click event
                            handlePrintOrder(order);
                          }}
                        >
                          <Printer className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                      No orders found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {!isLoading && ordersData && ordersData.totalPages > 1 && (
            <div className="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {filteredOrders?.length || 0} of {ordersData.total} orders
              </div>

              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(1)}
                      disabled={currentPage === 1}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      <ChevronsLeft className="h-4 w-4" />
                    </PaginationLink>
                  </PaginationItem>

                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, ordersData.totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (ordersData.totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (currentPage >= ordersData.totalPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = ordersData.totalPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={currentPage === pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(ordersData.totalPages, currentPage + 1))}
                      disabled={currentPage === ordersData.totalPages}
                      className={currentPage === ordersData.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(ordersData.totalPages)}
                      disabled={currentPage === ordersData.totalPages}
                      className={currentPage === ordersData.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      <ChevronsRight className="h-4 w-4" />
                    </PaginationLink>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Order Details Sheet */}
      <Sheet
        open={!!viewOrderDetails}
        onOpenChange={(open) => !open && setViewOrderDetails(null)}
      >
        <SheetContent className="w-full sm:max-w-xl md:max-w-2xl overflow-y-auto">
          <SheetHeader>
            <div className="flex justify-between items-start">
              <div>
                <SheetTitle>Order Details</SheetTitle>
                <SheetDescription>
                  {viewOrderDetails && (
                    <div className="flex items-center mt-1">
                      <span className="text-sm font-medium">
                        #{viewOrderDetails.order.orderNumber}
                      </span>
                      <Badge
                        variant="outline"
                        className={cn(
                          "ml-3 rounded-full",
                          orderStatusColors[viewOrderDetails.order.status as keyof typeof orderStatusColors]
                        )}
                      >
                        {viewOrderDetails.order.status}
                      </Badge>
                    </div>
                  )}
                </SheetDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshOrderDetails}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </SheetHeader>

          {viewOrderDetails && (
            <>
              <div className="grid grid-cols-2 gap-4 py-2">
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">Order Type</h4>
                  <p>
                    {viewOrderDetails.order.orderType === 'dine_in' ? 'Dine-in' :
                     viewOrderDetails.order.orderType === 'takeaway' ? 'Takeaway' :
                     viewOrderDetails.order.orderType === 'eat_and_pay' ? 'Eat & Pay' : 'Online'}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">Date & Time</h4>
                  <p>{new Date(viewOrderDetails.order.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">Payment Method</h4>
                  <p className="capitalize">{viewOrderDetails.order.paymentMethod}</p>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-muted-foreground mb-1">Payment Status</h4>
                  <p className="capitalize">{viewOrderDetails.order.paymentStatus}</p>
                </div>
                {viewOrderDetails.order.notes && (
                  <div className="col-span-2">
                    <h4 className="text-sm font-semibold text-muted-foreground mb-1">Notes</h4>
                    <p>{viewOrderDetails.order.notes}</p>
                  </div>
                )}
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-semibold mb-2">Order Items</h4>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Item</TableHead>
                        <TableHead className="text-right">Unit Price</TableHead>
                        <TableHead className="text-right">Quantity</TableHead>
                        <TableHead className="text-right">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {viewOrderDetails.items.map((item: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell>{item.productName || `Product ${item.productId}`}</TableCell>
                          <TableCell className="text-right">₹{item.unitPrice}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">₹{item.totalPrice}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>₹{viewOrderDetails.order.subtotal}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tax</span>
                    <span>₹{viewOrderDetails.order.taxAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Discount</span>
                    <span>₹{viewOrderDetails.order.discountAmount}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>₹{viewOrderDetails.order.totalAmount}</span>
                  </div>
                </div>
              </div>

              <SheetFooter className="flex flex-col gap-3">
                {/* Status Update Buttons - Show all separately */}
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    className="flex-1"
                    onClick={() => handleUpdateStatus('preparing')}
                    disabled={updateOrderStatusMutation.isPending || viewOrderDetails.order.status === 'completed' || viewOrderDetails.order.status === 'cancelled'}
                    variant={viewOrderDetails.order.status === 'preparing' ? "default" : "outline"}
                  >
                    {viewOrderDetails.order.status === 'preparing' ? '✓ ' : ''}Mark as Preparing
                  </Button>

                  <Button
                    className="flex-1"
                    onClick={() => handleUpdateStatus('ready')}
                    disabled={updateOrderStatusMutation.isPending || viewOrderDetails.order.status === 'completed' || viewOrderDetails.order.status === 'cancelled'}
                    variant={viewOrderDetails.order.status === 'ready' ? "default" : "outline"}
                  >
                    {viewOrderDetails.order.status === 'ready' ? '✓ ' : ''}Mark as Ready
                  </Button>

                  <Button
                    className="flex-1"
                    onClick={() => handleUpdateStatus('completed')}
                    disabled={updateOrderStatusMutation.isPending || viewOrderDetails.order.status === 'completed' || viewOrderDetails.order.status === 'cancelled'}
                    variant={viewOrderDetails.order.status === 'completed' ? "default" : "outline"}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {viewOrderDetails.order.status === 'completed' ? '✓ ' : ''}Mark as Completed
                  </Button>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-2">
                  {(viewOrderDetails.order.status === 'pending' ||
                    viewOrderDetails.order.status === 'preparing' ||
                    viewOrderDetails.order.status === 'ready') && (
                    <Button
                      variant="destructive"
                      className="flex-1"
                      onClick={() => handleUpdateStatus('cancelled')}
                      disabled={updateOrderStatusMutation.isPending}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Cancel Order
                    </Button>
                  )}

                  <Button
                    variant="secondary"
                    className="flex-1"
                    onClick={() => handlePrintOrder(viewOrderDetails)}
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    Print Receipt
                  </Button>
                </div>
              </SheetFooter>
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
}
