import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function debugMarkAsRead() {
  const client = await pool.connect();
  try {
    console.log('🔍 Debugging Mark-as-Read Functionality...\n');

    // 1. Check current unread notifications
    const unreadResult = await client.query(`
      SELECT 
        n.id,
        n.title,
        n.message,
        nr.user_id,
        nr.status,
        n.shop_id
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.status = 'unread'
      ORDER BY n.created_at DESC
      LIMIT 10
    `);

    console.log('📋 Current unread notifications:');
    unreadResult.rows.forEach((notif, index) => {
      console.log(`   ${index + 1}. ID: ${notif.id}, User: ${notif.user_id}, Shop: ${notif.shop_id}`);
      console.log(`      Title: ${notif.title}`);
      console.log(`      Status: ${notif.status}`);
    });

    if (unreadResult.rows.length === 0) {
      console.log('   No unread notifications found.');
      return;
    }

    // 2. Test marking one notification as read
    const testNotification = unreadResult.rows[0];
    console.log(`\n🧪 Testing mark-as-read for notification ID: ${testNotification.id}, User: ${testNotification.user_id}`);

    const markReadResult = await client.query(`
      UPDATE notification_recipients 
      SET status = 'read', read_at = NOW()
      WHERE notification_id = $1 AND user_id = $2
      RETURNING *
    `, [testNotification.id, testNotification.user_id]);

    if (markReadResult.rowCount > 0) {
      console.log('   ✅ Successfully marked as read in database');
      console.log('   Updated record:', markReadResult.rows[0]);
    } else {
      console.log('   ❌ Failed to mark as read - no rows updated');
    }

    // 3. Verify the change
    const verifyResult = await client.query(`
      SELECT status, read_at
      FROM notification_recipients
      WHERE notification_id = $1 AND user_id = $2
    `, [testNotification.id, testNotification.user_id]);

    console.log('\n✅ Verification:');
    if (verifyResult.rows.length > 0) {
      const record = verifyResult.rows[0];
      console.log(`   Status: ${record.status}`);
      console.log(`   Read at: ${record.read_at}`);
    }

    // 4. Check updated unread count for this user
    const countResult = await client.query(`
      SELECT COUNT(*) as unread_count
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.user_id = $1 AND nr.status = 'unread'
    `, [testNotification.user_id]);

    console.log(`\n📊 Updated unread count for user ${testNotification.user_id}: ${countResult.rows[0].unread_count}`);

    // 5. Show remaining unread notifications for this user
    const remainingResult = await client.query(`
      SELECT 
        n.id,
        n.title,
        nr.status
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.user_id = $1 AND nr.status = 'unread'
      ORDER BY n.created_at DESC
      LIMIT 5
    `, [testNotification.user_id]);

    console.log(`\n📋 Remaining unread notifications for user ${testNotification.user_id}:`);
    remainingResult.rows.forEach((notif, index) => {
      console.log(`   ${index + 1}. ID: ${notif.id} - ${notif.title} (${notif.status})`);
    });

    console.log('\n🎯 What this means:');
    console.log('   - The database update is working correctly');
    console.log('   - The issue is likely in the frontend not refreshing the data');
    console.log('   - Try hard refresh (Ctrl+Shift+R) in your browser');
    console.log('   - Check browser console for any errors');

    console.log('\n💡 To test in browser console:');
    console.log(`
// Test the API directly:
fetch('/api/notifications/${testNotification.id}/read', {
  method: 'PATCH',
  headers: {
    'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
    'X-Shop-ID': '${testNotification.shop_id}'
  }
})
.then(response => response.json())
.then(data => console.log('Mark as read response:', data))
.catch(error => console.error('Error:', error));
    `);

  } catch (error) {
    console.error('❌ Error debugging mark-as-read:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

debugMarkAsRead();
