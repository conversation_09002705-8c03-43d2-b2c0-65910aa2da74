import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function createProperTableMapping() {
  const client = await pool.connect();
  try {
    console.log('Current table data:');
    const currentTables = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(currentTables.rows);
    
    // The issue: You want "Table 3" to have ID 3, but currently:
    // - ID 2 has "table 3" 
    // - ID 3 has "Table 1"
    // - ID 5 has "Table 3"
    
    // Solution: Create a clean table mapping
    // We'll update the table at ID 3 to be "Table 3" and remove duplicates
    
    console.log('\n🔧 Creating proper table mapping...');
    
    // Step 1: Update ID 3 to be "Table 3" 
    await client.query(
      'UPDATE tables SET name = $1 WHERE id = $2',
      ['Table 3', 3]
    );
    console.log('✅ Updated table ID 3 to "Table 3"');
    
    // Step 2: Update ID 2 to be "Table 2" (since it was "table 3")
    await client.query(
      'UPDATE tables SET name = $1 WHERE id = $2',
      ['Table 2', 2]
    );
    console.log('✅ Updated table ID 2 to "Table 2"');
    
    // Step 3: Update ID 5 to be "Table 5" (since it was duplicate "Table 3")
    await client.query(
      'UPDATE tables SET name = $1 WHERE id = $2',
      ['Table 5', 5]
    );
    console.log('✅ Updated table ID 5 to "Table 5"');
    
    // Step 4: Clean up other tables for consistency
    const cleanupUpdates = [
      { id: 1, name: 'Table 1' },
      { id: 4, name: 'Table 4' },
      { id: 6, name: 'Table 6' },
      { id: 7, name: 'Table 7' },
      { id: 8, name: 'Table 8' },
      { id: 9, name: 'Table 9' },
      { id: 10, name: 'Table 10' },
      { id: 11, name: 'Table 11' },
      { id: 12, name: 'Table 12' },
      { id: 13, name: 'Table 13' },
      { id: 14, name: 'Table 14' },
    ];
    
    for (const update of cleanupUpdates) {
      await client.query(
        'UPDATE tables SET name = $1 WHERE id = $2',
        [update.name, update.id]
      );
      console.log(`✅ Updated table ID ${update.id} to "${update.name}"`);
    }
    
    console.log('\n📋 Final table mapping:');
    const finalTables = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(finalTables.rows);
    
    console.log('\n🎉 SUCCESS! Now when you select "Table 3", it will use ID 3!');
    console.log('Table names now match their IDs for better consistency.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createProperTableMapping();
