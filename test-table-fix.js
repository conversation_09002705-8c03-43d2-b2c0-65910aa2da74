import { Pool } from 'pg';
import dotenv from 'dotenv';
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function testTableFix() {
  const client = await pool.connect();
  try {
    console.log('🔍 Testing Table 3 fix...');
    
    const result = await client.query('SELECT id, name FROM tables WHERE id = 3');
    
    if (result.rows.length > 0) {
      const table3 = result.rows[0];
      console.log('Table ID 3 details:', table3);
      
      if (table3.name === 'Table 3') {
        console.log('✅ FIXED: Table 3 now has correct mapping (ID 3 → "Table 3")');
        console.log('✅ When you select "Table 3", it will store as ID 3');
      } else {
        console.log('❌ NOT FIXED: Table ID 3 has name:', table3.name);
      }
    } else {
      console.log('❌ No table found with ID 3');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

testTableFix();
