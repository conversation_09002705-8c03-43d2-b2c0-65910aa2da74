import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Test credentials
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

async function login() {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    const data = await response.json();
    authToken = data.token;
    console.log('✅ Login successful');
    return data;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

async function testRefundsAPI() {
  console.log('🧪 Testing refunds API...\n');

  try {
    // Step 1: Login
    await login();

    // Step 2: Test refunds API without any filters
    console.log('\n📋 Testing refunds API without filters...');
    const response1 = await fetch(`${BASE_URL}/api/refunds`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (response1.ok) {
      const refunds1 = await response1.json();
      console.log(`✅ No filters: Found ${refunds1.length} refunds`);
      refunds1.forEach(refund => {
        console.log(`  - Refund ${refund.id}: ${refund.orderNumber}, ₹${refund.amount}, ${refund.refundMethod}`);
      });
    } else {
      console.log(`❌ No filters: Failed with status ${response1.status}`);
    }

    // Step 3: Test with date range (like the frontend does)
    console.log('\n📅 Testing refunds API with date range...');
    const startDate = new Date('2025-05-12T09:26:41.511Z');
    const endDate = new Date('2025-06-11T09:26:41.511Z');
    
    const params = new URLSearchParams({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });
    
    const response2 = await fetch(`${BASE_URL}/api/refunds?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (response2.ok) {
      const refunds2 = await response2.json();
      console.log(`✅ With date range: Found ${refunds2.length} refunds`);
      refunds2.forEach(refund => {
        console.log(`  - Refund ${refund.id}: ${refund.orderNumber}, ₹${refund.amount}, ${refund.refundMethod}, ${refund.createdAt}`);
      });
    } else {
      console.log(`❌ With date range: Failed with status ${response2.status}`);
    }

    // Step 4: Test payments API to see refund status
    console.log('\n💳 Testing payments API to check refund status...');
    const response3 = await fetch(`${BASE_URL}/api/payments`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (response3.ok) {
      const payments = await response3.json();
      console.log(`✅ Found ${payments.length} payments`);
      
      // Find payments with refunds
      const refundedPayments = payments.filter(p => p.refundStatus !== 'not_refunded');
      console.log(`💰 Payments with refunds: ${refundedPayments.length}`);
      
      refundedPayments.forEach(payment => {
        console.log(`  - Payment ${payment.orderNumber}: Status=${payment.refundStatus}, Amount=₹${payment.refundAmount}, Method=${payment.refundMethod}`);
      });
    } else {
      console.log(`❌ Payments API: Failed with status ${response3.status}`);
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testRefundsAPI();
