// Test subscription payment methods directly with database
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function testSubscriptionPaymentMethods() {
  const client = await pool.connect();
  try {
    console.log('🧪 Testing subscription payment methods...\n');
    
    // Test 1: Check if table exists
    console.log('1. Checking if subscription_payment_methods table exists...');
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'subscription_payment_methods'
      );
    `);
    console.log('   ✅ Table exists:', tableExists.rows[0].exists);
    
    // Test 2: Check table structure
    console.log('\n2. Checking table structure...');
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'subscription_payment_methods' 
      ORDER BY ordinal_position;
    `);
    console.log('   ✅ Table columns:');
    columns.rows.forEach(col => {
      console.log(`      ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Test 3: Try to query the table
    console.log('\n3. Testing basic query...');
    const result = await client.query('SELECT COUNT(*) FROM subscription_payment_methods');
    console.log('   ✅ Current records count:', result.rows[0].count);
    
    // Test 4: Test query with shop filter
    console.log('\n4. Testing shop-specific query...');
    const shopResult = await client.query(`
      SELECT * FROM subscription_payment_methods 
      WHERE shop_id = $1 AND active = true
    `, [1]);
    console.log('   ✅ Records for shop 1:', shopResult.rows.length);
    
    console.log('\n🎉 All tests passed! The subscription_payment_methods table is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

testSubscriptionPaymentMethods();
