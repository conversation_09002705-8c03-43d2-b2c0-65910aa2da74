import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function createNotificationsForCurrentShop() {
  const client = await pool.connect();
  try {
    console.log('🔔 Creating notifications for your current shop...\n');

    // Get all shops and users to understand the current setup
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    const usersResult = await client.query('SELECT id, name, email FROM users ORDER BY id');

    console.log('🏪 Available shops:');
    shopsResult.rows.forEach(shop => {
      console.log(`   Shop ${shop.id}: ${shop.name}`);
    });

    console.log('\n👥 Available users:');
    usersResult.rows.forEach(user => {
      console.log(`   User ${user.id}: ${user.name} (${user.email})`);
    });

    // Ask user to specify which shop they want notifications for
    console.log('\n🎯 I will create notifications for ALL shops so you can see them regardless of which shop you select.');
    console.log('This way, when you switch shops, you will always see notifications for that shop.\n');

    // Clear all existing notifications first
    await client.query('DELETE FROM notification_recipients');
    await client.query('DELETE FROM notifications');
    console.log('🗑️  Cleared all existing notifications\n');

    // Create notifications for each shop
    for (const shop of shopsResult.rows) {
      console.log(`📝 Creating notifications for ${shop.name} (Shop ID: ${shop.id})...`);

      // Create 5 different notifications for this shop
      const notifications = [
        {
          title: `${shop.name} - Stock Alert!`,
          message: `Urgent: Coffee beans are out of stock at ${shop.name}. Please reorder immediately!`,
          type: 'stock',
          priority: 'urgent'
        },
        {
          title: `${shop.name} - Low Stock Warning`,
          message: `Ice cream stock is running low at ${shop.name} (2 remaining, minimum: 5)`,
          type: 'stock',
          priority: 'high'
        },
        {
          title: `${shop.name} - New Order Received`,
          message: `Order #ORD-${String(shop.id).padStart(3, '0')} has been placed at ${shop.name} by customer Sarah Johnson`,
          type: 'order',
          priority: 'normal'
        },
        {
          title: `Welcome to ${shop.name}!`,
          message: `Your notification system is now active for ${shop.name}. You'll receive real-time alerts for orders, stock levels, and important updates.`,
          type: 'system',
          priority: 'normal'
        },
        {
          title: `${shop.name} - Daily Sales Update`,
          message: `Great day at ${shop.name}! Today's sales: ₹2,450 from 28 orders. Keep up the excellent work!`,
          type: 'system',
          priority: 'normal'
        }
      ];

      // Create each notification for this shop
      for (let i = 0; i < notifications.length; i++) {
        const notif = notifications[i];
        
        // Create notification in database
        const notificationResult = await client.query(`
          INSERT INTO notifications (
            title, message, type, priority, recipient_type, recipient_id, shop_id, created_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW() - INTERVAL '${i * 2} hours')
          RETURNING id
        `, [
          notif.title,
          notif.message,
          notif.type,
          notif.priority,
          'shop',
          shop.id,
          shop.id
        ]);

        const notificationId = notificationResult.rows[0].id;

        // Create recipient records for ALL users (so any user can see notifications for any shop)
        for (const user of usersResult.rows) {
          // Make some notifications read and some unread for variety
          const status = (i < 3) ? 'unread' : 'read'; // First 3 are unread, last 2 are read
          
          await client.query(`
            INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
            VALUES ($1, $2, $3, NOW() - INTERVAL '${i * 2} hours')
          `, [notificationId, user.id, status]);
        }

        console.log(`   ✅ ${notif.title} (${notif.priority}) - ${i < 3 ? 'UNREAD' : 'READ'}`);
      }

      // Create notification settings for all users for this shop
      for (const user of usersResult.rows) {
        const types = ['stock', 'order', 'system', 'marketing'];
        for (const type of types) {
          await client.query(`
            INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
            enabled = $4, delivery_methods = $5
          `, [user.id, shop.id, type, true, JSON.stringify(['in_app'])]);
        }
      }
    }

    // Show summary for each shop
    console.log('\n📊 Summary by Shop:');
    for (const shop of shopsResult.rows) {
      const shopSummary = await client.query(`
        SELECT 
          COUNT(DISTINCT n.id) as total_notifications,
          COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_notifications
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE n.shop_id = $1
      `, [shop.id]);

      const summary = shopSummary.rows[0];
      console.log(`   ${shop.name} (ID: ${shop.id}):`);
      console.log(`     - Total notifications: ${summary.total_notifications}`);
      console.log(`     - Unread notifications: ${summary.unread_notifications}`);
    }

    // Show summary for each user
    console.log('\n📊 Summary by User:');
    for (const user of usersResult.rows) {
      const userSummary = await client.query(`
        SELECT COUNT(*) as unread_count
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE nr.user_id = $1 AND nr.status = 'unread'
      `, [user.id]);

      console.log(`   ${user.name}: ${userSummary.rows[0].unread_count} unread notifications across all shops`);
    }

    console.log('\n🎉 SUCCESS! Created notifications for ALL shops');
    
    console.log('\n🎯 What to do now:');
    console.log('   1. Refresh your browser (Ctrl+Shift+R)');
    console.log('   2. Login with any user account');
    console.log('   3. Select ANY shop from the dropdown');
    console.log('   4. Check the notification bell - should show "3" unread notifications');
    console.log('   5. Switch to different shops to see different notifications');
    console.log('   6. Each shop will have its own set of notifications');

    console.log('\n💡 Notification Details:');
    console.log('   - Each shop has 5 notifications (3 unread, 2 read)');
    console.log('   - Stock alerts (urgent and high priority)');
    console.log('   - Order notifications (normal priority)');
    console.log('   - Welcome and sales update messages');
    console.log('   - All users can see notifications for any shop they select');

    console.log('\n🔔 Expected Results:');
    console.log('   - Notification bell shows red badge "3" for any shop');
    console.log('   - Clicking bell shows 5 notifications (3 unread, 2 read)');
    console.log('   - Different shops show different notification content');
    console.log('   - Mark-as-read functionality should now work correctly');

  } catch (error) {
    console.error('❌ Error creating notifications:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createNotificationsForCurrentShop();
