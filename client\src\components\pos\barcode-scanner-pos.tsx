import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ScanLine, X, Camera, Check, AlertCircle } from 'lucide-react';
import { She<PERSON>, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/context/auth-context';
import { useToast } from '@/hooks/use-toast';

interface Product {
  id: number;
  name: string;
  price: number;
  barcode?: string;
  active: boolean;
  categoryId?: number;
  description?: string;
}

interface BarcodeScannerPOSProps {
  onProductFound?: (product: Product) => void;
  className?: string;
  triggerClassName?: string;
  showTrigger?: boolean;
}

export function BarcodeScannerPOS({
  onProductFound,
  className,
  triggerClassName,
  showTrigger = true
}: BarcodeScannerPOSProps) {
  const { token } = useAuth();
  const { toast } = useToast();
  const [isScanning, setIsScanning] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [lastScanned, setLastScanned] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [manualBarcode, setManualBarcode] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [foundProduct, setFoundProduct] = useState<Product | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Query to search product by barcode
  const { data: searchedProduct, isLoading: isSearching, error: searchError, refetch: searchProduct } = useQuery({
    queryKey: ['/api/products/barcode', lastScanned],
    queryFn: async () => {
      if (!lastScanned) return null;
      const response = await apiRequest('GET', `/api/products/barcode/${encodeURIComponent(lastScanned)}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Product not found');
        }
        throw new Error('Failed to search product');
      }
      return response.json();
    },
    enabled: !!lastScanned && !!token,
    retry: false,
  });

  // Handle product found
  useEffect(() => {
    if (searchedProduct) {
      setFoundProduct(searchedProduct);
      if (onProductFound) {
        onProductFound(searchedProduct);
      }
      toast({
        title: "Product Found!",
        description: `${searchedProduct.name} - ₹${searchedProduct.price}`,
        className: "bg-green-50 border-green-200",
      });
    }
  }, [searchedProduct, onProductFound, toast]);

  // Handle search error
  useEffect(() => {
    if (searchError) {
      setFoundProduct(null);
      toast({
        variant: "destructive",
        title: "Product Not Found",
        description: searchError.message || "No product found with this barcode",
      });
    }
  }, [searchError, toast]);

  // Mock function to simulate barcode scanning
  // In a real implementation, you would use a library like quagga.js or zxing
  const mockScanBarcode = () => {
    // Simulate scanning delay
    setTimeout(() => {
      const mockBarcodes = [
        '8901234567890',
        '5901234123457',
        '4001234567890',
        '9781234567897',
        '0123456789012'
      ];
      const randomBarcode = mockBarcodes[Math.floor(Math.random() * mockBarcodes.length)];
      setLastScanned(randomBarcode);
      setIsScanning(false);
    }, 2000);
  };

  const startScanning = async () => {
    setError(null);
    setLastScanned(null);
    setFoundProduct(null);

    // Check if browser supports getUserMedia
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setError('Your browser does not support camera access');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setHasPermission(true);
        setIsScanning(true);

        // In a real implementation, you would start the barcode detection here
        // For this demo, we'll use the mock function
        mockScanBarcode();
      }
    } catch (err) {
      setHasPermission(false);
      setError('Could not access camera. Please check permissions.');
    }
  };

  const stopScanning = () => {
    setIsScanning(false);
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
  };

  const handleManualSearch = () => {
    if (manualBarcode.trim()) {
      setLastScanned(manualBarcode.trim());
      setManualBarcode('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleManualSearch();
    }
  };

  const resetScanner = () => {
    setLastScanned(null);
    setFoundProduct(null);
    setError(null);
    setManualBarcode('');
    stopScanning();
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, []);

  const scannerContent = (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <ScanLine className="mr-2 h-5 w-5" />
            Barcode Scanner
          </span>
          {showTrigger && (
            <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Manual Barcode Input */}
        <div className="space-y-2">
          <Label htmlFor="manual-barcode">Enter Barcode Manually</Label>
          <div className="flex space-x-2">
            <Input
              id="manual-barcode"
              placeholder="Enter barcode..."
              value={manualBarcode}
              onChange={(e) => setManualBarcode(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isSearching}
            />
            <Button 
              onClick={handleManualSearch} 
              disabled={!manualBarcode.trim() || isSearching}
              size="sm"
            >
              Search
            </Button>
          </div>
        </div>

        {/* Camera Scanner */}
        <div className="relative aspect-video w-full overflow-hidden rounded-md bg-muted">
          {isScanning ? (
            <>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                className="h-full w-full object-cover"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-1/2 w-3/4 border-2 border-primary opacity-70"></div>
              </div>
              <div className="absolute inset-x-0 top-0 h-px animate-scan bg-primary"></div>
            </>
          ) : (
            <div className="flex h-full w-full flex-col items-center justify-center">
              <Camera className="mb-2 h-8 w-8 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                {lastScanned ? 'Scan completed' : 'Camera preview will appear here'}
              </p>
            </div>
          )}
          <canvas ref={canvasRef} className="hidden"></canvas>
        </div>

        {/* Scanning Status */}
        {isSearching && (
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Searching product...</p>
          </div>
        )}

        {/* Scanned Barcode */}
        {lastScanned && (
          <div className="rounded-md bg-muted p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Scanned Barcode:</p>
                <p className="text-lg font-mono">{lastScanned}</p>
              </div>
              <Check className="h-5 w-5 text-green-500" />
            </div>
          </div>
        )}

        {/* Found Product */}
        {foundProduct && (
          <div className="rounded-md bg-green-50 border border-green-200 p-3">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="font-medium text-green-800">{foundProduct.name}</p>
                <p className="text-sm text-green-600">Price: ₹{foundProduct.price}</p>
                {foundProduct.description && (
                  <p className="text-xs text-green-600 mt-1">{foundProduct.description}</p>
                )}
              </div>
              <Check className="h-5 w-5 text-green-500 mt-1" />
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex items-center justify-between p-4 pt-0 gap-2">
        <div className="flex items-center">
          {isScanning ? (
            <Button variant="destructive" onClick={stopScanning} className="min-w-[120px]">
              Cancel Scan
            </Button>
          ) : (
            <Button onClick={startScanning} className="min-w-[140px]">
              {lastScanned ? 'Scan Again' : 'Start Camera Scan'}
            </Button>
          )}
        </div>

        <div className="flex items-center ml-auto">
          {(lastScanned || foundProduct) && (
            <Button variant="outline" onClick={resetScanner} className="min-w-[80px]">
              Reset
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );

  if (!showTrigger) {
    return scannerContent;
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className={triggerClassName}>
          <ScanLine className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="p-0 sm:max-w-[400px]">
        <SheetHeader className="px-4 pt-4">
          <SheetTitle>Barcode Scanner</SheetTitle>
        </SheetHeader>
        {scannerContent}
      </SheetContent>
    </Sheet>
  );
}
