// Simple script to create test notifications
// Run this in browser console or as a Node.js script

async function createTestNotifications() {
  const API_BASE = 'http://localhost:5000';
  
  // You'll need to get your actual auth token from browser localStorage
  // Open browser console and run: localStorage.getItem('auth_token')
  const token = 'YOUR_AUTH_TOKEN_HERE'; // Replace with actual token
  
  const testNotifications = [
    {
      endpoint: '/api/notifications/order',
      data: {
        orderId: 1,
        orderNumber: 'ORD-TEST-001',
        customerName: '<PERSON>'
      }
    },
    {
      endpoint: '/api/notifications/stock',
      data: {
        productName: 'Coffee Beans',
        currentStock: 2,
        minStock: 10
      }
    },
    {
      endpoint: '/api/notifications/marketing',
      data: {
        title: 'Special Offer!',
        message: 'Get 20% off on all beverages today!'
      }
    },
    {
      endpoint: '/api/notifications/system',
      data: {
        title: 'System Update',
        message: 'New features have been added to your POS system',
        priority: 'normal'
      }
    }
  ];

  console.log('Creating test notifications...');

  for (const test of testNotifications) {
    try {
      const response = await fetch(`${API_BASE}${test.endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': '1', // Replace with your shop ID
          'X-Branch-ID': '1' // Replace with your branch ID
        },
        body: JSON.stringify(test.data)
      });
      
      if (response.ok) {
        console.log(`✅ Created: ${test.endpoint}`);
      } else {
        console.log(`❌ Failed: ${test.endpoint} - Status: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${test.endpoint} - ${error.message}`);
    }
  }

  console.log('Done! Check your notification bell now.');
}

// To use this script:
// 1. Open browser console on your app
// 2. Get your auth token: localStorage.getItem('auth_token')
// 3. Replace 'YOUR_AUTH_TOKEN_HERE' with the actual token
// 4. Copy and paste this entire script into console
// 5. Run: createTestNotifications()

console.log('Test notification script loaded. Update the token and run createTestNotifications()');
