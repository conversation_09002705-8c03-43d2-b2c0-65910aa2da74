import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function fixTableMapping() {
  const client = await pool.connect();
  try {
    console.log('Current table data:');
    const currentTables = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(currentTables.rows);
    
    // Strategy: Update table names to match logical table numbers
    // We'll rename tables to have consistent numbering
    
    const updates = [
      // Update table with ID 5 (currently "table3") to be "Table 3"
      { id: 5, newName: 'Table 3' },
      // Update other tables to have consistent naming
      { id: 8, newName: 'Table 6' }, // Already correct
      { id: 9, newName: 'Table 1' }, // Already correct
      { id: 10, newName: 'Table 2' }, // Currently "T1", change to "Table 2"
      { id: 12, newName: 'Table 4' }, // Currently "T3", change to "Table 4"
      { id: 13, newName: 'Table 5' }, // Currently "T4", change to "Table 5"
    ];
    
    console.log('\nApplying table name updates...');
    
    for (const update of updates) {
      await client.query(
        'UPDATE tables SET name = $1 WHERE id = $2',
        [update.newName, update.id]
      );
      console.log(`Updated table ID ${update.id} to name "${update.newName}"`);
    }
    
    console.log('\nUpdated table data:');
    const updatedTables = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(updatedTables.rows);
    
    console.log('\n✅ Table mapping fixed! Now table names are more consistent.');
    console.log('Note: Table IDs remain the same for data integrity, but names are now clearer.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixTableMapping();
