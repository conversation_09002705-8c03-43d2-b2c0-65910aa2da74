import {
  users,
  roles,
  shopSettings,
  shops,
  userShops,
  branches,
  userBranches,
  products,
  categories,
  tables,
  customers,
  onlinePlatforms,
  orders,
  orderItems,
  expenses,
  purchases,
  taxSettings,
  discountSettings,
  paymentMethods,
  printerSettings,
  userPreferences,
  roundingSettings,
  stockMovements,
  stockTransfers,
  stockTransferItems,
  subscriptionPlans,
  subscriptions,
  subscriptionPaymentMethods,
  refunds,
  orderNumberSettings,
  notifications,
  notificationRecipients,
  notificationSettings,
  supportTickets,
  type User,
  type InsertUser,
  type Role,
  type InsertRole,
  type ShopSettings,
  type InsertShopSettings,
  type Shop,
  type InsertShop,
  type UserShop,
  type InsertUserShop,
  type Branch,
  type InsertBranch,
  type UserBranch,
  type InsertUserBranch,
  type Product,
  type InsertProduct,
  type Category,
  type InsertCategory,
  type Table,
  type InsertTable,
  type Customer,
  type InsertCustomer,
  type OnlinePlatform,
  type InsertOnlinePlatform,
  type Order,
  type InsertOrder,
  type OrderItem,
  type InsertOrderItem,
  type Expense,
  type InsertExpense,
  type Purchase,
  type InsertPurchase,
  type TaxSetting,
  type InsertTaxSetting,
  type DiscountSetting,
  type InsertDiscountSetting,
  type PaymentMethod,
  type InsertPaymentMethod,
  type PrinterSetting,
  type InsertPrinterSetting,
  type UserPreference,
  type InsertUserPreference,
  type RoundingSetting,
  type InsertRoundingSetting,
  type StockMovement,
  type InsertStockMovement,
  type StockTransfer,
  type InsertStockTransfer,
  type StockTransferItem,
  type InsertStockTransferItem,
  type SubscriptionPlan,
  type InsertSubscriptionPlan,
  type Subscription,
  type InsertSubscription,
  type SubscriptionPaymentMethod,
  type InsertSubscriptionPaymentMethod,
  type Refund,
  type InsertRefund,
  type OrderNumberSettings,
  type InsertOrderNumberSettings,
  type Notification,
  type InsertNotification,
  type NotificationRecipient,
  type InsertNotificationRecipient,
  type NotificationSetting,
  type InsertNotificationSetting,
  type SupportTicket,
  type InsertSupportTicket
} from "@shared/schema";
import { db } from "./db";
import { eq, and, or, like, desc, asc, gte, lte, isNull, isNotNull, getTableColumns, sql, ne, inArray } from "drizzle-orm";
import bcrypt from "bcrypt";
import { randomBytes } from "crypto";

// Interface for all storage operations
export interface IStorage {
  // User operations
  createUser(user: InsertUser): Promise<User>;
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  updateUser(id: number, userData: Partial<InsertUser>): Promise<User | undefined>;
  storeOtp(username: string, otp: string): Promise<boolean>;
  verifyOtp(username: string, otp: string): Promise<boolean>;
  updatePassword(username: string, password: string): Promise<boolean>;
  getUsersByShop(shopId: number, branchId?: number): Promise<User[]>;
  getUsersByBranch(branchId: number): Promise<User[]>;

  // Role operations
  createRole(role: InsertRole): Promise<Role>;
  getAllRoles(): Promise<Role[]>;
  getRoleById(id: number): Promise<Role | undefined>;
  getRoleByName(name: string): Promise<Role | undefined>;
  updateRole(id: number, role: Partial<InsertRole>): Promise<Role | undefined>;
  updateUserRole(userId: number, roleId: number): Promise<User | undefined>;
  deleteRole(id: number): Promise<boolean>;

  // Legacy Shop settings (for backward compatibility)
  getShopSettings(): Promise<ShopSettings | undefined>;
  updateShopSettings(settings: InsertShopSettings): Promise<ShopSettings>;

  // Shop operations
  createShop(shop: InsertShop): Promise<Shop>;
  getShopById(id: number): Promise<Shop | undefined>;
  getShopByAccessCode(accessCode: string): Promise<Shop | undefined>;
  updateShop(id: number, shop: Partial<InsertShop>): Promise<Shop | undefined>;
  getUserShops(userId: number): Promise<Shop[]>;

  // User-Shop operations
  addUserToShop(userShop: InsertUserShop): Promise<UserShop>;
  getUserShopRole(userId: number, shopId: number): Promise<string | undefined>;
  removeUserFromShop(userId: number, shopId: number): Promise<boolean>;

  // Branch operations
  createBranch(branch: InsertBranch): Promise<Branch>;
  getBranchById(id: number): Promise<Branch | undefined>;
  updateBranch(id: number, branch: Partial<InsertBranch>): Promise<Branch | undefined>;
  getShopBranches(shopId: number): Promise<Branch[]>;
  getUserBranches(userId: number, shopId: number): Promise<Branch[]>;

  // User-Branch operations
  addUserToBranch(userBranch: InsertUserBranch): Promise<UserBranch>;
  getUserBranchRole(userId: number, branchId: number): Promise<string | undefined>;
  removeUserFromBranch(userId: number, branchId: number): Promise<boolean>;

  // Product operations
  createProduct(product: InsertProduct): Promise<Product>;
  getProductById(id: number): Promise<Product | undefined>;
  updateProduct(id: number, product: Partial<InsertProduct>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;
  getAllProducts(active?: boolean, shopId?: number, branchId?: number): Promise<Product[]>;
  getProductByBarcode(barcode: string, shopId?: number, branchId?: number): Promise<Product | undefined>;
  getProductsByBarcodes(barcodes: string[], shopId?: number, branchId?: number): Promise<Product[]>;
  getAllProductsWithBarcodes(shopId?: number, branchId?: number): Promise<Product[]>;

  // Product Branch Price operations
  getProductBranchPrices(productId: number): Promise<ProductBranchPrice[]>;
  createProductBranchPrice(branchPrice: InsertProductBranchPrice): Promise<ProductBranchPrice>;
  updateProductBranchPrice(id: number, branchPrice: Partial<InsertProductBranchPrice>): Promise<ProductBranchPrice | undefined>;
  deleteProductBranchPrice(id: number): Promise<boolean>;

  // Category operations
  createCategory(category: InsertCategory): Promise<Category>;
  getAllCategories(shopId?: number, branchId?: number): Promise<Category[]>;
  getCategoryById(id: number): Promise<Category | undefined>;
  updateCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined>;
  deleteCategory(id: number): Promise<Category | undefined>;

  // Table operations
  createTable(table: InsertTable): Promise<Table>;
  getAllTables(shopId?: number, branchId?: number): Promise<Table[]>;
  getTableById(id: number): Promise<Table | undefined>;
  updateTable(id: number, table: Partial<InsertTable>): Promise<Table | undefined>;
  updateTableStatus(id: number, status: string): Promise<Table | undefined>;
  deleteTable(id: number): Promise<Table | undefined>;
  checkTableHasOrders(tableId: number): Promise<boolean>;

  // Customer operations
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  getCustomerById(id: number): Promise<Customer | undefined>;
  getCustomerByPhone(phone: string, shopId?: number, branchId?: number): Promise<Customer | undefined>;
  updateCustomer(id: number, customer: Partial<InsertCustomer>): Promise<Customer | undefined>;
  deleteCustomer(id: number): Promise<Customer | undefined>;
  getAllCustomers(shopId?: number, branchId?: number): Promise<Customer[]>;
  getCustomerOrders(customerId: number, page?: number, pageSize?: number): Promise<{ orders: Order[], total: number, totalPages: number }>;
  updateCustomerLoyaltyPoints(id: number, points: number, operation: 'add' | 'subtract' | 'set'): Promise<Customer | undefined>;
  updateCustomerLastVisit(id: number, date?: Date): Promise<Customer | undefined>;
  getCustomersByLoyaltyTier(tier: string, shopId?: number, branchId?: number): Promise<Customer[]>;
  getCustomersForCampaign(shopId?: number, branchId?: number): Promise<Customer[]>;

  // Online platform operations
  createOnlinePlatform(platform: InsertOnlinePlatform): Promise<OnlinePlatform>;
  getOnlinePlatformById(id: number): Promise<OnlinePlatform | undefined>;
  updateOnlinePlatform(id: number, platform: Partial<InsertOnlinePlatform>): Promise<OnlinePlatform | undefined>;
  getAllOnlinePlatforms(shopId?: number, branchId?: number): Promise<OnlinePlatform[]>;
  deleteOnlinePlatform(id: number): Promise<OnlinePlatform | undefined>;

  // Order operations
  createOrder(order: InsertOrder, items: InsertOrderItem[]): Promise<Order>;
  createOrderOnly(order: InsertOrder): Promise<Order>; // Step 1 of two-step process
  addOrderItems(orderId: number, items: InsertOrderItem[]): Promise<OrderItem[]>; // Step 2 of two-step process
  getOrderById(id: number): Promise<Order | undefined>;
  getOrderWithItems(id: number): Promise<{ order: Order, items: any[] } | undefined>;
  getActiveOrderByTableId(tableId: number): Promise<{ order: Order, items: any[] } | undefined>;
  updateOrderStatus(id: number, status: string): Promise<Order | undefined>;
  updateOrderTable(id: number, tableId: number): Promise<Order | undefined>;
  deleteOrder(id: number): Promise<boolean>; // Delete order and its items
  getRecentOrders(limit?: number, shopId?: number, branchId?: number): Promise<Order[]>;
  getAllOrders(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number): Promise<Order[]>;
  getAllOrdersForStatusSummary(shopId?: number, branchId?: number): Promise<Order[]>;
  getAllOrderItemsByOrderIds(orderIds: number[]): Promise<OrderItem[]>;
  getAllOrdersPaginated(page: number, pageSize: number, shopId?: number, branchId?: number): Promise<{ orders: Order[], total: number, totalPages: number }>;

  // Expense operations
  createExpense(expense: InsertExpense): Promise<Expense>;
  getExpenseById(id: number): Promise<Expense | undefined>;
  updateExpense(id: number, expense: Partial<InsertExpense>): Promise<Expense | undefined>;
  deleteExpense(id: number): Promise<boolean>;
  getAllExpenses(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number, category?: string): Promise<Expense[]>;

  // Purchase operations
  createPurchase(purchase: InsertPurchase): Promise<Purchase>;
  getPurchaseById(id: number): Promise<Purchase | undefined>;
  updatePurchase(id: number, purchase: Partial<InsertPurchase>): Promise<Purchase | undefined>;
  deletePurchase(id: number): Promise<boolean>;
  getAllPurchases(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number, category?: string): Promise<Purchase[]>;

  // Branch access validation
  validateUserBranchAccess(userId: number, shopId: number, branchId?: number): Promise<boolean>;
  validateExpenseAccess(userId: number, expenseId: number): Promise<boolean>;
  validatePurchaseAccess(userId: number, purchaseId: number): Promise<boolean>;

  // Tax settings operations
  createTaxSetting(taxSetting: InsertTaxSetting): Promise<TaxSetting>;
  getTaxSettingById(id: number): Promise<TaxSetting | undefined>;
  updateTaxSetting(id: number, taxSetting: Partial<InsertTaxSetting>): Promise<TaxSetting | undefined>;
  deleteTaxSetting(id: number): Promise<boolean>;
  getAllTaxSettings(shopId?: number, branchId?: number): Promise<TaxSetting[]>;

  // Discount settings operations
  createDiscountSetting(discountSetting: InsertDiscountSetting): Promise<DiscountSetting>;
  getDiscountSettingById(id: number): Promise<DiscountSetting | undefined>;
  updateDiscountSetting(id: number, discountSetting: Partial<InsertDiscountSetting>): Promise<DiscountSetting | undefined>;
  deleteDiscountSetting(id: number): Promise<boolean>;
  getAllDiscountSettings(shopId?: number, branchId?: number): Promise<DiscountSetting[]>;

  // Payment methods operations
  createPaymentMethod(paymentMethod: InsertPaymentMethod): Promise<PaymentMethod>;
  getPaymentMethodById(id: number): Promise<PaymentMethod | undefined>;
  updatePaymentMethod(id: number, paymentMethod: Partial<InsertPaymentMethod>): Promise<PaymentMethod | undefined>;
  getAllPaymentMethods(active?: boolean, shopId?: number, branchId?: number): Promise<PaymentMethod[]>;
  deletePaymentMethod(id: number): Promise<PaymentMethod | undefined>;

  // Printer settings operations
  createPrinterSetting(printerSetting: InsertPrinterSetting): Promise<PrinterSetting>;
  getPrinterSettingByShop(shopId: number, branchId?: number): Promise<PrinterSetting | undefined>;
  updatePrinterSetting(id: number, printerSetting: Partial<InsertPrinterSetting>): Promise<PrinterSetting | undefined>;

  // User preferences operations
  createUserPreference(userPreference: InsertUserPreference): Promise<UserPreference>;
  getUserPreferenceByUserId(userId: number): Promise<UserPreference | undefined>;
  updateUserPreference(id: number, userPreference: Partial<InsertUserPreference>): Promise<UserPreference | undefined>;

  // Rounding settings operations
  createRoundingSetting(roundingSetting: InsertRoundingSetting): Promise<RoundingSetting>;
  getRoundingSettingByShop(shopId: number, branchId?: number): Promise<RoundingSetting | undefined>;
  updateRoundingSetting(id: number, roundingSetting: Partial<InsertRoundingSetting>): Promise<RoundingSetting | undefined>;

  // Refund operations
  createRefund(refund: InsertRefund): Promise<Refund>;
  getRefundsByOrderId(orderId: number): Promise<Refund[]>;
  getAllRefunds(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number): Promise<Refund[]>;
  getTotalRefundAmount(shopId?: number, branchId?: number, startDate?: Date, endDate?: Date): Promise<number>;

  // Order Number Settings operations
  getOrderNumberSettings(shopId: number, branchId?: number): Promise<OrderNumberSettings | null>;
  createOrderNumberSettings(settings: InsertOrderNumberSettings): Promise<OrderNumberSettings>;
  updateOrderNumberSettings(id: number, settings: Partial<InsertOrderNumberSettings>): Promise<OrderNumberSettings | undefined>;
  previewNextOrderNumber(shopId: number, branchId?: number): Promise<string>;
  generateOrderNumber(shopId: number, branchId?: number): Promise<string>;
  incrementSequence(shopId: number, branchId?: number): Promise<void>;

  // Dashboard data
  getDashboardStats(shopId?: number, branchId?: number): Promise<{
    todaySales: number;
    totalOrders: number;
    avgOrderValue: number;
    todayExpenses: number;
  }>;
  getTopProducts(period: 'today' | 'week' | 'month', limit?: number, shopId?: number, branchId?: number): Promise<any[]>;

  // Utilities data
  getDatabaseStats(shopId?: number, branchId?: number): Promise<any>;
  getBusinessAnalytics(shopId?: number, branchId?: number, period?: string): Promise<any>;

  // Subscription operations
  getAllSubscriptionPlans(): Promise<SubscriptionPlan[]>;
  getSubscriptionPlanById(id: number): Promise<SubscriptionPlan | undefined>;
  createSubscriptionPlan(plan: InsertSubscriptionPlan): Promise<SubscriptionPlan>;
  updateSubscriptionPlan(id: number, plan: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | undefined>;
  deleteSubscriptionPlan(id: number): Promise<boolean>;

  getShopSubscriptions(shopId: number): Promise<Subscription[]>;
  getShopSubscriptionsWithPlans(shopId: number): Promise<any[]>;
  getSubscriptionById(id: number): Promise<Subscription | undefined>;
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  updateSubscription(id: number, subscription: Partial<InsertSubscription>): Promise<Subscription | undefined>;
  updateSubscriptionStatus(id: number, status: string): Promise<Subscription | undefined>;
  getSubscriptionStats(): Promise<any>;

  // Subscription payment methods operations
  createSubscriptionPaymentMethod(paymentMethod: InsertSubscriptionPaymentMethod): Promise<SubscriptionPaymentMethod>;
  getSubscriptionPaymentMethodsByShop(shopId: number): Promise<SubscriptionPaymentMethod[]>;
  updateSubscriptionPaymentMethod(id: number, paymentMethod: Partial<InsertSubscriptionPaymentMethod>): Promise<SubscriptionPaymentMethod | undefined>;
  deleteSubscriptionPaymentMethod(id: number): Promise<SubscriptionPaymentMethod | undefined>;

  // Stock Movement operations
  createStockMovement(movement: InsertStockMovement): Promise<StockMovement>;
  getStockMovements(shopId?: number, branchId?: number): Promise<StockMovement[]>;
  getStockMovementById(id: number): Promise<StockMovement | undefined>;
  getStockMovementStats(shopId?: number, branchId?: number): Promise<{
    totalMovements: number;
    inMovements: number;
    outMovements: number;
    transferMovements: number;
    adjustmentMovements: number;
  }>;

  // Stock Transfer operations
  createStockTransfer(transfer: InsertStockTransfer): Promise<StockTransfer>;
  createStockTransferItem(item: InsertStockTransferItem): Promise<StockTransferItem>;
  getStockTransfers(shopId?: number, branchId?: number): Promise<StockTransfer[]>;
  getStockTransferById(id: number): Promise<StockTransfer | undefined>;
  getStockTransferItems(transferId: number): Promise<StockTransferItem[]>;
  updateStockTransferStatus(id: number, status: string): Promise<StockTransfer | undefined>;

  // Notification operations
  createNotification(notification: InsertNotification): Promise<Notification>;
  getNotificationById(id: number): Promise<Notification | undefined>;
  getUserNotifications(userId: number, shopId?: number, branchId?: number, page?: number, pageSize?: number): Promise<{ notifications: any[], total: number, totalPages: number }>;
  getUnreadNotificationCount(userId: number, shopId?: number, branchId?: number): Promise<number>;
  markNotificationAsRead(notificationId: number, userId: number): Promise<boolean>;
  deleteNotification(notificationId: number, userId: number): Promise<boolean>;
  createNotificationForUsers(notification: InsertNotification, userIds: number[]): Promise<Notification>;
  createNotificationForShop(notification: InsertNotification, shopId: number): Promise<Notification>;
  createNotificationForBranch(notification: InsertNotification, branchId: number): Promise<Notification>;

  // Notification Settings operations
  getUserNotificationSettings(userId: number, shopId?: number): Promise<NotificationSetting[]>;
  updateNotificationSetting(userId: number, shopId: number, notificationType: string, enabled: boolean, deliveryMethods?: string[]): Promise<NotificationSetting>;
  getDefaultNotificationSettings(userId: number, shopId: number): Promise<NotificationSetting[]>;

  // Support Ticket operations
  createSupportTicket(ticket: InsertSupportTicket): Promise<SupportTicket>;
  getSupportTicketById(id: number): Promise<SupportTicket | undefined>;
  getSupportTicketsByEmail(email: string): Promise<SupportTicket[]>;
  updateSupportTicket(id: number, ticket: Partial<InsertSupportTicket>): Promise<SupportTicket | undefined>;
  getAllSupportTickets(status?: string, priority?: string, category?: string): Promise<SupportTicket[]>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async createUser(userData: InsertUser): Promise<User> {
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

    const [user] = await db
      .insert(users)
      .values({
        ...userData,
        password: hashedPassword
      })
      .returning();

    return user;
  }

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, id));

    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.username, username));

    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, email));

    return user;
  }

  async updateUser(id: number, userData: Partial<InsertUser>): Promise<User | undefined> {
    if (userData.password) {
      const saltRounds = 10;
      userData.password = await bcrypt.hash(userData.password, saltRounds);
    }

    const [user] = await db
      .update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning();

    return user;
  }

  async storeOtp(username: string, otp: string): Promise<boolean> {
    // Create OTP valid for 10 minutes
    const expiry = new Date();
    expiry.setMinutes(expiry.getMinutes() + 10);

    const [user] = await db
      .update(users)
      .set({
        otp,
        otpExpiry: expiry
      })
      .where(eq(users.username, username))
      .returning();

    return !!user;
  }

  async verifyOtp(username: string, otp: string): Promise<boolean> {
    const [user] = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.username, username),
          eq(users.otp, otp),
          gte(users.otpExpiry as any, new Date())
        )
      );

    if (user) {
      // Clear OTP after successful verification
      await db
        .update(users)
        .set({
          otp: null,
          otpExpiry: null
        })
        .where(eq(users.id, user.id));

      return true;
    }

    return false;
  }

  async updatePassword(username: string, password: string): Promise<boolean> {
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    const [user] = await db
      .update(users)
      .set({
        password: hashedPassword
      })
      .where(eq(users.username, username))
      .returning();

    return !!user;
  }

  async getUsersByShop(shopId: number, branchId?: number): Promise<User[]> {
    try {
      console.log(`Getting users for shop ${shopId}, branch ${branchId}`);

      if (branchId) {
        // Get users for specific branch
        const userBranchData = await db
          .select({
            user: users
          })
          .from(userBranches)
          .innerJoin(users, eq(userBranches.userId, users.id))
          .innerJoin(branches, eq(userBranches.branchId, branches.id))
          .where(
            and(
              eq(userBranches.branchId, branchId),
              eq(branches.shopId, shopId)
            )
          );

        return userBranchData.map(data => data.user);
      } else {
        // Get all users for shop (across all branches)
        const userShopData = await db
          .select({
            user: users
          })
          .from(userShops)
          .innerJoin(users, eq(userShops.userId, users.id))
          .where(eq(userShops.shopId, shopId));

        return userShopData.map(data => data.user);
      }
    } catch (error) {
      console.error('Error getting users by shop:', error);
      throw error;
    }
  }

  async getUsersByBranch(branchId: number): Promise<User[]> {
    try {
      console.log(`Getting users for branch ${branchId}`);

      const userBranchData = await db
        .select({
          user: users
        })
        .from(userBranches)
        .innerJoin(users, eq(userBranches.userId, users.id))
        .where(eq(userBranches.branchId, branchId));

      return userBranchData.map(data => data.user);
    } catch (error) {
      console.error('Error getting users by branch:', error);
      throw error;
    }
  }

  async getAllUsers(): Promise<User[]> {
    try {
      console.log('Getting all users');
      const allUsers = await db.select().from(users);
      console.log(`Found ${allUsers.length} users`);
      return allUsers;
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  // Role operations
  async createRole(roleData: InsertRole): Promise<Role> {
    const [role] = await db
      .insert(roles)
      .values(roleData)
      .returning();

    return role;
  }

  async getAllRoles(): Promise<Role[]> {
    return db.select().from(roles);
  }

  async getRoleById(id: number): Promise<Role | undefined> {
    const [role] = await db
      .select()
      .from(roles)
      .where(eq(roles.id, id));

    return role;
  }

  async getRoleByName(name: string): Promise<Role | undefined> {
    const [role] = await db
      .select()
      .from(roles)
      .where(eq(roles.name, name));

    return role;
  }

  async updateRole(id: number, roleData: Partial<InsertRole>): Promise<Role | undefined> {
    const [role] = await db
      .update(roles)
      .set(roleData)
      .where(eq(roles.id, id))
      .returning();

    return role;
  }

  async updateUserRole(userId: number, roleId: number): Promise<User | undefined> {
    const [user] = await db
      .update(users)
      .set({ roleId })
      .where(eq(users.id, userId))
      .returning();

    return user;
  }

  async deleteRole(id: number): Promise<boolean> {
    // Check if any users are using this role
    const usersWithRole = await db
      .select()
      .from(users)
      .where(eq(users.roleId, id));

    // If users are using this role, don't delete it
    if (usersWithRole.length > 0) {
      return false;
    }

    const result = await db
      .delete(roles)
      .where(eq(roles.id, id));

    return result.rowCount > 0;
  }

  // Legacy Shop settings (for backward compatibility)
  async getShopSettings(): Promise<ShopSettings | undefined> {
    const [settings] = await db.select().from(shopSettings);
    return settings;
  }

  async updateShopSettings(settingsData: InsertShopSettings): Promise<ShopSettings> {
    const currentSettings = await this.getShopSettings();

    if (currentSettings) {
      const [settings] = await db
        .update(shopSettings)
        .set(settingsData)
        .where(eq(shopSettings.id, currentSettings.id))
        .returning();

      return settings;
    } else {
      const [settings] = await db
        .insert(shopSettings)
        .values(settingsData)
        .returning();

      return settings;
    }
  }

  // Shop operations
  async createShop(shopData: InsertShop): Promise<Shop> {
    // Generate a random access code if not provided
    if (!shopData.accessCode) {
      shopData.accessCode = randomBytes(4).toString('hex').toUpperCase();
    }

    const [shop] = await db
      .insert(shops)
      .values(shopData)
      .returning();

    // Add the creator as an owner of the shop
    await this.addUserToShop({
      userId: shopData.createdBy,
      shopId: shop.id,
      role: 'owner'
    });

    return shop;
  }

  async getShopById(id: number): Promise<Shop | undefined> {
    const [shop] = await db
      .select()
      .from(shops)
      .where(eq(shops.id, id));

    return shop;
  }

  async getShopByAccessCode(accessCode: string): Promise<Shop | undefined> {
    const [shop] = await db
      .select()
      .from(shops)
      .where(eq(shops.accessCode, accessCode));

    return shop;
  }

  async updateShop(id: number, shopData: Partial<InsertShop>): Promise<Shop | undefined> {
    const [shop] = await db
      .update(shops)
      .set(shopData)
      .where(eq(shops.id, id))
      .returning();

    return shop;
  }

  async getUserShops(userId: number): Promise<Shop[]> {
    const userShopsData = await db
      .select({
        shop: shops
      })
      .from(userShops)
      .innerJoin(shops, eq(userShops.shopId, shops.id))
      .where(eq(userShops.userId, userId));

    return userShopsData.map(data => data.shop);
  }

  // User-Shop operations
  async addUserToShop(userShopData: InsertUserShop): Promise<UserShop> {
    try {
      // Check if the user is already in the shop
      const [existingUserShop] = await db
        .select()
        .from(userShops)
        .where(
          and(
            eq(userShops.userId, userShopData.userId),
            eq(userShops.shopId, userShopData.shopId)
          )
        );

      if (existingUserShop) {
        // Update the role if it's different
        if (existingUserShop.role !== userShopData.role) {
          const [updatedUserShop] = await db
            .update(userShops)
            .set({ role: userShopData.role })
            .where(eq(userShops.id, existingUserShop.id))
            .returning();

          return updatedUserShop;
        }

        return existingUserShop;
      }

      // Add the user to the shop
      const [userShopRecord] = await db
        .insert(userShops)
        .values(userShopData)
        .returning();

      return userShopRecord;
    } catch (error) {
      console.error("Error adding user to shop:", error);

      // If there's a unique constraint violation, the user is already in the shop
      // In this case, try to fetch the existing record
      const [existingUserShop] = await db
        .select()
        .from(userShops)
        .where(
          and(
            eq(userShops.userId, userShopData.userId),
            eq(userShops.shopId, userShopData.shopId)
          )
        );

      if (existingUserShop) {
        return existingUserShop;
      }

      throw error;
    }
  }

  async getUserShopRole(userId: number, shopId: number): Promise<string | undefined> {
    const [userShop] = await db
      .select()
      .from(userShops)
      .where(
        and(
          eq(userShops.userId, userId),
          eq(userShops.shopId, shopId)
        )
      );

    return userShop?.role;
  }

  async removeUserFromShop(userId: number, shopId: number): Promise<boolean> {
    const result = await db
      .delete(userShops)
      .where(
        and(
          eq(userShops.userId, userId),
          eq(userShops.shopId, shopId)
        )
      );

    return result.rowCount > 0;
  }

  // Branch operations
  async createBranch(branchData: InsertBranch): Promise<Branch> {
    console.log('Creating branch with data:', branchData);

    // Check if this is the first branch for the shop
    const existingBranches = await this.getShopBranches(branchData.shopId);
    console.log(`Found ${existingBranches.length} existing branches for shop ${branchData.shopId}`);

    // If this is the first branch, mark it as the main branch
    if (existingBranches.length === 0) {
      branchData.isMainBranch = true;
      console.log('Setting as main branch since this is the first branch');
    }

    try {
      const [branch] = await db
        .insert(branches)
        .values(branchData)
        .returning();

      console.log('Branch created successfully:', branch);

      // Add the creator as a manager of the branch
      console.log(`Adding user ${branchData.createdBy} as manager of branch ${branch.id}`);
      await this.addUserToBranch({
        userId: branchData.createdBy,
        branchId: branch.id,
        role: 'manager'
      });

      return branch;
    } catch (error) {
      console.error('Error creating branch:', error);
      throw error;
    }
  }

  async getBranchById(id: number): Promise<Branch | undefined> {
    const [branch] = await db
      .select()
      .from(branches)
      .where(eq(branches.id, id));

    return branch;
  }

  async updateBranch(id: number, branchData: Partial<InsertBranch>): Promise<Branch | undefined> {
    // If trying to update isMainBranch to true, ensure no other branch in the same shop is main
    if (branchData.isMainBranch === true) {
      const branch = await this.getBranchById(id);
      if (branch) {
        // Set all other branches in this shop to not be main
        await db
          .update(branches)
          .set({ isMainBranch: false })
          .where(
            and(
              eq(branches.shopId, branch.shopId),
              eq(branches.isMainBranch, true)
            )
          );
      }
    }

    const [updatedBranch] = await db
      .update(branches)
      .set(branchData)
      .where(eq(branches.id, id))
      .returning();

    return updatedBranch;
  }

  async getShopBranches(shopId: number): Promise<Branch[]> {
    console.log(`Getting all branches for shop ${shopId}`);
    try {
      const result = await db
        .select()
        .from(branches)
        .where(eq(branches.shopId, shopId))
        .orderBy(desc(branches.isMainBranch), asc(branches.name));

      console.log(`Found ${result.length} branches for shop ${shopId}:`, result);
      return result;
    } catch (error) {
      console.error(`Error getting branches for shop ${shopId}:`, error);
      throw error;
    }
  }

  async getUserBranches(userId: number, shopId: number): Promise<Branch[]> {
    console.log(`Getting branches for user ${userId} in shop ${shopId}`);

    // First check if user is a shop owner or admin
    const userShopRole = await this.getUserShopRole(userId, shopId);
    console.log(`User role in shop: ${userShopRole}`);

    // Shop owners and admins have access to all branches
    if (userShopRole === 'owner' || userShopRole === 'admin') {
      console.log('User is owner/admin, getting all shop branches');
      const allBranches = await this.getShopBranches(shopId);
      console.log(`Found ${allBranches.length} branches for shop ${shopId}`);
      return allBranches;
    }

    // Otherwise, get only the branches the user is assigned to
    console.log('User is not owner/admin, getting assigned branches');
    try {
      const userBranchesData = await db
        .select({
          branch: branches
        })
        .from(userBranches)
        .innerJoin(branches, eq(userBranches.branchId, branches.id))
        .where(
          and(
            eq(userBranches.userId, userId),
            eq(branches.shopId, shopId)
          )
        );

      console.log(`Found ${userBranchesData.length} assigned branches for user ${userId}`);
      return userBranchesData.map(data => data.branch);
    } catch (error) {
      console.error('Error getting user branches:', error);
      throw error;
    }
  }

  // User-Branch operations
  async addUserToBranch(userBranchData: InsertUserBranch): Promise<UserBranch> {
    try {
      // Check if the user is already in the branch
      const [existingUserBranch] = await db
        .select()
        .from(userBranches)
        .where(
          and(
            eq(userBranches.userId, userBranchData.userId),
            eq(userBranches.branchId, userBranchData.branchId)
          )
        );

      if (existingUserBranch) {
        // Update the role if it's different
        if (existingUserBranch.role !== userBranchData.role) {
          const [updatedUserBranch] = await db
            .update(userBranches)
            .set({ role: userBranchData.role })
            .where(eq(userBranches.id, existingUserBranch.id))
            .returning();

          return updatedUserBranch;
        }

        return existingUserBranch;
      }

      // Add the user to the branch
      const [userBranchRecord] = await db
        .insert(userBranches)
        .values(userBranchData)
        .returning();

      return userBranchRecord;
    } catch (error) {
      console.error("Error adding user to branch:", error);

      // If there's a unique constraint violation, the user is already in the branch
      // In this case, try to fetch the existing record
      const [existingUserBranch] = await db
        .select()
        .from(userBranches)
        .where(
          and(
            eq(userBranches.userId, userBranchData.userId),
            eq(userBranches.branchId, userBranchData.branchId)
          )
        );

      if (existingUserBranch) {
        return existingUserBranch;
      }

      throw error;
    }
  }

  async getUserBranchRole(userId: number, branchId: number): Promise<string | undefined> {
    console.log(`Getting role for user ${userId} in branch ${branchId}`);

    try {
      // First get the branch to find its shop
      const branch = await this.getBranchById(branchId);
      if (!branch) {
        console.log(`Branch ${branchId} not found`);
        return undefined;
      }

      console.log(`Branch ${branchId} belongs to shop ${branch.shopId}`);

      // Check if user is shop owner or admin (they have access to all branches)
      const shopRole = await this.getUserShopRole(userId, branch.shopId);
      console.log(`User ${userId} has shop role: ${shopRole}`);

      if (shopRole === 'owner' || shopRole === 'admin') {
        console.log(`User ${userId} is shop owner/admin, returning shop role`);
        return shopRole;
      }

      // Otherwise, check specific branch role
      const [userBranch] = await db
        .select()
        .from(userBranches)
        .where(
          and(
            eq(userBranches.userId, userId),
            eq(userBranches.branchId, branchId)
          )
        );

      console.log(`User ${userId} branch role: ${userBranch?.role || 'none'}`);
      return userBranch?.role;
    } catch (error) {
      console.error(`Error getting user branch role for user ${userId} in branch ${branchId}:`, error);
      return undefined;
    }
  }

  async removeUserFromBranch(userId: number, branchId: number): Promise<boolean> {
    const result = await db
      .delete(userBranches)
      .where(
        and(
          eq(userBranches.userId, userId),
          eq(userBranches.branchId, branchId)
        )
      );

    return result.rowCount > 0;
  }

  // Product operations
  async createProduct(productData: InsertProduct): Promise<Product> {
    const [product] = await db
      .insert(products)
      .values(productData)
      .returning();

    return product;
  }

  async getProductById(id: number): Promise<Product | undefined> {
    const [product] = await db
      .select()
      .from(products)
      .where(eq(products.id, id));

    return product;
  }

  async updateProduct(id: number, productData: Partial<InsertProduct>): Promise<Product | undefined> {
    const [product] = await db
      .update(products)
      .set({
        ...productData,
        updatedAt: new Date()
      })
      .where(eq(products.id, id))
      .returning();

    return product;
  }

  async deleteProduct(id: number): Promise<boolean> {
    // Soft delete by setting active to false
    const [product] = await db
      .update(products)
      .set({
        active: false,
        updatedAt: new Date()
      })
      .where(eq(products.id, id))
      .returning();

    return !!product;
  }

  async getAllProducts(active?: boolean, shopId?: number, branchId?: number): Promise<Product[]> {
    let query = db.select().from(products);

    // Build conditions array
    const conditions = [];

    if (active !== undefined) {
      conditions.push(eq(products.active, active));
    }

    // Filter by shop ID if provided
    if (shopId !== undefined) {
      conditions.push(eq(products.shopId, shopId));
    }

    // Filter by branch ID if provided
    if (branchId !== undefined) {
      conditions.push(eq(products.branchId, branchId));
    }

    // Apply all conditions if any
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async getProductByBarcode(barcode: string, shopId?: number, branchId?: number): Promise<Product | undefined> {
    let query = db.select().from(products);

    // Build conditions array
    const conditions = [eq(products.barcode, barcode)];

    // Filter by shop ID if provided
    if (shopId !== undefined) {
      conditions.push(eq(products.shopId, shopId));
    }

    // Filter by branch ID if provided
    if (branchId !== undefined) {
      conditions.push(eq(products.branchId, branchId));
    }

    // Apply all conditions
    query = query.where(and(...conditions));

    const [product] = await query;
    return product;
  }

  async getProductsByBarcodes(barcodes: string[], shopId?: number, branchId?: number): Promise<Product[]> {
    if (barcodes.length === 0) {
      return [];
    }

    let query = db.select().from(products);

    // Build conditions array
    const conditions = [inArray(products.barcode, barcodes)];

    // Filter by shop ID if provided
    if (shopId !== undefined) {
      conditions.push(eq(products.shopId, shopId));
    }

    // Filter by branch ID if provided
    if (branchId !== undefined) {
      conditions.push(eq(products.branchId, branchId));
    }

    // Apply all conditions
    query = query.where(and(...conditions));

    return await query;
  }

  async getAllProductsWithBarcodes(shopId?: number, branchId?: number): Promise<Product[]> {
    let query = db.select().from(products);

    // Build conditions array
    const conditions = [isNotNull(products.barcode)];

    // Filter by shop ID if provided
    if (shopId !== undefined) {
      conditions.push(eq(products.shopId, shopId));
    }

    // Filter by branch ID if provided
    if (branchId !== undefined) {
      conditions.push(eq(products.branchId, branchId));
    }

    // Apply all conditions
    query = query.where(and(...conditions));

    return await query;
  }

  // Product Branch Price operations
  async getProductBranchPrices(productId: number): Promise<ProductBranchPrice[]> {
    return db
      .select()
      .from(productBranchPrices)
      .where(eq(productBranchPrices.productId, productId));
  }

  async createProductBranchPrice(branchPriceData: InsertProductBranchPrice): Promise<ProductBranchPrice> {
    // Check if a price for this product and branch already exists
    const existingPrices = await db
      .select()
      .from(productBranchPrices)
      .where(
        and(
          eq(productBranchPrices.productId, branchPriceData.productId),
          eq(productBranchPrices.branchId, branchPriceData.branchId)
        )
      );

    // If it exists, update it instead of creating a new one
    if (existingPrices.length > 0) {
      const [updatedPrice] = await db
        .update(productBranchPrices)
        .set({
          price: branchPriceData.price,
          active: branchPriceData.active,
          updatedAt: new Date()
        })
        .where(eq(productBranchPrices.id, existingPrices[0].id))
        .returning();

      return updatedPrice;
    }

    // Otherwise, create a new branch price
    const [branchPrice] = await db
      .insert(productBranchPrices)
      .values(branchPriceData)
      .returning();

    return branchPrice;
  }

  async updateProductBranchPrice(id: number, branchPriceData: Partial<InsertProductBranchPrice>): Promise<ProductBranchPrice | undefined> {
    const [branchPrice] = await db
      .update(productBranchPrices)
      .set({
        ...branchPriceData,
        updatedAt: new Date()
      })
      .where(eq(productBranchPrices.id, id))
      .returning();

    return branchPrice;
  }

  async deleteProductBranchPrice(id: number): Promise<boolean> {
    const result = await db
      .delete(productBranchPrices)
      .where(eq(productBranchPrices.id, id));

    return result.rowCount > 0;
  }

  // Category operations
  async createCategory(categoryData: InsertCategory): Promise<Category> {
    const [category] = await db
      .insert(categories)
      .values(categoryData)
      .returning();

    return category;
  }

  async getAllCategories(shopId?: number, branchId?: number): Promise<Category[]> {
    let query = db.select().from(categories);
    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(categories.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(categories.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async getCategoryById(id: number): Promise<Category | undefined> {
    const [category] = await db
      .select()
      .from(categories)
      .where(eq(categories.id, id));

    return category;
  }
  async updateCategory(id: number, categoryData: Partial<InsertCategory>): Promise<Category | undefined> {
    try {
      // First check if category exists
      const existingCategory = await this.getCategoryById(id);
      if (!existingCategory) {
        return undefined;
      }

      // Preserve existing shopId and branchId if not provided
      const updateData = {
        ...categoryData,
        shopId: categoryData.shopId || existingCategory.shopId,
        branchId: categoryData.branchId || existingCategory.branchId
      };

      const [category] = await db
        .update(categories)
        .set(updateData)
        .where(eq(categories.id, id))
        .returning();

      return category;
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  async deleteCategory(id: number): Promise<Category | undefined> {
    try {
      console.log(`Storage: Deleting category with ID: ${id}`);

      // First, update all products that reference this category to set categoryId to NULL
      // This maintains product data while removing the category reference
      const updatedProducts = await db
        .update(products)
        .set({ categoryId: null })
        .where(eq(products.categoryId, id))
        .returning({ id: products.id });

      console.log(`Storage: Updated ${updatedProducts.length} products to remove category reference`);

      // Now delete the category and return the deleted record
      const [deletedCategory] = await db
        .delete(categories)
        .where(eq(categories.id, id))
        .returning();

      console.log(`Storage: Deleted category: ${deletedCategory?.name || 'unknown'}`);

      return deletedCategory;
    } catch (error) {
      console.error(`Storage: Error deleting category ${id}:`, error);
      throw error;
    }
  }

  // Table operations
  async createTable(tableData: InsertTable): Promise<Table> {
    const [table] = await db
      .insert(tables)
      .values(tableData)
      .returning();

    return table;
  }

  async getAllTables(shopId?: number, branchId?: number): Promise<Table[]> {
    let query = db.select().from(tables);
    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(tables.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(tables.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async getTableById(id: number): Promise<Table | undefined> {
    const [table] = await db
      .select()
      .from(tables)
      .where(eq(tables.id, id));

    return table;
  }

  async updateTable(id: number, tableData: Partial<InsertTable>): Promise<Table | undefined> {
    const [table] = await db
      .update(tables)
      .set(tableData)
      .where(eq(tables.id, id))
      .returning();

    return table;
  }

  async updateTableStatus(id: number, status: string): Promise<Table | undefined> {
    const [table] = await db
      .update(tables)
      .set({ status })
      .where(eq(tables.id, id))
      .returning();

    return table;
  }

  async deleteTable(id: number): Promise<Table | undefined> {
    try {
      console.log(`Storage: Deleting table with ID: ${id}`);

      // First, update all orders that reference this table to set tableId to NULL
      // This maintains order history while removing the table reference
      const updatedOrders = await db
        .update(orders)
        .set({ tableId: null })
        .where(eq(orders.tableId, id))
        .returning({ id: orders.id });

      console.log(`Storage: Updated ${updatedOrders.length} orders to remove table reference`);

      // Now delete the table and return the deleted record
      const [deletedTable] = await db
        .delete(tables)
        .where(eq(tables.id, id))
        .returning();

      console.log(`Storage: Deleted table: ${deletedTable?.name || 'unknown'}`);

      return deletedTable;
    } catch (error) {
      console.error(`Storage: Error deleting table ${id}:`, error);
      throw error;
    }
  }

  async checkTableHasOrders(tableId: number): Promise<boolean> {
    try {
      console.log(`Storage: Checking if table ${tableId} has orders`);

      // Check if there are any orders associated with this table
      const ordersWithTable = await db
        .select({ id: orders.id })
        .from(orders)
        .where(eq(orders.tableId, tableId))
        .limit(1);

      const hasOrders = ordersWithTable.length > 0;
      console.log(`Storage: Table ${tableId} has orders: ${hasOrders}`);

      return hasOrders;
    } catch (error) {
      console.error(`Storage: Error checking table orders ${tableId}:`, error);
      throw error;
    }
  }

  // Customer operations
  async createCustomer(customerData: InsertCustomer): Promise<Customer> {
    const [customer] = await db
      .insert(customers)
      .values(customerData)
      .returning();

    return customer;
  }

  async getCustomerById(id: number): Promise<Customer | undefined> {
    const [customer] = await db
      .select()
      .from(customers)
      .where(eq(customers.id, id));

    return customer;
  }

  async getCustomerByPhone(phone: string, shopId?: number, branchId?: number): Promise<Customer | undefined> {
    let query = db
      .select()
      .from(customers)
      .where(eq(customers.phone, phone));

    if (shopId !== undefined) {
      query = query.where(eq(customers.shopId, shopId));
    }

    if (branchId !== undefined) {
      query = query.where(eq(customers.branchId, branchId));
    }

    const [customer] = await query;
    return customer;
  }

  async updateCustomer(id: number, customerData: Partial<InsertCustomer>): Promise<Customer | undefined> {
    const [customer] = await db
      .update(customers)
      .set(customerData)
      .where(eq(customers.id, id))
      .returning();

    return customer;
  }

  async deleteCustomer(id: number): Promise<Customer | undefined> {
    try {
      console.log(`Storage: Deleting customer with ID: ${id}`);

      // Delete the customer and return the deleted record
      const [deletedCustomer] = await db
        .delete(customers)
        .where(eq(customers.id, id))
        .returning();

      console.log(`Storage: Deleted customer: ${deletedCustomer?.name || 'unknown'}`);

      return deletedCustomer;
    } catch (error) {
      console.error(`Storage: Error deleting customer ${id}:`, error);
      throw error;
    }
  }

  async getAllCustomers(shopId?: number, branchId?: number): Promise<Customer[]> {
    try {
      console.log(`Storage: Getting customers with shopId: ${shopId}, branchId: ${branchId}`);

      let query = db.select().from(customers);

      // Apply filters
      if (shopId !== undefined) {
        if (branchId !== undefined) {
          // Both shop and branch specified
          query = query.where(
            and(
              eq(customers.shopId, shopId),
              eq(customers.branchId, branchId)
            )
          );
        } else {
          // Only shop specified, include all customers for this shop
          query = query.where(eq(customers.shopId, shopId));
        }
      }

      const result = await query;
      console.log(`Storage: Retrieved ${result.length} customers`);
      return result;
    } catch (error) {
      console.error('Storage: Error getting customers:', error);
      throw error;
    }
  }

  async updateCustomerLoyaltyPoints(id: number, points: number, operation: 'add' | 'subtract' | 'set'): Promise<Customer | undefined> {
    try {
      // Get current customer to get current loyalty points
      const customer = await this.getCustomerById(id);
      if (!customer) {
        return undefined;
      }

      let newPoints: number;
      let newTier: string = customer.loyaltyTier;

      // Calculate new points based on operation
      switch (operation) {
        case 'add':
          newPoints = customer.loyaltyPoints + points;
          break;
        case 'subtract':
          newPoints = Math.max(0, customer.loyaltyPoints - points); // Ensure points don't go below 0
          break;
        case 'set':
          newPoints = points;
          break;
        default:
          newPoints = customer.loyaltyPoints;
      }

      // Determine loyalty tier based on points
      if (newPoints >= 1000) {
        newTier = 'platinum';
      } else if (newPoints >= 500) {
        newTier = 'gold';
      } else if (newPoints >= 200) {
        newTier = 'silver';
      } else {
        newTier = 'standard';
      }

      // Update customer with new points and tier
      const [updatedCustomer] = await db
        .update(customers)
        .set({
          loyaltyPoints: newPoints,
          loyaltyTier: newTier
        })
        .where(eq(customers.id, id))
        .returning();

      return updatedCustomer;
    } catch (error) {
      console.error(`Error updating loyalty points for customer ${id}:`, error);
      throw error;
    }
  }

  async updateCustomerLastVisit(id: number, date: Date = new Date()): Promise<Customer | undefined> {
    try {
      const [customer] = await db
        .update(customers)
        .set({
          lastVisitDate: date
        })
        .where(eq(customers.id, id))
        .returning();

      return customer;
    } catch (error) {
      console.error(`Error updating last visit date for customer ${id}:`, error);
      throw error;
    }
  }

  async getCustomersByLoyaltyTier(tier: string, shopId?: number, branchId?: number): Promise<Customer[]> {
    let query = db
      .select()
      .from(customers)
      .where(eq(customers.loyaltyTier, tier));

    if (shopId !== undefined) {
      query = query.where(eq(customers.shopId, shopId));
    }

    if (branchId !== undefined) {
      query = query.where(eq(customers.branchId, branchId));
    }

    return await query;
  }

  async getCustomersForCampaign(shopId?: number, branchId?: number): Promise<Customer[]> {
    let query = db
      .select()
      .from(customers)
      .where(eq(customers.campaignOptIn, true));

    if (shopId !== undefined) {
      query = query.where(eq(customers.shopId, shopId));
    }

    if (branchId !== undefined) {
      query = query.where(eq(customers.branchId, branchId));
    }

    return await query;
  }

  async getCustomerOrders(customerId: number, page: number = 1, pageSize: number = 10): Promise<{ orders: Order[], total: number, totalPages: number }> {
    try {
      console.log(`Storage: Getting orders for customer ID: ${customerId}, page: ${page}, pageSize: ${pageSize}`);

      // Calculate offset based on page and pageSize
      const offset = (page - 1) * pageSize;

      // Build query to count total orders for this customer
      const countQuery = db
        .select({ count: sql`count(*)` })
        .from(orders)
        .where(eq(orders.customerId, customerId));

      // Execute count query
      const [countResult] = await countQuery;
      const total = Number(countResult?.count || 0);
      const totalPages = Math.ceil(total / pageSize);

      console.log(`Storage: Found ${total} total orders for customer ${customerId}`);

      // Get paginated orders for this customer
      const customerOrders = await db
        .select()
        .from(orders)
        .where(eq(orders.customerId, customerId))
        .orderBy(desc(orders.createdAt))
        .limit(pageSize)
        .offset(offset);

      console.log(`Storage: Retrieved ${customerOrders.length} orders for customer ${customerId}`);

      return {
        orders: customerOrders,
        total,
        totalPages
      };
    } catch (error) {
      console.error(`Storage: Error getting orders for customer ${customerId}:`, error);
      throw error;
    }
  }

  // Online platform operations
  async createOnlinePlatform(platformData: InsertOnlinePlatform): Promise<OnlinePlatform> {
    const [platform] = await db
      .insert(onlinePlatforms)
      .values(platformData)
      .returning();

    return platform;
  }

  async getOnlinePlatformById(id: number): Promise<OnlinePlatform | undefined> {
    const [platform] = await db
      .select()
      .from(onlinePlatforms)
      .where(eq(onlinePlatforms.id, id));

    return platform;
  }

  async updateOnlinePlatform(id: number, platformData: Partial<InsertOnlinePlatform>): Promise<OnlinePlatform | undefined> {
    const [platform] = await db
      .update(onlinePlatforms)
      .set(platformData)
      .where(eq(onlinePlatforms.id, id))
      .returning();

    return platform;
  }

  async getAllOnlinePlatforms(shopId?: number, branchId?: number): Promise<OnlinePlatform[]> {
    let query = db.select().from(onlinePlatforms);
    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(onlinePlatforms.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(onlinePlatforms.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async deleteOnlinePlatform(id: number): Promise<OnlinePlatform | undefined> {
    try {
      console.log(`Storage: Deleting online platform with ID: ${id}`);

      // Delete the platform and return the deleted record
      const [deletedPlatform] = await db
        .delete(onlinePlatforms)
        .where(eq(onlinePlatforms.id, id))
        .returning();

      console.log(`Storage: Deleted platform: ${deletedPlatform?.name || 'unknown'}`);

      return deletedPlatform;
    } catch (error) {
      console.error(`Storage: Error deleting platform ${id}:`, error);
      throw error;
    }
  }

  // Order operations
  async createOrder(orderData: InsertOrder, itemsData: InsertOrderItem[]): Promise<Order> {
    try {
      console.log('Storage: Creating order with data:', JSON.stringify(orderData, null, 2));
      console.log('Storage: Order items:', JSON.stringify(itemsData, null, 2));

      // Ensure userId is present
      if (!orderData.userId) {
        console.error('Storage: userId is missing from order data');
        throw new Error('userId is required for creating an order');
      }

      // Generate unique order number using settings
      if (!orderData.orderNumber) {
        orderData.orderNumber = await this.generateOrderNumber(orderData.shopId, orderData.branchId);
        console.log('Storage: Generated order number:', orderData.orderNumber);
      }

      // Create order
      console.log('Storage: Inserting order into database');
      const [order] = await db
        .insert(orders)
        .values(orderData)
        .returning();
      console.log('Storage: Order created:', order);

      // Create order items
      if (itemsData.length > 0) {
        console.log('Storage: Inserting order items');

        // Ensure each item has the orderId
        const itemsWithOrderId = itemsData.map(item => {
          // If item already has an orderId, check if it matches the created order
          if (item.orderId && item.orderId !== order.id) {
            console.warn(`Storage: Item has orderId ${item.orderId} but should be ${order.id}. Overriding.`);
          }

          return {
            ...item,
            orderId: order.id // Always set the correct orderId
          };
        });

        console.log('Storage: Items with orderId:', itemsWithOrderId);

        await db.insert(orderItems).values(itemsWithOrderId);
        console.log('Storage: Order items inserted successfully');
      }

      // If order is for a table, update table status
      if (order.tableId && order.orderType === 'dine_in') {
        console.log(`Storage: Updating table ${order.tableId} status to occupied`);
        await db
          .update(tables)
          .set({ status: 'occupied' })
          .where(eq(tables.id, order.tableId));
        console.log('Storage: Table status updated successfully');
      }

      return order;
    } catch (error) {
      console.error('Storage: Error creating order:', error);
      console.error('Storage: Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');
      throw error; // Re-throw to be handled by the route handler
    }
  }

  // Step 1: Create order without items (for two-step process)
  async createOrderOnly(orderData: InsertOrder): Promise<Order> {
    try {
      console.log('Storage: Creating order only (step 1) with data:', JSON.stringify(orderData, null, 2));

      // Ensure userId is present and is a number
      if (!orderData.userId) {
        console.error('Storage: userId is missing from order data');
        throw new Error('userId is required for creating an order');
      }

      // Ensure all required fields are present and have the correct types
      const processedOrderData = {
        ...orderData,
        userId: Number(orderData.userId),
        shopId: Number(orderData.shopId),
        subtotal: Number(orderData.subtotal || 0),
        taxAmount: Number(orderData.taxAmount || 0),
        discountAmount: Number(orderData.discountAmount || 0),
        totalAmount: Number(orderData.totalAmount || 0),
      };

      // Generate unique order number using settings
      if (!processedOrderData.orderNumber) {
        processedOrderData.orderNumber = await this.generateOrderNumber(processedOrderData.shopId, processedOrderData.branchId);
        console.log('Storage: Generated order number:', processedOrderData.orderNumber);
      }

      // Ensure required fields have default values if missing
      if (!processedOrderData.status) {
        processedOrderData.status = 'pending';
      }

      if (!processedOrderData.paymentStatus) {
        processedOrderData.paymentStatus = 'pending';
      }

      if (!processedOrderData.notes) {
        processedOrderData.notes = '';
      }

      // Create order
      console.log('Storage: Inserting order into database (step 1) with processed data:', JSON.stringify(processedOrderData, null, 2));
      const [order] = await db
        .insert(orders)
        .values(processedOrderData)
        .returning();
      console.log('Storage: Order created (step 1):', order);

      // If order is for a table, update table status
      if (order.tableId && order.orderType === 'dine_in') {
        console.log(`Storage: Updating table ${order.tableId} status to occupied`);
        await db
          .update(tables)
          .set({ status: 'occupied' })
          .where(eq(tables.id, order.tableId));
        console.log('Storage: Table status updated successfully');
      }

      return order;
    } catch (error) {
      console.error('Storage: Error creating order only (step 1):', error);
      console.error('Storage: Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');
      throw error; // Re-throw to be handled by the route handler
    }
  }

  // Step 2: Add items to an existing order (for two-step process)
  async addOrderItems(orderId: number, itemsData: InsertOrderItem[]): Promise<OrderItem[]> {
    try {
      console.log(`Storage: Adding items to order ${orderId} (step 2):`, JSON.stringify(itemsData, null, 2));

      if (itemsData.length === 0) {
        console.log('Storage: No items to add');
        return [];
      }

      // Ensure each item has the correct orderId and all required fields with proper types
      const itemsWithOrderId = itemsData.map(item => {
        // If item already has an orderId, check if it matches the provided orderId
        if (item.orderId && item.orderId !== orderId) {
          console.warn(`Storage: Item has orderId ${item.orderId} but should be ${orderId}. Overriding.`);
        }

        // Ensure all numeric fields are properly converted to numbers
        return {
          productId: Number(item.productId),
          quantity: Number(item.quantity),
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          orderId: Number(orderId) // Always set the correct orderId as a number
        };
      });

      console.log('Storage: Processed items with orderId:', JSON.stringify(itemsWithOrderId, null, 2));

      // Insert items
      const insertedItems = await db
        .insert(orderItems)
        .values(itemsWithOrderId)
        .returning();
      console.log('Storage: Items added successfully (step 2):', insertedItems);

      return insertedItems;
    } catch (error) {
      console.error(`Storage: Error adding items to order ${orderId} (step 2):`, error);
      console.error('Storage: Full error stack:', error instanceof Error ? error.stack : 'No stack trace available');

      // Log more details about the error
      if (error instanceof Error) {
        console.error('Storage: Error message:', error.message);
        console.error('Storage: Error name:', error.name);
      }

      throw error; // Re-throw to be handled by the route handler
    }
  }

  async getOrderById(id: number): Promise<Order | undefined> {
    const [order] = await db
      .select()
      .from(orders)
      .where(eq(orders.id, id));

    return order;
  }

  async getOrderWithItems(id: number): Promise<{ order: any, items: any[] } | undefined> {
    // Get order with table name
    const [orderResult] = await db
      .select({
        // Order fields
        id: orders.id,
        orderNumber: orders.orderNumber,
        orderType: orders.orderType,
        status: orders.status,
        tableId: orders.tableId,
        customerId: orders.customerId,
        platformId: orders.platformId,
        numberOfPersons: orders.numberOfPersons,
        subtotal: orders.subtotal,
        taxAmount: orders.taxAmount,
        taxRate: orders.taxRate,
        discountAmount: orders.discountAmount,
        totalAmount: orders.totalAmount,
        paymentMethod: orders.paymentMethod,
        paymentStatus: orders.paymentStatus,
        notes: orders.notes,
        createdAt: orders.createdAt,
        userId: orders.userId,
        shopId: orders.shopId,
        branchId: orders.branchId,
        // Table name
        tableName: tables.name
      })
      .from(orders)
      .leftJoin(tables, eq(orders.tableId, tables.id))
      .where(eq(orders.id, id));

    if (!orderResult) return undefined;

    // Join order items with products to get product names
    const items = await db
      .select({
        id: orderItems.id,
        orderId: orderItems.orderId,
        productId: orderItems.productId,
        quantity: orderItems.quantity,
        unitPrice: orderItems.unitPrice,
        totalPrice: orderItems.totalPrice,
        productName: products.name,
        notes: orderItems.notes
      })
      .from(orderItems)
      .leftJoin(products, eq(orderItems.productId, products.id))
      .where(eq(orderItems.orderId, orderResult.id));

    return { order: orderResult, items };
  }

  async getActiveOrderByTableId(tableId: number): Promise<{ order: Order, items: any[] } | undefined> {
    // Find the active order for this table (status not completed or cancelled)
    const [order] = await db
      .select()
      .from(orders)
      .where(
        and(
          eq(orders.tableId, tableId),
          eq(orders.orderType, 'dine_in'),
          // Order is active if status is not completed or cancelled
          and(
            ne(orders.status, 'completed'),
            ne(orders.status, 'cancelled')
          )
        )
      )
      .orderBy(desc(orders.createdAt))
      .limit(1);

    if (!order) return undefined;

    // Join order items with products to get product names
    const items = await db
      .select({
        id: orderItems.id,
        orderId: orderItems.orderId,
        productId: orderItems.productId,
        quantity: orderItems.quantity,
        unitPrice: orderItems.unitPrice,
        totalPrice: orderItems.totalPrice,
        productName: products.name,
        notes: orderItems.notes
      })
      .from(orderItems)
      .leftJoin(products, eq(orderItems.productId, products.id))
      .where(eq(orderItems.orderId, order.id));

    return { order, items };
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const [order] = await db
      .update(orders)
      .set({ status })
      .where(eq(orders.id, id))
      .returning();

    // If order is completed or cancelled and is a dine-in order, update table status
    if (order && (status === 'completed' || status === 'cancelled') && order.tableId && order.orderType === 'dine_in') {
      await db
        .update(tables)
        .set({ status: 'available' })
        .where(eq(tables.id, order.tableId));
    }

    return order;
  }

  async updateOrderTable(id: number, tableId: number): Promise<Order | undefined> {
    const [order] = await db
      .update(orders)
      .set({ tableId })
      .where(eq(orders.id, id))
      .returning();

    return order;
  }

  async deleteOrder(id: number): Promise<boolean> {
    try {
      console.log(`Storage: Deleting order with ID: ${id}`);

      // First, delete all order items
      await db
        .delete(orderItems)
        .where(eq(orderItems.orderId, id));
      console.log('Storage: Order items deleted successfully');

      // Then, delete the order itself
      const result = await db
        .delete(orders)
        .where(eq(orders.id, id));
      console.log('Storage: Order deleted successfully');

      return result.length > 0;
    } catch (error) {
      console.error(`Storage: Error deleting order ${id}:`, error);
      throw error;
    }
  }

  async getRecentOrders(limit: number = 10, shopId?: number, branchId?: number): Promise<any[]> {
    let query = db
      .select({
        // Order fields
        id: orders.id,
        orderNumber: orders.orderNumber,
        orderType: orders.orderType,
        status: orders.status,
        tableId: orders.tableId,
        customerId: orders.customerId,
        platformId: orders.platformId,
        numberOfPersons: orders.numberOfPersons,
        subtotal: orders.subtotal,
        taxAmount: orders.taxAmount,
        taxRate: orders.taxRate,
        discountAmount: orders.discountAmount,
        totalAmount: orders.totalAmount,
        paymentMethod: orders.paymentMethod,
        paymentStatus: orders.paymentStatus,
        notes: orders.notes,
        createdAt: orders.createdAt,
        userId: orders.userId,
        shopId: orders.shopId,
        branchId: orders.branchId,
        // Table name
        tableName: tables.name
      })
      .from(orders)
      .leftJoin(tables, eq(orders.tableId, tables.id))
      .orderBy(desc(orders.createdAt))
      .limit(limit);

    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(orders.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(orders.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async getAllOrders(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number): Promise<Order[]> {
    let query = db.select().from(orders);
    const conditions = [];

    if (startDate && endDate) {
      conditions.push(
        and(
          gte(orders.createdAt, startDate),
          lte(orders.createdAt, endDate)
        )
      );
    } else if (startDate) {
      conditions.push(gte(orders.createdAt, startDate));
    } else if (endDate) {
      conditions.push(lte(orders.createdAt, endDate));
    }

    if (shopId !== undefined) {
      conditions.push(eq(orders.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(orders.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query.orderBy(desc(orders.createdAt));
  }

  async getAllOrdersForStatusSummary(shopId?: number, branchId?: number): Promise<Order[]> {
    let query = db.select({
      id: orders.id,
      status: orders.status
    }).from(orders);

    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(orders.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(orders.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async getAllOrdersPaginated(
    page: number,
    pageSize: number,
    shopId?: number,
    branchId?: number,
    status?: string,
    orderType?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{ orders: any[], total: number, totalPages: number }> {
    try {
      console.log('Storage: getAllOrdersPaginated called with params:', {
        page, pageSize, shopId, branchId, status, orderType,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString()
      });

      // Calculate offset based on page and pageSize
      const offset = (page - 1) * pageSize;
      console.log('Storage: Calculated offset:', offset);

      // Build conditions
      const conditions = [];
      if (shopId !== undefined) {
        console.log('Storage: Adding shopId condition:', shopId);
        conditions.push(eq(orders.shopId, shopId));
      }

      if (branchId !== undefined) {
        console.log('Storage: Adding branchId condition:', branchId);
        conditions.push(eq(orders.branchId, branchId));
      }

      // Add status filter if provided and not 'all'
      if (status && status !== 'all') {
        console.log('Storage: Adding status condition:', status);
        conditions.push(eq(orders.status, status));
      }

      // Add order type filter if provided and not 'all'
      if (orderType && orderType !== 'all') {
        console.log('Storage: Adding orderType condition:', orderType);
        conditions.push(eq(orders.orderType, orderType));
      }

      // Add date range filters if provided
      if (startDate) {
        console.log('Storage: Adding startDate condition:', startDate.toISOString());
        conditions.push(gte(orders.createdAt, startDate));
      }

      if (endDate) {
        console.log('Storage: Adding endDate condition:', endDate.toISOString());
        conditions.push(lte(orders.createdAt, endDate));
      }

      // Get total count for pagination
      console.log('Storage: Building count query');
      const countQuery = db.select({ count: sql`count(*)` }).from(orders);
      if (conditions.length > 0) {
        countQuery.where(and(...conditions));
      }
      const [countResult] = await countQuery;
      const total = Number(countResult?.count || 0);
      const totalPages = Math.ceil(total / pageSize);

      // Get paginated orders with table names
      console.log('Storage: Building orders query with table join');
      let query = db
        .select({
          // Order fields
          id: orders.id,
          orderNumber: orders.orderNumber,
          orderType: orders.orderType,
          status: orders.status,
          tableId: orders.tableId,
          customerId: orders.customerId,
          platformId: orders.platformId,
          numberOfPersons: orders.numberOfPersons,
          subtotal: orders.subtotal,
          taxAmount: orders.taxAmount,
          taxRate: orders.taxRate,
          discountAmount: orders.discountAmount,
          totalAmount: orders.totalAmount,
          paymentMethod: orders.paymentMethod,
          paymentStatus: orders.paymentStatus,
          notes: orders.notes,
          createdAt: orders.createdAt,
          userId: orders.userId,
          shopId: orders.shopId,
          branchId: orders.branchId,
          // Table name
          tableName: tables.name
        })
        .from(orders)
        .leftJoin(tables, eq(orders.tableId, tables.id));

      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      const paginatedOrders = await query
        .orderBy(desc(orders.createdAt))
        .limit(pageSize)
        .offset(offset);

      console.log('Storage: Retrieved orders with table names:', paginatedOrders.length);
      console.log('Storage: First few orders:', paginatedOrders.slice(0, 3).map(o => ({
        orderNumber: o.orderNumber,
        createdAt: o.createdAt,
        status: o.status,
        orderType: o.orderType
      })));

      return {
        orders: paginatedOrders,
        total,
        totalPages
      };
    } catch (error) {
      console.error('Storage: Error in getAllOrdersPaginated:', error);
      throw error; // Re-throw to be handled by the route handler
    }
  }

  async getAllOrderItemsByOrderIds(orderIds: number[]): Promise<OrderItem[]> {
    try {
      if (!orderIds || orderIds.length === 0) {
        return [];
      }

      const items = await db
        .select()
        .from(orderItems)
        .where(sql`order_id IN (${orderIds.join(',')})`);

      return items;
    } catch (error) {
      throw error;
    }
  }

  // Expense operations
  async createExpense(expenseData: InsertExpense): Promise<Expense> {
    const [expense] = await db
      .insert(expenses)
      .values(expenseData)
      .returning();

    return expense;
  }

  async getExpenseById(id: number): Promise<Expense | undefined> {
    const [expense] = await db
      .select()
      .from(expenses)
      .where(eq(expenses.id, id));

    return expense;
  }

  async updateExpense(id: number, expenseData: Partial<InsertExpense>): Promise<Expense | undefined> {
    const [expense] = await db
      .update(expenses)
      .set(expenseData)
      .where(eq(expenses.id, id))
      .returning();

    return expense;
  }

  async deleteExpense(id: number): Promise<boolean> {
    const result = await db
      .delete(expenses)
      .where(eq(expenses.id, id));

    return result.rowCount > 0;
  }

  async getAllExpenses(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number, category?: string): Promise<Expense[]> {
    let query = db.select().from(expenses);
    const conditions = [];

    if (startDate && endDate) {
      conditions.push(
        and(
          gte(expenses.date, startDate),
          lte(expenses.date, endDate)
        )
      );
    } else if (startDate) {
      conditions.push(gte(expenses.date, startDate));
    } else if (endDate) {
      conditions.push(lte(expenses.date, endDate));
    }

    if (shopId !== undefined) {
      conditions.push(eq(expenses.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(expenses.branchId, branchId));
    }

    if (category !== undefined) {
      conditions.push(eq(expenses.category, category));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query.orderBy(desc(expenses.date));
  }

  // Purchase operations
  async createPurchase(purchaseData: InsertPurchase): Promise<Purchase> {
    const [purchase] = await db
      .insert(purchases)
      .values(purchaseData)
      .returning();

    return purchase;
  }

  async getPurchaseById(id: number): Promise<Purchase | undefined> {
    const [purchase] = await db
      .select()
      .from(purchases)
      .where(eq(purchases.id, id));

    return purchase;
  }

  async updatePurchase(id: number, purchaseData: Partial<InsertPurchase>): Promise<Purchase | undefined> {
    const [purchase] = await db
      .update(purchases)
      .set(purchaseData)
      .where(eq(purchases.id, id))
      .returning();

    return purchase;
  }

  async deletePurchase(id: number): Promise<boolean> {
    const result = await db
      .delete(purchases)
      .where(eq(purchases.id, id));

    return result.rowCount > 0;
  }

  async getAllPurchases(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number, category?: string): Promise<Purchase[]> {
    let query = db.select().from(purchases);
    const conditions = [];

    if (startDate && endDate) {
      conditions.push(
        and(
          gte(purchases.date, startDate),
          lte(purchases.date, endDate)
        )
      );
    } else if (startDate) {
      conditions.push(gte(purchases.date, startDate));
    } else if (endDate) {
      conditions.push(lte(purchases.date, endDate));
    }

    if (shopId !== undefined) {
      conditions.push(eq(purchases.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(purchases.branchId, branchId));
    }

    if (category !== undefined && category !== "") {
      conditions.push(eq(purchases.supplierName, category));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query.orderBy(desc(purchases.date));
  }

  // Tax settings operations
  async createTaxSetting(taxSettingData: InsertTaxSetting): Promise<TaxSetting> {
    const [taxSetting] = await db
      .insert(taxSettings)
      .values(taxSettingData)
      .returning();

    return taxSetting;
  }

  async getTaxSettingById(id: number): Promise<TaxSetting | undefined> {
    const [taxSetting] = await db
      .select()
      .from(taxSettings)
      .where(eq(taxSettings.id, id));

    return taxSetting;
  }

  async updateTaxSetting(id: number, taxSettingData: Partial<InsertTaxSetting>): Promise<TaxSetting | undefined> {
    const [taxSetting] = await db
      .update(taxSettings)
      .set(taxSettingData)
      .where(eq(taxSettings.id, id))
      .returning();

    return taxSetting;
  }

  async deleteTaxSetting(id: number): Promise<boolean> {
    const result = await db
      .delete(taxSettings)
      .where(eq(taxSettings.id, id));

    return result.rowCount > 0;
  }

  async getAllTaxSettings(shopId?: number, branchId?: number): Promise<TaxSetting[]> {
    let query = db.select().from(taxSettings).orderBy(asc(taxSettings.name));
    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(taxSettings.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(taxSettings.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  // Discount settings operations
  async createDiscountSetting(discountSettingData: InsertDiscountSetting): Promise<DiscountSetting> {
    const [discountSetting] = await db
      .insert(discountSettings)
      .values(discountSettingData)
      .returning();

    return discountSetting;
  }

  async getDiscountSettingById(id: number): Promise<DiscountSetting | undefined> {
    const [discountSetting] = await db
      .select()
      .from(discountSettings)
      .where(eq(discountSettings.id, id));

    return discountSetting;
  }

  async updateDiscountSetting(id: number, discountSettingData: Partial<InsertDiscountSetting>): Promise<DiscountSetting | undefined> {
    const [discountSetting] = await db
      .update(discountSettings)
      .set(discountSettingData)
      .where(eq(discountSettings.id, id))
      .returning();

    return discountSetting;
  }

  async deleteDiscountSetting(id: number): Promise<boolean> {
    const result = await db
      .delete(discountSettings)
      .where(eq(discountSettings.id, id));

    return result.rowCount > 0;
  }

  async getAllDiscountSettings(shopId?: number, branchId?: number): Promise<DiscountSetting[]> {
    let query = db.select().from(discountSettings).orderBy(asc(discountSettings.name));
    const conditions = [];

    if (shopId !== undefined) {
      conditions.push(eq(discountSettings.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(discountSettings.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  // Payment methods operations
  async createPaymentMethod(paymentMethodData: InsertPaymentMethod): Promise<PaymentMethod> {
    const [paymentMethod] = await db
      .insert(paymentMethods)
      .values(paymentMethodData)
      .returning();

    return paymentMethod;
  }

  async getPaymentMethodById(id: number): Promise<PaymentMethod | undefined> {
    const [paymentMethod] = await db
      .select()
      .from(paymentMethods)
      .where(eq(paymentMethods.id, id));

    return paymentMethod;
  }

  async updatePaymentMethod(id: number, paymentMethodData: Partial<InsertPaymentMethod>): Promise<PaymentMethod | undefined> {
    const [paymentMethod] = await db
      .update(paymentMethods)
      .set(paymentMethodData)
      .where(eq(paymentMethods.id, id))
      .returning();

    return paymentMethod;
  }

  async getAllPaymentMethods(active?: boolean, shopId?: number, branchId?: number): Promise<PaymentMethod[]> {
    let query = db.select().from(paymentMethods);
    const conditions = [];

    if (active !== undefined) {
      conditions.push(eq(paymentMethods.active, active));
    }

    if (shopId !== undefined) {
      conditions.push(eq(paymentMethods.shopId, shopId));
    }

    if (branchId !== undefined) {
      conditions.push(eq(paymentMethods.branchId, branchId));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query;
  }

  async deletePaymentMethod(id: number): Promise<PaymentMethod | undefined> {
    try {
      console.log(`Storage: Deleting payment method with ID: ${id}`);

      // Delete the payment method and return the deleted record
      const [deletedPaymentMethod] = await db
        .delete(paymentMethods)
        .where(eq(paymentMethods.id, id))
        .returning();

      console.log(`Storage: Deleted payment method: ${deletedPaymentMethod?.name || 'unknown'}`);

      return deletedPaymentMethod;
    } catch (error) {
      console.error(`Storage: Error deleting payment method ${id}:`, error);
      throw error;
    }
  }

  // Printer settings operations
  async createPrinterSetting(printerSettingData: InsertPrinterSetting): Promise<PrinterSetting> {
    const [printerSetting] = await db
      .insert(printerSettings)
      .values(printerSettingData)
      .returning();

    return printerSetting;
  }

  async getPrinterSettingByShop(shopId: number, branchId?: number): Promise<PrinterSetting | undefined> {
    let query = db.select().from(printerSettings);
    const conditions = [eq(printerSettings.shopId, shopId)];

    if (branchId !== undefined) {
      conditions.push(eq(printerSettings.branchId, branchId));
    }

    const [printerSetting] = await query.where(and(...conditions));
    return printerSetting;
  }

  async updatePrinterSetting(id: number, printerSettingData: Partial<InsertPrinterSetting>): Promise<PrinterSetting | undefined> {
    // Update the updatedAt timestamp
    printerSettingData.updatedAt = new Date();

    const [printerSetting] = await db
      .update(printerSettings)
      .set(printerSettingData)
      .where(eq(printerSettings.id, id))
      .returning();

    return printerSetting;
  }

  // User preferences operations
  async createUserPreference(userPreferenceData: InsertUserPreference): Promise<UserPreference> {
    const [userPreference] = await db
      .insert(userPreferences)
      .values(userPreferenceData)
      .returning();

    return userPreference;
  }

  async getUserPreferenceByUserId(userId: number): Promise<UserPreference | undefined> {
    const [userPreference] = await db
      .select()
      .from(userPreferences)
      .where(eq(userPreferences.userId, userId));

    return userPreference;
  }

  async updateUserPreference(id: number, userPreferenceData: Partial<InsertUserPreference>): Promise<UserPreference | undefined> {
    // Update the updatedAt timestamp
    userPreferenceData.updatedAt = new Date();

    const [userPreference] = await db
      .update(userPreferences)
      .set(userPreferenceData)
      .where(eq(userPreferences.id, id))
      .returning();

    return userPreference;
  }

  // Rounding settings operations
  async createRoundingSetting(roundingSettingData: InsertRoundingSetting): Promise<RoundingSetting> {
    const [roundingSetting] = await db
      .insert(roundingSettings)
      .values(roundingSettingData)
      .returning();

    return roundingSetting;
  }

  async getRoundingSettingByShop(shopId: number, branchId?: number): Promise<RoundingSetting | undefined> {
    let query = db.select().from(roundingSettings);
    const conditions = [eq(roundingSettings.shopId, shopId)];

    if (branchId !== undefined) {
      conditions.push(eq(roundingSettings.branchId, branchId));
    }

    const [roundingSetting] = await query.where(and(...conditions));
    return roundingSetting;
  }

  async updateRoundingSetting(id: number, roundingSettingData: Partial<InsertRoundingSetting>): Promise<RoundingSetting | undefined> {
    // Update the updatedAt timestamp
    roundingSettingData.updatedAt = new Date();

    const [roundingSetting] = await db
      .update(roundingSettings)
      .set(roundingSettingData)
      .where(eq(roundingSettings.id, id))
      .returning();

    return roundingSetting;
  }

  // Dashboard data
  async getDashboardStats(shopId?: number, branchId?: number): Promise<{
    todaySales: number;
    totalOrders: number;
    avgOrderValue: number;
    todayExpenses: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Build conditions for orders
    const orderConditions = [
      gte(orders.createdAt, today),
      lte(orders.createdAt, tomorrow)
    ];

    if (shopId !== undefined) {
      orderConditions.push(eq(orders.shopId, shopId));
    }

    if (branchId !== undefined) {
      orderConditions.push(eq(orders.branchId, branchId));
    }

    // Get today's sales
    const todayOrders = await db
      .select()
      .from(orders)
      .where(and(...orderConditions));

    const grossSales = todayOrders.reduce((sum, order) => sum + Number(order.totalAmount), 0);

    // Get today's refunds to subtract from sales
    const refundConditions = [
      gte(refunds.createdAt, today),
      lte(refunds.createdAt, tomorrow)
    ];

    if (shopId !== undefined) {
      refundConditions.push(eq(refunds.shopId, shopId));
    }

    if (branchId !== undefined) {
      refundConditions.push(eq(refunds.branchId, branchId));
    }

    const todayRefunds = await db
      .select()
      .from(refunds)
      .where(and(...refundConditions));

    const totalRefunds = todayRefunds.reduce((sum, refund) => sum + Number(refund.amount), 0);

    // Calculate net sales (gross sales minus refunds)
    const todaySales = grossSales - totalRefunds;
    const totalOrders = todayOrders.length;
    const avgOrderValue = totalOrders > 0 ? todaySales / totalOrders : 0;

    // Build conditions for expenses
    const expenseConditions = [
      gte(expenses.date, today),
      lte(expenses.date, tomorrow)
    ];

    if (shopId !== undefined) {
      expenseConditions.push(eq(expenses.shopId, shopId));
    }

    if (branchId !== undefined) {
      expenseConditions.push(eq(expenses.branchId, branchId));
    }

    // Get today's expenses
    const todayExpensesData = await db
      .select()
      .from(expenses)
      .where(and(...expenseConditions));

    const todayExpenses = todayExpensesData.reduce((sum, expense) => sum + Number(expense.amount), 0);

    return {
      todaySales,
      totalOrders,
      avgOrderValue,
      todayExpenses
    };
  }

  async getTopProducts(period: 'today' | 'week' | 'month', limit: number = 5, shopId?: number, branchId?: number): Promise<any[]> {
    try {
      console.log(`getTopProducts called with period: ${period}, limit: ${limit}, shopId: ${shopId}, branchId: ${branchId}`);

      // Calculate the date range based on the period
      const endDate = new Date();
      const startDate = new Date();

      if (period === 'today') {
        startDate.setHours(0, 0, 0, 0);
      } else if (period === 'week') {
        startDate.setDate(startDate.getDate() - 7);
      } else if (period === 'month') {
        startDate.setMonth(startDate.getMonth() - 1);
      }

      // Build conditions array
      const conditions = [eq(products.active, true)];

      if (shopId !== undefined) {
        conditions.push(eq(products.shopId, shopId));
      }

      if (branchId !== undefined) {
        conditions.push(sql`(${products.branchId} = ${branchId} OR ${products.branchId} IS NULL)`);
      }

      // Use Drizzle ORM to build the query
      const query = db
        .select({
          id: products.id,
          name: products.name,
          category: categories.name,
          quantity_sold: sql<number>`COALESCE(SUM(${orderItems.quantity}), 0)`,
          revenue: sql<number>`COALESCE(SUM(${orderItems.totalPrice}), 0)`
        })
        .from(products)
        .leftJoin(orderItems, eq(products.id, orderItems.productId))
        .leftJoin(orders, and(
          eq(orderItems.orderId, orders.id),
          gte(orders.createdAt, startDate),
          lte(orders.createdAt, endDate),
          branchId !== undefined ? sql`(${orders.branchId} = ${branchId} OR ${orders.branchId} IS NULL)` : undefined
        ))
        .leftJoin(categories, eq(products.categoryId, categories.id))
        .where(and(...conditions))
        .groupBy(products.id, products.name, categories.name)
        .having(sql`COALESCE(SUM(${orderItems.quantity}), 0) > 0`)
        .orderBy(sql`COALESCE(SUM(${orderItems.quantity}), 0) DESC`)
        .limit(limit);

      const result = await query;

      // If no results, return sample data for testing
      if (result.length === 0) {
        return this.getSampleTopProducts();
      }

      // Format the results
      const formattedResults = result.map((row: any) => ({
        ...row,
        quantity_sold: Number(row.quantity_sold),
        revenue: Number(row.revenue)
      }));

      return formattedResults;
    } catch (error) {
      // Return sample data in case of error
      return this.getSampleTopProducts();
    }
  }

  // Helper method to provide sample data when no real data is available
  private getSampleTopProducts(): any[] {
    return [
      {
        id: 1,
        name: "Butter Chicken",
        category: "Main Course",
        quantity_sold: 24,
        revenue: 7200
      },
      {
        id: 2,
        name: "Garlic Naan",
        category: "Breads",
        quantity_sold: 48,
        revenue: 2400
      },
      {
        id: 3,
        name: "Masala Chai",
        category: "Beverages",
        quantity_sold: 36,
        revenue: 1800
      },
      {
        id: 4,
        name: "Gulab Jamun",
        category: "Desserts",
        quantity_sold: 18,
        revenue: 1440
      }
    ];
  }

  // Branch access validation methods
  async validateUserBranchAccess(userId: number, shopId: number, branchId?: number): Promise<boolean> {
    try {
      console.log(`Validating user ${userId} access to shop ${shopId}, branch ${branchId}`);

      // First check if user has access to the shop
      const userShopRole = await this.getUserShopRole(userId, shopId);
      console.log(`User shop role: ${userShopRole}`);

      if (!userShopRole) {
        console.log(`User ${userId} has no access to shop ${shopId}`);
        return false;
      }

      // Shop owners and admins have access to all branches
      if (userShopRole === 'owner' || userShopRole === 'admin') {
        console.log(`User ${userId} is shop owner/admin, has access to all branches`);
        return true;
      }

      // If no specific branch is requested, user has general shop access
      if (!branchId) {
        console.log(`No specific branch requested, user has shop access`);
        return true;
      }

      // Check specific branch access
      const userBranchRole = await this.getUserBranchRole(userId, branchId);
      console.log(`User branch role: ${userBranchRole}`);

      return !!userBranchRole;
    } catch (error) {
      console.error(`Error validating user branch access:`, error);
      return false;
    }
  }

  async validateExpenseAccess(userId: number, expenseId: number): Promise<boolean> {
    try {
      console.log(`Validating user ${userId} access to expense ${expenseId}`);

      // Get the expense record
      const expense = await this.getExpenseById(expenseId);
      if (!expense) {
        console.log(`Expense ${expenseId} not found`);
        return false;
      }

      console.log(`Expense belongs to shop ${expense.shopId}, branch ${expense.branchId}`);

      // Validate user has access to the expense's shop and branch
      return await this.validateUserBranchAccess(userId, expense.shopId, expense.branchId || undefined);
    } catch (error) {
      console.error(`Error validating expense access:`, error);
      return false;
    }
  }

  async validatePurchaseAccess(userId: number, purchaseId: number): Promise<boolean> {
    try {
      console.log(`Validating user ${userId} access to purchase ${purchaseId}`);

      // Get the purchase record
      const purchase = await this.getPurchaseById(purchaseId);
      if (!purchase) {
        console.log(`Purchase ${purchaseId} not found`);
        return false;
      }

      console.log(`Purchase belongs to shop ${purchase.shopId}, branch ${purchase.branchId}`);

      // Validate user has access to the purchase's shop and branch
      return await this.validateUserBranchAccess(userId, purchase.shopId, purchase.branchId || undefined);
    } catch (error) {
      console.error(`Error validating purchase access:`, error);
      return false;
    }
  }

  // Subscription operations
  async getAllSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const plans = await db
        .select()
        .from(subscriptionPlans)
        .where(eq(subscriptionPlans.active, true))
        .orderBy(asc(subscriptionPlans.price));

      return plans;
    } catch (error) {
      console.error('Error getting subscription plans:', error);
      throw error;
    }
  }

  async getSubscriptionPlanById(id: number): Promise<SubscriptionPlan | undefined> {
    try {
      const [plan] = await db
        .select()
        .from(subscriptionPlans)
        .where(eq(subscriptionPlans.id, id));

      return plan;
    } catch (error) {
      console.error(`Error getting subscription plan ${id}:`, error);
      throw error;
    }
  }

  async createSubscriptionPlan(planData: InsertSubscriptionPlan): Promise<SubscriptionPlan> {
    try {
      const [plan] = await db
        .insert(subscriptionPlans)
        .values(planData)
        .returning();

      return plan;
    } catch (error) {
      console.error('Error creating subscription plan:', error);
      throw error;
    }
  }

  async updateSubscriptionPlan(id: number, planData: Partial<InsertSubscriptionPlan>): Promise<SubscriptionPlan | undefined> {
    try {
      const [plan] = await db
        .update(subscriptionPlans)
        .set({ ...planData, updatedAt: new Date() })
        .where(eq(subscriptionPlans.id, id))
        .returning();

      return plan;
    } catch (error) {
      console.error(`Error updating subscription plan ${id}:`, error);
      throw error;
    }
  }

  async deleteSubscriptionPlan(id: number): Promise<boolean> {
    try {
      // Check if any subscriptions are using this plan
      const subscriptionsWithPlan = await db
        .select()
        .from(subscriptions)
        .where(eq(subscriptions.planId, id));

      // If subscriptions are using this plan, don't delete it
      if (subscriptionsWithPlan.length > 0) {
        return false;
      }

      const result = await db
        .delete(subscriptionPlans)
        .where(eq(subscriptionPlans.id, id));

      return result.rowCount > 0;
    } catch (error) {
      console.error(`Error deleting subscription plan ${id}:`, error);
      throw error;
    }
  }

  async getShopSubscriptions(shopId: number): Promise<Subscription[]> {
    try {
      const shopSubscriptions = await db
        .select()
        .from(subscriptions)
        .where(eq(subscriptions.shopId, shopId))
        .orderBy(desc(subscriptions.createdAt));

      return shopSubscriptions;
    } catch (error) {
      console.error(`Error getting subscriptions for shop ${shopId}:`, error);
      throw error;
    }
  }

  async getShopSubscriptionsWithPlans(shopId: number): Promise<any[]> {
    try {
      const shopSubscriptionsWithPlans = await db
        .select({
          id: subscriptions.id,
          shopId: subscriptions.shopId,
          planId: subscriptions.planId,
          status: subscriptions.status,
          startDate: subscriptions.startDate,
          endDate: subscriptions.endDate,
          totalAmount: subscriptions.totalAmount,
          autoRenew: subscriptions.autoRenew,
          createdAt: subscriptions.createdAt,
          updatedAt: subscriptions.updatedAt,
          planName: subscriptionPlans.name,
          planDescription: subscriptionPlans.description,
          planPrice: subscriptionPlans.price,
          planBillingCycle: subscriptionPlans.billingCycle
        })
        .from(subscriptions)
        .leftJoin(subscriptionPlans, eq(subscriptions.planId, subscriptionPlans.id))
        .where(eq(subscriptions.shopId, shopId))
        .orderBy(desc(subscriptions.createdAt));

      // Handle cases where plan data is missing by providing fallback names
      const processedSubscriptions = shopSubscriptionsWithPlans.map(sub => {
        const getFallbackPlanName = (planId: number): string => {
          const fallbackNames: { [key: number]: string } = {
            1: 'Basic Plan',
            2: 'Professional Plan',
            3: 'Enterprise Plan'
          };
          return fallbackNames[planId] || 'Unknown Plan';
        };

        return {
          ...sub,
          planName: sub.planName || getFallbackPlanName(sub.planId),
          planDescription: sub.planDescription || 'Plan details not available'
        };
      });

      return processedSubscriptions;
    } catch (error) {
      console.error(`Error getting subscriptions with plans for shop ${shopId}:`, error);
      throw error;
    }
  }

  async getSubscriptionById(id: number): Promise<Subscription | undefined> {
    try {
      const [subscription] = await db
        .select()
        .from(subscriptions)
        .where(eq(subscriptions.id, id));

      return subscription;
    } catch (error) {
      console.error(`Error getting subscription ${id}:`, error);
      throw error;
    }
  }

  async createSubscription(subscriptionData: InsertSubscription): Promise<Subscription> {
    try {
      const [subscription] = await db
        .insert(subscriptions)
        .values(subscriptionData)
        .returning();

      return subscription;
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  async updateSubscription(id: number, subscriptionData: Partial<InsertSubscription>): Promise<Subscription | undefined> {
    try {
      const [subscription] = await db
        .update(subscriptions)
        .set({ ...subscriptionData, updatedAt: new Date() })
        .where(eq(subscriptions.id, id))
        .returning();

      return subscription;
    } catch (error) {
      console.error(`Error updating subscription ${id}:`, error);
      throw error;
    }
  }

  async updateSubscriptionStatus(id: number, status: string): Promise<Subscription | undefined> {
    try {
      const [subscription] = await db
        .update(subscriptions)
        .set({ status, updatedAt: new Date() })
        .where(eq(subscriptions.id, id))
        .returning();

      return subscription;
    } catch (error) {
      console.error(`Error updating subscription status ${id}:`, error);
      throw error;
    }
  }

  async getSubscriptionStats(): Promise<any> {
    try {
      // Get total subscriptions count
      const [totalResult] = await db
        .select({ count: sql`count(*)` })
        .from(subscriptions);

      // Get active subscriptions count
      const [activeResult] = await db
        .select({ count: sql`count(*)` })
        .from(subscriptions)
        .where(eq(subscriptions.status, 'active'));

      // Get total revenue
      const [revenueResult] = await db
        .select({ total: sql`sum(${subscriptions.totalAmount})` })
        .from(subscriptions);

      return {
        totalSubscriptions: Number(totalResult?.count || 0),
        activeSubscriptions: Number(activeResult?.count || 0),
        totalRevenue: Number(revenueResult?.total || 0)
      };
    } catch (error) {
      console.error('Error getting subscription stats:', error);
      throw error;
    }
  }

  // Subscription payment methods operations
  async createSubscriptionPaymentMethod(paymentMethodData: InsertSubscriptionPaymentMethod): Promise<SubscriptionPaymentMethod> {
    try {
      console.log('Creating subscription payment method:', paymentMethodData);

      // For security, only store last 4 digits of card number
      const maskedCardNumber = paymentMethodData.cardNumber.slice(-4);

      const [paymentMethod] = await db
        .insert(subscriptionPaymentMethods)
        .values({
          ...paymentMethodData,
          cardNumber: maskedCardNumber // Only store last 4 digits
        })
        .returning();

      console.log('Subscription payment method created successfully:', paymentMethod);
      return paymentMethod;
    } catch (error) {
      console.error('Error creating subscription payment method:', error);
      throw error;
    }
  }

  async getSubscriptionPaymentMethodsByShop(shopId: number): Promise<SubscriptionPaymentMethod[]> {
    try {
      console.log(`=== STORAGE: Getting subscription payment methods for shop ${shopId} ===`);
      console.log('Database connection status:', !!db);
      console.log('subscriptionPaymentMethods table:', !!subscriptionPaymentMethods);

      console.log('Building query...');
      const query = db
        .select()
        .from(subscriptionPaymentMethods)
        .where(
          and(
            eq(subscriptionPaymentMethods.shopId, shopId),
            eq(subscriptionPaymentMethods.active, true)
          )
        )
        .orderBy(desc(subscriptionPaymentMethods.createdAt));

      console.log('Executing query...');
      const paymentMethods = await query;
      console.log('Query executed successfully');

      console.log(`Retrieved ${paymentMethods.length} subscription payment methods`);
      console.log('Payment methods data:', JSON.stringify(paymentMethods, null, 2));
      return paymentMethods;
    } catch (error) {
      console.error('=== STORAGE ERROR: Getting subscription payment methods ===');
      console.error('Error details:', error);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);

      // Check if it's a table not found error
      if (error.message && (error.message.includes('relation') && error.message.includes('does not exist'))) {
        console.error('=== TABLE MISSING: subscription_payment_methods table does not exist ===');
        console.log('=== ATTEMPTING TO CREATE TABLE ===');

        try {
          // Create the table using raw SQL
          await db.execute(sql`
            CREATE TABLE IF NOT EXISTS subscription_payment_methods (
              id SERIAL PRIMARY KEY,
              shop_id INTEGER NOT NULL REFERENCES shops(id),
              method_type TEXT NOT NULL DEFAULT 'card',
              card_number TEXT,
              card_holder_name TEXT,
              expiry_month INTEGER,
              expiry_year INTEGER,
              billing_address TEXT,
              is_default BOOLEAN DEFAULT false NOT NULL,
              active BOOLEAN DEFAULT true NOT NULL,
              created_at TIMESTAMP DEFAULT NOW() NOT NULL,
              updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
              created_by INTEGER NOT NULL REFERENCES users(id)
            );
          `);

          console.log('=== TABLE CREATED SUCCESSFULLY ===');

          // Retry the query
          const paymentMethods = await query;
          console.log(`Retrieved ${paymentMethods.length} subscription payment methods after table creation`);
          return paymentMethods;

        } catch (createError) {
          console.error('=== ERROR CREATING TABLE ===', createError);
          // Return empty array if table creation fails
          return [];
        }
      }

      throw error;
    }
  }

  async updateSubscriptionPaymentMethod(id: number, paymentMethodData: Partial<InsertSubscriptionPaymentMethod>): Promise<SubscriptionPaymentMethod | undefined> {
    try {
      console.log(`Updating subscription payment method ${id}:`, paymentMethodData);

      // If updating card number, only store last 4 digits
      if (paymentMethodData.cardNumber) {
        paymentMethodData.cardNumber = paymentMethodData.cardNumber.slice(-4);
      }

      const [paymentMethod] = await db
        .update(subscriptionPaymentMethods)
        .set({
          ...paymentMethodData,
          updatedAt: new Date()
        })
        .where(eq(subscriptionPaymentMethods.id, id))
        .returning();

      console.log('Subscription payment method updated successfully:', paymentMethod);
      return paymentMethod;
    } catch (error) {
      console.error('Error updating subscription payment method:', error);
      throw error;
    }
  }

  async deleteSubscriptionPaymentMethod(id: number): Promise<SubscriptionPaymentMethod | undefined> {
    try {
      console.log(`Deleting subscription payment method ${id}`);

      // Soft delete by setting active to false
      const [paymentMethod] = await db
        .update(subscriptionPaymentMethods)
        .set({
          active: false,
          updatedAt: new Date()
        })
        .where(eq(subscriptionPaymentMethods.id, id))
        .returning();

      console.log('Subscription payment method deleted successfully:', paymentMethod);
      return paymentMethod;
    } catch (error) {
      console.error('Error deleting subscription payment method:', error);
      throw error;
    }
  }

  // Stock Movement operations
  async createStockMovement(movementData: InsertStockMovement): Promise<StockMovement> {
    try {
      console.log('Creating stock movement:', movementData);
      const [movement] = await db
        .insert(stockMovements)
        .values(movementData)
        .returning();

      console.log('Stock movement created successfully:', movement);
      return movement;
    } catch (error) {
      console.error('Error creating stock movement:', error);
      throw error;
    }
  }

  async getStockMovements(shopId?: number, branchId?: number): Promise<StockMovement[]> {
    try {
      console.log(`[STORAGE] Getting stock movements for shop ${shopId}, branch ${branchId}`);

      if (shopId !== undefined && branchId !== undefined) {
        // Filter by both shop and branch
        console.log(`[STORAGE] Filtering stock movements by shop ${shopId} and branch ${branchId}`);

        // First, get all branches for this shop to ensure we only get movements from valid branches
        const shopBranches = await db
          .select({ id: branches.id })
          .from(branches)
          .where(eq(branches.shopId, shopId));

        const shopBranchIds = shopBranches.map(b => b.id);
        console.log(`[STORAGE] Shop ${shopId} has branches: ${shopBranchIds.join(', ')}`);

        if (shopBranchIds.length === 0) {
          console.log(`[STORAGE] No branches found for shop ${shopId}, returning empty array`);
          return [];
        }

        const movements = await db
          .select({
            id: stockMovements.id,
            productId: stockMovements.productId,
            branchId: stockMovements.branchId,
            movementType: stockMovements.movementType,
            quantity: stockMovements.quantity,
            referenceType: stockMovements.referenceType,
            referenceId: stockMovements.referenceId,
            reason: stockMovements.reason,
            notes: stockMovements.notes,
            createdBy: stockMovements.createdBy,
            createdAt: stockMovements.createdAt
          })
          .from(stockMovements)
          .innerJoin(branches, eq(stockMovements.branchId, branches.id))
          .where(
            and(
              eq(branches.shopId, shopId),
              eq(stockMovements.branchId, branchId),
              // Additional safety: ensure branchId is in the list of valid shop branches
              inArray(stockMovements.branchId, shopBranchIds)
            )
          )
          .orderBy(desc(stockMovements.createdAt));

        console.log(`[STORAGE] Retrieved ${movements.length} stock movements (shop + branch filter)`);

        // Additional validation: ensure all movements belong to the correct shop
        const validatedMovements = movements.filter(movement => {
          // This should always be true due to the join, but adding as extra safety
          return movement.branchId === branchId;
        });

        if (validatedMovements.length !== movements.length) {
          console.warn(`[STORAGE] Filtered out ${movements.length - validatedMovements.length} invalid movements`);
        }

        return validatedMovements;
      } else if (shopId !== undefined) {
        // Filter by shop only
        console.log(`[STORAGE] Filtering stock movements by shop ${shopId} only`);
        const movements = await db
          .select({
            id: stockMovements.id,
            productId: stockMovements.productId,
            branchId: stockMovements.branchId,
            movementType: stockMovements.movementType,
            quantity: stockMovements.quantity,
            referenceType: stockMovements.referenceType,
            referenceId: stockMovements.referenceId,
            reason: stockMovements.reason,
            notes: stockMovements.notes,
            createdBy: stockMovements.createdBy,
            createdAt: stockMovements.createdAt
          })
          .from(stockMovements)
          .innerJoin(branches, eq(stockMovements.branchId, branches.id))
          .where(eq(branches.shopId, shopId))
          .orderBy(desc(stockMovements.createdAt));

        console.log(`[STORAGE] Retrieved ${movements.length} stock movements (shop filter only)`);

        // Additional validation: ensure all movements belong to branches of the correct shop
        const validatedMovements = movements.filter(movement => {
          // The join should ensure this, but adding extra safety
          return movement.branchId !== null;
        });

        if (validatedMovements.length !== movements.length) {
          console.warn(`[STORAGE] Filtered out ${movements.length - validatedMovements.length} movements with invalid branch references`);
        }

        return validatedMovements;
      } else if (branchId !== undefined) {
        // Filter by branch only (fallback)
        console.log(`[STORAGE] Filtering stock movements by branch ${branchId} only`);
        const movements = await db
          .select()
          .from(stockMovements)
          .where(eq(stockMovements.branchId, branchId))
          .orderBy(desc(stockMovements.createdAt));

        console.log(`[STORAGE] Retrieved ${movements.length} stock movements (branch filter only)`);
        return movements;
      } else {
        // No filters - get all movements
        console.log(`[STORAGE] Getting all stock movements (no filters)`);
        const movements = await db
          .select()
          .from(stockMovements)
          .orderBy(desc(stockMovements.createdAt));

        console.log(`[STORAGE] Retrieved ${movements.length} stock movements (no filters)`);
        return movements;
      }
    } catch (error) {
      console.error('[STORAGE] Error getting stock movements:', error);
      throw error;
    }
  }

  async getStockMovementById(id: number): Promise<StockMovement | undefined> {
    try {
      console.log(`[STORAGE] Getting stock movement by ID: ${id}`);
      const [movement] = await db
        .select()
        .from(stockMovements)
        .where(eq(stockMovements.id, id));

      console.log('[STORAGE] Stock movement retrieved:', movement);
      return movement;
    } catch (error) {
      console.error('[STORAGE] Error getting stock movement by ID:', error);
      throw error;
    }
  }

  async getStockMovementStats(shopId?: number, branchId?: number): Promise<{
    totalMovements: number;
    inMovements: number;
    outMovements: number;
    transferMovements: number;
    adjustmentMovements: number;
  }> {
    try {
      console.log(`[STORAGE] Getting stock movement stats for shop ${shopId}, branch ${branchId}`);

      // Get all movements for the shop/branch
      const movements = await this.getStockMovements(shopId, branchId);

      const stats = {
        totalMovements: movements.length,
        inMovements: movements.filter(m => m.movementType === 'in').length,
        outMovements: movements.filter(m => m.movementType === 'out').length,
        transferMovements: movements.filter(m => m.movementType === 'transfer').length,
        adjustmentMovements: movements.filter(m => m.movementType === 'adjustment').length,
      };

      console.log(`[STORAGE] Stock movement stats calculated:`, stats);
      return stats;
    } catch (error) {
      console.error('[STORAGE] Error getting stock movement stats:', error);
      throw error;
    }
  }

  // Stock Transfer operations
  async createStockTransfer(transferData: InsertStockTransfer): Promise<StockTransfer> {
    try {
      console.log('Creating stock transfer:', transferData);
      const [transfer] = await db
        .insert(stockTransfers)
        .values(transferData)
        .returning();

      console.log('Stock transfer created successfully:', transfer);
      return transfer;
    } catch (error) {
      console.error('Error creating stock transfer:', error);
      throw error;
    }
  }

  async createStockTransferItem(itemData: InsertStockTransferItem): Promise<StockTransferItem> {
    try {
      console.log('Creating stock transfer item:', itemData);
      const [item] = await db
        .insert(stockTransferItems)
        .values(itemData)
        .returning();

      console.log('Stock transfer item created successfully:', item);
      return item;
    } catch (error) {
      console.error('Error creating stock transfer item:', error);
      throw error;
    }
  }

  async getStockTransfers(shopId?: number, branchId?: number): Promise<StockTransfer[]> {
    try {
      console.log(`[STORAGE] Getting stock transfers for shop ${shopId}, branch ${branchId}`);

      if (shopId !== undefined && branchId !== undefined) {
        // Filter by both shop and branch
        console.log(`[STORAGE] Filtering by shop ${shopId} and branch ${branchId}`);
        const transfers = await db
          .select({
            id: stockTransfers.id,
            fromBranchId: stockTransfers.fromBranchId,
            toBranchId: stockTransfers.toBranchId,
            status: stockTransfers.status,
            transferDate: stockTransfers.transferDate,
            notes: stockTransfers.notes,
            createdBy: stockTransfers.createdBy,
            approvedBy: stockTransfers.approvedBy,
            completedAt: stockTransfers.completedAt,
            createdAt: stockTransfers.createdAt
          })
          .from(stockTransfers)
          .innerJoin(branches, eq(stockTransfers.fromBranchId, branches.id))
          .where(
            and(
              eq(branches.shopId, shopId),
              or(
                eq(stockTransfers.fromBranchId, branchId),
                eq(stockTransfers.toBranchId, branchId)
              )
            )
          )
          .orderBy(desc(stockTransfers.createdAt));

        console.log(`[STORAGE] Retrieved ${transfers.length} stock transfers (shop + branch filter)`);
        return transfers;
      } else if (shopId !== undefined) {
        // Filter by shop only (for stats)
        console.log(`[STORAGE] Filtering by shop ${shopId} only`);
        const transfers = await db
          .select({
            id: stockTransfers.id,
            fromBranchId: stockTransfers.fromBranchId,
            toBranchId: stockTransfers.toBranchId,
            status: stockTransfers.status,
            transferDate: stockTransfers.transferDate,
            notes: stockTransfers.notes,
            createdBy: stockTransfers.createdBy,
            approvedBy: stockTransfers.approvedBy,
            completedAt: stockTransfers.completedAt,
            createdAt: stockTransfers.createdAt
          })
          .from(stockTransfers)
          .innerJoin(branches, eq(stockTransfers.fromBranchId, branches.id))
          .where(eq(branches.shopId, shopId))
          .orderBy(desc(stockTransfers.createdAt));

        console.log(`[STORAGE] Retrieved ${transfers.length} stock transfers (shop filter only)`);
        return transfers;
      } else {
        // No filters - get all transfers
        console.log(`[STORAGE] Getting all stock transfers (no filters)`);
        const transfers = await db
          .select()
          .from(stockTransfers)
          .orderBy(desc(stockTransfers.createdAt));

        console.log(`[STORAGE] Retrieved ${transfers.length} stock transfers (no filters)`);
        return transfers;
      }
    } catch (error) {
      console.error('[STORAGE] Error getting stock transfers:', error);
      throw error;
    }
  }

  async getStockTransferById(id: number): Promise<StockTransfer | undefined> {
    try {
      console.log(`Getting stock transfer by ID: ${id}`);
      const [transfer] = await db
        .select()
        .from(stockTransfers)
        .where(eq(stockTransfers.id, id));

      console.log('Stock transfer retrieved:', transfer);
      return transfer;
    } catch (error) {
      console.error('Error getting stock transfer by ID:', error);
      throw error;
    }
  }

  async getStockTransferItems(transferId: number): Promise<StockTransferItem[]> {
    try {
      console.log(`Getting stock transfer items for transfer ID: ${transferId}`);
      const items = await db
        .select()
        .from(stockTransferItems)
        .where(eq(stockTransferItems.transferId, transferId))
        .orderBy(stockTransferItems.id);

      console.log(`Retrieved ${items.length} stock transfer items`);
      return items;
    } catch (error) {
      console.error('Error getting stock transfer items:', error);
      throw error;
    }
  }

  async updateStockTransferStatus(id: number, status: string): Promise<StockTransfer | undefined> {
    try {
      console.log(`Updating stock transfer ${id} status to: ${status}`);
      const [transfer] = await db
        .update(stockTransfers)
        .set({
          status,
          completedAt: status === 'completed' ? new Date() : null
        })
        .where(eq(stockTransfers.id, id))
        .returning();

      console.log('Stock transfer status updated:', transfer);
      return transfer;
    } catch (error) {
      console.error('Error updating stock transfer status:', error);
      throw error;
    }
  }

  // Refund operations
  async createRefund(refundData: InsertRefund): Promise<Refund> {
    try {
      console.log('Creating refund:', refundData);
      const [refund] = await db
        .insert(refunds)
        .values(refundData)
        .returning();

      console.log('Refund created successfully:', refund);
      return refund;
    } catch (error) {
      console.error('Error creating refund:', error);
      throw error;
    }
  }

  async getRefundsByOrderId(orderId: number): Promise<Refund[]> {
    try {
      console.log(`Getting refunds for order ID: ${orderId}`);
      const orderRefunds = await db
        .select()
        .from(refunds)
        .where(eq(refunds.orderId, orderId))
        .orderBy(desc(refunds.createdAt));

      console.log(`Retrieved ${orderRefunds.length} refunds for order ${orderId}`);
      return orderRefunds;
    } catch (error) {
      console.error('Error getting refunds by order ID:', error);
      throw error;
    }
  }

  async getAllRefunds(startDate?: Date, endDate?: Date, shopId?: number, branchId?: number): Promise<Refund[]> {
    try {
      console.log(`Getting all refunds with filters - startDate: ${startDate}, endDate: ${endDate}, shopId: ${shopId}, branchId: ${branchId}`);

      const conditions = [];

      if (startDate) {
        // Convert to start of day to ensure we capture all refunds from that date
        const startOfDay = new Date(startDate);
        startOfDay.setHours(0, 0, 0, 0);
        console.log(`Start date filter: ${startOfDay.toISOString()}`);
        conditions.push(gte(refunds.createdAt, startOfDay));
      }

      if (endDate) {
        // Convert to end of day to ensure we capture all refunds until end of that date
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        console.log(`End date filter: ${endOfDay.toISOString()}`);
        conditions.push(lte(refunds.createdAt, endOfDay));
      }

      // More flexible shop filtering - if shopId is provided, filter by it
      // But if no shopId is provided or if it's undefined/null, get all refunds
      if (shopId !== undefined && shopId !== null) {
        console.log(`Shop filter: ${shopId}`);
        conditions.push(eq(refunds.shopId, shopId));
      }

      // For branch filtering, be more flexible:
      // - If branchId is provided and not null, filter by it
      // - If branchId is null/undefined, don't filter by branch (get all branches for the shop)
      if (branchId !== undefined && branchId !== null) {
        console.log(`Branch filter: ${branchId}`);
        conditions.push(eq(refunds.branchId, branchId));
      }

      console.log(`Applying ${conditions.length} filter conditions`);

      const allRefunds = await db
        .select()
        .from(refunds)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(refunds.createdAt));

      console.log(`Retrieved ${allRefunds.length} refunds from database`);

      // Debug: Log the first few refunds with their dates
      if (allRefunds.length > 0) {
        console.log('Sample refunds:');
        allRefunds.slice(0, 3).forEach(refund => {
          console.log(`  Refund ${refund.id}: ${refund.createdAt.toISOString()}`);
        });
      }

      return allRefunds;
    } catch (error) {
      console.error('Error getting all refunds:', error);
      throw error;
    }
  }

  async getTotalRefundAmount(shopId?: number, branchId?: number, startDate?: Date, endDate?: Date): Promise<number> {
    try {
      console.log(`Getting total refund amount with filters - shopId: ${shopId}, branchId: ${branchId}, startDate: ${startDate}, endDate: ${endDate}`);

      const conditions = [];

      if (startDate) {
        conditions.push(gte(refunds.createdAt, startDate));
      }

      if (endDate) {
        conditions.push(lte(refunds.createdAt, endDate));
      }

      if (shopId !== undefined) {
        conditions.push(eq(refunds.shopId, shopId));
      }

      if (branchId !== undefined) {
        conditions.push(eq(refunds.branchId, branchId));
      }

      const [result] = await db
        .select({ total: sql`COALESCE(SUM(${refunds.amount}), 0)` })
        .from(refunds)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const totalRefundAmount = Number(result?.total || 0);
      console.log(`Total refund amount: ${totalRefundAmount}`);
      return totalRefundAmount;
    } catch (error) {
      console.error('Error getting total refund amount:', error);
      throw error;
    }
  }

  // Order Number Settings operations
  async getOrderNumberSettings(shopId: number, branchId?: number): Promise<OrderNumberSettings | null> {
    try {
      console.log(`Getting order number settings for shopId: ${shopId}, branchId: ${branchId}`);

      const conditions = [eq(orderNumberSettings.shopId, shopId)];

      if (branchId !== undefined) {
        conditions.push(eq(orderNumberSettings.branchId, branchId));
      } else {
        conditions.push(isNull(orderNumberSettings.branchId));
      }

      const [settings] = await db
        .select()
        .from(orderNumberSettings)
        .where(and(...conditions))
        .limit(1);

      console.log(`Retrieved order number settings:`, settings);
      return settings || null;
    } catch (error) {
      console.error('Error getting order number settings:', error);
      throw error;
    }
  }

  async createOrderNumberSettings(settingsData: InsertOrderNumberSettings): Promise<OrderNumberSettings> {
    try {
      console.log('Creating order number settings:', settingsData);
      const [settings] = await db
        .insert(orderNumberSettings)
        .values({
          ...settingsData,
          updatedAt: new Date()
        })
        .returning();

      console.log('Order number settings created successfully:', settings);
      return settings;
    } catch (error) {
      console.error('Error creating order number settings:', error);
      throw error;
    }
  }

  async updateOrderNumberSettings(id: number, settingsData: Partial<InsertOrderNumberSettings>): Promise<OrderNumberSettings | undefined> {
    try {
      console.log(`Updating order number settings ${id}:`, settingsData);
      const [settings] = await db
        .update(orderNumberSettings)
        .set({
          ...settingsData,
          updatedAt: new Date()
        })
        .where(eq(orderNumberSettings.id, id))
        .returning();

      console.log('Order number settings updated successfully:', settings);
      return settings;
    } catch (error) {
      console.error('Error updating order number settings:', error);
      throw error;
    }
  }

  // Preview the next order number without incrementing sequence
  async previewNextOrderNumber(shopId: number, branchId?: number): Promise<string> {
    try {
      console.log(`Previewing next order number for shopId: ${shopId}, branchId: ${branchId}`);

      // Get order number settings (optional)
      const settings = await this.getOrderNumberSettings(shopId, branchId);

      // If no settings exist, create default settings and use them
      if (!settings) {
        console.log('No order number settings found, creating default settings');
        const defaultSettings = {
          prefix: "ORD",
          suffix: "",
          numberType: "sequential" as const,
          sequentialStart: 1,
          currentSequence: 1,
          dateFormat: "",
          separator: "/",
          minDigits: 2,
          resetPeriod: "never" as const,
          active: true,
          shopId: shopId,
          branchId: branchId || null
        };

        try {
          const createdSettings = await this.createOrderNumberSettings(defaultSettings);
          console.log('Created default order number settings:', createdSettings);
          // Use the newly created settings for preview
          return await this.previewNextOrderNumber(shopId, branchId);
        } catch (error) {
          console.error('Failed to create default settings, using fallback:', error);
          return `ORD/01`;
        }
      }

      // If settings exist but are not active, use default format
      if (!settings.active) {
        console.log('Order number settings found but not active, using default format');
        return `ORD/01`;
      }

      // Check if sequence needs to be reset (but don't actually reset it)
      const now = new Date();
      let previewSequence = settings.currentSequence;

      // Check if we would need to reset the sequence
      if (settings.resetPeriod !== "never" && settings.lastResetDate) {
        const lastReset = new Date(settings.lastResetDate);
        let shouldReset = false;

        switch (settings.resetPeriod) {
          case "daily":
            shouldReset = now.toDateString() !== lastReset.toDateString();
            break;
          case "monthly":
            shouldReset = now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear();
            break;
          case "yearly":
            shouldReset = now.getFullYear() !== lastReset.getFullYear();
            break;
        }

        if (shouldReset) {
          previewSequence = settings.sequentialStart;
        }
      }

      // Build order number based on settings
      const parts = [];

      // Add prefix
      if (settings.prefix) {
        parts.push(settings.prefix);
      }

      // Add date format if specified
      if (settings.dateFormat) {
        const dateStr = this.formatDate(now, settings.dateFormat);
        parts.push(dateStr);
      }

      // Add number part based on type (preview the NEXT number)
      let numberPart = "";
      switch (settings.numberType) {
        case "sequential":
          // Preview the next sequence number (current + 1)
          const nextSequence = previewSequence + 1;
          numberPart = nextSequence.toString().padStart(settings.minDigits, '0');
          break;
        case "random":
          const randomNum = Math.floor(Math.random() * Math.pow(10, settings.minDigits));
          numberPart = randomNum.toString().padStart(settings.minDigits, '0');
          break;
        case "date_based":
          const timestamp = now.getTime().toString().slice(-settings.minDigits);
          numberPart = timestamp.padStart(settings.minDigits, '0');
          break;
        default:
          const defaultNext = previewSequence + 1;
          numberPart = defaultNext.toString().padStart(settings.minDigits, '0');
      }
      parts.push(numberPart);

      // Add suffix
      if (settings.suffix) {
        parts.push(settings.suffix);
      }

      // Join with separator
      const orderNumber = parts.join(settings.separator);

      console.log(`Previewed next order number: ${orderNumber}`);
      return orderNumber;
    } catch (error) {
      console.error('Error previewing next order number:', error);
      // Always fallback to proper default format with 2 digits
      const sequence = Math.floor(1 + Math.random() * 99);
      return `ORD/${sequence.toString().padStart(2, '0')}`;
    }
  }

  async generateOrderNumber(shopId: number, branchId?: number): Promise<string> {
    try {
      console.log(`Generating order number for shopId: ${shopId}, branchId: ${branchId}`);

      // Get order number settings (optional)
      const settings = await this.getOrderNumberSettings(shopId, branchId);

      // If no settings exist, create default settings and use them
      if (!settings) {
        console.log('No order number settings found, creating default settings');
        const defaultSettings = {
          prefix: "ORD",
          suffix: "",
          numberType: "sequential" as const,
          sequentialStart: 1,
          currentSequence: 1,
          dateFormat: "",
          separator: "/",
          minDigits: 2,
          resetPeriod: "never" as const,
          active: true,
          shopId: shopId,
          branchId: branchId || null
        };

        try {
          const createdSettings = await this.createOrderNumberSettings(defaultSettings);
          console.log('Created default order number settings:', createdSettings);
          // Use the newly created settings
          return await this.generateOrderNumber(shopId, branchId);
        } catch (error) {
          console.error('Failed to create default settings, using fallback:', error);
          return `ORD/01`;
        }
      }

      // If settings exist but are not active, use default format
      if (!settings.active) {
        console.log('Order number settings found but not active, using default format');
        return `ORD/01`;
      }

      // Check if sequence needs to be reset
      await this.checkAndResetSequence(settings);

      let orderNumber = "";
      const now = new Date();

      // Build order number based on settings
      const parts = [];

      // Add prefix
      if (settings.prefix) {
        parts.push(settings.prefix);
      }

      // Add date format if specified
      if (settings.dateFormat) {
        const dateStr = this.formatDate(now, settings.dateFormat);
        parts.push(dateStr);
      }

      // If using sequential numbering, increment the sequence BEFORE using it
      if (settings.numberType === "sequential") {
        await this.incrementSequence(shopId, branchId);
        // Get the updated sequence number
        const updatedSettings = await this.getOrderNumberSettings(shopId, branchId);
        if (updatedSettings) {
          settings.currentSequence = updatedSettings.currentSequence;
        }
      }

      // Add number part based on type
      let numberPart = "";
      switch (settings.numberType) {
        case "sequential":
          numberPart = settings.currentSequence.toString().padStart(settings.minDigits, '0');
          break;
        case "random":
          const randomNum = Math.floor(Math.random() * Math.pow(10, settings.minDigits));
          numberPart = randomNum.toString().padStart(settings.minDigits, '0');
          break;
        case "date_based":
          const timestamp = now.getTime().toString().slice(-settings.minDigits);
          numberPart = timestamp.padStart(settings.minDigits, '0');
          break;
        default:
          numberPart = settings.currentSequence.toString().padStart(settings.minDigits, '0');
      }
      parts.push(numberPart);

      // Add suffix
      if (settings.suffix) {
        parts.push(settings.suffix);
      }

      // Join with separator
      orderNumber = parts.join(settings.separator);

      console.log(`Generated order number: ${orderNumber}`);
      return orderNumber;
    } catch (error) {
      console.error('Error generating order number:', error);
      // Always fallback to proper default format with 2 digits
      const sequence = Math.floor(1 + Math.random() * 99);
      return `ORD/${sequence.toString().padStart(2, '0')}`;
    }
  }

  async incrementSequence(shopId: number, branchId?: number): Promise<void> {
    try {
      console.log(`Incrementing sequence for shopId: ${shopId}, branchId: ${branchId}`);

      const conditions = [eq(orderNumberSettings.shopId, shopId)];

      if (branchId !== undefined) {
        conditions.push(eq(orderNumberSettings.branchId, branchId));
      } else {
        conditions.push(isNull(orderNumberSettings.branchId));
      }

      await db
        .update(orderNumberSettings)
        .set({
          currentSequence: sql`${orderNumberSettings.currentSequence} + 1`,
          updatedAt: new Date()
        })
        .where(and(...conditions));

      console.log('Sequence incremented successfully');
    } catch (error) {
      console.error('Error incrementing sequence:', error);
      throw error;
    }
  }

  private async checkAndResetSequence(settings: OrderNumberSettings): Promise<void> {
    if (settings.resetPeriod === "never" || !settings.lastResetDate) {
      return;
    }

    const now = new Date();
    const lastReset = new Date(settings.lastResetDate);
    let shouldReset = false;

    switch (settings.resetPeriod) {
      case "daily":
        shouldReset = now.toDateString() !== lastReset.toDateString();
        break;
      case "monthly":
        shouldReset = now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear();
        break;
      case "yearly":
        shouldReset = now.getFullYear() !== lastReset.getFullYear();
        break;
    }

    if (shouldReset) {
      console.log(`Resetting sequence for ${settings.resetPeriod} period`);
      await db
        .update(orderNumberSettings)
        .set({
          currentSequence: settings.sequentialStart,
          lastResetDate: now,
          updatedAt: now
        })
        .where(eq(orderNumberSettings.id, settings.id));
    }
  }

  private formatDate(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    switch (format) {
      case "YYYY":
        return year.toString();
      case "YY":
        return year.toString().slice(-2);
      case "MM":
        return month;
      case "DD":
        return day;
      case "YYYYMM":
        return `${year}${month}`;
      case "YYYYMMDD":
        return `${year}${month}${day}`;
      case "DDMM":
        return `${day}${month}`;
      case "DDMMYY":
        return `${day}${month}${year.toString().slice(-2)}`;
      case "DDMMYYYY":
        return `${day}${month}${year}`;
      case "MMYY":
        return `${month}${year.toString().slice(-2)}`;
      case "MMYYYY":
        return `${month}${year}`;
      default:
        return "";
    }
  }

  // Notification operations
  async createNotification(notificationData: InsertNotification): Promise<Notification> {
    const [notification] = await db
      .insert(notifications)
      .values(notificationData)
      .returning();

    return notification;
  }

  async getNotificationById(id: number): Promise<Notification | undefined> {
    const [notification] = await db
      .select()
      .from(notifications)
      .where(eq(notifications.id, id));

    return notification;
  }

  async getUserNotifications(userId: number, shopId?: number, branchId?: number, page: number = 1, pageSize: number = 20): Promise<{ notifications: any[], total: number, totalPages: number }> {
    try {
      let whereConditions = [eq(notificationRecipients.userId, userId)];

      if (shopId) {
        whereConditions.push(eq(notifications.shopId, shopId));
      }

      if (branchId) {
        whereConditions.push(eq(notifications.branchId, branchId));
      }

      // Get total count
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(notificationRecipients)
        .innerJoin(notifications, eq(notificationRecipients.notificationId, notifications.id))
        .where(and(...whereConditions));

      const total = totalResult[0]?.count || 0;
      const totalPages = Math.ceil(total / pageSize);
      const offset = (page - 1) * pageSize;

      // Get notifications with pagination
      const notificationData = await db
        .select({
          id: notifications.id,
          title: notifications.title,
          message: notifications.message,
          type: notifications.type,
          priority: notifications.priority,
          status: notificationRecipients.status,
          data: notifications.data,
          created_at: notifications.createdAt,
          read_at: notificationRecipients.readAt
        })
        .from(notificationRecipients)
        .innerJoin(notifications, eq(notificationRecipients.notificationId, notifications.id))
        .where(and(...whereConditions))
        .orderBy(desc(notifications.createdAt))
        .limit(pageSize)
        .offset(offset);

      return {
        notifications: notificationData,
        total,
        totalPages
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  async getUnreadNotificationCount(userId: number, shopId?: number, branchId?: number): Promise<number> {
    try {
      let whereConditions = [
        eq(notificationRecipients.userId, userId),
        eq(notificationRecipients.status, 'unread')
      ];

      if (shopId) {
        whereConditions.push(eq(notifications.shopId, shopId));
      }

      if (branchId) {
        whereConditions.push(eq(notifications.branchId, branchId));
      }

      const result = await db
        .select({ count: sql<number>`count(*)` })
        .from(notificationRecipients)
        .innerJoin(notifications, eq(notificationRecipients.notificationId, notifications.id))
        .where(and(...whereConditions));

      return result[0]?.count || 0;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      return 0;
    }
  }

  async markNotificationAsRead(notificationId: number, userId: number): Promise<boolean> {
    try {
      const result = await db
        .update(notificationRecipients)
        .set({
          status: 'read',
          readAt: new Date()
        })
        .where(
          and(
            eq(notificationRecipients.notificationId, notificationId),
            eq(notificationRecipients.userId, userId)
          )
        );

      return result.rowCount > 0;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  async deleteNotification(notificationId: number, userId: number): Promise<boolean> {
    try {
      const result = await db
        .delete(notificationRecipients)
        .where(
          and(
            eq(notificationRecipients.notificationId, notificationId),
            eq(notificationRecipients.userId, userId)
          )
        );

      return result.rowCount > 0;
    } catch (error) {
      console.error('Error deleting notification:', error);
      return false;
    }
  }

  async createNotificationForUsers(notificationData: InsertNotification, userIds: number[]): Promise<Notification> {
    try {
      // Create the notification
      const [notification] = await db
        .insert(notifications)
        .values(notificationData)
        .returning();

      // Create notification recipients for each user
      const recipients = userIds.map(userId => ({
        notificationId: notification.id,
        userId,
        status: 'unread' as const
      }));

      await db
        .insert(notificationRecipients)
        .values(recipients);

      return notification;
    } catch (error) {
      console.error('Error creating notification for users:', error);
      throw error;
    }
  }

  async createNotificationForShop(notificationData: InsertNotification, shopId: number): Promise<Notification> {
    try {
      // Get all users in the shop
      const shopUsers = await this.getUsersByShop(shopId);
      const userIds = shopUsers.map(user => user.id);

      if (userIds.length === 0) {
        throw new Error('No users found in shop');
      }

      return await this.createNotificationForUsers({
        ...notificationData,
        shopId,
        recipientType: 'shop',
        recipientId: shopId
      }, userIds);
    } catch (error) {
      console.error('Error creating notification for shop:', error);
      throw error;
    }
  }

  async createNotificationForBranch(notificationData: InsertNotification, branchId: number): Promise<Notification> {
    try {
      // Get all users in the branch
      const branchUsers = await this.getUsersByBranch(branchId);
      const userIds = branchUsers.map(user => user.id);

      if (userIds.length === 0) {
        throw new Error('No users found in branch');
      }

      return await this.createNotificationForUsers({
        ...notificationData,
        branchId,
        recipientType: 'branch',
        recipientId: branchId
      }, userIds);
    } catch (error) {
      console.error('Error creating notification for branch:', error);
      throw error;
    }
  }

  // Notification Settings operations
  async getUserNotificationSettings(userId: number, shopId?: number): Promise<NotificationSetting[]> {
    try {
      let whereConditions = [eq(notificationSettings.userId, userId)];

      if (shopId) {
        whereConditions.push(eq(notificationSettings.shopId, shopId));
      }

      const settings = await db
        .select()
        .from(notificationSettings)
        .where(and(...whereConditions));

      return settings;
    } catch (error) {
      console.error('Error getting user notification settings:', error);
      return [];
    }
  }

  async updateNotificationSetting(userId: number, shopId: number, notificationType: string, enabled: boolean, deliveryMethods?: string[]): Promise<NotificationSetting> {
    try {
      // Try to update existing setting
      const [existingSetting] = await db
        .select()
        .from(notificationSettings)
        .where(
          and(
            eq(notificationSettings.userId, userId),
            eq(notificationSettings.shopId, shopId),
            eq(notificationSettings.notificationType, notificationType)
          )
        );

      if (existingSetting) {
        const [updated] = await db
          .update(notificationSettings)
          .set({
            enabled,
            deliveryMethods: deliveryMethods || existingSetting.deliveryMethods
          })
          .where(eq(notificationSettings.id, existingSetting.id))
          .returning();

        return updated;
      } else {
        // Create new setting
        const [created] = await db
          .insert(notificationSettings)
          .values({
            userId,
            shopId,
            notificationType,
            enabled,
            deliveryMethods: deliveryMethods || ['in_app']
          })
          .returning();

        return created;
      }
    } catch (error) {
      console.error('Error updating notification setting:', error);
      throw error;
    }
  }

  async getDefaultNotificationSettings(userId: number, shopId: number): Promise<NotificationSetting[]> {
    try {
      const defaultTypes = ['order', 'stock', 'marketing', 'system'];
      const settings: NotificationSetting[] = [];

      for (const type of defaultTypes) {
        const setting = await this.updateNotificationSetting(userId, shopId, type, true, ['in_app']);
        settings.push(setting);
      }

      return settings;
    } catch (error) {
      console.error('Error creating default notification settings:', error);
      return [];
    }
  }

  // Support Ticket operations
  async createSupportTicket(ticketData: InsertSupportTicket): Promise<SupportTicket> {
    try {
      console.log('Storage: Creating support ticket with data:', ticketData);

      const [ticket] = await db
        .insert(supportTickets)
        .values({
          ...ticketData,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();

      console.log('Storage: Support ticket created:', ticket);
      return ticket;
    } catch (error) {
      console.error('Storage: Error creating support ticket:', error);
      throw error;
    }
  }

  async getSupportTicketById(id: number): Promise<SupportTicket | undefined> {
    try {
      const [ticket] = await db
        .select()
        .from(supportTickets)
        .where(eq(supportTickets.id, id));

      return ticket;
    } catch (error) {
      console.error('Storage: Error getting support ticket by ID:', error);
      throw error;
    }
  }

  async getSupportTicketsByEmail(email: string): Promise<SupportTicket[]> {
    try {
      console.log(`Storage: Getting support tickets for email: ${email}`);

      const tickets = await db
        .select()
        .from(supportTickets)
        .where(eq(supportTickets.email, email))
        .orderBy(desc(supportTickets.createdAt));

      console.log(`Storage: Found ${tickets.length} support tickets for email: ${email}`);
      return tickets;
    } catch (error) {
      console.error('Storage: Error getting support tickets by email:', error);
      throw error;
    }
  }

  async updateSupportTicket(id: number, ticketData: Partial<InsertSupportTicket>): Promise<SupportTicket | undefined> {
    try {
      console.log(`Storage: Updating support ticket ${id} with data:`, ticketData);

      const [ticket] = await db
        .update(supportTickets)
        .set({
          ...ticketData,
          updatedAt: new Date()
        })
        .where(eq(supportTickets.id, id))
        .returning();

      console.log('Storage: Support ticket updated:', ticket);
      return ticket;
    } catch (error) {
      console.error('Storage: Error updating support ticket:', error);
      throw error;
    }
  }

  async getAllSupportTickets(status?: string, priority?: string, category?: string): Promise<SupportTicket[]> {
    try {
      console.log('Storage: Getting all support tickets with filters:', { status, priority, category });

      let query = db.select().from(supportTickets);

      const conditions = [];
      if (status) {
        conditions.push(eq(supportTickets.status, status));
      }
      if (priority) {
        conditions.push(eq(supportTickets.priority, priority));
      }
      if (category) {
        conditions.push(eq(supportTickets.category, category));
      }

      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      const tickets = await query.orderBy(desc(supportTickets.createdAt));

      console.log(`Storage: Found ${tickets.length} support tickets`);
      return tickets;
    } catch (error) {
      console.error('Storage: Error getting all support tickets:', error);
      throw error;
    }
  }
  // Utilities methods
  async getDatabaseStats(shopId?: number, branchId?: number): Promise<any> {
    try {
      console.log(`[STORAGE] Getting database stats for shop ${shopId}, branch ${branchId}`);

      // Get counts for all major tables
      const [
        usersCount,
        rolesCount,
        shopsCount,
        branchesCount,
        productsCount,
        categoriesCount,
        tablesCount,
        customersCount,
        ordersCount,
        expensesCount,
        purchasesCount,
        stockMovementsCount,
        stockTransfersCount,
        subscriptionsCount,
        notificationsCount
      ] = await Promise.all([
        // Users count
        db.select({ count: sql`count(*)` }).from(users).then(result => Number(result[0]?.count || 0)),

        // Roles count
        db.select({ count: sql`count(*)` }).from(roles).then(result => Number(result[0]?.count || 0)),

        // Shops count
        db.select({ count: sql`count(*)` }).from(shops).then(result => Number(result[0]?.count || 0)),

        // Branches count
        shopId ?
          db.select({ count: sql`count(*)` }).from(branches).where(eq(branches.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(branches).then(result => Number(result[0]?.count || 0)),

        // Products count
        shopId ?
          db.select({ count: sql`count(*)` }).from(products).where(eq(products.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(products).then(result => Number(result[0]?.count || 0)),

        // Categories count
        shopId ?
          db.select({ count: sql`count(*)` }).from(categories).where(eq(categories.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(categories).then(result => Number(result[0]?.count || 0)),

        // Tables count
        shopId ?
          db.select({ count: sql`count(*)` }).from(tables).where(eq(tables.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(tables).then(result => Number(result[0]?.count || 0)),

        // Customers count
        shopId ?
          db.select({ count: sql`count(*)` }).from(customers).where(eq(customers.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(customers).then(result => Number(result[0]?.count || 0)),

        // Orders count
        shopId ?
          db.select({ count: sql`count(*)` }).from(orders).where(eq(orders.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(orders).then(result => Number(result[0]?.count || 0)),

        // Expenses count
        shopId ?
          db.select({ count: sql`count(*)` }).from(expenses).where(eq(expenses.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(expenses).then(result => Number(result[0]?.count || 0)),

        // Purchases count
        shopId ?
          db.select({ count: sql`count(*)` }).from(purchases).where(eq(purchases.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(purchases).then(result => Number(result[0]?.count || 0)),

        // Stock movements count (stockMovements doesn't have shopId, it has branchId)
        shopId ?
          db.select({ count: sql`count(*)` })
            .from(stockMovements)
            .innerJoin(branches, eq(stockMovements.branchId, branches.id))
            .where(eq(branches.shopId, shopId))
            .then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(stockMovements).then(result => Number(result[0]?.count || 0)),

        // Stock transfers count (stockTransfers doesn't have shopId, it has fromBranchId)
        shopId ?
          db.select({ count: sql`count(*)` })
            .from(stockTransfers)
            .innerJoin(branches, eq(stockTransfers.fromBranchId, branches.id))
            .where(eq(branches.shopId, shopId))
            .then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(stockTransfers).then(result => Number(result[0]?.count || 0)),

        // Subscriptions count
        db.select({ count: sql`count(*)` }).from(subscriptions).then(result => Number(result[0]?.count || 0)),

        // Notifications count
        shopId ?
          db.select({ count: sql`count(*)` }).from(notifications).where(eq(notifications.shopId, shopId)).then(result => Number(result[0]?.count || 0)) :
          db.select({ count: sql`count(*)` }).from(notifications).then(result => Number(result[0]?.count || 0))
      ]);

      const stats = {
        tables: {
          users: usersCount,
          roles: rolesCount,
          shops: shopsCount,
          branches: branchesCount,
          products: productsCount,
          categories: categoriesCount,
          tables: tablesCount,
          customers: customersCount,
          orders: ordersCount,
          expenses: expensesCount,
          purchases: purchasesCount,
          stockMovements: stockMovementsCount,
          stockTransfers: stockTransfersCount,
          subscriptions: subscriptionsCount,
          notifications: notificationsCount
        },
        totals: {
          totalRecords: usersCount + rolesCount + shopsCount + branchesCount + productsCount +
                       categoriesCount + tablesCount + customersCount + ordersCount + expensesCount +
                       purchasesCount + stockMovementsCount + stockTransfersCount + subscriptionsCount + notificationsCount,
          businessRecords: productsCount + categoriesCount + customersCount + ordersCount + expensesCount + purchasesCount,
          inventoryRecords: stockMovementsCount + stockTransfersCount
        },
        timestamp: new Date().toISOString()
      };

      console.log(`[STORAGE] Database stats:`, stats);
      return stats;
    } catch (error) {
      console.error('[STORAGE] Error getting database stats:', error);
      throw error;
    }
  }

  async getBusinessAnalytics(shopId?: number, branchId?: number, period: string = 'month'): Promise<any> {
    try {
      console.log(`[STORAGE] Getting business analytics for shop ${shopId}, branch ${branchId}, period ${period}`);

      // Calculate date range based on period
      const now = new Date();
      let startDate = new Date();

      switch (period) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(now.getMonth() - 1);
      }

      // Build conditions
      const orderConditions = [
        gte(orders.createdAt, startDate),
        lte(orders.createdAt, now)
      ];

      if (shopId !== undefined) {
        orderConditions.push(eq(orders.shopId, shopId));
      }

      if (branchId !== undefined) {
        orderConditions.push(eq(orders.branchId, branchId));
      }

      // Get orders data
      const ordersData = await db
        .select()
        .from(orders)
        .where(and(...orderConditions))
        .orderBy(desc(orders.createdAt));

      // Calculate analytics
      const totalOrders = ordersData.length;
      const totalRevenue = ordersData.reduce((sum, order) => sum + (order.total || 0), 0);
      const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // Order status breakdown
      const ordersByStatus = ordersData.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Order type breakdown
      const ordersByType = ordersData.reduce((acc, order) => {
        acc[order.orderType] = (acc[order.orderType] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Revenue by day (for charts)
      const revenueByDay = ordersData.reduce((acc, order) => {
        const date = order.createdAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + (order.total || 0);
        return acc;
      }, {} as Record<string, number>);

      // Get top products
      const topProducts = await this.getTopProducts(period as any, 10, shopId, branchId);

      const analytics = {
        summary: {
          totalOrders,
          totalRevenue,
          avgOrderValue,
          period,
          dateRange: {
            start: startDate.toISOString(),
            end: now.toISOString()
          }
        },
        breakdowns: {
          ordersByStatus,
          ordersByType,
          revenueByDay
        },
        topProducts,
        timestamp: new Date().toISOString()
      };

      console.log(`[STORAGE] Business analytics calculated:`, analytics);
      return analytics;
    } catch (error) {
      console.error('[STORAGE] Error getting business analytics:', error);
      throw error;
    }
  }
}

export const storage = new DatabaseStorage();
