# Barcode API Usage Guide

This document provides examples of how to use the NembooBill Barcode API endpoints.

## Authentication

All API endpoints require authentication. Include the Bearer token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Available Endpoints

### 1. Search Product by Barcode

**Endpoint:** `GET /api/products/barcode/:barcode`

**Description:** Find a product using its barcode

**Example:**
```bash
curl -X GET "http://localhost:3000/api/products/barcode/8901234567890" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
{
  "id": 1,
  "name": "Sample Product",
  "price": 99.99,
  "barcode": "8901234567890",
  "active": true,
  "categoryId": 1,
  "quantity": 50
}
```

### 2. Validate Barcode

**Endpoint:** `POST /api/products/barcode/validate`

**Description:** Check if a barcode is available for use

**Example:**
```bash
curl -X POST "http://localhost:3000/api/products/barcode/validate" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"barcode": "8901234567890"}'
```

**Response (if barcode exists):**
```json
{
  "valid": false,
  "message": "Barcode already exists",
  "existingProduct": {
    "id": 1,
    "name": "Sample Product",
    "price": 99.99
  }
}
```

**Response (if barcode is available):**
```json
{
  "valid": true,
  "message": "Barcode is available"
}
```

### 3. Generate Unique Barcode

**Endpoint:** `POST /api/products/barcode/generate`

**Description:** Generate a unique barcode for a new product

**Example:**
```bash
curl -X POST "http://localhost:3000/api/products/barcode/generate" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
{
  "barcode": "1234567890123",
  "message": "Barcode generated successfully"
}
```

### 4. Bulk Barcode Search

**Endpoint:** `POST /api/products/barcode/bulk-search`

**Description:** Search for multiple products using their barcodes (max 100 barcodes)

**Example:**
```bash
curl -X POST "http://localhost:3000/api/products/barcode/bulk-search" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"barcodes": ["8901234567890", "5901234123457", "4001234567890"]}'
```

**Response:**
```json
{
  "found": [
    {
      "id": 1,
      "name": "Product 1",
      "barcode": "8901234567890",
      "price": 99.99
    }
  ],
  "foundCount": 1,
  "notFound": ["5901234123457", "4001234567890"],
  "notFoundCount": 2,
  "totalRequested": 3
}
```

### 5. Get Products with Barcodes

**Endpoint:** `GET /api/products/with-barcodes`

**Description:** Retrieve all products that have barcodes assigned

**Example:**
```bash
curl -X GET "http://localhost:3000/api/products/with-barcodes" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Product 1",
    "barcode": "8901234567890",
    "price": 99.99,
    "quantity": 50,
    "categoryId": 1
  },
  {
    "id": 2,
    "name": "Product 2",
    "barcode": "5901234123457",
    "price": 149.99,
    "quantity": 30,
    "categoryId": 2
  }
]
```

### 6. Barcode Inventory Report

**Endpoint:** `GET /api/inventory/barcode-report`

**Description:** Get a comprehensive report of barcode usage in inventory

**Example:**
```bash
curl -X GET "http://localhost:3000/api/inventory/barcode-report" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Response:**
```json
{
  "summary": {
    "totalProducts": 100,
    "productsWithBarcodes": 75,
    "productsWithoutBarcodes": 25,
    "barcodePercentage": 75
  },
  "productsWithBarcodes": [
    {
      "id": 1,
      "name": "Product 1",
      "barcode": "8901234567890",
      "price": 99.99,
      "quantity": 50,
      "categoryId": 1
    }
  ],
  "productsWithoutBarcodes": [
    {
      "id": 2,
      "name": "Product 2",
      "price": 149.99,
      "quantity": 30,
      "categoryId": 2
    }
  ]
}
```

### 7. API Documentation

**Endpoint:** `GET /api/barcode/docs`

**Description:** Get complete API documentation for barcode endpoints

**Example:**
```bash
curl -X GET "http://localhost:3000/api/barcode/docs"
```

## Integration Examples

### JavaScript/Frontend Integration

```javascript
// Search for product by barcode
async function searchProductByBarcode(barcode) {
  try {
    const response = await fetch(`/api/products/barcode/${barcode}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const product = await response.json();
      console.log('Product found:', product);
      return product;
    } else if (response.status === 404) {
      console.log('Product not found');
      return null;
    }
  } catch (error) {
    console.error('Error searching product:', error);
  }
}

// Generate new barcode
async function generateBarcode() {
  try {
    const response = await fetch('/api/products/barcode/generate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('Generated barcode:', result.barcode);
      return result.barcode;
    }
  } catch (error) {
    console.error('Error generating barcode:', error);
  }
}

// Validate barcode before use
async function validateBarcode(barcode) {
  try {
    const response = await fetch('/api/products/barcode/validate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ barcode })
    });
    
    if (response.ok) {
      const result = await response.json();
      return result.valid;
    }
  } catch (error) {
    console.error('Error validating barcode:', error);
  }
}
```

## Error Handling

All endpoints return appropriate HTTP status codes:

- **200**: Success
- **400**: Bad Request (invalid parameters)
- **401**: Unauthorized (missing or invalid token)
- **404**: Not Found (product not found)
- **500**: Internal Server Error

## Notes

1. All endpoints automatically filter results by the user's current shop and branch
2. Only active products are returned in search results
3. Barcodes are case-sensitive and must be exact matches
4. Generated barcodes follow EAN-13 format (13 digits)
5. Bulk search is limited to 100 barcodes per request for performance
6. The system ensures barcode uniqueness within each shop/branch scope
