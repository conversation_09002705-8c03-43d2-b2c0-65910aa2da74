// <PERSON>ript to run the support tickets table migration
import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const { Pool } = pg;

// Create a new pool using individual connection parameters
const pool = new Pool({
  host: '**************',
  port: 5432,
  database: 'nemboobill',
  user: 'postgres',
  password: 'Cloud@2025',
  ssl: false
});

async function runMigration() {
  const client = await pool.connect();
  try {
    console.log('🚀 Running support tickets table migration...\n');

    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'migrations', 'create_support_tickets_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    await client.query(migrationSQL);
    
    console.log('✅ Migration completed successfully!');
    console.log('📋 Created support_tickets table with:');
    console.log('   - Basic ticket information (name, email, subject, message)');
    console.log('   - Priority levels (low, medium, high)');
    console.log('   - Categories (technical, billing, feature, general)');
    console.log('   - Status tracking (open, in_progress, resolved, closed)');
    console.log('   - Optional user, shop, and branch associations');
    console.log('   - Support agent assignment capability');
    console.log('   - Resolution tracking with timestamps');
    console.log('   - Proper indexes for performance');
    console.log('   - Comprehensive documentation');
    
  } catch (error) {
    console.error('❌ Error running migration:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

runMigration();
