# Database Setup Guide

## Current Issue
The application is trying to connect to a remote PostgreSQL database at `**************:5432` but getting connection timeouts. This could be due to:

- Network connectivity issues
- Firewall blocking the connection
- Database server being down
- Incorrect credentials or configuration

## Solution Options

### Option 1: Set Up Local PostgreSQL Database (Recommended for Development)

#### Step 1: Install PostgreSQL
**Windows:**
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user
4. Default port is usually 5432

**macOS:**
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### Step 2: Create Database and User
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE nemboobill;

-- Create user (optional, you can use postgres user)
CREATE USER nemboobill_user WITH PASSWORD 'your_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE nemboobill TO nemboobill_user;

-- Exit
\q
```

#### Step 3: Update .env File
The .env file has been updated to use local database:
```
DATABASE_URL=postgres://postgres:password@localhost:5432/nemboobill
```

Replace `password` with your actual PostgreSQL password.

#### Step 4: Run Database Migrations
```bash
npm run db:migrate
```

### Option 2: Fix Remote Database Connection

If you need to use the remote database at `**************`:

1. **Check Network Connectivity:**
   ```bash
   ping **************
   telnet ************** 5432
   ```

2. **Verify Database Server Status:**
   - Contact your database administrator
   - Check if the server is running
   - Verify firewall rules allow connections from your IP

3. **Test Connection:**
   ```bash
   psql -h ************** -p 5432 -U postgres -d nemboobill
   ```

4. **Check Credentials:**
   - Verify username: `postgres`
   - Verify password: `Cloud@2025`
   - Verify database name: `nemboobill`

### Option 3: Use Docker PostgreSQL (Alternative)

If you prefer using Docker:

```bash
# Run PostgreSQL in Docker
docker run --name nemboobill-postgres \
  -e POSTGRES_DB=nemboobill \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15

# Update .env to use Docker database
DATABASE_URL=postgres://postgres:password@localhost:5432/nemboobill
```

## Next Steps

1. Choose one of the options above
2. Update the DATABASE_URL in .env file accordingly
3. Restart the development server: `npm run dev`
4. The application should now connect successfully

## Troubleshooting

If you continue to have issues:

1. Check the server logs for detailed error messages
2. Verify PostgreSQL is running: `sudo systemctl status postgresql` (Linux) or check Services (Windows)
3. Test connection manually using psql or a database client
4. Check firewall settings if using remote database
5. Ensure the database exists and user has proper permissions

## Database Schema

The application will automatically create the necessary tables when it connects successfully. The schema includes:

- Users and authentication
- Shops and branches
- Products and categories
- Orders and billing
- Inventory management
- Support tickets
- Subscriptions and payments
