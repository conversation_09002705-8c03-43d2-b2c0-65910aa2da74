﻿// Script to export data from remote database
import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const { Pool } = pg;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Remote database connection (source)
const remotePool = new Pool({
  host: '**************',
  port: 5432,
  database: 'nemboobill',
  user: 'postgres',
  password: 'Cloud@2025',
  ssl: false
});

// Tables to export (in order to handle foreign key dependencies)
const tables = [
  'roles',
  'users',
  'shops',
  'branches',
  'user_shops',
  'user_branches',
  'categories',
  'products',
  'tables',
  'customers',
  'online_platforms',
  'payment_methods',
  'orders',
  'order_items',
  'expenses',
  'purchases',
  'tax_settings',
  'discount_settings',
  'shop_settings',
  'printer_settings',
  'user_preferences',
  'rounding_settings',
  'stock_movements',
  'stock_transfers',
  'stock_transfer_items'
];

async function exportData() {
  const client = await remotePool.connect();
  
  try {
    console.log('Starting data export from remote database...');
    
    const exportData = {};
    
    for (const table of tables) {
      try {
        console.log(Exporting table: );
        
        // Check if table exists
        const tableExists = await client.query(
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 
          );
        , [table]);
        
        if (!tableExists.rows[0].exists) {
          console.log(Table  does not exist, skipping...);
          continue;
        }
        
        // Export table data
        const result = await client.query(SELECT * FROM  ORDER BY id);
        exportData[table] = result.rows;
        console.log(Exported  rows from );
        
      } catch (error) {
        console.error(Error exporting table :, error.message);
        // Continue with other tables
      }
    }
    
    // Save to JSON file
    const exportPath = path.join(__dirname, '..', 'data-export.json');
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
    
    console.log(Data export completed successfully!);
    console.log(Export saved to: );
    
    // Print summary
    console.log('\nExport Summary:');
    Object.keys(exportData).forEach(table => {
      console.log(${table}:  rows);
    });
    
  } catch (error) {
    console.error('Error during export:', error);
  } finally {
    client.release();
    await remotePool.end();
  }
}

exportData();
