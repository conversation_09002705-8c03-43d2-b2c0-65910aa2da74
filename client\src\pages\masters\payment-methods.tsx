import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest, queryClient, invalidateAndRefetch, logQueryCache } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/auth-context";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Search, Plus, Edit, AlertCircle, Loader2, Trash2 } from "lucide-react";

// Form schema
const paymentMethodSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  active: z.boolean().default(true),
});

type PaymentMethodFormValues = z.infer<typeof paymentMethodSchema>;

// Define PaymentMethod interface
interface PaymentMethod {
  id: number;
  name: string;
  description?: string;
  active: boolean;
  shopId: number;
}

export default function PaymentMethods() {
  const { token } = useAuth();
  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [paymentMethodToDelete, setPaymentMethodToDelete] = useState<PaymentMethod | null>(null);
  const [error, setError] = useState<string | null>(null);

  // State to force component re-render
  const [refreshKey, setRefreshKey] = useState(0);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Form
  const form = useForm<PaymentMethodFormValues>({
    resolver: zodResolver(paymentMethodSchema),
    defaultValues: {
      name: "",
      description: "",
      active: true,
    },
  });

  const editForm = useForm<PaymentMethodFormValues>({
    resolver: zodResolver(paymentMethodSchema),
    defaultValues: {
      name: "",
      description: "",
      active: true,
    },
  });

  // Fetch payment methods
  const { data: paymentMethods, isLoading: isLoadingPaymentMethods, refetch: refetchPaymentMethods } = useQuery<PaymentMethod[]>({
    queryKey: ['/api/payment-methods', refreshKey], // Add refreshKey to force refetch when it changes
    enabled: !!token,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Force a refetch when component mounts
  useEffect(() => {
    if (token) {
      console.log("Payment methods component mounted - forcing data refresh");
      // Force a refetch to ensure we have the latest data
      queryClient.removeQueries({ queryKey: ['/api/payment-methods'] });
      refetchPaymentMethods();

      // Log the query cache state for debugging
      logQueryCache();
    }
  }, [token, refetchPaymentMethods]);

  // Function to force a refresh of the payment methods data
  const forceRefresh = () => {
    console.log("Forcing refresh of payment methods data");
    setRefreshKey(prevKey => prevKey + 1);
    queryClient.removeQueries({ queryKey: ['/api/payment-methods'] });
    setTimeout(() => {
      refetchPaymentMethods();
    }, 100); // Small delay to ensure query is removed before refetching
  };

  // Log when payment methods data changes
  useEffect(() => {
    console.log("Payment methods data changed:", paymentMethods);
  }, [paymentMethods]);

  // Filter payment methods
  const filteredPaymentMethods = paymentMethods?.filter((method: PaymentMethod) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase();
    return (
      method.name.toLowerCase().includes(search) ||
      (method.description && method.description.toLowerCase().includes(search))
    );
  });

  // Pagination utility function
  interface PaginatedData<T> {
    data: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }

  const paginateData = (data: PaymentMethod[] | undefined, page: number, size: number): PaginatedData<PaymentMethod> => {
    if (!data || !Array.isArray(data)) return { data: [], total: 0, page, pageSize: size, totalPages: 0 };

    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const paginatedData = data.slice(startIndex, endIndex);
    const total = data.length;
    const totalPages = Math.ceil(total / size);

    return {
      data: paginatedData,
      total,
      page,
      pageSize: size,
      totalPages
    };
  };

  // Paginate filtered payment methods
  const paginatedData = paginateData(filteredPaymentMethods, currentPage, pageSize);
  const displayedPaymentMethods = paginatedData.data;

  // Create payment method mutation
  const createPaymentMethodMutation = useMutation({
    mutationFn: async (data: PaymentMethodFormValues) => {
      console.log("Submitting payment method data:", data);
      try {
        const response = await apiRequest("POST", "/api/payment-methods", data);
        const responseData = await response.json();
        console.log("Payment method creation response:", responseData);
        return responseData;
      } catch (error) {
        console.error("Error in payment method creation API call:", error);
        throw error;
      }
    },
    onSuccess: async (responseData) => {
      console.log("Payment method created successfully:", responseData);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/payment-methods');

      // Force a refresh of the payment methods data
      forceRefresh();

      setIsAddDialogOpen(false);
      form.reset();

      toast({
        title: "Success",
        description: "Payment method created successfully",
      });
    },
    onError: (err: any) => {
      console.error("Error creating payment method:", err);
      const errorMessage = err.message || "Failed to create payment method";
      setError(errorMessage);

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    },
  });

  // Update payment method mutation
  const updatePaymentMethodMutation = useMutation({
    mutationFn: async (data: PaymentMethodFormValues & { id: number }) => {
      const { id, ...updateData } = data;
      console.log(`Updating payment method ${id} with data:`, updateData);
      const response = await apiRequest("PATCH", `/api/payment-methods/${id}`, updateData);
      console.log("Update payment method response:", response);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Update payment method error:", errorText);
        throw new Error(errorText);
      }
      return response;
    },
    onSuccess: async (response) => {
      console.log("Payment method updated successfully");

      // Get the updated payment method data
      const updatedPaymentMethod = await response.json();
      console.log("Updated payment method:", updatedPaymentMethod);

      // Immediately invalidate and refetch data
      await invalidateAndRefetch('/api/payment-methods');

      // Force a refresh of the payment methods data
      forceRefresh();

      setIsEditDialogOpen(false);
      setSelectedPaymentMethod(null);
      editForm.reset();

      toast({
        title: "Success",
        description: "Payment method updated successfully",
      });
    },
    onError: (err: any) => {
      console.error("Error updating payment method:", err);
      setError("Failed to update payment method");

      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update payment method",
      });
    },
  });

  // Delete payment method mutation
  const deletePaymentMethodMutation = useMutation({
    mutationFn: async (paymentMethodId: number) => {
      const response = await apiRequest("DELETE", `/api/payment-methods/${paymentMethodId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete payment method");
      }
      return response.json();
    },
    onSuccess: async () => {
      // Invalidate and refetch payment methods
      await invalidateAndRefetch('/api/payment-methods');

      setIsDeleteDialogOpen(false);
      setPaymentMethodToDelete(null);

      toast({
        title: "Success",
        description: "Payment method deleted successfully",
      });

      // Force a refresh of the payment methods data
      forceRefresh();
    },
    onError: (err: any) => {
      console.error("Error deleting payment method:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to delete payment method";

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    },
  });

  // Set form values for edit
  const setFormForEdit = (paymentMethod: PaymentMethod) => {
    editForm.reset({
      name: paymentMethod.name,
      description: paymentMethod.description || "",
      active: paymentMethod.active,
    });
  };

  // Form submission handlers
  const handleCreatePaymentMethod = (data: PaymentMethodFormValues) => {
    console.log("Creating payment method with data:", data);
    createPaymentMethodMutation.mutate(data);
  };

  const handleUpdatePaymentMethod = (data: PaymentMethodFormValues) => {
    if (!selectedPaymentMethod) return;

    console.log("Updating payment method with data:", data);
    updatePaymentMethodMutation.mutate({
      id: selectedPaymentMethod.id,
      ...data,
    });
  };

  const handleDeletePaymentMethod = async () => {
    if (!paymentMethodToDelete) return;

    try {
      await deletePaymentMethodMutation.mutateAsync(paymentMethodToDelete.id);
    } catch (error) {
      // Error handling is done in mutation's onError
      console.error("Failed to delete payment method:", error);
    }
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Loading state
  const isLoading =
    isLoadingPaymentMethods ||
    createPaymentMethodMutation.isPending ||
    updatePaymentMethodMutation.isPending ||
    deletePaymentMethodMutation.isPending;



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Payment Methods</h1>
        <Sheet open={isAddDialogOpen} onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            // If opening add modal, close edit modal if it's open
            if (open && isEditDialogOpen) {
              setIsEditDialogOpen(false);
              setSelectedPaymentMethod(null);
            }
            // If closing, reset the form
            if (!open) {
              form.reset();
              setError(null);
            }
          }}>
          <SheetTrigger asChild>
            <Button onClick={() => {
              // Close edit modal if it's open when clicking Add Payment Method
              if (isEditDialogOpen) {
                setIsEditDialogOpen(false);
                setSelectedPaymentMethod(null);
              }
            }}>
              <Plus className="h-4 w-4 mr-2" />
              Add Payment Method
            </Button>
          </SheetTrigger>
          <SheetContent side="right">
            <SheetHeader>
              <SheetTitle>Add New Payment Method</SheetTitle>
              <SheetDescription>
                Add a new payment method to your system
              </SheetDescription>
            </SheetHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleCreatePaymentMethod)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter payment method name" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter description (optional)"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          Enable or disable this payment method
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <SheetFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </Button>
                </SheetFooter>
              </form>
            </Form>
          </SheetContent>
        </Sheet>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Payment Method List</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {/* Search */}
          <div className="flex mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search payment methods..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Payment Methods Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead className="hidden md:table-cell">Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingPaymentMethods ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-40" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : displayedPaymentMethods && displayedPaymentMethods.length > 0 ? (
                  displayedPaymentMethods.map((method: PaymentMethod) => (
                    <TableRow key={method.id}>
                      <TableCell className="font-medium">{method.name}</TableCell>
                      <TableCell className="hidden md:table-cell">
                        {method.description || <span className="text-gray-400">-</span>}
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          method.active
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          {method.active ? "Active" : "Inactive"}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              setSelectedPaymentMethod(method);
                              setFormForEdit(method);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            disabled={deletePaymentMethodMutation.isPending}
                            onClick={() => {
                              setPaymentMethodToDelete(method);
                              setIsDeleteDialogOpen(true);
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            {deletePaymentMethodMutation.isPending && paymentMethodToDelete?.id === method.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      No payment methods found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {paginatedData.totalPages > 1 && (
            <div className="flex items-center justify-between px-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, paginatedData.total)} of {paginatedData.total} payment methods
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, paginatedData.totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum;
                    if (paginatedData.totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      // If near start, show first 5 pages
                      pageNum = i + 1;
                    } else if (currentPage >= paginatedData.totalPages - 2) {
                      // If near end, show last 5 pages
                      pageNum = paginatedData.totalPages - 4 + i;
                    } else {
                      // Otherwise show 2 before and 2 after current page
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          isActive={currentPage === pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className="cursor-pointer"
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(paginatedData.totalPages, currentPage + 1))}
                      className={currentPage === paginatedData.totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        // If closing edit modal, reset selected payment method
        if (!open) {
          setSelectedPaymentMethod(null);
          editForm.reset();
          setError(null);
        }
        // If opening edit modal, close add modal if it's open
        if (open && isAddDialogOpen) {
          setIsAddDialogOpen(false);
          form.reset();
        }
      }}>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Edit Payment Method</SheetTitle>
            <SheetDescription>
              Update payment method details
            </SheetDescription>
          </SheetHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdatePaymentMethod)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Enable or disable this payment method
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <SheetFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Payment Method</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the payment method "{paymentMethodToDelete?.name}"?
              This action cannot be undone and may affect orders using this payment method.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deletePaymentMethodMutation.isPending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePaymentMethod}
              disabled={deletePaymentMethodMutation.isPending}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {deletePaymentMethodMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
