// Test a simple endpoint to check if server is responding
const testSimpleEndpoint = async () => {
  try {
    console.log('Testing basic server connectivity...');
    
    const response = await fetch('http://localhost:5000/api/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.text();
      console.log('Response data:', data);
    } else {
      const error = await response.text();
      console.log('Error response:', error);
    }
    
  } catch (error) {
    console.log('Network error:', error.message);
    console.log('Error details:', error);
  }
};

testSimpleEndpoint();
