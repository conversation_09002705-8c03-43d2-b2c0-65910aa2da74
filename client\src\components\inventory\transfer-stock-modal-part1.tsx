﻿import { useState, useMemo, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

import {
  Dialog,
  DialogContentRightSlide,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog-right-slide";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowRightLeft, Package, Search } from "lucide-react";

interface TransferStockModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function TransferStockModal({ open, onOpenChange, onSuccess }: TransferStockModalProps) {
  const { token } = useAuth();
  const { currentBranch, currentShop, userBranches } = useApp();
  const { toast } = useToast();

  const [selectedProduct, setSelectedProduct] = useState("");
  const [fromBranch, setFromBranch] = useState(currentBranch?.id?.toString() || "");
  const [toBranch, setToBranch] = useState("");
  const [quantity, setQuantity] = useState("");
  const [reason, setReason] = useState("");
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [productSearch, setProductSearch] = useState("");

  // Fetch products with optimized query
  const { data: products, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products', currentBranch?.id],
    enabled: !!token && open,
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/products");
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Fetch branches - use userBranches from context if available, otherwise fetch from API
  const { data: branches, isLoading: isLoadingBranches } = useQuery({
    queryKey: ['/api/branches', currentShop?.id],
    enabled: !!token && open && !!currentShop,
    queryFn: async () => {
      // If we already have branches in context, use them
      if (userBranches && userBranches.length > 0) {
        return userBranches;
      }

      const response = await apiRequest("GET", "/api/branches");
      if (!response.ok) {
        throw new Error('Failed to fetch branches');
      }
      return response.json();
    },
    // Use userBranches as initial data if available
    initialData: userBranches && userBranches.length > 0 ? userBranches : undefined,
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 15 * 60 * 1000, // 15 minutes
  });

  // Optimized product filtering with useMemo
  const filteredProducts = useMemo(() => {
    if (!products) return [];
    
    if (!productSearch.trim()) {
      return products.slice(0, 50); // Limit initial display to 50 items
    }
    
    const searchTerm = productSearch.toLowerCase();
    return products.filter((product: any) => 
      product.name.toLowerCase().includes(searchTerm) ||
      product.sku?.toLowerCase().includes(searchTerm)
    ).slice(0, 50); // Limit search results to 50 items
  }, [products, productSearch]);

  // Optimized product selection handler
  const handleProductSelect = useCallback((value: string) => {
    setSelectedProduct(value);
    setProductSearch(""); // Clear search when product is selected
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedProduct || !fromBranch || !toBranch || !quantity || !reason) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please fill in all required fields",
      });
      return;
    }

    if (fromBranch === toBranch) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Source and destination branches must be different",
      });
      return;
    }

    const transferQuantity = parseInt(quantity);
    if (isNaN(transferQuantity) || transferQuantity <= 0) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Please enter a valid quantity",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await apiRequest("POST", "/api/stock-transfers", {
        productId: parseInt(selectedProduct),
        fromBranchId: parseInt(fromBranch),
        toBranchId: parseInt(toBranch),
        quantity: transferQuantity,
        reason,
        notes
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create stock transfer');
      }

      toast({
        title: "Transfer Created",
        description: "Stock transfer has been created successfully",
      });

      // Reset form
      setSelectedProduct("");
      setFromBranch(currentBranch?.id?.toString() || "");
      setToBranch("");
      setQuantity("");
      setReason("");
      setNotes("");
      setProductSearch("");
      
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating stock transfer:', error);
      toast({
        variant: "destructive",
        title: "Transfer Failed",
        description: error instanceof Error ? error.message : "Failed to create stock transfer",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProductData = products?.find((p: any) => p.id === parseInt(selectedProduct));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContentRightSlide className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowRightLeft className="h-5 w-5" />
            Transfer Stock
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="product">Product *</Label>
            {isLoadingProducts ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <div className="space-y-2">
                {/* Search input for products */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search products..."
                    value={productSearch}
                    onChange={(e) => setProductSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedProduct} onValueChange={handleProductSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a product" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {filteredProducts.length === 0 ? (
                      <div className="p-2 text-sm text-gray-500 text-center">
                        {productSearch ? "No products found" : "No products available"}
                      </div>
                    ) : (
                      filteredProducts.map((product: any) => (
                        <SelectItem key={product.id} value={product.id.toString()}>
                          <div className="flex items-center gap-2 w-full">
                            <Package className="h-4 w-4 flex-shrink-0" />
                            <span className="truncate">{product.name}</span>
                            <span className="text-sm text-gray-500 flex-shrink-0">
                              (Stock: {product.quantity || 0})
                            </span>
                          </div>
                        </SelectItem>
                      ))
                    )}
                    {products && products.length > 50 && !productSearch && (
                      <div className="p-2 text-xs text-gray-400 text-center border-t">
                        Showing first 50 products. Use search to find more.
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fromBranch">From Branch *</Label>
              {isLoadingBranches ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select value={fromBranch} onValueChange={setFromBranch}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches?.map((branch: any) => (
                      <SelectItem key={branch.id} value={branch.id.toString()}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="toBranch">To Branch *</Label>
              {isLoadingBranches ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select value={toBranch} onValueChange={setToBranch}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select destination branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches?.filter((branch: any) => branch.id.toString() !== fromBranch)
                      .map((branch: any) => (
                        <SelectItem key={branch.id} value={branch.id.toString()}>
                          {branch.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity *</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              max={selectedProductData?.quantity || undefined}
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              placeholder="Enter quantity to transfer"
            />
            {selectedProductData && (
              <p className="text-sm text-gray-500">
                Available stock: {selectedProductData.quantity || 0}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason *</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select transfer reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="restock">Restock</SelectItem>
                <SelectItem value="rebalance">Inventory Rebalance</SelectItem>
                <SelectItem value="demand">High Demand</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes (optional)"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating Transfer..." : "Create Transfer"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContentRightSlide>
    </Dialog>
  );
}
