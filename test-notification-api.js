import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function testNotificationAPI() {
  const client = await pool.connect();
  try {
    console.log('🧪 Testing notification API logic...\n');

    // Test the exact same logic as the API
    const userId = 2; // Your user ID (Arun S)
    const shopId = 12; // likitha shop
    
    console.log(`Testing for User ID: ${userId}, Shop ID: ${shopId}`);

    // Test 1: Get notifications for specific shop (same as API)
    console.log('\n📋 Test 1: Get notifications for likitha shop (ID: 12)');
    
    const notificationsResult = await client.query(`
      SELECT 
        n.id,
        n.title,
        n.message,
        n.type,
        n.priority,
        nr.status,
        n.created_at,
        nr.read_at
      FROM notification_recipients nr
      INNER JOIN notifications n ON nr.notification_id = n.id
      WHERE nr.user_id = $1 AND n.shop_id = $2
      ORDER BY n.created_at DESC
      LIMIT 20
    `, [userId, shopId]);

    console.log(`   Found ${notificationsResult.rows.length} notifications:`);
    notificationsResult.rows.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.title} (${notif.status})`);
      console.log(`      Type: ${notif.type}, Priority: ${notif.priority}`);
      console.log(`      Created: ${notif.created_at}`);
    });

    // Test 2: Get unread count for specific shop
    console.log('\n🔢 Test 2: Get unread count for likitha shop');
    
    const unreadCountResult = await client.query(`
      SELECT COUNT(*) as count
      FROM notification_recipients nr
      INNER JOIN notifications n ON nr.notification_id = n.id
      WHERE nr.user_id = $1 AND n.shop_id = $2 AND nr.status = 'unread'
    `, [userId, shopId]);

    const unreadCount = unreadCountResult.rows[0].count;
    console.log(`   Unread count: ${unreadCount}`);

    // Test 3: Get notifications WITHOUT shop filter (all shops)
    console.log('\n📋 Test 3: Get ALL notifications (no shop filter)');
    
    const allNotificationsResult = await client.query(`
      SELECT 
        n.id,
        n.title,
        n.shop_id,
        s.name as shop_name,
        nr.status
      FROM notification_recipients nr
      INNER JOIN notifications n ON nr.notification_id = n.id
      INNER JOIN shops s ON n.shop_id = s.id
      WHERE nr.user_id = $1
      ORDER BY n.created_at DESC
      LIMIT 10
    `, [userId]);

    console.log(`   Found ${allNotificationsResult.rows.length} notifications across all shops:`);
    allNotificationsResult.rows.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.title} (Shop: ${notif.shop_name}, Status: ${notif.status})`);
    });

    // Test 4: Check if shop ID 12 exists and has notifications
    console.log('\n🏪 Test 4: Verify shop ID 12 exists and has notifications');
    
    const shopVerification = await client.query(`
      SELECT 
        s.id,
        s.name,
        COUNT(n.id) as notification_count
      FROM shops s
      LEFT JOIN notifications n ON s.id = n.shop_id
      WHERE s.id = $1
      GROUP BY s.id, s.name
    `, [shopId]);

    if (shopVerification.rows.length > 0) {
      const shop = shopVerification.rows[0];
      console.log(`   ✅ Shop exists: ${shop.name} (ID: ${shop.id})`);
      console.log(`   📧 Notifications for this shop: ${shop.notification_count}`);
    } else {
      console.log(`   ❌ Shop ID ${shopId} not found!`);
    }

    // Test 5: Check what the frontend should receive
    console.log('\n🌐 Test 5: What frontend should receive for likitha shop');
    
    if (notificationsResult.rows.length > 0) {
      console.log('   ✅ Frontend should show:');
      console.log(`      - Notification bell with red badge showing: ${unreadCount}`);
      console.log(`      - ${notificationsResult.rows.length} notifications in the list`);
      console.log('      - Notifications specific to "likitha" shop');
      
      const unreadNotifications = notificationsResult.rows.filter(n => n.status === 'unread');
      console.log(`      - ${unreadNotifications.length} unread notifications`);
      console.log(`      - ${notificationsResult.rows.length - unreadNotifications.length} read notifications`);
    } else {
      console.log('   ❌ Frontend will show: No notifications (empty list)');
    }

    console.log('\n🎯 Conclusion:');
    if (notificationsResult.rows.length > 0) {
      console.log('   ✅ Database has notifications for your shop');
      console.log('   ✅ API logic should work correctly');
      console.log('   🔍 Issue is likely in:');
      console.log('      - Frontend not sending correct shop ID');
      console.log('      - Frontend not receiving/displaying the data');
      console.log('      - Cache issues in the browser');
    } else {
      console.log('   ❌ No notifications found for this user/shop combination');
      console.log('   🔍 Issue is in the database data');
    }

  } catch (error) {
    console.error('❌ Error testing notification API:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

testNotificationAPI();
