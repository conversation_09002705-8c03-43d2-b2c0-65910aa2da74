import React, { useState, useEffect, useCallback } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest, invalidateAndRefetch, createOrderTwoStep, createSplitBillOrders, transferOrderToTable, mergeTablesOrders, queryClient, cartOperations } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useFullscreen } from "@/hooks/use-fullscreen";
import { useNextOrderNumber } from "@/hooks/useNextOrderNumber";
import { POSEvents, subscribeToPOSEvent, unsubscribeFromPOSEvent } from "@/lib/socket";
import { NotificationHelpers } from "@/lib/notification-helpers";
import { TableGrid } from "@/components/pos/table-grid";
import { TableSelectionView } from "@/components/pos/table-selection-view";
import { BranchSelector } from "@/components/branch-selector";
import { OrderTypeSelector, OrderType } from "@/components/pos/order-type-selector";
import { CartSidebar } from "@/components/pos/cart-sidebar";
import { CategorySelector } from "@/components/pos/category-selector";
import { PaymentModal } from "@/components/pos/payment-modal";
import { DraftOrdersModal } from "@/components/pos/draft-orders-modal";
import { ReceiptModal } from "@/components/pos/receipt-modal";
import { TableTransferModal } from "@/components/pos/table-transfer-modal";
import { TableMergeModal } from "@/components/pos/table-merge-modal";
import { CustomerModal } from "@/components/pos/customer-modal";
import { SelectedProductsWidget } from "@/components/selected-products-widget";
import { useSelectedProducts } from "@/hooks/use-selected-products";
import { QuickSearch } from "@/components/utilities/quick-search";
import { BarcodeScannerPOS } from "@/components/pos/barcode-scanner-pos";
import { z } from "zod";
import "./pos.css";

import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Search,
  Plus,
  Minus,
  ShoppingBag,
  Home,
  Package,
  Printer,
  Save,
  X,
  Receipt,
  Loader2,
  ChevronDown,
  ChevronUp,
  User,
  Phone,
  MapPin,
  Users,
  ArrowLeft,
  Maximize,
  Minimize,
  FileEdit,
  Clock,
  XCircle,
  ArrowRight,
  Merge,
  Edit
} from "lucide-react";

// Interface for order items
interface OrderItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

// Interface for product category
interface Category {
  id: number;
  name: string;
}

// Interface for product
interface Product {
  id: number;
  name: string;
  price: number;
  categoryId: number;
  active: boolean;
}

// Interface for table
interface Table {
  id: number;
  name: string;
  capacity: number;
  status: string;
  shopId: number;
}

// Interface for customer
interface Customer {
  id: number;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  shopId: number;
}

// Interface for payment method
interface PaymentMethod {
  id: number;
  name: string;
  description?: string;
  active: boolean;
  shopId: number;
}

export default function POSPage() {
  const { token, user } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const { selectedProducts, clearAllProducts } = useSelectedProducts();

  // Fetch next order number for display
  const { data: nextOrderData, refetch: refetchNextOrderNumber } = useNextOrderNumber();

  // State for order
  const [orderType, setOrderType] = useState<OrderType>("dine_in");
  const [tableId, setTableId] = useState<number | null>(null);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [numberOfPersons, setNumberOfPersons] = useState<number>(2);
  const [customerId, setCustomerId] = useState<number | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  // Remove activeTab state as we'll use orderType directly
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [subtotal, setSubtotal] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [discountType, setDiscountType] = useState<"percentage" | "fixed">("percentage");
  const [discountValue, setDiscountValue] = useState("0.00");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("cash");
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCustomerDetailsOpen, setIsCustomerDetailsOpen] = useState(true);
  const [isPaymentDetailsOpen, setIsPaymentDetailsOpen] = useState(true);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [paymentOptions, setPaymentOptions] = useState<any>(null); // Store options for after payment
  const [isDraftOrdersModalOpen, setIsDraftOrdersModalOpen] = useState(false);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [completedOrder, setCompletedOrder] = useState<any>(null);
  const [isTableTransferModalOpen, setIsTableTransferModalOpen] = useState(false);
  const [currentOrderForTransfer, setCurrentOrderForTransfer] = useState<any>(null);
  const [isTableMergeModalOpen, setIsTableMergeModalOpen] = useState(false);
  const [currentTableForMerge, setCurrentTableForMerge] = useState<any>(null);
  const [currentOrderForMerge, setCurrentOrderForMerge] = useState<any>(null);

  // State for customer modal
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);
  const [customerToEdit, setCustomerToEdit] = useState<any>(null);

  // Fetch products from API - request all products for POS
  const { data: products, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products'],
    enabled: !!token && !!currentShop,
    queryFn: async () => {
      // Request all products by setting a large page size
      const response = await fetch('/api/products?pageSize=1000', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      return response.json();
    }
  });

  // Fetch categories from API
  const { data: categories, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['/api/categories'],
    enabled: !!token && !!currentShop,
  });

  // Fetch tables from API
  const { data: tables, isLoading: isLoadingTables, refetch: refetchTables } = useQuery({
    queryKey: ['/api/tables'],
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchInterval: 5000, // Refetch every 5 seconds to ensure fresh data
    refetchIntervalInBackground: true, // Continue refetching in background
  });



  // Handle real-time table status updates
  const handleTableStatusUpdate = useCallback((data: { tableId: number, status: string }) => {
    // Update the table status in the cache
    queryClient.setQueryData(['/api/tables'], (oldData: any) => {
      if (!oldData) return oldData;

      const updatedData = oldData.map((table: any) => {
        if (table.id === data.tableId) {
          return { ...table, status: data.status };
        }
        return table;
      });

      return updatedData;
    });

    // If the currently selected table is updated, update the selectedTable state
    if (tableId === data.tableId) {
      setSelectedTable((prev: any) => {
        if (!prev) return prev;
        return { ...prev, status: data.status };
      });
    }
  }, [tableId, queryClient, tables]);

  // Handle full tables update
  const handleTablesUpdate = useCallback((data: any[]) => {
    // Update the tables data in the cache
    queryClient.setQueryData(['/api/tables'], data);

    // If there's a selected table, update it with the new data
    if (tableId) {
      const updatedTable = data.find((table: any) => table.id === tableId);
      if (updatedTable) {
        setSelectedTable(updatedTable);
      }
    }
  }, [tableId, queryClient]);

  // Subscribe to real-time table status updates
  useEffect(() => {
    if (token && currentShop) {
      subscribeToPOSEvent(POSEvents.TABLE_STATUS_UPDATED, handleTableStatusUpdate);
      subscribeToPOSEvent(POSEvents.TABLES_UPDATED, handleTablesUpdate);

      return () => {
        unsubscribeFromPOSEvent(POSEvents.TABLE_STATUS_UPDATED, handleTableStatusUpdate);
        unsubscribeFromPOSEvent(POSEvents.TABLES_UPDATED, handleTablesUpdate);
      };
    }
  }, [token, currentShop, handleTableStatusUpdate, handleTablesUpdate]);

  // Fetch tax settings from API
  const { data: taxSettings, isLoading: isLoadingTaxSettings } = useQuery({
    queryKey: ['/api/settings/tax'],
    enabled: !!token && !!currentShop,
  });

  // Fetch customers from API with shop context
  const { data: customers, isLoading: isLoadingCustomers, refetch: refetchCustomers } = useQuery({
    queryKey: ['/api/customers', currentShop?.id, currentBranch?.id],
    enabled: !!token && !!currentShop,
    staleTime: 0, // Always consider data stale to ensure fresh data
    gcTime: 0, // Don't cache to prevent stale data
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window gains focus
    queryFn: async () => {
      const response = await fetch('/api/customers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }
      return response.json();
    }
  });



  // Function to load existing order items for a table
  const loadExistingOrderItems = async (tableId: number) => {
    try {
      const response = await apiRequest("GET", `/api/tables/${tableId}/active-order`);

      if (response.ok) {
        const orderData = await response.json();

        // Convert order items to cart items format
        const existingOrderItems = orderData.items.map((item: any) => ({
          productId: item.productId,
          productName: item.productName,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          notes: item.notes || ""
        }));

        // Set the order items
        setOrderItems(existingOrderItems);

        toast({
          title: "Order Loaded",
          description: `Loaded existing order with ${existingOrderItems.length} items`,
        });

        return true;
      } else if (response.status === 404) {
        // No active order for this table - this is normal
        return false;
      } else {
        throw new Error(`Failed to load order: ${response.status}`);
      }
    } catch (error) {
      console.error("Error loading existing order items:", error);
      toast({
        title: "Error",
        description: "Failed to load existing order items",
        variant: "destructive",
      });
      return false;
    }
  };

  // Fetch payment methods from API
  const { data: paymentMethods, isLoading: isLoadingPaymentMethods } = useQuery({
    queryKey: ['/api/payment-methods', { active: true }],
    enabled: !!token && !!currentShop,
  });

  // Update default payment method when payment methods are loaded (only if not already set)
  useEffect(() => {
    if (Array.isArray(paymentMethods) && paymentMethods.length > 0 && selectedPaymentMethod === "cash") {
      const firstMethodCode = paymentMethods[0].name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');

      setSelectedPaymentMethod(firstMethodCode);
    }
  }, [paymentMethods, selectedPaymentMethod]);

  // Fetch recent orders from API for table merge functionality
  const { data: recentOrders, isLoading: isLoadingRecentOrders } = useQuery({
    queryKey: ['/api/orders/recent'],
    enabled: !!token && !!currentShop,
  });

  // Create table orders mapping for merge functionality
  const tableOrders = React.useMemo(() => {
    if (!recentOrders || !Array.isArray(recentOrders)) return {};

    const mapping: { [tableId: number]: any } = {};
    recentOrders.forEach((order: any) => {
      if (order.tableId && order.status !== 'completed' && order.status !== 'cancelled') {
        mapping[order.tableId] = order;
      }
    });

    return mapping;
  }, [recentOrders]);

  // Calculate totals when items change
  useEffect(() => {
    const subtotalValue = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);
    setSubtotal(subtotalValue);

    // Calculate tax based on individual product tax rates
    let taxValue = 0;
    // Handle both paginated response format {products: [...], pagination: {...}} and direct array format
    const productsArray = Array.isArray(products) ? products : (products?.products || []);

    orderItems.forEach(item => {
      // Get the product to access its tax rate
      const product = productsArray.find((p: any) => p.id === item.productId);
      let itemTaxRate = 0;

      if (product?.taxRate && product.taxRate > 0) {
        // Use product-specific tax rate
        itemTaxRate = product.taxRate;
      } else {
        // Fall back to global tax rate if product has no specific rate
        itemTaxRate = Array.isArray(taxSettings) && taxSettings.length > 0 && taxSettings[0].active
          ? taxSettings[0].rate
          : 5;
      }

      // Calculate tax for this item
      const itemTax = item.totalPrice * (itemTaxRate / 100);
      taxValue += itemTax;
    });

    setTaxAmount(taxValue);

    // Calculate discount
    let discount = 0;
    if (discountType === "percentage") {
      discount = subtotalValue * (parseFloat(discountValue) / 100);
    } else {
      discount = parseFloat(discountValue);
    }
    setDiscountAmount(discount);

    const totalValue = subtotalValue + taxValue - discount;
    setTotalAmount(totalValue);
  }, [orderItems, taxSettings, discountValue, discountType, products]);

  // Update selected table when tableId changes
  useEffect(() => {
    if (tableId && Array.isArray(tables)) {
      const table = tables.find((t: any) => t.id === tableId);
      if (table) {
        setSelectedTable(table);
      }
    } else {
      setSelectedTable(null);
    }
  }, [tableId, tables]);

  // Clear any previously saved customer data on component mount to ensure clean state
  useEffect(() => {
    // Clear localStorage customer data to prevent automatic restoration
    localStorage.removeItem('pos_selected_customer');
    localStorage.removeItem('pos_order_items');
    localStorage.removeItem('pos_table_selection');
    // Ensure no customer is selected by default
    setSelectedCustomer(null);
    setCustomerId(null);
    // Clear any existing order items to prevent data leakage
    setOrderItems([]);
    setTableId(null);
    setSelectedTable(null);
  }, []); // Empty dependency array to run only once on mount

  // Clear data when shop changes to prevent data leakage between shops
  useEffect(() => {
    if (currentShop) {

      // Clear all POS state when shop changes
      setSelectedCustomer(null);
      setCustomerId(null);
      setOrderItems([]);
      setTableId(null);
      setSelectedTable(null);
      setDiscountValue("0.00");
      setDiscountType("percentage");

      // Clear localStorage data
      localStorage.removeItem('pos_selected_customer');
      localStorage.removeItem('pos_order_items');
      localStorage.removeItem('pos_table_selection');
    }
  }, [currentShop?.id]); // Depend on shop ID to trigger when shop changes

  // Refetch customer data when component mounts or when returning to page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && refetchCustomers) {

        refetchCustomers();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refetchCustomers]);

  // Update selected customer when customerId changes
  useEffect(() => {
    if (customerId && Array.isArray(customers)) {
      const customer = customers.find((c: any) => c.id === customerId);
      if (customer) {
        setSelectedCustomer(customer);
      } else {
        // If customer not found, try to refetch customer data
        if (refetchCustomers) {
          refetchCustomers();
        }
      }
    } else {
      setSelectedCustomer(null);
    }
  }, [customerId, customers, refetchCustomers]);

  // Filter products based on search and category
  // Handle both paginated response format {products: [...], pagination: {...}} and direct array format
  const productsArray = Array.isArray(products) ? products : (products?.products || []);
  const filteredProducts = productsArray.filter((product: any) => {
    let matchesSearch = true;
    let matchesCategory = true;

    if (searchTerm) {
      matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
    }

    if (selectedCategory !== null) {
      matchesCategory = product.categoryId === selectedCategory;
    }

    return matchesSearch && matchesCategory && product.active;
  });

  // Add product to order
  const handleAddProduct = async (product: Product) => {
    try {
      const existingItemIndex = orderItems.findIndex(item => item.productId === product.id);

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const newQuantity = orderItems[existingItemIndex].quantity + 1;

        // Call the API to update cart item
        await cartOperations.updateCartItem(product.id, newQuantity);

        // Update local state
        const updatedItems = [...orderItems];
        updatedItems[existingItemIndex].quantity = newQuantity;
        updatedItems[existingItemIndex].totalPrice =
          updatedItems[existingItemIndex].quantity * updatedItems[existingItemIndex].unitPrice;
        setOrderItems(updatedItems);
      } else {
        // Call the API to add new item to cart
        await cartOperations.addToCart(product.id, 1);

        // Add new item to local state
        const newItem: OrderItem = {
          productId: product.id,
          productName: product.name,
          quantity: 1,
          unitPrice: product.price,
          totalPrice: product.price,
        };
        setOrderItems([...orderItems, newItem]);
      }

      // If there are selected products from quick search, scroll to top to show them
      if (selectedProducts.length > 0) {
        setTimeout(() => {
          window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 100);
      }
    } catch (error) {
      console.error("Error adding product to cart:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to add product to cart. Please try again.",
      });
    }
  };

  // Update item quantity
  const handleUpdateQuantity = async (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    try {
      const productId = orderItems[index].productId;

      // Call the API to update cart item
      await cartOperations.updateCartItem(productId, newQuantity);

      // Update local state
      const updatedItems = [...orderItems];
      updatedItems[index].quantity = newQuantity;
      updatedItems[index].totalPrice = updatedItems[index].quantity * updatedItems[index].unitPrice;
      setOrderItems(updatedItems);
    } catch (error) {
      console.error("Error updating cart item quantity:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update item quantity. Please try again.",
      });
    }
  };

  // Remove item from order
  const handleRemoveItem = async (index: number) => {
    try {
      const productId = orderItems[index].productId;

      // Call the API to remove item from cart
      await cartOperations.removeFromCart(productId);

      // Update local state
      const updatedItems = [...orderItems];
      updatedItems.splice(index, 1);
      setOrderItems(updatedItems);

      toast({
        title: "Item Removed",
        description: "Item removed from cart",
      });
    } catch (error) {
      console.error("Error removing item from cart:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to remove item from cart. Please try again.",
      });
    }
  };

  // Update item notes
  const handleUpdateNotes = (index: number, notes: string) => {
    const updatedItems = [...orderItems];
    updatedItems[index].notes = notes;
    setOrderItems(updatedItems);
  };

  // Create order mutation using two-step process
  const createOrderMutation = useMutation({
    mutationFn: async (data: any) => {
      // Extract order and items data
      const { order, items, ...options } = data;

      // Verify shop ID is present
      if (!order.shopId) {
        throw new Error("Shop ID is required for creating an order");
      }

      // Ensure shop ID is a number
      order.shopId = Number(order.shopId);

      // Use the two-step order creation process
      return createOrderTwoStep(order, items);
    },
    onSuccess: async (data, variables) => {
      // Immediately invalidate and refetch orders data and all related queries
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/orders/all',
        '/api/orders/status-summary',
        '/api/products',
        '/api/tables'
      ]);

      // Force immediate table data refresh if this was a dine-in order
      if (orderType === "dine_in" && tableId) {

        try {
          // Force refetch tables data
          if (refetchTables) {
            await refetchTables();
          }
        } catch (error) {
          console.error('Error refreshing table data:', error);
        }
      }

      // Refetch next order number to update the header
      refetchNextOrderNumber();

      // Store the completed order for receipt with items included

      // Map the items to include product names from the current order items
      const itemsWithProductNames = (data.items || []).map((item: any) => {
        const orderItem = orderItems.find(oi => oi.productId === item.productId);
        return {
          ...item,
          productName: orderItem?.productName || `Product ${item.productId}`,
          notes: orderItem?.notes || item.notes || ''
        };
      });

      // Combine order and items data for the receipt
      const orderWithItems = {
        ...(data.order || data),
        items: itemsWithProductNames
      };

      setCompletedOrder(orderWithItems);

      // If this is a dine-in order with a table, immediately update table status to occupied
      if (orderType === "dine_in" && tableId) {
        // Update the table status in the cache immediately
        queryClient.setQueryData(['/api/tables'], (oldData: any) => {
          if (!oldData) return oldData;

          return oldData.map((table: any) => {
            if (table.id === tableId) {
              return { ...table, status: 'occupied' };
            }
            return table;
          });
        });

        // Also update the selected table state
        if (selectedTable) {
          setSelectedTable({ ...selectedTable, status: 'occupied' });
        }

        // Force immediate UI update by invalidating table queries
        queryClient.invalidateQueries({ queryKey: ['/api/tables'] });

        // Also force another refresh after a short delay to ensure server-side update is reflected
        setTimeout(async () => {
          console.log('Secondary table refresh after order creation');
          try {
            if (refetchTables) {
              await refetchTables();
            }
            queryClient.invalidateQueries({ queryKey: ['/api/tables'] });
          } catch (error) {
            console.error('Error in secondary table refresh:', error);
          }
        }, 500); // Wait 500ms for server-side processing
      }

      // Create order notification
      try {
        const customerName = selectedCustomer?.name || 'Walk-in Customer';
        await NotificationHelpers.createOrderNotification(
          orderWithItems.id,
          orderWithItems.orderNumber,
          customerName
        );
      } catch (error) {
        console.error('Failed to create order notification:', error);
      }

      // Get order number for toast message
      const orderNumber = orderWithItems.orderNumber || data.order?.orderNumber || data.orderNumber;

      // Show success toast and receipt modal if receipt printing was requested
      if (variables.printReceipt) {
        // Show success toast immediately
        toast({
          title: "Success",
          description: `Order #${orderNumber} saved and receipt ready!`,
        });

        // Show printing toast almost immediately
        setTimeout(() => {
          toast({
            title: "Printing Receipt",
            description: `Receipt for order #${orderNumber} sent to printer`,
          });
        }, 50); // Super fast - only 0.05 seconds!

        // Open receipt modal quickly after printing toast
        setTimeout(() => {
          setIsReceiptModalOpen(true);
        }, 300); // Much faster - only 0.3 seconds total
      } else {
        // Show regular success toast for non-print orders
        const successMessage = variables.printKOT ?
          `Kitchen Order Ticket for #${orderNumber} sent to printer!` :
          `Order #${orderNumber} saved successfully!`;

        toast({
          title: "Success",
          description: successMessage,
        });
      }

      // Clear selected products from quick search for all order types
      clearAllProducts();

      // Reset order if not KOT only
      if (!variables.kotOnly) {
        setOrderItems([]);
        setDiscountValue("0.00");
        setTableId(null);
        setSelectedTable(null);
        setCustomerId(null);
        setSelectedCustomer(null);
        localStorage.removeItem('pos_selected_customer');
        // Reset to first available payment method
        if (Array.isArray(paymentMethods) && paymentMethods.length > 0) {
          const firstMethodCode = paymentMethods[0].name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
          setSelectedPaymentMethod(firstMethodCode);
        } else {
          setSelectedPaymentMethod("cash");
        }

        // Force invalidate all order-related queries to ensure fresh data
        queryClient.invalidateQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   (queryKey[0].includes('/api/orders') || queryKey[0].includes('/api/tables'));
          }
        });
        queryClient.removeQueries({
          predicate: (query) => {
            const queryKey = query.queryKey;
            return Array.isArray(queryKey) && queryKey.length > 0 &&
                   typeof queryKey[0] === 'string' &&
                   (queryKey[0].includes('/api/orders') || queryKey[0].includes('/api/tables'));
          }
        });

        // Also clear the query cache completely for orders
        queryClient.clear();

        // Navigate to orders page only for "Save Only" (not for "Save and Print" or KOT)
        if (variables.saveOnly && !variables.printReceipt && !variables.kotOnly) {
          setTimeout(() => {
            // Force one more invalidation right before navigation
            queryClient.invalidateQueries({
              predicate: (query) => {
                const queryKey = query.queryKey;
                return Array.isArray(queryKey) && queryKey.length > 0 &&
                       typeof queryKey[0] === 'string' && queryKey[0].includes('/api/orders');
              }
            });
            // Add a flag to localStorage to indicate fresh order creation
            localStorage.setItem('freshOrderCreated', Date.now().toString());
            setLocation("/billing/orders");
          }, 1500); // Wait 1.5 seconds to show the success message
        }
      }

      setIsProcessing(false);
    },
    onError: (err: any) => {
      // Extract error details if available
      let errorMessage = "Failed to create order. Please try again.";
      if (err.response?.data?.errors) {
        const validationErrors = err.response.data.errors;
        errorMessage = `Validation errors: ${JSON.stringify(validationErrors)}`;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      setIsProcessing(false);

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    },
  });

  // Create split bill orders mutation
  const createSplitBillOrdersMutation = useMutation({
    mutationFn: async (data: { originalOrder: any, splits: any[] }) => {
      console.log("Creating split bill orders:", data);
      return await createSplitBillOrders(data.originalOrder, data.splits);
    },
    onSuccess: async (data) => {
      // Immediately invalidate and refetch orders data and all related queries
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/orders/all',
        '/api/orders/status-summary',
        '/api/products',
        '/api/tables'
      ]);

      toast({
        title: "Success",
        description: `Split bill created successfully! ${data.totalOrders} separate orders generated.`,
      });

      // Clear the cart and reset form
      setOrderItems([]);
      setDiscountValue("");
      // Clear selected products from quick search
      clearAllProducts();
      // Reset to first available payment method
      if (Array.isArray(paymentMethods) && paymentMethods.length > 0) {
        const firstMethodCode = paymentMethods[0].name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
        setSelectedPaymentMethod(firstMethodCode);
      } else {
        setSelectedPaymentMethod("cash");
      }
      setCustomerId(null);
      setSelectedCustomer(null);
      localStorage.removeItem('pos_selected_customer');
      setTableId(null);
      setSelectedTable(null);
      setNumberOfPersons(1);
      setIsProcessing(false);
    },
    onError: (error: any) => {
      console.error("Error creating split bill orders:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create split bill orders. Please try again.",
      });
      setIsProcessing(false);
    },
  });

  // Table transfer mutation
  const tableTransferMutation = useMutation({
    mutationFn: async (data: { fromTableId: number, toTableId: number, orderId: number }) => {
      console.log("Transferring order to different table:", data);
      return await transferOrderToTable(data.fromTableId, data.toTableId, data.orderId);
    },
    onSuccess: async (data) => {
      // Immediately invalidate and refetch orders data and all related queries
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/orders/all',
        '/api/orders/status-summary',
        '/api/tables'
      ]);

      toast({
        title: "Success",
        description: `Order transferred successfully from ${data.transfer.fromTable} to ${data.transfer.toTable}`,
      });

      // Close the modal and reset state
      setIsTableTransferModalOpen(false);
      setCurrentOrderForTransfer(null);
      setIsProcessing(false);
    },
    onError: (error: any) => {
      console.error("Error transferring order:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to transfer order. Please try again.",
      });
      setIsProcessing(false);
    },
  });

  // Table merge mutation
  const tableMergeMutation = useMutation({
    mutationFn: async (data: { primaryTableId: number, secondaryTableIds: number[], orderIds: number[] }) => {
      console.log("Merging tables:", data);
      return await mergeTablesOrders(data.primaryTableId, data.secondaryTableIds, data.orderIds);
    },
    onSuccess: async (data) => {
      // Immediately invalidate and refetch orders data and all related queries
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/orders/all',
        '/api/orders/status-summary',
        '/api/tables'
      ]);

      toast({
        title: "Success",
        description: `Tables merged successfully! ${data.merge.secondaryTables.length + 1} tables combined into ${data.merge.primaryTable}`,
      });

      // Close the modal and reset state
      setIsTableMergeModalOpen(false);
      setCurrentTableForMerge(null);
      setCurrentOrderForMerge(null);
      setIsProcessing(false);
    },
    onError: (error: any) => {
      console.error("Error merging tables:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to merge tables. Please try again.",
      });
      setIsProcessing(false);
    },
  });

  // Handle table transfer
  const handleTableTransfer = (fromTableId: number, toTableId: number, orderId: number) => {
    setIsProcessing(true);
    tableTransferMutation.mutate({ fromTableId, toTableId, orderId });
  };

  // Open table transfer modal
  const openTableTransferModal = (table: Table, order: any) => {
    setCurrentOrderForTransfer(order);
    setIsTableTransferModalOpen(true);
  };

  // Handle table merge
  const handleTableMerge = (primaryTableId: number, secondaryTableIds: number[], orderIds: number[]) => {
    setIsProcessing(true);
    tableMergeMutation.mutate({ primaryTableId, secondaryTableIds, orderIds });
  };

  // Open table merge modal
  const openTableMergeModal = (table: Table, order: any) => {
    setCurrentTableForMerge(table);
    setCurrentOrderForMerge(order);
    setIsTableMergeModalOpen(true);
  };

  // Customer modal handlers
  const handleOpenCustomerModal = (customer?: any) => {
    setCustomerToEdit(customer || null);
    setIsCustomerModalOpen(true);
  };

  const handleCloseCustomerModal = () => {
    setIsCustomerModalOpen(false);
    setCustomerToEdit(null);
  };

  const handleCustomerSaved = async (customer: any) => {
    console.log('POS: Customer saved:', customer);

    // Always select the new/updated customer first
    setSelectedCustomer(customer);
    setCustomerId(customer.id);

    // Clear all customer-related cache to force fresh data
    queryClient.removeQueries({ queryKey: ['/api/customers'], exact: false });

    // Force immediate refetch of customer data
    try {
      console.log('POS: Forcing customer data refetch...');

      // Refetch customers with current shop context
      if (refetchCustomers) {
        await refetchCustomers();
      }

      console.log('POS: Customer data refetched successfully');

      // Show success message
      toast({
        title: "Success",
        description: "Customer saved successfully",
      });

    } catch (error) {
      console.error('Error refreshing customer data:', error);
      // Show error toast if refresh fails
      toast({
        variant: "destructive",
        title: "Warning",
        description: "Customer saved but data refresh failed. Please refresh the page to see updated data.",
      });
    }
  };

  // Print receipt function
  const printReceipt = (order: any) => {
    // In a real implementation, this would connect to a receipt printer
    console.log("Printing receipt for order:", order);

    // Simulate printing
    toast({
      title: "Printing Receipt",
      description: `Receipt for order #${order.orderNumber} sent to printer`,
    });
  };

  // Print KOT function
  const printKOT = (order: any) => {
    // In a real implementation, this would connect to a kitchen printer
    console.log("Printing KOT for order:", order);

    // Simulate printing
    toast({
      title: "Printing KOT",
      description: `Kitchen Order Ticket for order #${order.orderNumber} sent to printer`,
    });
  };

  // Resume draft or hold order
  const handleResumeOrder = (order: any) => {
    console.log("Resuming order:", order);

    // Set order type
    setOrderType(order.orderType as OrderType);

    // Set table if it's a dine-in order
    if (order.orderType === "dine_in" && order.tableId) {
      setTableId(order.tableId);
      // Find and set the selected table object
      if (Array.isArray(tables)) {
        const table = tables.find((t: any) => t.id === order.tableId);
        if (table) {
          setSelectedTable(table);
        }
      }
    }

    // Set customer if available
    if (order.customerId) {
      setCustomerId(order.customerId);
      // Find and set the selected customer object
      if (Array.isArray(customers)) {
        const customer = customers.find((c: any) => c.id === order.customerId);
        if (customer) {
          setSelectedCustomer(customer);
        }
      }
    }

    // Set discount
    if (order.discountAmount > 0) {
      // Determine if it's a percentage or fixed discount
      const percentageDiscount = (order.discountAmount / order.subtotal) * 100;
      if (Math.abs(percentageDiscount - Math.round(percentageDiscount)) < 0.01) {
        setDiscountType("percentage");
        setDiscountValue(percentageDiscount.toFixed(2));
      } else {
        setDiscountType("fixed");
        setDiscountValue(order.discountAmount.toFixed(2));
      }
    } else {
      setDiscountValue("0.00");
      setDiscountType("percentage");
    }

    // Set payment method if available
    if (order.paymentMethod) {
      setSelectedPaymentMethod(order.paymentMethod);
    }

    // Set order items with proper formatting
    if (order.items && order.items.length > 0) {
      const formattedItems = order.items.map((item: any) => ({
        productId: item.productId,
        productName: item.productName || "Product",
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        notes: item.notes || ""
      }));

      setOrderItems(formattedItems);
    } else {
      setOrderItems([]);
    }

    toast({
      title: "Order Resumed",
      description: `Resumed ${order.status === "draft" ? "draft" : "on hold"} order #${order.orderNumber}`,
    });
  };

  // Handle payment submission
  const handlePaymentSubmit = (paymentDetails: {
    method: string,
    amount: number,
    printReceipt: boolean
  }) => {
    console.log('POS: Payment submitted with method:', paymentDetails.method);
    setSelectedPaymentMethod(paymentDetails.method);
    setIsPaymentModalOpen(false);

    // Submit the order with the stored options and payment details
    // Use a timeout to ensure state is updated
    setTimeout(() => {
      console.log('POS: About to submit order with payment method:', paymentDetails.method);
      console.log('POS: Using stored payment options:', paymentOptions);

      // Merge stored options with payment details
      const finalOptions = {
        ...paymentOptions, // Use stored options (saveOnly, printReceipt, etc.)
        paymentMethod: paymentDetails.method // Override with selected payment method
      };

      handleSubmitOrder(finalOptions);

      // Clear stored options after use
      setPaymentOptions(null);
    }, 100);
  };

  // Submit order with options
  const handleSubmitOrder = (options: {
    saveOnly?: boolean,
    printReceipt?: boolean,
    kotOnly?: boolean,
    printKOT?: boolean,
    draft?: boolean,
    hold?: boolean,
    cancel?: boolean,
    splitBills?: any[],
    paymentMethod?: string
  } = {}) => {
    if (!currentShop || !currentShop.id) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No shop selected. Please select a shop first.",
      });
      console.error("Missing shop ID in handleSubmitOrder. Current shop:", currentShop);
      return;
    }

    // Ensure shop ID is available in localStorage
    if (!localStorage.getItem("current_shop")) {
      // If currentShop exists in state but not in localStorage, save it
      localStorage.setItem("current_shop", JSON.stringify(currentShop));
      console.log("Restored current_shop in localStorage:", currentShop);
    }

    if (orderItems.length === 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please add at least one item to the order",
      });
      return;
    }

    // Validate based on order type
    if (orderType === "dine_in") {
      // For table orders, table selection is mandatory
      if (!tableId) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Please select a table for dine-in order",
        });
        return;
      }
      // Customer information is optional for all order types
    } else {
      // Customer selection is now optional for all order types

      // For home delivery orders, ensure customer has an address
      if (orderType === "eat_and_pay" && selectedCustomer && !selectedCustomer.address) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Selected customer does not have a delivery address. Please update customer details or select a different customer.",
        });
        return;
      }
    }

    setIsProcessing(true);

    // Handle split bill orders
    if (options.splitBills && options.splitBills.length > 0) {
      // Order number will be generated by the server using the configured format
      const orderNumber = undefined; // Let server generate the order number

      // Prepare original order data
      const originalOrderData = {
        // orderNumber will be generated by the server
        orderType,
        status: "completed", // Split bills are immediately completed
        tableId: orderType === "dine_in" ? tableId : undefined,
        numberOfPersons: orderType === "dine_in" ? numberOfPersons : undefined,
        customerId: (orderType === "takeaway" || orderType === "eat_and_pay" || orderType === "online") ? customerId : undefined,
        subtotal: Number(subtotal),
        taxAmount: Number(taxAmount),
        discountAmount: Number(discountAmount),
        totalAmount: Number(totalAmount),
        paymentMethod: selectedPaymentMethod,
        paymentStatus: "paid",
        notes: "Split bill order",
        shopId: currentShop.id,
        branchId: currentBranch?.id,
        userId: user?.id ? Number(user.id) : undefined,
      };

      // Create split bill orders
      createSplitBillOrdersMutation.mutate({
        originalOrder: originalOrderData,
        splits: options.splitBills,
      });
      return;
    }

    // Order number will be generated by the server using the configured format
    const orderNumber = undefined; // Let server generate the order number

    // Determine order status based on options
    let orderStatus = "pending";

    if (options.draft) {
      orderStatus = "draft";
    } else if (options.hold) {
      orderStatus = "hold";
    } else if (options.cancel) {
      orderStatus = "cancelled";
    } else if (options.kotOnly) {
      orderStatus = "preparing";
    }

    // Determine which payment method to use
    const paymentMethodToUse = options.paymentMethod || selectedPaymentMethod;
    console.log('POS: Using payment method for order:', paymentMethodToUse);
    console.log('POS: Options payment method:', options.paymentMethod);
    console.log('POS: Selected payment method state:', selectedPaymentMethod);

    // Calculate effective tax rate (for future use)
    const effectiveTaxRate = subtotal > 0 ? (taxAmount / subtotal) * 100 : 0;

    // Prepare order data
    const orderData = {
      order: {
        // orderNumber will be generated by the server
        orderType,
        status: orderStatus,
        tableId: orderType === "dine_in" ? (tableId ? Number(tableId) : null) : null,
        numberOfPersons: orderType === "dine_in" ? (numberOfPersons ? Number(numberOfPersons) : null) : null,
        customerId: customerId ? Number(customerId) : null, // Customer can be associated with any order type
        subtotal: Number(subtotal),
        taxAmount: Number(taxAmount),
        taxRate: Number(effectiveTaxRate), // Store the effective tax rate
        discountAmount: Number(discountAmount),
        totalAmount: Number(totalAmount),
        paymentMethod: paymentMethodToUse, // Use payment method from options or state
        paymentStatus: "paid",
        notes: "",
        shopId: currentShop.id,
        branchId: currentBranch?.id, // Add branch ID field
        userId: user?.id ? Number(user.id) : undefined, // Add user ID field
      },
      items: orderItems.map(item => ({
        productId: Number(item.productId),
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        notes: item.notes || "",
      })),
      // Pass options to the mutation for handling in onSuccess
      saveOnly: options.saveOnly,
      printReceipt: options.printReceipt,
      kotOnly: options.kotOnly,
      printKOT: options.printKOT
    };

    // Submit the order data
    console.log('POS: Submitting order with type:', orderType);
    console.log('POS: Table ID:', tableId, 'Customer ID:', customerId, 'Number of Persons:', numberOfPersons);
    console.log('POS: Full order data:', JSON.stringify(orderData, null, 2));

    createOrderMutation.mutate(orderData);

    // Note: Receipt printing is handled in the success handler after order creation
    // This ensures we have the correct order number from the server

    if (options.printKOT) {
      printKOT(orderData.order);
    }
  };

  // Loading state
  const isLoading =
    isLoadingProducts ||
    isLoadingCategories ||
    isLoadingTables ||
    isLoadingTaxSettings ||
    isLoadingCustomers ||
    isLoadingPaymentMethods;

  // Determine if we should show the selected products widget
  // Show when there are selected products from quick search, regardless of order state
  const shouldShowSelectedProducts = selectedProducts.length > 0;



  return (
    <>
    {/* Selected Products Widget - Fixed at top - Only show when appropriate */}
    {shouldShowSelectedProducts && (
      <div className="fixed top-0 left-0 right-0 z-50 p-2 bg-white/95 backdrop-blur-sm border-b">
        <SelectedProductsWidget
          compact={true}
          showTitle={false}
          showTableAssignment={true}
          onTableAssign={async (tableId, products) => {
          console.log(`🚀 POS PAGE - QUICK PRODUCT SEARCH - Assigning ${products.length} products to table ${tableId}:`, products);
          console.log(`🚀 POS PAGE - ALWAYS CREATE NEW ORDER (Quick Product Search behavior)`);

          try {
            // ALWAYS CREATE NEW ORDER FOR QUICK PRODUCT SEARCH
            console.log(`🆕 Creating NEW order for table ${tableId} (Quick Product Search always creates new orders)`);
            let orderId;
            let orderNumber;

            const orderData = {
              orderType: "dine_in",
              status: "pending",
              tableId: tableId,
              numberOfPersons: 2, // Default value
              subtotal: products.reduce((sum, p) => sum + p.price, 0),
              taxAmount: products.reduce((sum, p) => sum + p.price, 0) * 0.05, // 5% tax
              discountAmount: 0,
              totalAmount: products.reduce((sum, p) => sum + p.price, 0) * 1.05,
              paymentMethod: "cash",
              paymentStatus: "pending",
              notes: `Order created from Quick Product Search`
            };

            const orderResponse = await apiRequest("POST", "/api/orders/create", { order: orderData });

            if (!orderResponse.ok) {
              throw new Error(`Failed to create order: ${orderResponse.status}`);
            }

            const createdOrder = await orderResponse.json();
            orderId = createdOrder.id;
            orderNumber = createdOrder.orderNumber;
            console.log('✅ New order created successfully:', createdOrder);

            // Step 2: Add items to the order (existing or new)
            const orderItems = products.map(product => ({
              productId: product.id,
              quantity: 1,
              unitPrice: product.price,
              totalPrice: product.price,
              notes: ""
            }));

            console.log(`Adding ${orderItems.length} items to order ${orderId}:`, orderItems);

            const itemsResponse = await apiRequest("POST", `/api/orders/${orderId}/items`, { items: orderItems });

            if (!itemsResponse.ok) {
              throw new Error(`Failed to add items to order: ${itemsResponse.status}`);
            }

            const addedItems = await itemsResponse.json();
            console.log('Items added successfully:', addedItems);

            // Refresh tables data to show updated status
            refetchTables();

            // Clear selected products after successful operation
            clearAllProducts();

            // Get table name from tables array
            const tableName = Array.isArray(tables) ?
              tables.find((t: any) => t.id === tableId)?.name || `Table ${tableId}` :
              `Table ${tableId}`;

            const actionText = "New Order Created";
            const descriptionText = `Order ${orderNumber} created for ${tableName} with ${products.length} items`;

            toast({
              title: `${actionText} Successfully! ✅`,
              description: descriptionText,
              className: "bg-green-50 border-green-200 text-green-800",
            });

          } catch (error) {
            console.error('Error processing table assignment:', error);
            toast({
              variant: "destructive",
              title: "Error",
              description: error instanceof Error ? error.message : "Failed to process table assignment",
            });
          }
        }}
      />
      </div>
    )}

    <div className={`flex h-screen overflow-hidden ${shouldShowSelectedProducts ? 'pt-16' : 'pt-0'}`}>
      {/* Left Sidebar - Categories (15% width) */}
      <div className="w-[15%] bg-[#343A40] text-white flex flex-col">
        <div className="p-3 flex items-center justify-between text-white border-b border-gray-700">
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-[#495057] mr-2"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="h-5 w-5 mr-1" />
            Back
          </Button>
          <span className="font-bold text-sm uppercase tracking-wider">Categories</span>
        </div>

        <CategorySelector
          categories={Array.isArray(categories) ? categories : []}
          selectedCategoryId={selectedCategory}
          onSelectCategory={setSelectedCategory}
          variant="vertical"
          className="flex-1"
        />
      </div>

      {/* Middle Section - Products (50% width) */}
      <div className="w-[50%] flex flex-col bg-[#F8F9FA]">
        {/* Header with New Order and Cancel Buttons */}
        <div className="p-3 bg-[#007BFF] text-white flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <h1 className="text-lg font-bold">
                {nextOrderData?.nextOrderNumber ? `Order ${nextOrderData.nextOrderNumber}` : 'New Order'}
              </h1>
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-[#0069d9] hover:text-white p-1 h-6 w-6"
                onClick={async () => {
                  // Clear all order number related cache
                  queryClient.removeQueries({ queryKey: ['nextOrderNumber'] });
                  queryClient.invalidateQueries({ queryKey: ['nextOrderNumber'], exact: false });

                  // Force immediate refetch
                  await refetchNextOrderNumber();

                  toast({
                    title: "Order Number Refreshed",
                    description: "Order number has been updated",
                    variant: "default",
                  });
                }}
                title="Refresh order number"
              >
                🔄
              </Button>
            </div>

            {/* Branch Selector */}
            <div className="flex items-center">
              <BranchSelector
                variant="ghost"
                size="sm"
                className="text-white hover:bg-[#0069d9] hover:text-white"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              className="bg-[#28A745] hover:bg-[#218838] text-white"
              onClick={() => {
                // Reset all order data
                setOrderItems([]);
                setDiscountValue("0.00");
                setDiscountType("percentage");
                setOrderType("dine_in");
                setTableId(null);
                setSelectedTable(null);
                setCustomerId(null);
                setSelectedCustomer(null);
                localStorage.removeItem('pos_selected_customer');
                setNumberOfPersons(2);
                setSelectedPaymentMethod("cash");
                setSearchTerm("");
                setSelectedCategory(null);

                // Clear selected products from quick search
                clearAllProducts();

                // Ensure UI elements are in the right state
                setIsCustomerDetailsOpen(true);
                setIsPaymentDetailsOpen(true);

                // Refetch next order number to update the header
                refetchNextOrderNumber();

                toast({
                  title: "New Order",
                  description: "Started a new order",
                });
              }}
            >
              NEW ORDER
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="bg-[#6C757D] hover:bg-[#5a6268] text-white"
              onClick={() => setIsDraftOrdersModalOpen(true)}
            >
              <FileEdit className="h-4 w-4 mr-1" />
              SAVED ORDERS
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="bg-[#007BFF] hover:bg-[#0069d9] text-white"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit Full Screen" : "Enter Full Screen"}
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="bg-[#6C757D] hover:bg-[#5a6268] text-white"
              onClick={() => {
                if (orderItems.length > 0) {
                  if (confirm("Are you sure you want to cancel this order? All items will be lost.")) {
                    setOrderItems([]);
                    setDiscountValue("0.00");
                    setLocation("/billing/orders");
                  }
                } else {
                  setLocation("/billing/orders");
                }
              }}
            >
              CANCEL
            </Button>
            {orderItems.length > 0 && (
              <div className="bg-[#DC3545] rounded-full h-8 w-8 flex items-center justify-center">
                <span className="text-white font-bold">{orderItems.length}</span>
              </div>
            )}
          </div>
        </div>

        {/* Search Bar with Quick Search */}
        <div className="p-3 bg-white border-b">
          <div className="pos-search-container">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search item"
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <QuickSearch
              onProductSelect={handleAddProduct}
              showTrigger={true}
              triggerClassName="pos-quick-search-trigger"
            />
          </div>
        </div>

        {/* Order Type Selector */}
        <div className="bg-[#007BFF] text-white p-3">
          <OrderTypeSelector
            value={orderType}
            onChange={(value) => {
              setOrderType(value);

              // Reset appropriate state based on order type
              if (value === "dine_in") {
                // For dine-in, customer is optional but table is required
                // Keep table selection if any
                setIsCustomerDetailsOpen(true);
              } else {
                // For other order types, customer is optional and table is not needed
                setTableId(null);
                setSelectedTable(null);
                setIsCustomerDetailsOpen(true);
              }

              // Show toast with green color for all order type changes
              toast({
                title: "Order Type Changed",
                description: `Changed to ${value.replace('_', ' ')} order type`,
                variant: "success" as any, // Green color for all order types
              });
            }}
            variant="horizontal"
            className="w-full justify-center"
          />
        </div>

        {/* Content based on order type */}
        <div className="flex-1 overflow-auto">
          {orderType === "dine_in" && !tableId && (
            <TableSelectionView
              tables={Array.isArray(tables) ? tables : []}
              isLoading={isLoadingTables}
              selectedTableId={tableId}
              onTableSelect={async (table) => {
                console.log('POS: Table selected from TableSelectionView:', table);
                console.log('POS: Current tableId before setting:', tableId);
                console.log('POS: Setting tableId to:', table.id);
                setTableId(table.id);
                setSelectedTable(table);
                console.log('POS: Table selection state updated');

                // If table is occupied, try to load existing order items
                if (table.status === "occupied") {
                  console.log('POS: Table is occupied, loading existing order items');
                  await loadExistingOrderItems(table.id);
                } else {
                  console.log('POS: Table is available, clearing order items');
                  // Clear any existing items for available tables
                  setOrderItems([]);
                }

                toast({
                  title: "Table Selected",
                  description: `Selected table: ${table.name}`,
                });

                console.log('POS: Table selection completed, tableId should now be:', table.id);
              }}
              onRefresh={refetchTables}
            />
          )}

          {(orderType !== "dine_in" || (orderType === "dine_in" && tableId)) && (
            <div className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {filteredProducts?.map((product) => (
                  <button
                    key={product.id}
                    className="bg-white rounded-lg p-4 text-center shadow hover:shadow-lg transition-all relative overflow-hidden flex flex-col justify-between min-h-[120px] border border-gray-100 hover:border-[#007BFF] active:scale-[0.98]"
                    onClick={() => handleAddProduct(product)}
                  >
                    {/* Add popular badge to some products */}
                    {product.id % 5 === 0 && (
                      <div className="absolute top-0 left-0 bg-[#007BFF] text-white text-xs py-1.5 px-3 rounded-br-lg font-medium shadow-sm">
                        Popular
                      </div>
                    )}
                    <div>
                      <div className="mt-2 font-medium text-base truncate">{product.name}</div>
                      <div className="text-[#007BFF] font-semibold mt-2 text-lg">{product.price.toFixed(2)}</div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Section - Order Summary (35% width) */}
      <div className="w-[35%] pos-right-sidebar">
        {/* Fixed Header Content */}
        <div className="pos-right-sidebar-header">
          {/* Table and Customer Details */}
          <div className="flex-shrink-0">
          {/* Table Header */}
          {orderType === "dine_in" && (
            <div className="p-3 border-b">
              {/* Table Status Summary */}
              {Array.isArray(tables) && (
                <div className="mb-3 p-2 bg-blue-50 rounded-md">
                  <div className="flex justify-between items-center mb-1">
                    <div className="text-xs font-medium text-blue-800">Table Status Overview</div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('🔄 Manual table refresh triggered');
                        refetchTables();
                        toast({
                          title: "Tables Refreshed",
                          description: "Table status data has been updated",
                          variant: "default",
                        });
                      }}
                      className="h-6 px-2 text-xs"
                    >
                      🔄 Refresh
                    </Button>
                  </div>
                  <div className="flex gap-4 text-xs">
                    <span className="text-green-600">
                      Available: {tables.filter((t: any) => t.status === 'available').length}
                    </span>
                    <span className="text-red-600">
                      Occupied: {tables.filter((t: any) => t.status === 'occupied').length}
                    </span>
                    <span className="text-yellow-600">
                      Reserved: {tables.filter((t: any) => t.status === 'reserved').length}
                    </span>
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    Last updated: {new Date().toLocaleTimeString()}
                  </div>

                </div>
              )}

              <div className="flex justify-between items-center">
                <div className="flex gap-6">
                  <div className="flex flex-col">
                  <div className="text-xs text-gray-500">
                    Table No.
                    <span className="text-red-500 ml-1">*</span>
                  </div>
                  <div className="flex items-center">
                    <Select
                      value={tableId?.toString() || ""}
                      onValueChange={async (value) => {
                        const newTableId = value ? parseInt(value) : null;
                        setTableId(newTableId);

                        if (newTableId && Array.isArray(tables)) {
                          const selectedTable = tables.find((t: any) => t.id === newTableId);
                          if (selectedTable) {
                            setSelectedTable(selectedTable);

                            // If table is occupied, try to load existing order items
                            if (selectedTable.status === "occupied") {
                              await loadExistingOrderItems(selectedTable.id);
                            } else {
                              // Clear any existing items for available tables
                              setOrderItems([]);
                            }
                          }
                        }
                      }}
                    >
                      <SelectTrigger className={`h-8 min-h-0 border-0 p-0 font-medium ${orderType === "dine_in" && !tableId ? "text-red-500" : ""}`}>
                        <SelectValue placeholder="Select Table" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.isArray(tables) && tables.filter((table: any) => table && table.id && table.name).map((table: any) => (
                          <SelectItem
                            key={table.id}
                            value={table.id.toString()}
                            disabled={table.status === "occupied" && table.id !== tableId}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span>{table.name}</span>
                              <div className="flex items-center gap-2 ml-2">
                                <Users className="h-3 w-3" />
                                <span className="text-xs">{table.capacity}</span>
                                {table.status !== "available" && (
                                  <Badge variant="outline" className="text-xs">
                                    {table.status}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {orderType === "dine_in" && !tableId && (
                    <div className="text-xs text-red-500 mt-1">
                      Table selection is required
                    </div>
                  )}
                </div>

                <div className="flex flex-col">
                  <div className="text-xs text-gray-500">
                    Persons
                    <span className="text-red-500 ml-1">*</span>
                  </div>
                  <div className="flex items-center">
                    <Select
                      value={numberOfPersons.toString()}
                      onValueChange={(value) => setNumberOfPersons(parseInt(value))}
                    >
                      <SelectTrigger className="h-8 min-h-0 border-0 p-0 font-medium">
                        <SelectValue placeholder={numberOfPersons.toString()} />
                      </SelectTrigger>
                      <SelectContent>
                        {[1, 2, 3, 4, 5, 6, 8, 10, 12].map((num) => (
                          <SelectItem key={num} value={num.toString()}>
                            {num} {num === 1 ? 'Person' : 'Persons'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                </div>
              </div>

              {/* Selected Table Information */}
              {selectedTable && (
                <div className="mt-3 p-2 bg-gray-50 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="text-sm font-medium">{selectedTable.name}</div>
                      <Badge variant="outline" className={
                        selectedTable.status === 'available' ? 'bg-green-100 text-green-800' :
                        selectedTable.status === 'occupied' ? 'bg-red-100 text-red-800' :
                        selectedTable.status === 'reserved' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }>
                        {selectedTable.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>Capacity: {selectedTable.capacity}</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Persons: {numberOfPersons} / {selectedTable.capacity}
                    {numberOfPersons > selectedTable.capacity && (
                      <span className="text-red-500 ml-2">⚠ Exceeds capacity</span>
                    )}
                  </div>
                </div>
              )}

              {/* Table Actions - Show only when table is selected and has items */}
              {tableId && selectedTable && orderItems.length > 0 && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Create a mock order object for transfer
                      const mockOrder = {
                        id: Date.now(), // Temporary ID for demo
                        orderNumber: nextOrderData?.nextOrderNumber || 'TEMP-ORDER',
                        orderType: orderType,
                        status: 'pending',
                        tableId: tableId,
                        subtotal: subtotal,
                        taxAmount: taxAmount,
                        discountAmount: discountAmount,
                        totalAmount: totalAmount,
                        paymentMethod: selectedPaymentMethod,
                        paymentStatus: 'pending',
                        createdAt: new Date().toISOString(),
                        items: orderItems,
                      };
                      openTableTransferModal(selectedTable, mockOrder);
                    }}
                    className="flex items-center gap-1"
                  >
                    <ArrowRight className="h-4 w-4" />
                    Transfer
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Create a mock order object for merge
                      const mockOrder = {
                        id: Date.now(), // Temporary ID for demo
                        orderNumber: nextOrderData?.nextOrderNumber || 'TEMP-ORDER',
                        orderType: orderType,
                        status: 'pending',
                        tableId: tableId,
                        subtotal: subtotal,
                        taxAmount: taxAmount,
                        discountAmount: discountAmount,
                        totalAmount: totalAmount,
                        paymentMethod: selectedPaymentMethod,
                        paymentStatus: 'pending',
                        createdAt: new Date().toISOString(),
                        items: orderItems,
                      };
                      openTableMergeModal(selectedTable, mockOrder);
                    }}
                    className="flex items-center gap-1"
                  >
                    <Merge className="h-4 w-4" />
                    Merge
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Customer Details */}
          <div className="p-3 border-b">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">
                Customer Details
              </h3>
              <button
                className="text-gray-500"
                onClick={() => setIsCustomerDetailsOpen(!isCustomerDetailsOpen)}
              >
                {isCustomerDetailsOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </button>
            </div>

            {isCustomerDetailsOpen && (
              <div className="mt-3">
                <div className="text-sm text-gray-500 mb-3">
                  Customer information is optional for all order types
                </div>
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Select
                      key={`customer-select-${customers?.length || 0}`}
                      value={customerId?.toString() || ""}
                      onValueChange={(value) => {
                        const newCustomerId = value ? parseInt(value) : null;
                        setCustomerId(newCustomerId);

                        // Clear localStorage if no customer selected
                        if (!newCustomerId) {
                          localStorage.removeItem('pos_selected_customer');
                        }
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select a customer" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.isArray(customers) && customers.map((customer: any) => (
                          <SelectItem key={customer.id} value={customer.id.toString()}>
                            <div className="flex flex-col">
                              <span className="font-medium">{customer.name}</span>
                              <span className="text-xs text-muted-foreground">{customer.phone}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full flex items-center justify-center gap-1"
                      onClick={() => handleOpenCustomerModal()}
                    >
                      <Plus className="h-4 w-4" />
                      Add New Customer
                    </Button>
                  </div>

                  {selectedCustomer && (
                    <div className="space-y-2 mt-2 p-2 bg-gray-50 rounded-md">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center text-sm">
                            <User className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{selectedCustomer.name}</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <Phone className="h-4 w-4 mr-2 text-gray-500" />
                            <span>{selectedCustomer.phone}</span>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="ml-2"
                          onClick={() => handleOpenCustomerModal(selectedCustomer)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                      {selectedCustomer.address && orderType === "eat_and_pay" && (
                        <div className="flex items-start text-sm">
                          <MapPin className="h-4 w-4 mr-2 mt-0.5 text-gray-500" />
                          <span>{selectedCustomer.address}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          </div>
        </div>

        {/* Scrollable Cart Sidebar */}
        <div className="pos-right-sidebar-content">
          <CartSidebar
            items={orderItems}
            onUpdateQuantity={handleUpdateQuantity}
            onRemoveItem={handleRemoveItem}
            onUpdateNotes={handleUpdateNotes}
            subtotal={subtotal}
            taxAmount={taxAmount}
            discountAmount={discountAmount}
            totalAmount={totalAmount}
            discountValue={discountValue}
            onDiscountChange={setDiscountValue}
            discountType={discountType}
            onDiscountTypeChange={setDiscountType}
            onSubmitOrder={(options) => {
              if (options.saveOnly && !options.kotOnly) {
                // Open payment modal for both "Save" and "Save & Print"
                // Store the options to use after payment
                setPaymentOptions(options);
                setIsPaymentModalOpen(true);
              } else {
                // For KOT or other options, proceed directly
                handleSubmitOrder(options);
              }
            }}
            isProcessing={isProcessing}
            className="border-l-0 pos-cart-sidebar"
            orderType={orderType}
          />
        </div>
      </div>
    </div>

    {/* Payment Modal */}
    <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => {
            setIsPaymentModalOpen(false);
            setPaymentOptions(null); // Clear stored options when modal is closed
          }}
          onSubmit={handlePaymentSubmit}
          totalAmount={totalAmount}
          paymentMethods={Array.isArray(paymentMethods) ? paymentMethods : []}
          isProcessing={isProcessing}
        />

    {/* Draft Orders Modal */}
    <DraftOrdersModal
      isOpen={isDraftOrdersModalOpen}
      onClose={() => setIsDraftOrdersModalOpen(false)}
      onResumeOrder={handleResumeOrder}
    />

    {/* Receipt Modal */}
    <ReceiptModal
      isOpen={isReceiptModalOpen}
      onClose={() => setIsReceiptModalOpen(false)}
      order={completedOrder}
      customer={selectedCustomer}
      tableName={selectedTable?.name}
    />

    {/* Table Transfer Modal */}
    <TableTransferModal
      isOpen={isTableTransferModalOpen}
      onClose={() => setIsTableTransferModalOpen(false)}
      currentTable={selectedTable}
      currentOrder={currentOrderForTransfer}
      availableTables={Array.isArray(tables) ? tables : []}
      onTransferConfirm={handleTableTransfer}
      isProcessing={isProcessing}
    />

    {/* Table Merge Modal */}
    <TableMergeModal
      isOpen={isTableMergeModalOpen}
      onClose={() => setIsTableMergeModalOpen(false)}
      primaryTable={currentTableForMerge}
      primaryOrder={currentOrderForMerge}
      availableTables={Array.isArray(tables) ? tables : []}
      tableOrders={tableOrders}
      onMergeConfirm={handleTableMerge}
      isProcessing={isProcessing}
    />

    {/* Customer Modal */}
    <CustomerModal
      isOpen={isCustomerModalOpen}
      onClose={handleCloseCustomerModal}
      customer={customerToEdit}
      onCustomerSaved={handleCustomerSaved}
    />
    </>
  );
}
