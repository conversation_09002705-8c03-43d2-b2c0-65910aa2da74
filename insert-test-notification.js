import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function insertTestNotification() {
  const client = await pool.connect();
  try {
    console.log('🔔 Creating test notification...\n');

    // 1. Insert notification
    const notificationResult = await client.query(`
      INSERT INTO notifications (title, message, type, priority, recipient_type, recipient_id, shop_id, created_by)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id
    `, [
      'Test Notification',
      'This is a test notification to verify the system is working!',
      'system',
      'normal',
      'shop',
      1, // shop ID
      1, // shop ID
      1  // created by user ID
    ]);

    const notificationId = notificationResult.rows[0].id;
    console.log(`✅ Created notification with ID: ${notificationId}`);

    // 2. Insert notification recipient (assuming user ID 1 exists)
    await client.query(`
      INSERT INTO notification_recipients (notification_id, user_id, status)
      VALUES ($1, $2, $3)
    `, [notificationId, 1, 'unread']);

    console.log('✅ Created notification recipient record');

    // 3. Verify the data
    const verifyResult = await client.query(`
      SELECT n.*, nr.user_id, nr.status as recipient_status
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE n.id = $1
    `, [notificationId]);

    console.log('\n📋 Notification created:');
    console.log('   Title:', verifyResult.rows[0].title);
    console.log('   Message:', verifyResult.rows[0].message);
    console.log('   Type:', verifyResult.rows[0].type);
    console.log('   Status:', verifyResult.rows[0].recipient_status);
    console.log('   User ID:', verifyResult.rows[0].user_id);

    console.log('\n🎉 Test notification created successfully!');
    console.log('💡 Now refresh your browser and check the notification bell.');
    
  } catch (error) {
    console.error('❌ Error creating test notification:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
insertTestNotification();
