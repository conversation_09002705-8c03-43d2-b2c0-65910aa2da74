// Simple test for subscription endpoints
const testEndpoint = async (url) => {
  try {
    const response = await fetch(url, {
      headers: {
        'Authorization': 'Bearer test-token',
        'X-Shop-ID': '1'
      }
    });
    
    console.log(`${url} - Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`  ✅ Success - Response:`, JSON.stringify(data).substring(0, 100) + '...');
    } else {
      const error = await response.text();
      console.log(`  ❌ Error:`, error.substring(0, 200));
    }
  } catch (err) {
    console.log(`  ❌ Network Error:`, err.message);
  }
  console.log('');
};

const runTests = async () => {
  console.log('🧪 Testing Subscription Endpoints...\n');
  
  await testEndpoint('http://localhost:5000/api/subscription-plans');
  await testEndpoint('http://localhost:5000/api/subscriptions');
  await testEndpoint('http://localhost:5000/api/subscriptions/usage');
  await testEndpoint('http://localhost:5000/api/payment-methods/subscription');
  
  console.log('✅ Tests completed');
};

runTests();
