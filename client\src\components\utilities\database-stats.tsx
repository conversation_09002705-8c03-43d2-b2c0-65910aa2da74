import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Database, Users, Package, ShoppingCart, Receipt, Building, Table } from 'lucide-react';

interface DatabaseStats {
  tables: {
    users: number;
    roles: number;
    shops: number;
    branches: number;
    products: number;
    categories: number;
    tables: number;
    customers: number;
    orders: number;
    expenses: number;
    purchases: number;
    stockMovements: number;
    stockTransfers: number;
    subscriptions: number;
    notifications: number;
  };
  totals: {
    totalRecords: number;
    businessRecords: number;
    inventoryRecords: number;
  };
  timestamp: string;
}

const tableConfig = [
  { key: 'users', label: 'Users', icon: Users, color: 'bg-blue-500' },
  { key: 'roles', label: 'Roles', icon: Users, color: 'bg-purple-500' },
  { key: 'shops', label: 'Shops', icon: Building, color: 'bg-green-500' },
  { key: 'branches', label: 'Branches', icon: Building, color: 'bg-teal-500' },
  { key: 'products', label: 'Products', icon: Package, color: 'bg-orange-500' },
  { key: 'categories', label: 'Categories', icon: Table, color: 'bg-yellow-500' },
  { key: 'tables', label: 'Tables', icon: Table, color: 'bg-indigo-500' },
  { key: 'customers', label: 'Customers', icon: Users, color: 'bg-pink-500' },
  { key: 'orders', label: 'Orders', icon: ShoppingCart, color: 'bg-red-500' },
  { key: 'expenses', label: 'Expenses', icon: Receipt, color: 'bg-gray-500' },
  { key: 'purchases', label: 'Purchases', icon: Receipt, color: 'bg-cyan-500' },
  { key: 'stockMovements', label: 'Stock Movements', icon: Package, color: 'bg-lime-500' },
  { key: 'stockTransfers', label: 'Stock Transfers', icon: Package, color: 'bg-emerald-500' },
  { key: 'subscriptions', label: 'Subscriptions', icon: Receipt, color: 'bg-violet-500' },
  { key: 'notifications', label: 'Notifications', icon: Database, color: 'bg-rose-500' },
];

export function DatabaseStats() {
  const { data: dbStats, isLoading, error } = useQuery<DatabaseStats>({
    queryKey: ['/api/utilities/database-stats'],
    refetchInterval: 60000, // Refresh every minute
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Database Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !dbStats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Database Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            Failed to load database statistics
          </div>
        </CardContent>
      </Card>
    );
  }

  const maxCount = Math.max(...Object.values(dbStats.tables));

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Records</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary">
              {dbStats.totals.totalRecords.toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">
              Across all tables
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Business Records</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {dbStats.totals.businessRecords.toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">
              Products, orders, customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Inventory Records</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-600">
              {dbStats.totals.inventoryRecords.toLocaleString()}
            </div>
            <p className="text-sm text-muted-foreground">
              Stock movements & transfers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Table Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Table Statistics
          </CardTitle>
          <CardDescription>
            Record counts for each database table
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tableConfig.map((config) => {
              const count = dbStats.tables[config.key as keyof typeof dbStats.tables];
              const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0;
              const Icon = config.icon;

              return (
                <div key={config.key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${config.color}`} />
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{config.label}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {count.toLocaleString()}
                      </Badge>
                    </div>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Statistics Metadata</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            <p>Last updated: {new Date(dbStats.timestamp).toLocaleString()}</p>
            <p className="mt-1">
              Statistics are automatically refreshed every minute to provide real-time insights.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
