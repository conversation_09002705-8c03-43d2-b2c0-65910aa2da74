import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Test credentials
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

async function login() {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    const data = await response.json();
    authToken = data.token;
    console.log('✅ Login successful');
    return data;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

async function getPayments() {
  try {
    const response = await fetch(`${BASE_URL}/api/payments`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Get payments failed: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('❌ Get payments failed:', error.message);
    return [];
  }
}

async function processRefund(orderId, amount, reason = 'Test refund') {
  try {
    const refundData = {
      orderId: orderId,
      amount: amount,
      reason: reason,
      refundMethod: 'cash'
    };

    console.log('Processing refund with data:', refundData);

    const response = await fetch(`${BASE_URL}/api/refunds`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(refundData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Process refund failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('💰 Refund processed successfully:', data);
    return data;
  } catch (error) {
    console.error('❌ Process refund failed:', error.message);
    throw error;
  }
}

async function testRefundProcess() {
  console.log('🧪 Testing refund process...\n');

  try {
    // Step 1: Login
    await login();

    // Step 2: Get payments to find one to refund
    console.log('\n📋 Getting payments...');
    const payments = await getPayments();
    
    if (payments.length === 0) {
      console.log('⚠️ No payments found. Cannot test refund process.');
      return;
    }

    // Find a payment that can be refunded (paid status)
    const refundablePayment = payments.find(payment => 
      payment.status === 'paid' && 
      payment.orderStatus === 'cancelled' &&
      payment.refundStatus === 'not_refunded'
    );
    
    if (!refundablePayment) {
      console.log('⚠️ No refundable payments found. Using first payment for testing...');
      // Use the first payment for testing
      const testPayment = payments[0];
      console.log(`Using payment: ${testPayment.orderNumber} (Amount: ${testPayment.amount})`);
      
      // Try to process a small refund
      const refundAmount = Math.min(10, testPayment.amount);
      
      try {
        await processRefund(testPayment.orderId, refundAmount, 'Test refund - checking persistence');
      } catch (error) {
        console.log('Expected error for non-refundable order:', error.message);
        return;
      }
    } else {
      console.log(`\n💰 Found refundable payment: ${refundablePayment.orderNumber} (Amount: ${refundablePayment.amount})`);
      
      // Process a partial refund
      const refundAmount = Math.min(50, refundablePayment.amount);
      await processRefund(refundablePayment.orderId, refundAmount, 'Test refund - checking persistence');
    }

    // Step 3: Get payments again to check if refund status is updated
    console.log('\n🔄 Getting payments again to check refund status...');
    const updatedPayments = await getPayments();
    
    // Find the payment we just refunded
    const refundedPayment = updatedPayments.find(p => 
      p.orderId === (refundablePayment?.orderId || payments[0].orderId)
    );
    
    if (refundedPayment) {
      console.log('\n📊 Refund status after processing:');
      console.log(`  - Refund Status: ${refundedPayment.refundStatus}`);
      console.log(`  - Refund Amount: ${refundedPayment.refundAmount}`);
      console.log(`  - Refund Method: ${refundedPayment.refundMethod}`);
      
      if (refundedPayment.refundStatus !== 'not_refunded') {
        console.log('✅ Refund status updated correctly!');
      } else {
        console.log('❌ Refund status not updated - this is the bug!');
      }
    }

    // Step 4: Get refunds to verify they're stored
    console.log('\n💸 Getting all refunds...');
    const response = await fetch(`${BASE_URL}/api/refunds`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (response.ok) {
      const refunds = await response.json();
      console.log(`Found ${refunds.length} refunds in database`);
      refunds.forEach(refund => {
        console.log(`  - Refund ${refund.id}: Order ${refund.orderNumber}, Amount ${refund.amount}`);
      });
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testRefundProcess();
