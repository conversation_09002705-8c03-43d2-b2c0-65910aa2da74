import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Test credentials (you may need to adjust these)
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

async function login() {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_USER),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.status}`);
    }

    const data = await response.json();
    authToken = data.token;
    console.log('✅ Login successful');
    return data;
  } catch (error) {
    console.error('❌ Login failed:', error.message);
    throw error;
  }
}

async function getDashboardStats() {
  try {
    const response = await fetch(`${BASE_URL}/api/dashboard/stats`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Dashboard stats failed: ${response.status}`);
    }

    const data = await response.json();
    console.log('📊 Dashboard Stats:', data);
    return data;
  } catch (error) {
    console.error('❌ Dashboard stats failed:', error.message);
    throw error;
  }
}

async function getOrders() {
  try {
    const response = await fetch(`${BASE_URL}/api/orders`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Get orders failed: ${response.status}`);
    }

    const data = await response.json();
    console.log(`📋 Found ${data.orders?.length || 0} orders`);
    return data.orders || [];
  } catch (error) {
    console.error('❌ Get orders failed:', error.message);
    return [];
  }
}

async function processRefund(orderId, amount, reason = 'Test refund') {
  try {
    const refundData = {
      orderId: orderId,
      amount: amount,
      reason: reason,
      refundMethod: 'cash'
    };

    const response = await fetch(`${BASE_URL}/api/refunds`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(refundData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Process refund failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('💰 Refund processed:', data);
    return data;
  } catch (error) {
    console.error('❌ Process refund failed:', error.message);
    throw error;
  }
}

async function getRefunds() {
  try {
    const response = await fetch(`${BASE_URL}/api/refunds`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Get refunds failed: ${response.status}`);
    }

    const data = await response.json();
    console.log(`💸 Found ${data.length} refunds`);
    return data;
  } catch (error) {
    console.error('❌ Get refunds failed:', error.message);
    return [];
  }
}

async function runTest() {
  console.log('🚀 Starting refund dashboard test...\n');

  try {
    // Step 1: Login
    await login();

    // Step 2: Get initial dashboard stats
    console.log('\n📊 Getting initial dashboard stats...');
    const initialStats = await getDashboardStats();

    // Step 3: Get orders to find one to refund
    console.log('\n📋 Getting orders...');
    const orders = await getOrders();
    
    if (orders.length === 0) {
      console.log('⚠️ No orders found. Please create some orders first.');
      return;
    }

    // Find a paid order to refund
    const paidOrder = orders.find(order => order.paymentStatus === 'paid' && order.status === 'cancelled');
    
    if (!paidOrder) {
      console.log('⚠️ No paid cancelled orders found. Creating a test scenario...');
      console.log('Please ensure you have at least one paid cancelled order to test refunds.');
      return;
    }

    console.log(`\n💰 Found order to refund: ${paidOrder.orderNumber} (Amount: ${paidOrder.totalAmount})`);

    // Step 4: Process a refund
    console.log('\n💸 Processing refund...');
    const refundAmount = Math.min(50, paidOrder.totalAmount); // Refund up to 50 or the full amount
    await processRefund(paidOrder.id, refundAmount, 'Test refund for dashboard verification');

    // Step 5: Get updated dashboard stats
    console.log('\n📊 Getting updated dashboard stats...');
    const updatedStats = await getDashboardStats();

    // Step 6: Compare stats
    console.log('\n📈 Comparing dashboard stats:');
    console.log(`Initial sales: ${initialStats.todaySales}`);
    console.log(`Updated sales: ${updatedStats.todaySales}`);
    console.log(`Expected difference: -${refundAmount}`);
    console.log(`Actual difference: ${updatedStats.todaySales - initialStats.todaySales}`);

    if (Math.abs((updatedStats.todaySales - initialStats.todaySales) + refundAmount) < 0.01) {
      console.log('✅ Dashboard stats updated correctly! Refunds are being subtracted from sales.');
    } else {
      console.log('❌ Dashboard stats not updated correctly. There might be an issue.');
    }

    // Step 7: Get refunds to verify they're stored
    console.log('\n💸 Getting all refunds...');
    const refunds = await getRefunds();

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
runTest();
