// Script to clear all stock movements and transfers for a specific shop
import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

async function clearShopData() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    console.log('Connected to database successfully');
    
    const shopId = 1; // Shop to clear data for
    
    // Get all branches for this shop
    const branchesResult = await client.query('SELECT id FROM branches WHERE shop_id = $1', [shopId]);
    const branchIds = branchesResult.rows.map(row => row.id);
    
    console.log(`Shop ${shopId} has branches: ${branchIds.join(', ')}`);
    
    if (branchIds.length === 0) {
      console.log('No branches found for this shop');
      return;
    }
    
    // Delete stock transfer items first (foreign key constraint)
    const transfersResult = await client.query(`
      SELECT id FROM stock_transfers 
      WHERE from_branch_id = ANY($1) OR to_branch_id = ANY($1)
    `, [branchIds]);
    
    const transferIds = transfersResult.rows.map(row => row.id);
    
    if (transferIds.length > 0) {
      console.log(`Deleting transfer items for ${transferIds.length} transfers`);
      await client.query('DELETE FROM stock_transfer_items WHERE transfer_id = ANY($1)', [transferIds]);
      
      console.log(`Deleting ${transferIds.length} stock transfers`);
      await client.query('DELETE FROM stock_transfers WHERE id = ANY($1)', [transferIds]);
    }
    
    // Delete stock movements
    const movementsResult = await client.query('SELECT COUNT(*) FROM stock_movements WHERE branch_id = ANY($1)', [branchIds]);
    const movementsCount = parseInt(movementsResult.rows[0].count);
    
    if (movementsCount > 0) {
      console.log(`Deleting ${movementsCount} stock movements`);
      await client.query('DELETE FROM stock_movements WHERE branch_id = ANY($1)', [branchIds]);
    }
    
    console.log(`Successfully cleared all stock data for shop ${shopId}`);
    console.log(`- Deleted ${transferIds.length} stock transfers`);
    console.log(`- Deleted ${movementsCount} stock movements`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

clearShopData();
