import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Lay<PERSON>, Clock } from "lucide-react";

export default function SettingsBranchesPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Branch Settings</h1>
          <p className="text-gray-500 mt-1">
            Configure branch-specific settings and sub-branch management
          </p>
        </div>
      </div>

      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md text-center">
          <CardHeader className="pb-4">
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-primary/10 rounded-full">
                <Clock className="h-12 w-12 text-primary" />
              </div>
            </div>
            <CardTitle className="text-2xl">Coming Soon</CardTitle>
            <CardDescription className="text-base">
              Sub-branch management and advanced branch settings are currently under development
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Layers className="h-4 w-4" />
              <span>This feature will be available in a future update</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
