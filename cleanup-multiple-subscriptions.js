import pkg from 'pg';
const { Pool } = pkg;

// Database connection
const pool = new Pool({
  user: 'postgres',
  host: '**************',
  database: 'nemboobill',
  password: 'Cloud@2025',
  port: 5432,
});

async function cleanupMultipleSubscriptions() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Checking for shops with multiple active subscriptions...\n');
    
    // Find shops with multiple active subscriptions
    const multipleSubscriptionsQuery = `
      SELECT 
        shop_id,
        COUNT(*) as subscription_count,
        ARRAY_AGG(id ORDER BY created_at DESC) as subscription_ids,
        ARRAY_AGG(plan_id ORDER BY created_at DESC) as plan_ids,
        ARRAY_AGG(created_at ORDER BY created_at DESC) as created_dates
      FROM subscriptions 
      WHERE status = 'active'
      GROUP BY shop_id
      HAVING COUNT(*) > 1
      ORDER BY shop_id;
    `;
    
    const result = await client.query(multipleSubscriptionsQuery);
    
    if (result.rows.length === 0) {
      console.log('✅ No shops found with multiple active subscriptions.');
      return;
    }
    
    console.log(`Found ${result.rows.length} shops with multiple active subscriptions:\n`);
    
    for (const shop of result.rows) {
      console.log(`🏪 Shop ID: ${shop.shop_id}`);
      console.log(`   📊 Total active subscriptions: ${shop.subscription_count}`);
      console.log(`   🆔 Subscription IDs: ${shop.subscription_ids.join(', ')}`);
      console.log(`   📋 Plan IDs: ${shop.plan_ids.join(', ')}`);
      console.log(`   📅 Created dates: ${shop.created_dates.map(d => new Date(d).toLocaleDateString()).join(', ')}`);
      
      // Keep the latest subscription (first in the ordered array)
      const latestSubscriptionId = shop.subscription_ids[0];
      const subscriptionsToCancel = shop.subscription_ids.slice(1);
      
      console.log(`   ✅ Keeping latest subscription: #${latestSubscriptionId}`);
      console.log(`   ❌ Cancelling older subscriptions: #${subscriptionsToCancel.join(', #')}`);
      
      // Cancel older subscriptions
      for (const subscriptionId of subscriptionsToCancel) {
        await client.query(
          `UPDATE subscriptions 
           SET status = 'cancelled', updated_at = NOW() 
           WHERE id = $1`,
          [subscriptionId]
        );
        console.log(`   🔄 Cancelled subscription #${subscriptionId}`);
      }
      
      console.log('');
    }
    
    console.log('✅ Cleanup completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Shops processed: ${result.rows.length}`);
    console.log(`   - Subscriptions cancelled: ${result.rows.reduce((sum, shop) => sum + (shop.subscription_count - 1), 0)}`);
    console.log(`   - Active subscriptions remaining: ${result.rows.length}`);
    
    // Show final state
    console.log('\n🔍 Final verification - Active subscriptions per shop:');
    const finalCheck = await client.query(`
      SELECT 
        shop_id,
        COUNT(*) as active_count,
        ARRAY_AGG(id) as subscription_ids
      FROM subscriptions 
      WHERE status = 'active'
      GROUP BY shop_id
      ORDER BY shop_id;
    `);
    
    for (const shop of finalCheck.rows) {
      console.log(`   Shop ${shop.shop_id}: ${shop.active_count} active subscription(s) - ID(s): ${shop.subscription_ids.join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the cleanup
cleanupMultipleSubscriptions();
