import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient, invalidateAndRefetch } from "@/lib/queryClient";
import { useAuth } from "@/context/auth-context";
import { exportToExcel, formatDataForExport } from "@/lib/export-utils";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  She<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Alert, AlertDescription } from "@/components/ui/alert";

import {
  ShoppingBag,
  Receipt,
  Plus,
  Calendar as CalendarIcon,
  Loader2,
  AlertCircle,
  Search,
  FileDown,
  RefreshCw,
} from "lucide-react";

// Purchase form schema
const purchaseFormSchema = z.object({
  supplierName: z.string().min(2, { message: "Supplier name is required" }),
  invoiceNumber: z.string().optional(),
  amount: z.coerce.number().positive({ message: "Amount must be positive" }),
  date: z.date(),
  notes: z.string().optional(),
});

// Expense form schema
const expenseFormSchema = z.object({
  category: z.string().min(2, { message: "Category is required" }),
  amount: z.coerce.number().positive({ message: "Amount must be positive" }),
  date: z.date(),
  notes: z.string().optional(),
});

type PurchaseFormValues = z.infer<typeof purchaseFormSchema>;
type ExpenseFormValues = z.infer<typeof expenseFormSchema>;



export default function Purchases() {
  const { token } = useAuth();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("purchases");
  const [isPurchaseDialogOpen, setIsPurchaseDialogOpen] = useState(false);
  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshingPurchases, setIsRefreshingPurchases] = useState(false);
  const [isRefreshingExpenses, setIsRefreshingExpenses] = useState(false);

  // Search state
  const [purchaseSearchInput, setPurchaseSearchInput] = useState("");
  const [expenseSearchInput, setExpenseSearchInput] = useState("");
  const [purchaseSearchTerm, setPurchaseSearchTerm] = useState("");
  const [expenseSearchTerm, setExpenseSearchTerm] = useState("");

  // Category filter state
  const [expenseCategories, setExpenseCategories] = useState<string[]>([]);
  const [selectedExpenseCategories, setSelectedExpenseCategories] = useState<string[]>([]);
  const [supplierNames, setSupplierNames] = useState<string[]>([]);
  const [selectedSupplier, setSelectedSupplier] = useState<string>("");

  // Debounce search terms
  useEffect(() => {
    const timer = setTimeout(() => {
      setPurchaseSearchTerm(purchaseSearchInput);
    }, 300);

    return () => clearTimeout(timer);
  }, [purchaseSearchInput]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setExpenseSearchTerm(expenseSearchInput);
    }, 300);

    return () => clearTimeout(timer);
  }, [expenseSearchInput]);

  // Purchase form
  const purchaseForm = useForm<PurchaseFormValues>({
    resolver: zodResolver(purchaseFormSchema),
    defaultValues: {
      supplierName: "",
      invoiceNumber: "",
      amount: undefined,
      date: new Date(),
      notes: "",
    },
  });

  // Expense form
  const expenseForm = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseFormSchema),
    defaultValues: {
      category: "",
      amount: undefined,
      date: new Date(),
      notes: "",
    },
  });

  // Fetch purchases and expenses from API
  const { data: purchases, isLoading: isPurchasesLoading, refetch: refetchPurchases } = useQuery({
    queryKey: ['/api/purchases', selectedSupplier],
    enabled: !!token,
    queryFn: async () => {
      let url = '/api/purchases';
      if (selectedSupplier) {
        url += `?category=${encodeURIComponent(selectedSupplier)}`;
      }

      const response = await apiRequest("GET", url);
      if (!response.ok) {
        throw new Error('Failed to fetch purchases');
      }
      return response.json();
    }
  });

  const { data: expenses, isLoading: isExpensesLoading, refetch: refetchExpenses } = useQuery({
    queryKey: ['/api/expenses', selectedExpenseCategories],
    enabled: !!token,
    queryFn: async () => {
      let url = '/api/expenses';
      if (selectedExpenseCategories.length === 1) {
        url += `?category=${encodeURIComponent(selectedExpenseCategories[0])}`;
      }

      const response = await apiRequest("GET", url);
      if (!response.ok) {
        throw new Error('Failed to fetch expenses');
      }
      return response.json();
    }
  });

  // Filter purchases based on search term
  const filteredPurchases = purchases?.filter(purchase => {
    if (!purchaseSearchTerm) return true;

    const searchTermLower = purchaseSearchTerm.toLowerCase();
    return (
      purchase.supplierName.toLowerCase().includes(searchTermLower) ||
      (purchase.invoiceNumber && purchase.invoiceNumber.toLowerCase().includes(searchTermLower)) ||
      (purchase.notes && purchase.notes.toLowerCase().includes(searchTermLower))
    );
  });

  // Filter expenses based on search term
  const filteredExpenses = expenses?.filter(expense => {
    if (!expenseSearchTerm) return true;

    const searchTermLower = expenseSearchTerm.toLowerCase();
    return (
      expense.category.toLowerCase().includes(searchTermLower) ||
      (expense.description && expense.description.toLowerCase().includes(searchTermLower))
    );
  });

  // Refetch data when tab changes
  useEffect(() => {
    const fetchData = async () => {
      if (activeTab === "purchases" && refetchPurchases) {
        setIsRefreshingPurchases(true);
        try {
          await refetchPurchases();
        } finally {
          setIsRefreshingPurchases(false);
        }
      } else if (activeTab === "expenses" && refetchExpenses) {
        setIsRefreshingExpenses(true);
        try {
          await refetchExpenses();
        } finally {
          setIsRefreshingExpenses(false);
        }
      }
    };

    fetchData();
  }, [activeTab, refetchPurchases, refetchExpenses]);

  // Extract unique categories from expenses
  useEffect(() => {
    if (expenses && expenses.length > 0) {
      const uniqueCategories = Array.from(new Set(expenses.map(expense => expense.category)));
      setExpenseCategories(uniqueCategories);
    }
  }, [expenses]);

  // Extract unique supplier names from purchases
  useEffect(() => {
    if (purchases && purchases.length > 0) {
      const uniqueSuppliers = Array.from(new Set(purchases.map(purchase => purchase.supplierName)));
      setSupplierNames(uniqueSuppliers);
    }
  }, [purchases]);

  // Create purchase mutation
  const createPurchaseMutation = useMutation({
    mutationFn: async (data: PurchaseFormValues) => {
      // Format the data to match the schema exactly
      const formattedData = {
        supplierName: data.supplierName,
        invoiceNumber: data.invoiceNumber || "", // Use empty string instead of undefined
        amount: data.amount,
        date: data.date.toISOString(),
        notes: data.notes || "", // Use empty string instead of undefined
      };
      console.log("Submitting purchase data:", formattedData);

      // Use apiRequest to ensure shop and branch headers are included
      const response = await apiRequest("POST", "/api/purchases", formattedData);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error response:", errorText);
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.message || "Failed to create purchase");
        } catch (e) {
          throw new Error(`Failed to create purchase: ${response.status} ${response.statusText}`);
        }
      }

      const responseData = await response.json();
      console.log("Purchase creation response:", responseData);
      return responseData;
    },
    onSuccess: async () => {
      console.log("Purchase created successfully");
      // Directly refetch the purchases data with loading state
      setIsRefreshingPurchases(true);
      try {
        await refetchPurchases();
      } finally {
        setIsRefreshingPurchases(false);
      }

      purchaseForm.reset();
      setIsPurchaseDialogOpen(false);

      toast({
        title: "Purchase added",
        description: "The purchase has been successfully recorded.",
      });
    },
    onError: (error) => {
      console.error("Failed to create purchase:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem adding the purchase. Please try again.",
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });

  // Create expense mutation
  const createExpenseMutation = useMutation({
    mutationFn: async (data: ExpenseFormValues) => {
      // Format the date to ISO string for API compatibility and map notes to description
      const formattedData = {
        category: data.category,
        amount: data.amount,
        date: data.date.toISOString(),
        description: data.notes || "", // Map notes to description to match the schema, ensure it's not undefined
      };
      console.log("Submitting expense data:", formattedData);

      // Use apiRequest to ensure shop and branch headers are included
      const response = await apiRequest("POST", "/api/expenses", formattedData);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error response:", errorText);
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.message || "Failed to create expense");
        } catch (e) {
          throw new Error(`Failed to create expense: ${response.status} ${response.statusText}`);
        }
      }

      const responseData = await response.json();
      console.log("Expense creation response:", responseData);
      return responseData;
    },
    onSuccess: async () => {
      console.log("Expense created successfully");
      // Directly refetch the expenses data with loading state
      setIsRefreshingExpenses(true);
      try {
        await refetchExpenses();
      } finally {
        setIsRefreshingExpenses(false);
      }

      expenseForm.reset();
      setIsExpenseDialogOpen(false);

      toast({
        title: "Expense added",
        description: "The expense has been successfully recorded.",
      });
    },
    onError: (error) => {
      console.error("Failed to create expense:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem adding the expense. Please try again.",
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });

  // Handle purchase form submission
  function onSubmitPurchase(data: PurchaseFormValues) {
    setIsSubmitting(true);
    createPurchaseMutation.mutate(data);
  }

  // Handle expense form submission
  function onSubmitExpense(data: ExpenseFormValues) {
    setIsSubmitting(true);
    createExpenseMutation.mutate(data);
  }

  // Handle export functionality
  const handleExport = () => {
    try {
      if (activeTab === "purchases") {
        if (!purchases || purchases.length === 0) {
          toast({
            variant: "destructive",
            title: "Export failed",
            description: "No purchase data available to export",
          });
          return;
        }

        // Format the data for export
        const formattedData = formatDataForExport(purchases, {
          // Custom formatters for specific fields
          date: (value) => format(new Date(value), "MMM dd, yyyy"),
          amount: (value) => `$${parseFloat(value).toFixed(2)}`,
        });

        // Export to Excel
        const success = exportToExcel(
          formattedData,
          `Purchases_Report_${new Date().toISOString().split('T')[0]}`
        );

        if (success) {
          toast({
            title: "Export successful",
            description: "Purchases report has been exported to Excel",
          });
        } else {
          throw new Error("Export failed");
        }
      } else {
        if (!expenses || expenses.length === 0) {
          toast({
            variant: "destructive",
            title: "Export failed",
            description: "No expense data available to export",
          });
          return;
        }

        // Format the data for export
        const formattedData = formatDataForExport(expenses, {
          // Custom formatters for specific fields
          date: (value) => format(new Date(value), "MMM dd, yyyy"),
          amount: (value) => `$${parseFloat(value).toFixed(2)}`,
        });

        // Export to Excel
        const success = exportToExcel(
          formattedData,
          `Expenses_Report_${new Date().toISOString().split('T')[0]}`
        );

        if (success) {
          toast({
            title: "Export successful",
            description: "Expenses report has been exported to Excel",
          });
        } else {
          throw new Error("Export failed");
        }
      }
    } catch (error) {
      console.error("Export error:", error);
      toast({
        variant: "destructive",
        title: "Export failed",
        description: "There was an error exporting the report",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Purchases & Expenses</h1>
          <p className="text-gray-500">Manage your purchases and expenses</p>
        </div>

        <div className="flex gap-2">
          {activeTab === "purchases" ? (
            <Button onClick={() => setIsPurchaseDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Purchase
            </Button>
          ) : (
            <Button onClick={() => setIsExpenseDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Expense
            </Button>
          )}

          <Button variant="outline" onClick={handleExport}>
            <FileDown className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="purchases" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="purchases">
            <ShoppingBag className="h-4 w-4 mr-2" />
            Purchases
          </TabsTrigger>
          <TabsTrigger value="expenses">
            <Receipt className="h-4 w-4 mr-2" />
            Expenses
          </TabsTrigger>
        </TabsList>

        <TabsContent value="purchases">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CardTitle>Purchase History</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        setIsRefreshingPurchases(true);
                        try {
                          await refetchPurchases();
                        } finally {
                          setIsRefreshingPurchases(false);
                        }
                      }}
                      className="h-8 w-8 p-0"
                      disabled={isRefreshingPurchases}
                    >
                      {isRefreshingPurchases ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <div className="relative w-64">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search purchases..."
                      className="pl-8"
                      value={purchaseSearchInput}
                      onChange={(e) => setPurchaseSearchInput(e.target.value)}
                    />
                  </div>
                </div>

                {/* Supplier filter badges */}
                {supplierNames.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    <div className="flex items-center mr-2">
                      <span className="text-sm font-medium text-gray-500">Filter by supplier:</span>
                    </div>
                    {supplierNames.map((supplier) => (
                      <Button
                        key={supplier}
                        variant={selectedSupplier === supplier ? "default" : "outline"}
                        size="sm"
                        className="rounded-full h-8 px-3 text-xs"
                        onClick={() => {
                          if (selectedSupplier === supplier) {
                            setSelectedSupplier("");
                          } else {
                            setSelectedSupplier(supplier);
                          }
                        }}
                      >
                        {supplier}
                      </Button>
                    ))}
                    {selectedSupplier && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="rounded-full h-8 px-3 text-xs"
                        onClick={() => setSelectedSupplier("")}
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isPurchasesLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : filteredPurchases && filteredPurchases.length > 0 ? (
                <Table className="mt-3">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Date</TableHead>

                      <TableHead>Invoice #</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredPurchases.map((purchase) => (
                      <TableRow key={purchase.id}>
                          <TableCell>{purchase.supplierName}</TableCell>
                        <TableCell>{format(purchase.date, "MMM dd, yyyy")}</TableCell>

                        <TableCell>{purchase.invoiceNumber || "-"}</TableCell>
                        <TableCell className="text-right">${purchase.amount.toFixed(2)}</TableCell>
                        <TableCell className="max-w-xs truncate">{purchase.notes || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : purchases && purchases.length > 0 && filteredPurchases?.length === 0 ? (
                <div className="text-center py-10">
                  <Search className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No matching purchases</h3>
                  <p className="text-gray-500 mb-4">Try adjusting your search term to find what you're looking for.</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setPurchaseSearchInput("");
                      setPurchaseSearchTerm("");
                    }}
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                <div className="text-center py-10">
                  <ShoppingBag className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No purchases yet</h3>
                  <p className="text-gray-500 mb-4">Start adding your purchases to track your inventory costs.</p>
                  <Button onClick={() => setIsPurchaseDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Purchase
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="expenses">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex flex-col space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CardTitle>Expense History</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        setIsRefreshingExpenses(true);
                        try {
                          await refetchExpenses();
                        } finally {
                          setIsRefreshingExpenses(false);
                        }
                      }}
                      className="h-8 w-8 p-0"
                      disabled={isRefreshingExpenses}
                    >
                      {isRefreshingExpenses ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <div className="relative w-64">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search expenses..."
                      className="pl-8"
                      value={expenseSearchInput}
                      onChange={(e) => setExpenseSearchInput(e.target.value)}
                    />
                  </div>
                </div>

                {/* Category filter badges */}
                {expenseCategories.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    <div className="flex items-center mr-2">
                      <span className="text-sm font-medium text-gray-500">Filter by:</span>
                    </div>
                    {expenseCategories.map((category) => (
                      <Button
                        key={category}
                        variant={selectedExpenseCategories.includes(category) ? "default" : "outline"}
                        size="sm"
                        className="rounded-full h-8 px-3 text-xs"
                        onClick={() => {
                          if (selectedExpenseCategories.includes(category)) {
                            setSelectedExpenseCategories(
                              selectedExpenseCategories.filter((c) => c !== category)
                            );
                          } else {
                            setSelectedExpenseCategories([...selectedExpenseCategories, category]);
                          }
                        }}
                      >
                        {category}
                      </Button>
                    ))}
                    {selectedExpenseCategories.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="rounded-full h-8 px-3 text-xs"
                        onClick={() => setSelectedExpenseCategories([])}
                      >
                        Clear All
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isExpensesLoading ? (
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ) : filteredExpenses && filteredExpenses.length > 0 ? (
                <Table className="mt-3">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category</TableHead>
                      <TableHead>Date</TableHead>

                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredExpenses.map((expense) => (
                      <TableRow key={expense.id}>
                         <TableCell>{expense.category}</TableCell>
                        <TableCell>{format(expense.date, "MMM dd, yyyy")}</TableCell>

                        <TableCell className="text-right">${expense.amount.toFixed(2)}</TableCell>
                        <TableCell className="max-w-xs truncate">{expense.description || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : expenses && expenses.length > 0 && filteredExpenses?.length === 0 ? (
                <div className="text-center py-10">
                  <Search className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No matching expenses</h3>
                  <p className="text-gray-500 mb-4">Try adjusting your search term to find what you're looking for.</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setExpenseSearchInput("");
                      setExpenseSearchTerm("");
                    }}
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                <div className="text-center py-10">
                  <Receipt className="h-10 w-10 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No expenses yet</h3>
                  <p className="text-gray-500 mb-4">Start adding your expenses to track your business costs.</p>
                  <Button onClick={() => setIsExpenseDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add First Expense
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Purchase Sheet */}
      <Sheet open={isPurchaseDialogOpen} onOpenChange={setIsPurchaseDialogOpen}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader>
            <SheetTitle className="text-xl">Add New Purchase</SheetTitle>
            <SheetDescription>
              Record a new purchase from a supplier
            </SheetDescription>
          </SheetHeader>

          <Form {...purchaseForm}>
            <form onSubmit={purchaseForm.handleSubmit(onSubmitPurchase)} className="space-y-5 mt-6">
              <FormField
                control={purchaseForm.control}
                name="supplierName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Supplier Name</FormLabel>
                    <FormControl>
                      {supplierNames.length > 0 ? (
                        <div className="space-y-2">
                          <Input
                            placeholder="Enter supplier name"
                            {...field}
                            className="h-12 text-base"
                            list="supplier-list"
                          />
                          <datalist id="supplier-list">
                            {supplierNames.map((supplier) => (
                              <option key={supplier} value={supplier} />
                            ))}
                          </datalist>

                          <div className="flex flex-wrap gap-2 mt-1">
                            {supplierNames.slice(0, 4).map((supplier) => (
                              <Button
                                key={supplier}
                                type="button"
                                variant={field.value === supplier ? "default" : "outline"}
                                size="sm"
                                className="h-8 text-xs"
                                onClick={() => field.onChange(supplier)}
                              >
                                {supplier}
                              </Button>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <Input
                          placeholder="Enter supplier name"
                          {...field}
                          className="h-12 text-base"
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={purchaseForm.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Invoice Number (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter invoice number"
                        {...field}
                        className="h-12 text-base"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={purchaseForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0.00"
                        step="0.01"
                        {...field}
                        className="h-12 text-base"
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={purchaseForm.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="text-base">Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full h-12 pl-3 text-left font-normal text-base",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-5 w-5 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={purchaseForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any additional details..."
                        className="min-h-[100px] text-base"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="mt-8 flex-col gap-3 sm:flex-row">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsPurchaseDialogOpen(false)}
                  className="w-full sm:w-auto h-12 text-base"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto h-12 text-base"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Purchase"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Expense Sheet */}
      <Sheet open={isExpenseDialogOpen} onOpenChange={setIsExpenseDialogOpen}>
        <SheetContent side="right" className="w-full sm:max-w-md">
          <SheetHeader>
            <SheetTitle className="text-xl">Add New Expense</SheetTitle>
            <SheetDescription>
              Record a new business expense
            </SheetDescription>
          </SheetHeader>

          <Form {...expenseForm}>
            <form onSubmit={expenseForm.handleSubmit(onSubmitExpense)} className="space-y-5 mt-6">
              <FormField
                control={expenseForm.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Category</FormLabel>
                    <FormControl>
                      {expenseCategories.length > 0 ? (
                        <div className="grid grid-cols-2 gap-2 mt-1">
                          {expenseCategories.map((category) => (
                            <Button
                              key={category}
                              type="button"
                              variant={field.value === category ? "default" : "outline"}
                              className={cn(
                                "h-10 justify-start text-left font-normal",
                                field.value === category && "bg-primary text-primary-foreground"
                              )}
                              onClick={() => field.onChange(category)}
                            >
                              {category}
                            </Button>
                          ))}
                          <Button
                            type="button"
                            variant="outline"
                            className="h-10 justify-start text-left font-normal border-dashed"
                            onClick={() => {
                              const newCategory = window.prompt("Enter new category name");
                              if (newCategory && newCategory.trim()) {
                                field.onChange(newCategory.trim());
                                if (!expenseCategories.includes(newCategory.trim())) {
                                  setExpenseCategories([...expenseCategories, newCategory.trim()]);
                                }
                              }
                            }}
                          >
                            + Add New
                          </Button>
                        </div>
                      ) : (
                        <Input
                          placeholder="e.g., Rent, Utilities, Salaries"
                          {...field}
                          className="h-12 text-base"
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={expenseForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0.00"
                        step="0.01"
                        {...field}
                        className="h-12 text-base"
                        onChange={(e) => {
                          const value = e.target.value === "" ? undefined : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={expenseForm.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="text-base">Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full h-12 pl-3 text-left font-normal text-base",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-5 w-5 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={expenseForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any additional details..."
                        className="min-h-[100px] text-base"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <SheetFooter className="mt-8 flex-col gap-3 sm:flex-row">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsExpenseDialogOpen(false)}
                  className="w-full sm:w-auto h-12 text-base"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto h-12 text-base"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Expense"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>
    </div>
  );
}
