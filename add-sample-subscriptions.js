import pg from 'pg';
const { Pool } = pg;

// Use the same connection string as the app
const pool = new Pool({
  connectionString: '****************************************************/nemboobill?sslmode=disable'
});

async function addSampleSubscriptions() {
  console.log('Connecting to database...');
  const client = await pool.connect();
  console.log('Connected successfully!');

  try {
    console.log('Adding sample subscription data...');
    
    // First, let's check what shops and users exist
    const shopsResult = await client.query('SELECT id, name FROM shops LIMIT 5');
    const usersResult = await client.query('SELECT id, name FROM users LIMIT 5');
    const plansResult = await client.query('SELECT id, name, price FROM subscription_plans');
    
    console.log('Available shops:', shopsResult.rows);
    console.log('Available users:', usersResult.rows);
    console.log('Available plans:', plansResult.rows);
    
    if (shopsResult.rows.length === 0) {
      console.log('No shops found. Creating a sample shop first...');
      
      // Create a sample user first
      const userResult = await client.query(`
        INSERT INTO users (name, username, email, password, active) 
        VALUES ('Admin User', 'admin', '<EMAIL>', '$2b$10$hashedpassword', true)
        ON CONFLICT (username) DO UPDATE SET name = EXCLUDED.name
        RETURNING id, name;
      `);
      
      const userId = userResult.rows[0].id;
      console.log('Created/found user:', userResult.rows[0]);
      
      // Create a sample shop
      const shopResult = await client.query(`
        INSERT INTO shops (name, address, phone, shop_type, access_code, created_by) 
        VALUES ('Sample Restaurant', '123 Main St, City', '+1234567890', 'restaurant', 'SAMPLE123', $1)
        ON CONFLICT (access_code) DO UPDATE SET name = EXCLUDED.name
        RETURNING id, name;
      `, [userId]);
      
      console.log('Created/found shop:', shopResult.rows[0]);
    }
    
    // Get the shops and users again
    const finalShops = await client.query('SELECT id, name FROM shops LIMIT 3');
    const finalUsers = await client.query('SELECT id, name FROM users LIMIT 3');
    const plans = await client.query('SELECT id, name, price FROM subscription_plans ORDER BY price');
    
    if (finalShops.rows.length === 0 || finalUsers.rows.length === 0 || plans.rows.length === 0) {
      console.log('Missing required data. Cannot create subscriptions.');
      return;
    }
    
    // Create sample subscriptions for different shops
    const subscriptionsToCreate = [
      {
        shopId: finalShops.rows[0].id,
        planId: plans.rows[0].id, // Basic plan
        status: 'active',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        autoRenew: true,
        discountPercent: 0,
        discountAmount: 0,
        totalAmount: plans.rows[0].price,
        createdBy: finalUsers.rows[0].id
      },
      {
        shopId: finalShops.rows[0].id,
        planId: plans.rows[1].id, // Professional plan
        status: 'active',
        startDate: new Date('2024-06-01'),
        endDate: new Date('2025-05-31'),
        autoRenew: true,
        discountPercent: 10,
        discountAmount: plans.rows[1].price * 0.1,
        totalAmount: plans.rows[1].price * 0.9,
        createdBy: finalUsers.rows[0].id
      }
    ];
    
    // Add more subscriptions if we have more shops
    if (finalShops.rows.length > 1) {
      subscriptionsToCreate.push({
        shopId: finalShops.rows[1].id,
        planId: plans.rows[2].id, // Enterprise plan
        status: 'active',
        startDate: new Date('2024-03-01'),
        endDate: new Date('2025-02-28'),
        autoRenew: false,
        discountPercent: 15,
        discountAmount: plans.rows[2].price * 0.15,
        totalAmount: plans.rows[2].price * 0.85,
        createdBy: finalUsers.rows[0].id
      });
    }
    
    if (finalShops.rows.length > 2) {
      subscriptionsToCreate.push({
        shopId: finalShops.rows[2].id,
        planId: plans.rows[0].id, // Basic plan
        status: 'expired',
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-12-31'),
        autoRenew: false,
        discountPercent: 0,
        discountAmount: 0,
        totalAmount: plans.rows[0].price,
        createdBy: finalUsers.rows[0].id
      });
    }
    
    // Check if subscriptions already exist
    const existingSubscriptions = await client.query('SELECT COUNT(*) FROM subscriptions');
    if (parseInt(existingSubscriptions.rows[0].count) > 0) {
      console.log(`Found ${existingSubscriptions.rows[0].count} existing subscriptions. Skipping creation.`);
      return;
    }
    
    // Insert sample subscriptions
    for (const subscription of subscriptionsToCreate) {
      try {
        const result = await client.query(`
          INSERT INTO subscriptions (
            shop_id, plan_id, status, start_date, end_date, 
            auto_renew, discount_percent, discount_amount, total_amount, created_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          RETURNING id, shop_id, plan_id, status, total_amount;
        `, [
          subscription.shopId,
          subscription.planId,
          subscription.status,
          subscription.startDate,
          subscription.endDate,
          subscription.autoRenew,
          subscription.discountPercent,
          subscription.discountAmount,
          subscription.totalAmount,
          subscription.createdBy
        ]);
        
        console.log('✓ Created subscription:', result.rows[0]);
      } catch (error) {
        console.error('Error creating subscription:', error.message);
      }
    }
    
    // Show final statistics
    const finalStats = await client.query(`
      SELECT 
        COUNT(*) as total_subscriptions,
        COUNT(*) FILTER (WHERE status = 'active') as active_subscriptions,
        SUM(total_amount) as total_revenue
      FROM subscriptions;
    `);
    
    console.log('✓ Final subscription statistics:', finalStats.rows[0]);
    console.log('Sample subscription data added successfully!');
    
  } catch (error) {
    console.error('Error adding sample data:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addSampleSubscriptions();
