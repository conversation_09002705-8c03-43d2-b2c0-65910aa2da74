// Test script to verify Quick Product Search order creation
// Using built-in fetch (Node.js 18+)

const BASE_URL = 'http://localhost:5000';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MiwidXNlcm5hbWUiOiI5NTY2NTY3MTczIiwicm9sZSI6OCwiaWF0IjoxNzUwNjU0MzY0LCJleHAiOjE3NTA3NDA3NjR9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with actual token
const SHOP_ID = '12';

async function testQuickSearchOrderCreation() {
  console.log('🧪 Testing Quick Product Search Order Creation');
  console.log('='.repeat(50));

  try {
    // Step 1: Get current order count
    console.log('📊 Step 1: Getting current order count...');
    const ordersResponse = await fetch(`${BASE_URL}/api/orders/all?page=1&pageSize=10&status=all`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json'
      }
    });

    if (!ordersResponse.ok) {
      throw new Error(`Failed to fetch orders: ${ordersResponse.status}`);
    }

    const ordersData = await ordersResponse.json();
    const initialOrderCount = ordersData.total;
    console.log(`   Current order count: ${initialOrderCount}`);
    console.log(`   Most recent order: ${ordersData.orders[0]?.orderNumber || 'None'}`);

    // Step 2: Get available products
    console.log('\n🛍️ Step 2: Getting available products...');
    const productsResponse = await fetch(`${BASE_URL}/api/products`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json'
      }
    });

    if (!productsResponse.ok) {
      throw new Error(`Failed to fetch products: ${productsResponse.status}`);
    }

    const productsData = await productsResponse.json();
    console.log(`   Available products: ${productsData.length}`);
    
    if (productsData.length === 0) {
      throw new Error('No products available for testing');
    }

    const testProduct = productsData[0];
    console.log(`   Using test product: ${testProduct.name} (₹${testProduct.price})`);

    // Step 3: Get available tables
    console.log('\n🪑 Step 3: Getting available tables...');
    const tablesResponse = await fetch(`${BASE_URL}/api/tables`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json'
      }
    });

    if (!tablesResponse.ok) {
      throw new Error(`Failed to fetch tables: ${tablesResponse.status}`);
    }

    const tablesData = await tablesResponse.json();
    console.log(`   Available tables: ${tablesData.length}`);
    
    if (tablesData.length === 0) {
      throw new Error('No tables available for testing');
    }

    const testTable = tablesData.find(t => t.status === 'available') || tablesData[0];
    console.log(`   Using test table: ${testTable.name} (Status: ${testTable.status})`);

    // Step 4: Create order using the same logic as Quick Product Search
    console.log('\n🚀 Step 4: Creating order via Quick Product Search logic...');
    
    const subtotal = testProduct.price;
    const taxAmount = subtotal * 0.05; // 5% tax
    const totalAmount = subtotal + taxAmount;

    const orderData = {
      orderType: "dine_in",
      status: "pending",
      tableId: testTable.id,
      userId: 2, // From the token
      shopId: parseInt(SHOP_ID),
      branchId: null,
      numberOfPersons: 2,
      subtotal: Number(subtotal.toFixed(2)),
      taxAmount: Number(taxAmount.toFixed(2)),
      discountAmount: 0,
      totalAmount: Number(totalAmount.toFixed(2)),
      paymentMethod: "cash",
      paymentStatus: "pending",
      notes: "Test order created from Quick Product Search simulation"
    };

    const orderItems = [{
      productId: testProduct.id,
      quantity: 1,
      unitPrice: testProduct.price,
      totalPrice: testProduct.price,
      notes: "Added from Quick Product Search test"
    }];

    console.log('   Order data:', JSON.stringify(orderData, null, 2));
    console.log('   Order items:', JSON.stringify(orderItems, null, 2));

    // Step 4a: Create order (step 1)
    console.log('\n   📝 Step 4a: Creating order (step 1)...');
    const createOrderResponse = await fetch(`${BASE_URL}/api/orders/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ order: orderData })
    });

    if (!createOrderResponse.ok) {
      const errorData = await createOrderResponse.text();
      throw new Error(`Failed to create order: ${createOrderResponse.status} - ${errorData}`);
    }

    const createdOrder = await createOrderResponse.json();
    console.log(`   ✅ Order created: ${createdOrder.orderNumber} (ID: ${createdOrder.id})`);

    // Step 4b: Add items to order (step 2)
    console.log('\n   🛒 Step 4b: Adding items to order (step 2)...');
    const addItemsResponse = await fetch(`${BASE_URL}/api/orders/${createdOrder.id}/items`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ items: orderItems })
    });

    if (!addItemsResponse.ok) {
      const errorData = await addItemsResponse.text();
      throw new Error(`Failed to add items: ${addItemsResponse.status} - ${errorData}`);
    }

    const addedItems = await addItemsResponse.json();
    console.log(`   ✅ Items added: ${addedItems.length} items`);

    // Step 5: Verify order was created
    console.log('\n🔍 Step 5: Verifying order was created...');
    
    // Wait a moment for database consistency
    await new Promise(resolve => setTimeout(resolve, 1000));

    const verifyResponse = await fetch(`${BASE_URL}/api/orders/all?page=1&pageSize=10&status=all&_t=${Date.now()}`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    if (!verifyResponse.ok) {
      throw new Error(`Failed to verify orders: ${verifyResponse.status}`);
    }

    const verifyData = await verifyResponse.json();
    const newOrderCount = verifyData.total;
    const mostRecentOrder = verifyData.orders[0];

    console.log(`   Previous order count: ${initialOrderCount}`);
    console.log(`   New order count: ${newOrderCount}`);
    console.log(`   Most recent order: ${mostRecentOrder?.orderNumber || 'None'}`);
    console.log(`   Expected order: ${createdOrder.orderNumber}`);

    if (newOrderCount > initialOrderCount) {
      console.log('   ✅ Order count increased!');
    } else {
      console.log('   ❌ Order count did not increase');
    }

    if (mostRecentOrder?.orderNumber === createdOrder.orderNumber) {
      console.log('   ✅ New order appears at the top of the list!');
    } else {
      console.log('   ❌ New order does not appear at the top of the list');
    }

    // Step 6: Test the debug endpoint
    console.log('\n🔧 Step 6: Testing debug endpoint...');
    const debugResponse = await fetch(`${BASE_URL}/api/orders/recent-check`, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'X-Shop-ID': SHOP_ID,
        'Content-Type': 'application/json'
      }
    });

    if (debugResponse.ok) {
      const debugData = await debugResponse.json();
      console.log('   Debug endpoint response:', JSON.stringify(debugData, null, 2));
      
      const ourOrderInDebug = debugData.recentOrders?.find(o => o.orderNumber === createdOrder.orderNumber);
      if (ourOrderInDebug) {
        console.log('   ✅ Our order found in debug endpoint!');
      } else {
        console.log('   ❌ Our order NOT found in debug endpoint');
      }
    } else {
      console.log('   ❌ Debug endpoint failed');
    }

    console.log('\n🎉 Test completed successfully!');
    console.log(`📋 Summary: Created order ${createdOrder.orderNumber} with ${addedItems.length} items`);

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testQuickSearchOrderCreation();
