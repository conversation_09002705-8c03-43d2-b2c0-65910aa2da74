import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function checkAndCreateSubscriptionTables() {
  const client = await pool.connect();
  try {
    console.log('🔍 Checking subscription tables on remote database...\n');
    
    // Check if subscription_payment_methods table exists
    console.log('1. Checking subscription_payment_methods table...');
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'subscription_payment_methods'
      );
    `);
    
    if (tableExists.rows[0].exists) {
      console.log('   ✅ Table exists');
      
      // Check table structure
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'subscription_payment_methods' 
        ORDER BY ordinal_position;
      `);
      
      console.log('   📋 Current columns:');
      columns.rows.forEach(col => {
        console.log(`      ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
      
      // Check if required columns exist
      const requiredColumns = ['method_type', 'billing_address', 'created_by'];
      const existingColumns = columns.rows.map(col => col.column_name);
      const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
      
      if (missingColumns.length > 0) {
        console.log('   ⚠️  Missing required columns:', missingColumns.join(', '));
        console.log('   🔧 Recreating table with correct structure...');
        
        // Drop and recreate table
        await client.query('DROP TABLE IF EXISTS subscription_payment_methods CASCADE;');
        await createTable(client);
      } else {
        console.log('   ✅ Table structure is correct');
      }
    } else {
      console.log('   ❌ Table does not exist');
      console.log('   🔧 Creating table...');
      await createTable(client);
    }
    
    // Check other subscription tables
    console.log('\n2. Checking other subscription tables...');
    const otherTables = ['subscription_plans', 'subscriptions'];
    
    for (const tableName of otherTables) {
      const exists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        );
      `, [tableName]);
      
      console.log(`   ${tableName}: ${exists.rows[0].exists ? '✅' : '❌'}`);
    }
    
    console.log('\n🎉 Subscription tables check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

async function createTable(client) {
  await client.query(`
    CREATE TABLE subscription_payment_methods (
      id SERIAL PRIMARY KEY,
      shop_id INTEGER NOT NULL REFERENCES shops(id),
      method_type TEXT NOT NULL DEFAULT 'card',
      card_number TEXT,
      card_holder_name TEXT,
      expiry_month INTEGER,
      expiry_year INTEGER,
      billing_address TEXT,
      is_default BOOLEAN DEFAULT false NOT NULL,
      active BOOLEAN DEFAULT true NOT NULL,
      created_at TIMESTAMP DEFAULT NOW() NOT NULL,
      updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
      created_by INTEGER NOT NULL REFERENCES users(id)
    );
  `);
  console.log('   ✅ Table created successfully');
}

checkAndCreateSubscriptionTables();
