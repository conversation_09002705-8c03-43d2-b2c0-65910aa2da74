import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { RefreshCw, Clock, ChefHat, CheckCircle, XCircle, FileText, Pause } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { apiRequest } from "@/lib/queryClient";

interface OrderStatusSummaryProps {
  refreshInterval?: number;
  onRefresh?: () => void;
}

interface StatusSummary {
  pending: number;
  preparing: number;
  ready: number;
  completed: number;
  cancelled: number;
  draft: number;
  hold: number;
  total: number;
}

const statusConfig = {
  pending: {
    label: "New Orders",
    description: "Orders waiting to start",
    icon: Clock,
    color: "bg-blue-50 text-blue-700 border-blue-200",
    iconColor: "text-blue-600",
    bgColor: "bg-blue-50",
    priority: 1
  },
  preparing: {
    label: "In Kitchen",
    description: "Currently being prepared",
    icon: ChefHat,
    color: "bg-amber-50 text-amber-700 border-amber-200",
    iconColor: "text-amber-600",
    bgColor: "bg-amber-50",
    priority: 2
  },
  ready: {
    label: "Ready to Serve",
    description: "Ready for pickup/delivery",
    icon: CheckCircle,
    color: "bg-orange-50 text-orange-700 border-orange-200",
    iconColor: "text-orange-600",
    bgColor: "bg-orange-50",
    priority: 3
  },
  completed: {
    label: "Completed",
    description: "Successfully delivered",
    icon: CheckCircle,
    color: "bg-green-50 text-green-700 border-green-200",
    iconColor: "text-green-600",
    bgColor: "bg-green-50",
    priority: 4
  },
  cancelled: {
    label: "Cancelled",
    description: "Orders that were cancelled",
    icon: XCircle,
    color: "bg-red-50 text-red-700 border-red-200",
    iconColor: "text-red-600",
    bgColor: "bg-red-50",
    priority: 6
  },
  draft: {
    label: "Draft Orders",
    description: "Saved but not finalized",
    icon: FileText,
    color: "bg-gray-50 text-gray-700 border-gray-200",
    iconColor: "text-gray-600",
    bgColor: "bg-gray-50",
    priority: 7
  },
  hold: {
    label: "On Hold",
    description: "Temporarily paused orders",
    icon: Pause,
    color: "bg-purple-50 text-purple-700 border-purple-200",
    iconColor: "text-purple-600",
    bgColor: "bg-purple-50",
    priority: 5
  }
};

export default function OrderStatusSummary({
  refreshInterval = 30000,
  onRefresh
}: OrderStatusSummaryProps) {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch order status summary from API
  const {
    data: statusSummary,
    isLoading,
    error,
    refetch: refetchStatusSummary
  } = useQuery({
    queryKey: ['/api/orders/status-summary'],
    queryFn: async () => {
      const response = await apiRequest("GET", "/api/orders/status-summary");
      if (!response.ok) {
        throw new Error("Failed to fetch order status summary");
      }
      return response.json();
    },
    enabled: !!token && !!currentShop,
    refetchInterval: refreshInterval,
  });

  // Handle manual refresh
  const handleRefresh = async () => {
    if (!isRefreshing) {
      setIsRefreshing(true);
      await refetchStatusSummary();
      setLastUpdated(new Date());
      if (onRefresh) {
        onRefresh();
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };

  // Update last updated time when data changes
  useEffect(() => {
    if (statusSummary) {
      setLastUpdated(new Date());
    }
  }, [statusSummary]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900">📊 Today's Order Status</h2>
            <Skeleton className="h-4 w-64 mt-1" />
          </div>
          <div className="flex items-center gap-3">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
          {Array(7).fill(0).map((_, index) => (
            <Card key={index} className="border-l-4">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <Skeleton className="h-9 w-9 rounded-lg" />
                  <Skeleton className="h-5 w-10 rounded-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-3 w-24" />
                  <div className="flex items-baseline gap-1">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900">📊 Today's Order Status</h2>
            <p className="text-sm text-red-600 mt-1">
              Unable to load order data
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Try Again
          </Button>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="p-3 bg-red-100 rounded-full">
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
              <div>
                <h3 className="font-medium text-red-900">Connection Problem</h3>
                <p className="text-sm text-red-700 mt-1">
                  We couldn't load the order status data. Please check your connection and try again.
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="mt-2 border-red-300 text-red-700 hover:bg-red-100"
              >
                <RefreshCw className={cn("h-4 w-4 mr-2", isRefreshing && "animate-spin")} />
                Retry Now
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const summary = statusSummary as StatusSummary;

  // Sort statuses by priority for better user experience
  const sortedStatuses = Object.entries(statusConfig).sort(([, a], [, b]) => a.priority - b.priority);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-bold text-gray-900">📊 Today's Order Status</h2>
          <p className="text-sm text-gray-600 mt-1">
            Real-time overview of all orders • Updated {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-sm text-gray-500">
            Total: <span className="font-semibold text-gray-900">{summary.total}</span> orders
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Refresh
          </Button>
        </div>
      </div>

      {summary.total > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
          {sortedStatuses.map(([status, config]) => {
            const count = summary[status as keyof StatusSummary] || 0;
            const Icon = config.icon;
            const percentage = summary.total > 0 ? Math.round((count / summary.total) * 100) : 0;

            return (
              <Card
                key={status}
                className={cn(
                  "hover:shadow-lg transition-all duration-200 border-l-4 cursor-pointer",
                  config.bgColor,
                  count > 0 ? "shadow-sm" : "opacity-75"
                )}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className={cn("p-2 rounded-lg", config.bgColor)}>
                      <Icon className={cn("h-5 w-5", config.iconColor)} />
                    </div>
                    <Badge
                      variant="secondary"
                      className={cn("text-xs font-medium", config.color)}
                    >
                      {percentage}%
                    </Badge>
                  </div>

                  <div className="space-y-1">
                    <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                      {config.label}
                    </h3>
                    <p className="text-xs text-gray-500 leading-tight">
                      {config.description}
                    </p>
                    <div className="flex items-baseline gap-1">
                      <span className="text-2xl font-bold text-gray-900">
                        {count}
                      </span>
                      <span className="text-xs text-gray-500">
                        {count === 1 ? 'order' : 'orders'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card className="border-dashed border-2 border-gray-200">
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="p-3 bg-gray-100 rounded-full">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">No orders today yet</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Orders will appear here as they come in throughout the day
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/billing/pos'}
                className="mt-2"
              >
                Create First Order
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
