-- Drop table if exists to recreate it properly
DROP TABLE IF EXISTS support_tickets;

-- Create support_tickets table
CREATE TABLE support_tickets (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  priority TEXT DEFAULT 'medium' NOT NULL CHECK (priority IN ('low', 'medium', 'high')),
  category TEXT DEFAULT 'general' NOT NULL CHECK (category IN ('technical', 'billing', 'feature', 'general')),
  status TEXT DEFAULT 'open' NOT NULL CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
  user_id INTEGER REFERENCES users(id),
  shop_id INTEGER REFERENCES shops(id),
  branch_id INTEGER REFERENCES branches(id),
  assigned_to INTEGER REFERENCES users(id),
  resolution TEXT,
  resolved_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_support_tickets_email ON support_tickets(email);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_category ON support_tickets(category);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at);
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_shop_id ON support_tickets(shop_id);

-- Add comments for documentation
COMMENT ON TABLE support_tickets IS 'Support tickets submitted by users for help and assistance';
COMMENT ON COLUMN support_tickets.priority IS 'Priority level: low, medium, high';
COMMENT ON COLUMN support_tickets.category IS 'Category: technical, billing, feature, general';
COMMENT ON COLUMN support_tickets.status IS 'Status: open, in_progress, resolved, closed';
COMMENT ON COLUMN support_tickets.user_id IS 'Optional - ID of authenticated user who submitted the ticket';
COMMENT ON COLUMN support_tickets.shop_id IS 'Optional - Shop associated with the user';
COMMENT ON COLUMN support_tickets.branch_id IS 'Optional - Branch associated with the user';
COMMENT ON COLUMN support_tickets.assigned_to IS 'Optional - Support agent assigned to handle the ticket';
COMMENT ON COLUMN support_tickets.resolution IS 'Resolution notes when ticket is resolved';
COMMENT ON COLUMN support_tickets.resolved_at IS 'Timestamp when ticket was resolved';
