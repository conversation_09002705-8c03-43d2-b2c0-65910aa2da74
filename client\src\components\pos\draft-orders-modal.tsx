import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, FileEdit, Loader2, RefreshCw } from "lucide-react";

interface Order {
  id: number;
  orderNumber: string;
  orderType: string;
  status: string;
  tableId: number | null;
  customerId: number | null;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  createdAt: string;
  items: OrderItem[];
}

interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

interface DraftOrdersModalProps {
  isOpen: boolean;
  onClose: () => void;
  onResumeOrder: (order: Order) => void;
}

export function DraftOrdersModal({
  isOpen,
  onClose,
  onResumeOrder
}: DraftOrdersModalProps) {
  const [activeTab, setActiveTab] = useState("draft");

  // Fetch draft and hold orders with proper status parameter
  const { data: orders, isLoading, refetch } = useQuery({
    queryKey: ['/api/orders/all', activeTab],
    queryFn: async () => {
      const response = await apiRequest(
        "GET",
        `/api/orders/all?page=1&pageSize=50&status=${activeTab}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }
      return response.json();
    },
    enabled: isOpen,
  });

  // Get orders from the response
  const filteredOrders = orders?.orders || [];

  // Handle resume order - fetch complete order details with items and delete draft
  const handleResumeOrder = async (order: Order) => {
    try {
      // Fetch complete order details with items
      const response = await apiRequest("GET", `/api/orders/${order.id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch order details");
      }
      const orderData = await response.json();

      // Pass the complete order data with items to the parent
      onResumeOrder({
        ...orderData.order,
        items: orderData.items || []
      });

      // Delete the draft order after successful resumption
      try {
        const deleteResponse = await apiRequest("DELETE", `/api/orders/${order.id}`);
        if (deleteResponse.ok) {
          console.log("Draft order deleted successfully");
          // Refetch the orders to update the list
          refetch();
        } else {
          console.warn("Failed to delete draft order, but order was resumed successfully");
        }
      } catch (deleteError) {
        console.warn("Error deleting draft order:", deleteError);
        // Don't fail the resume operation if delete fails
      }

      onClose();
    } catch (error) {
      console.error("Error fetching order details:", error);
      // Fallback to basic order data if fetch fails
      onResumeOrder(order);
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] h-[80vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0 pb-4">
          <DialogTitle className="flex items-center justify-between">
            <span>Saved Orders</span>
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="draft" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
          <TabsList className="grid grid-cols-2 mb-4 flex-shrink-0">
            <TabsTrigger value="draft" className="flex items-center">
              <FileEdit className="h-4 w-4 mr-2" />
              Draft Orders
            </TabsTrigger>
            <TabsTrigger value="hold" className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              On Hold Orders
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <TabsContent value="draft" className="mt-0 pr-4">
                {isLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : filteredOrders.length === 0 ? (
                  <div className="text-center p-8 text-muted-foreground">
                    No draft orders found
                  </div>
                ) : (
                  <div className="space-y-4 pb-4">
                    {filteredOrders.map((order: Order) => (
                      <OrderCard
                        key={order.id}
                        order={order}
                        onResume={() => handleResumeOrder(order)}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="hold" className="mt-0 pr-4">
                {isLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : filteredOrders.length === 0 ? (
                  <div className="text-center p-8 text-muted-foreground">
                    No orders on hold
                  </div>
                ) : (
                  <div className="space-y-4 pb-4">
                    {filteredOrders.map((order: Order) => (
                      <OrderCard
                        key={order.id}
                        order={order}
                        onResume={() => handleResumeOrder(order)}
                      />
                    ))}
                  </div>
                )}
              </TabsContent>
            </ScrollArea>
          </div>
        </Tabs>

        <DialogFooter className="flex-shrink-0 pt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface OrderCardProps {
  order: Order;
  onResume: () => void;
}

function OrderCard({ order, onResume }: OrderCardProps) {
  // Format date
  const formattedDate = new Date(order.createdAt).toLocaleString();

  // Get order type display name
  const orderTypeDisplay =
    order.orderType === "dine_in" ? "Table" :
    order.orderType === "takeaway" ? "Parcel" :
    order.orderType === "eat_and_pay" ? "Eat & Pay" : "Online";

  return (
    <div className="border rounded-lg p-4 hover:border-primary transition-colors">
      <div className="flex justify-between items-start mb-2">
        <div>
          <h3 className="font-medium">{order.orderNumber}</h3>
          <p className="text-sm text-muted-foreground">{formattedDate}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">{orderTypeDisplay}</Badge>
          <Badge variant={order.status === "draft" ? "secondary" : "default"}>
            {order.status === "draft" ? "Draft" : "On Hold"}
          </Badge>
        </div>
      </div>

      <div className="space-y-2 mt-4">
        <div className="text-sm">
          <span className="text-muted-foreground">Items:</span> {order.items?.length || 0}
        </div>
        <div className="text-sm">
          <span className="text-muted-foreground">Total:</span> ₹{order.totalAmount.toFixed(2)}
        </div>
      </div>

      <Separator className="my-3" />

      <div className="flex justify-end">
        <Button onClick={onResume}>
          Resume Order
        </Button>
      </div>
    </div>
  );
}
