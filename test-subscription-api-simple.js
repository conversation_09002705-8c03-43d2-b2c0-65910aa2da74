import fetch from 'node-fetch';

async function testSubscriptionAPI() {
  try {
    console.log('🔍 Testing subscription API endpoints...\n');
    
    // Test 1: Check if server is running
    console.log('1. Testing server connection...');
    try {
      const healthResponse = await fetch('http://localhost:5000/api/test', {
        method: 'GET',
        timeout: 5000
      });
      console.log(`   Server status: ${healthResponse.status}`);
      if (healthResponse.ok) {
        const healthData = await healthResponse.text();
        console.log(`   Server response: ${healthData}`);
      }
    } catch (error) {
      console.log(`   ❌ Server connection failed: ${error.message}`);
      console.log('   Make sure the server is running on port 5000');
      return;
    }
    
    // Test 2: Test subscription plans endpoint (without auth)
    console.log('\n2. Testing subscription plans endpoint (no auth)...');
    try {
      const plansResponse = await fetch('http://localhost:5000/api/subscription-plans', {
        method: 'GET',
        timeout: 5000
      });
      console.log(`   Plans endpoint status: ${plansResponse.status}`);
      
      if (plansResponse.status === 401) {
        console.log('   ✓ Endpoint requires authentication (expected)');
      } else if (plansResponse.ok) {
        const plansData = await plansResponse.json();
        console.log(`   ✓ Plans loaded: ${plansData.length} plans found`);
      } else {
        const errorText = await plansResponse.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ Plans endpoint failed: ${error.message}`);
    }
    
    // Test 3: Test subscriptions endpoint (without auth)
    console.log('\n3. Testing subscriptions endpoint (no auth)...');
    try {
      const subscriptionsResponse = await fetch('http://localhost:5000/api/subscriptions', {
        method: 'GET',
        timeout: 5000
      });
      console.log(`   Subscriptions endpoint status: ${subscriptionsResponse.status}`);
      
      if (subscriptionsResponse.status === 401) {
        console.log('   ✓ Endpoint requires authentication (expected)');
      } else if (subscriptionsResponse.ok) {
        const subscriptionsData = await subscriptionsResponse.json();
        console.log(`   ✓ Subscriptions loaded: ${subscriptionsData.length} subscriptions found`);
      } else {
        const errorText = await subscriptionsResponse.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ Subscriptions endpoint failed: ${error.message}`);
    }
    
    console.log('\n📋 Test Summary:');
    console.log('- If server connection failed: Start the server with "npm run dev"');
    console.log('- If endpoints return 401: Authentication is working correctly');
    console.log('- The frontend should handle authentication automatically');
    console.log('\n✅ API test completed');
    
  } catch (error) {
    console.error('❌ Test script failed:', error);
  }
}

// Run the test
testSubscriptionAPI();
