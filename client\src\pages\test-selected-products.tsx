import React, { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SelectedProductsWidget } from '@/components/selected-products-widget';

export default function TestSelectedProductsPage() {
  const [, setLocation] = useLocation();
  const [localStorageData, setLocalStorageData] = useState<string>('');

  const checkLocalStorage = () => {
    const data = localStorage.getItem('selectedProducts');
    setLocalStorageData(data || 'No data found');
    console.log('localStorage data:', data);
  };

  const addTestProduct = () => {
    const testProduct = {
      id: 999,
      name: 'Test Product',
      price: 25.00,
      categoryName: 'Test Category'
    };

    const existingData = localStorage.getItem('selectedProducts');
    let products = [];

    if (existingData) {
      try {
        products = JSON.parse(existingData);
      } catch (e) {
        products = [];
      }
    }

    products.push(testProduct);
    localStorage.setItem('selectedProducts', JSON.stringify(products));
    checkLocalStorage();
    console.log('Added test product to localStorage');
  };

  const clearLocalStorage = () => {
    localStorage.removeItem('selectedProducts');
    checkLocalStorage();
    console.log('Cleared localStorage');
  };

  useEffect(() => {
    checkLocalStorage();
  }, []);

  return (
    <div className="container px-4 py-6 md:px-6 md:py-8">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={() => setLocation('/utilities')}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Test Selected Products</h1>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Selected Products Test Page</CardTitle>
            <CardDescription>
              This page demonstrates that selected products persist across page navigation.
              Go back to Utilities, select some products, then return here to see them.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Steps to test:
              </p>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Go back to Utilities page</li>
                <li>Use Quick Product Search to search for products (e.g., type "co")</li>
                <li>Click on products to select them</li>
                <li>Navigate back to this page</li>
                <li>Your selected products should still be visible below</li>
              </ol>

              <div className="mt-4 p-3 bg-gray-100 rounded">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">localStorage Debug:</h4>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={addTestProduct}>
                      Add Test
                    </Button>
                    <Button variant="outline" size="sm" onClick={clearLocalStorage}>
                      Clear
                    </Button>
                    <Button variant="outline" size="sm" onClick={checkLocalStorage}>
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Refresh
                    </Button>
                  </div>
                </div>
                <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-32">
                  {localStorageData}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Selected Products Widget */}
        <SelectedProductsWidget />

        <Card>
          <CardHeader>
            <CardTitle>Compact Version</CardTitle>
            <CardDescription>
              This is how the selected products appear in compact mode (used on Dashboard and POS)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SelectedProductsWidget compact={true} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
