import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function createRefundsTable() {
  const client = await pool.connect();
  try {
    console.log('Creating refunds table...');

    // Read the SQL file
    const sqlPath = path.join(__dirname, '..', 'create-refunds-table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL
    await client.query(sql);

    console.log('Refunds table created successfully!');
  } catch (error) {
    console.error('Error creating refunds table:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createRefundsTable();
