import { Request, Response } from "express";
import jwt from "jsonwebtoken";
import { storage } from "./storage";

// Create a JWT secret
const JWT_SECRET = process.env.JWT_SECRET || "nemboobill-secret-key";

// Storage instance is already imported from "./storage"

// Auth middleware to check JWT token
export const authMiddleware = (req: Request, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Access token is required' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    (req as any).user = decoded;

    // Extract shop ID from headers if available
    const shopId = req.headers['x-shop-id'];
    const requestPath = req.path || req.url || '';

    if (shopId) {
      console.log(`Request to ${requestPath} with shop ID: ${shopId}`);
      (req as any).shopId = parseInt(shopId as string);
    } else {
      console.log(`Request to ${requestPath} without shop ID`);
      // For endpoints that require shop ID, log a warning
      if (typeof requestPath === 'string' && (requestPath === '/api/branches' || requestPath.startsWith('/api/products') || requestPath.startsWith('/api/inventory'))) {
        console.warn(`Warning: ${requestPath} endpoint called without shop ID header`);
      }
    }

    // Extract branch ID from headers if available
    const branchId = req.headers['x-branch-id'];
    if (branchId) {
      console.log(`Request to ${requestPath} with branch ID: ${branchId}`);
      (req as any).branchId = parseInt(branchId as string);
    } else {
      console.log(`Request to ${requestPath} without branch ID`);
    }

    next();
  } catch (error) {
    return res.status(403).json({ message: 'Invalid or expired token' });
  }
};

// Permission middleware to check if user has required permission
export const permissionMiddleware = (requiredPermission: string) => {
  return async (req: Request, res: Response, next: Function) => {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Get user with role
      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }

      // If user has no role, deny access
      if (!user.roleId) {
        return res.status(403).json({ message: 'No role assigned' });
      }

      // Get user's role
      const role = await storage.getRoleById(user.roleId);

      if (!role) {
        return res.status(403).json({ message: 'Role not found' });
      }

      // Check if user's role has the required permission
      const hasPermission = role.permissions.includes(requiredPermission);

      if (!hasPermission) {
        return res.status(403).json({
          message: 'Permission denied',
          requiredPermission
        });
      }

      // User has permission, proceed
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({ message: 'Internal server error during permission check' });
    }
  };
};
