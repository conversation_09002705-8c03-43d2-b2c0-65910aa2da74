import { Switch, Route, useLocation } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider, useAuth } from "./context/auth-context";
import { AppProvider, useApp } from "./context/app-context";
import { SocketProvider } from "./context/socket-context";
import { NetworkStatusProvider } from "./context/network-status-context";
import React, { useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

// Layout components
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import MobileNav from "@/components/layout/mobile-nav";
import FullScreenLayout from "@/components/layout/full-screen-layout";

// Pages
import Login from "@/pages/login";
import OtpLogin from "@/pages/otp-login";
import Register from "@/pages/register";
import ForgotPassword from "@/pages/forgot-password";
import ResetPassword from "@/pages/reset-password";
import Dashboard from "@/pages/dashboard";

import Orders from "@/pages/billing/orders";
import POSPage from "@/pages/billing/pos";
import MobilePOSPage from "@/pages/billing/mobile-pos";
import SalesReport from "@/pages/reports/sales";
import TaxReport from "@/pages/reports/tax";
import PaymentsReport from "@/pages/reports/payments";
import InventoryReport from "@/pages/reports/inventory";
import InventoryDashboard from "@/pages/inventory";
import StockManagement from "@/pages/inventory/stock";
import StockTransfers from "@/pages/inventory/transfers";
import StockMovements from "@/pages/inventory/movements";
import Products from "@/pages/masters/products";
import Tables from "@/pages/masters/tables";
import Customers from "@/pages/masters/customers";
import Categories from "@/pages/masters/categories";
import PaymentMethods from "@/pages/masters/payment-methods";
import Platforms from "@/pages/masters/platforms";
import Settings from "@/pages/settings";
import ShopSettings from "@/pages/settings/shop";
import Users from "@/pages/settings/users";
import ProfileSettings from "@/pages/settings/profile";
import MobileSettings from "@/pages/settings/mobile";
import NotFound from "@/pages/not-found";
import CreateShop from "@/pages/create-shop";
import JoinShop from "@/pages/join-shop";
import SelectShop from "@/pages/select-shop";
import Support from "@/pages/support-simple";
import Purchases from "@/pages/purchases";
import PaymentManagement from "@/pages/payments";
import MorePage from "@/pages/more";
import ExpenseCategories from "@/pages/expense-categories";
import Branches from "@/pages/branches";
import SettingsBranchesPage from "@/pages/settings/branches";
import MobileBranches from "@/pages/mobile-branches";
import MobileReports from "@/pages/mobile-reports";
import Utilities from "@/pages/utilities";
import TestSelectedProductsPage from "@/pages/test-selected-products";
import { FloatingUtilityButton } from "@/components/utilities/floating-utility-button";
import SubscriptionManagement from "@/pages/subscription";
import SubscriptionDocs from "@/pages/subscription-docs";
import LandingPage from "@/pages/landing";
import SimpleLandingPage from "@/pages/simple-landing";
import NotificationSettingsPage from "@/pages/notification-settings";
import NotificationTestPage from "@/pages/notification-test";
import NotificationDebugPage from "@/pages/notification-debug";

import { Skeleton } from "@/components/ui/skeleton";

function AuthenticatedRoute({ children }: { children: React.ReactNode }) {

  // Real authentication implementation
  const { isAuthenticated, isLoading, hasShops, tokenChecked } = useAuth();
  const { currentShop } = useApp();
  const [location, setLocation] = useLocation();
  const currentPath = location;

  useEffect(() => {
    // Only perform redirects if token check is complete
    if (tokenChecked && !isLoading) {
      if (!isAuthenticated) {
        // Not authenticated, redirect to login
        console.log("Not authenticated, redirecting to login");
        setLocation("/login");
      } else if (isAuthenticated && hasShops && !currentShop &&
                currentPath && typeof currentPath === 'string' &&
                !currentPath.startsWith("/select-shop") &&
                !currentPath.startsWith("/create-shop") &&
                !currentPath.startsWith("/join-shop")) {
        // Authenticated with shops but no current shop selected
        console.log("Authenticated with shops but no current shop, redirecting to select-shop");
        setLocation("/select-shop");
      } else if (isAuthenticated && !hasShops &&
                currentPath && typeof currentPath === 'string' &&
                !currentPath.startsWith("/create-shop") &&
                !currentPath.startsWith("/join-shop")) {
        // Authenticated but no shops, redirect to create shop
        setLocation("/create-shop");
      }
    }
  }, [isAuthenticated, isLoading, hasShops, currentShop, currentPath, setLocation, tokenChecked]);

  // Show loading state if we're still loading or haven't completed token check
  if (isLoading || !tokenChecked) {
    // Return the AppLayout with a simple loading indicator instead of blocking everything
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Only check authentication after token check is complete
  if (tokenChecked && !isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}

function AuthLayout({ children }: { children: React.ReactNode }) {
  // Check if the current route is login
  const [location] = useLocation();
  const isLoginPage = location === "/login";

  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-background to-muted/50 bg-[url('https://img.freepik.com/free-photo/accounting-finance-business-payment-concept_53876-133697.jpg')] bg-cover bg-fixed p-4 relative">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1E3A70]/80 to-[#2A4580]/70 z-0"></div>

      {/* Pattern overlay */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wMiI+PHBhdGggZD0iTTM2IDM0djZoNnYtNmgtNnptNi02aDZ2LTZoLTZ2NnptLTYgMGg2di02aC02djZ6bTYgNmg2di02aC02djZ6bS0xMi0xMmg2di02aC02djZ6bTYgMTJoNnYtNmgtNnY2em0tNi02aDZ2LTZoLTZ2NnptMC02aDZ2LTZoLTZ2NnptLTYgMTJoNnYtNmgtNnY2em0wLTEyaDZ2LTZoLTZ2NnptMC02aDZ2LTZoLTZ2NnptLTYgMTJoNnYtNmgtNnY2em0wLTEyaDZ2LTZoLTZ2NnptLTYgMGg2di02aC02djZ6bTYgMTJoNnYtNmgtNnY2em0tNiAwaDZ2LTZoLTZ2NnoiLz48L2c+PC9nPjwvc3ZnPg==')] bg-fixed opacity-30 z-5"></div>

      <div className={`w-full ${isLoginPage ? 'max-w-5xl' : 'max-w-md'} relative z-10`}>
        {!isLoginPage && (
          <div className="text-center mb-6">
            <div className="flex items-center justify-center mb-4">
              <span className="bg-gradient-to-br from-primary to-secondary text-primary-foreground p-3 rounded-xl shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </span>
              <div className="ml-3">
                <span className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">NembooBill</span>
                <h2 className="text-muted-foreground text-sm">POS & Billing System</h2>
              </div>
            </div>
          </div>
        )}
        {children}
      </div>
    </div>
  );
}

function AppLayout({ children }: { children: React.ReactNode }) {
  // Use real app context
  const { isSidebarOpen, closeSidebar } = useApp();
  const isMobile = useIsMobile();

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Mobile overlay when sidebar is open */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={closeSidebar}
        ></div>
      )}

      {/* Only render sidebar on non-mobile devices */}
      {!isMobile && <Sidebar />}

      <main className="flex-1 flex flex-col h-full overflow-hidden">
        <Header />

        <div className="flex-1 overflow-y-auto p-6 bg-background bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDYwIDYwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwMDAwMDAiIGZpbGwtb3BhY2l0eT0iMC4wMyI+PHBhdGggZD0iTTM2IDM0djZoNnYtNmgtNnptNi02aDZ2LTZoLTZ2NnptLTYgMGg2di02aC02djZ6bTYgNmg2di02aC02djZ6bS0xMi0xMmg2di02aC02djZ6bTYgMTJoNnYtNmgtNnY2em0tNi02aDZ2LTZoLTZ2NnptMC02aDZ2LTZoLTZ2NnptLTYgMTJoNnYtNmgtNnY2em0wLTEyaDZ2LTZoLTZ2NnptMC02aDZ2LTZoLTZ2NnptLTYgMTJoNnYtNmgtNnY2em0wLTEyaDZ2LTZoLTZ2NnptLTYgMGg2di02aC02djZ6bTYgMTJoNnYtNmgtNnY2em0tNiAwaDZ2LTZoLTZ2NnoiLz48L2c+PC9nPjwvc3ZnPg==')] bg-fixed bg-gradient-to-br from-background via-background/95 to-muted/30">
          {children}
        </div>

        {/* Floating utility button for mobile */}
        {isMobile && <FloatingUtilityButton />}
      </main>

      <MobileNav />
    </div>
  );
}

function DebugAuthState() {
  const { isAuthenticated, isLoading, tokenChecked, token, user } = useAuth();
  const [, setLocation] = useLocation();

  return (
    <div className="min-h-screen p-8 bg-gray-100">
      <h1 className="text-2xl font-bold mb-4">Debug Auth State</h1>
      <div className="bg-white p-4 rounded shadow">
        <p><strong>isAuthenticated:</strong> {isAuthenticated ? 'true' : 'false'}</p>
        <p><strong>isLoading:</strong> {isLoading ? 'true' : 'false'}</p>
        <p><strong>tokenChecked:</strong> {tokenChecked ? 'true' : 'false'}</p>
        <p><strong>token:</strong> {token ? 'exists' : 'null'}</p>
        <p><strong>user:</strong> {user ? user.name : 'null'}</p>
      </div>
      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-4">🚀 Test All Landing Page Routes:</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          <button
            onClick={() => setLocation('/landing')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
          >
            📄 Landing Page
          </button>
          <button
            onClick={() => setLocation('/simple')}
            className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
          >
            ⚡ Simple Landing
          </button>
          <button
            onClick={() => setLocation('/welcome')}
            className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
          >
            👋 Welcome Page
          </button>
          <button
            onClick={() => setLocation('/')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
          >
            🏠 Home Route
          </button>
          <button
            onClick={() => setLocation('/test')}
            className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
          >
            🧪 Test Route
          </button>
          <button
            onClick={() => setLocation('/login')}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors"
          >
            🔐 Login Page
          </button>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>✅ All routes are now set up!</strong> The landing page should be accessible at any of the above routes.
          </p>
        </div>
      </div>
    </div>
  );
}

function LogoutHandler() {
  const { logout } = useAuth();
  const [, setLocation] = useLocation();

  // Clear auth and redirect to landing
  logout();
  setLocation('/landing');

  return <LandingPage />;
}

function ClearAuthAndShowLanding() {
  const [, setLocation] = useLocation();

  // Clear localStorage and redirect to landing page
  localStorage.removeItem("auth_token");
  localStorage.removeItem("current_shop");
  localStorage.removeItem("current_branch");

  // Redirect to landing page with proper URL
  useEffect(() => {
    setLocation('/landing');
  }, [setLocation]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Clearing authentication and redirecting to landing page...</p>
      </div>
    </div>
  );
}

function RedirectToLanding() {
  const [, setLocation] = useLocation();

  useEffect(() => {
    setLocation('/landing');
  }, [setLocation]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to landing page...</p>
      </div>
    </div>
  );
}

function Router() {
  const { isAuthenticated, isLoading, tokenChecked } = useAuth();

  return (
    <Switch>
      {/* LANDING PAGE ROUTES - Highest priority, always accessible */}
      <Route path="/landing" component={LandingPage} />
      <Route path="/welcome" component={LandingPage} />
      <Route path="/home" component={LandingPage} />
      <Route path="/simple" component={SimpleLandingPage} />
      <Route path="/force-landing" component={LandingPage} />

      {/* CLEAR AUTH AND SHOW LANDING */}
      <Route path="/clear" component={ClearAuthAndShowLanding} />

      {/* ROOT ROUTE - Redirect to landing page when not authenticated */}
      <Route path="/">
        {!tokenChecked || isLoading ? (
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading NembooBill...</p>
            </div>
          </div>
        ) : isAuthenticated ? (
          <AuthenticatedRoute>
            <AppLayout>
              <Dashboard />
            </AppLayout>
          </AuthenticatedRoute>
        ) : (
          <RedirectToLanding />
        )}
      </Route>

      {/* DEBUG AND TEST ROUTES */}
      <Route path="/debug">
        <DebugAuthState />
      </Route>

      <Route path="/test">
        <div className="min-h-screen bg-blue-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-blue-600 mb-4">✅ Routing Works!</h1>
            <p className="text-gray-600 mb-8">All routes are functional</p>
            <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
              <button
                onClick={() => window.location.href = '/landing'}
                className="bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700"
              >
                Landing Page
              </button>
              <button
                onClick={() => window.location.href = '/simple'}
                className="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700"
              >
                Simple Landing
              </button>
              <button
                onClick={() => window.location.href = '/welcome'}
                className="bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700"
              >
                Welcome Page
              </button>
              <button
                onClick={() => window.location.href = '/debug'}
                className="bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700"
              >
                Debug Page
              </button>
            </div>
          </div>
        </div>
      </Route>

      <Route path="/logout">
        <LogoutHandler />
      </Route>

      {/* Auth Routes */}
      <Route path="/login">
        {/* Don't redirect from login page if already on it */}
        <AuthLayout>
          <Login />
        </AuthLayout>
      </Route>
      <Route path="/otp-login">
        <AuthLayout>
          <OtpLogin />
        </AuthLayout>
      </Route>
      <Route path="/register">
        <AuthLayout>
          <Register />
        </AuthLayout>
      </Route>
      <Route path="/forgot-password">
        <AuthLayout>
          <ForgotPassword />
        </AuthLayout>
      </Route>
      <Route path="/reset-password">
        <AuthLayout>
          <ResetPassword />
        </AuthLayout>
      </Route>

      {/* Shop Management Routes */}
      <Route path="/select-shop">
        <AuthenticatedRoute>
          <FullScreenLayout>
            <SelectShop />
          </FullScreenLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/create-shop">
        <AuthenticatedRoute>
          <AuthLayout>
            <CreateShop />
          </AuthLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/join-shop">
        <AuthenticatedRoute>
          <AuthLayout>
            <JoinShop />
          </AuthLayout>
        </AuthenticatedRoute>
      </Route>

      {/* Protected Routes */}

      <Route path="/dashboard">
        <AuthenticatedRoute>
          <AppLayout>
            <Dashboard />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>


      <Route path="/billing/orders">
        <AuthenticatedRoute>
          <AppLayout>
            <Orders />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/billing/pos">
        <AuthenticatedRoute>
          <FullScreenLayout>
            <POSPage />
          </FullScreenLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/billing/mobile-pos">
        <AuthenticatedRoute>
          <FullScreenLayout>
            <MobilePOSPage />
          </FullScreenLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/reports/sales">
        <AuthenticatedRoute>
          <AppLayout>
            <SalesReport />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/reports/tax">
        <AuthenticatedRoute>
          <AppLayout>
            <TaxReport />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/reports/payments">
        <AuthenticatedRoute>
          <AppLayout>
            <PaymentsReport />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/reports/inventory">
        <AuthenticatedRoute>
          <AppLayout>
            <InventoryReport />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/inventory">
        <AuthenticatedRoute>
          <AppLayout>
            <InventoryDashboard />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/inventory/stock">
        <AuthenticatedRoute>
          <AppLayout>
            <StockManagement />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/inventory/transfers">
        <AuthenticatedRoute>
          <AppLayout>
            <StockTransfers key="stock-transfers-page" />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/inventory/movements">
        <AuthenticatedRoute>
          <AppLayout>
            <StockMovements key="stock-movements-page" />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/masters/products">
        <AuthenticatedRoute>
          <AppLayout>
            <Products />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/masters/tables">
        <AuthenticatedRoute>
          <AppLayout>
            <Tables />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/masters/customers">
        <AuthenticatedRoute>
          <AppLayout>
            <Customers />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/masters/categories">
        <AuthenticatedRoute>
          <AppLayout>
            <Categories />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/masters/payment-methods">
        <AuthenticatedRoute>
          <AppLayout>
            <PaymentMethods />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/masters/platforms">
        <AuthenticatedRoute>
          <AppLayout>
            <Platforms />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/settings">
        <AuthenticatedRoute>
          <AppLayout>
            <Settings />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/settings/shop">
        <AuthenticatedRoute>
          <AppLayout>
            <ShopSettings />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/settings/users">
        <AuthenticatedRoute>
          <AppLayout>
            <Users />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>



      <Route path="/settings/profile">
        <AuthenticatedRoute>
          <AppLayout>
            <ProfileSettings />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/settings/branches">
        <AuthenticatedRoute>
          <AppLayout>
            <SettingsBranchesPage />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/mobile/branches">
        <AuthenticatedRoute>
          <MobileBranches />
        </AuthenticatedRoute>
      </Route>

      <Route path="/mobile/reports">
        <AuthenticatedRoute>
          <MobileReports />
        </AuthenticatedRoute>
      </Route>

      <Route path="/mobile/settings">
        <AuthenticatedRoute>
          <MobileSettings />
        </AuthenticatedRoute>
      </Route>

      <Route path="/support">
        <AuthenticatedRoute>
          <AppLayout>
            <Support />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/payments">
        <AuthenticatedRoute>
          <AppLayout>
            <PaymentManagement />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/purchases">
        <AuthenticatedRoute>
          <AppLayout>
            <Purchases />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/more">
        <AuthenticatedRoute>
          <AppLayout>
            <MorePage />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/expense-categories">
        <AuthenticatedRoute>
          <AppLayout>
            <ExpenseCategories />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>


      <Route path="/subscription">
        <AuthenticatedRoute>
          <AppLayout>
            <SubscriptionManagement />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/subscription/docs">
        <AuthenticatedRoute>
          <AppLayout>
            <SubscriptionDocs />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>
      <Route path="/utilities">
        <AuthenticatedRoute>
          <AppLayout>
            <Utilities />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>
      <Route path="/test-selected-products">
        <AuthenticatedRoute>
          <AppLayout>
            <TestSelectedProductsPage />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      {/* Notification Routes */}
      <Route path="/notifications/settings">
        <AuthenticatedRoute>
          <AppLayout>
            <NotificationSettingsPage />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/notifications/test">
        <AuthenticatedRoute>
          <AppLayout>
            <NotificationTestPage />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      <Route path="/notifications/debug">
        <AuthenticatedRoute>
          <AppLayout>
            <NotificationDebugPage />
          </AppLayout>
        </AuthenticatedRoute>
      </Route>

      {/* Fallback to 404 */}
      <Route>
        {isAuthenticated ? (
          <AuthenticatedRoute>
            <AppLayout>
              <NotFound />
            </AppLayout>
          </AuthenticatedRoute>
        ) : (
          <AuthLayout>
            <NotFound />
          </AuthLayout>
        )}
      </Route>
    </Switch>
  );
}

function App() {
  return (
    <>
      <Toaster />
      <AuthProvider>
        <AppProvider>
          <SocketProvider>
            <NetworkStatusProvider>
              <Router />
            </NetworkStatusProvider>
          </SocketProvider>
        </AppProvider>
      </AuthProvider>
    </>
  );
}

export default App;
