import { Pool } from 'pg';

// Database connection
const connectionString = process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable';
console.log('Connecting to database:', connectionString);

const pool = new Pool({
  connectionString: connectionString,
});

async function addSubscriptionTables() {
  console.log('Getting database client...');
  const client = await pool.connect();
  console.log('Connected to database successfully');

  try {
    console.log('Starting subscription tables migration...');
    
    // Create subscription_plans table
    await client.query(`
      CREATE TABLE IF NOT EXISTS subscription_plans (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        price DOUBLE PRECISION NOT NULL,
        billing_cycle TEXT NOT NULL,
        features JSONB NOT NULL,
        max_branches INTEGER DEFAULT 1 NOT NULL,
        max_users INTEGER DEFAULT 5 NOT NULL,
        max_products INTEGER DEFAULT 100 NOT NULL,
        max_orders INTEGER DEFAULT 1000 NOT NULL,
        storage_limit INTEGER DEFAULT 1024 NOT NULL,
        support_level TEXT DEFAULT 'basic' NOT NULL,
        active BOOLEAN DEFAULT true NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);
    console.log('✓ Created subscription_plans table');

    // Create subscriptions table
    await client.query(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id SERIAL PRIMARY KEY,
        shop_id INTEGER NOT NULL REFERENCES shops(id),
        plan_id INTEGER NOT NULL REFERENCES subscription_plans(id),
        status TEXT DEFAULT 'active' NOT NULL,
        start_date TIMESTAMP NOT NULL,
        end_date TIMESTAMP NOT NULL,
        auto_renew BOOLEAN DEFAULT true NOT NULL,
        discount_percent DOUBLE PRECISION DEFAULT 0 NOT NULL,
        discount_amount DOUBLE PRECISION DEFAULT 0 NOT NULL,
        total_amount DOUBLE PRECISION NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
        created_by INTEGER NOT NULL REFERENCES users(id)
      );
    `);
    console.log('✓ Created subscriptions table');

    // Create subscription_payment_methods table
    await client.query(`
      CREATE TABLE IF NOT EXISTS subscription_payment_methods (
        id SERIAL PRIMARY KEY,
        shop_id INTEGER NOT NULL REFERENCES shops(id),
        method_type TEXT NOT NULL,
        card_number TEXT,
        card_holder_name TEXT,
        expiry_month INTEGER,
        expiry_year INTEGER,
        billing_address TEXT,
        is_default BOOLEAN DEFAULT false NOT NULL,
        active BOOLEAN DEFAULT true NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
        created_by INTEGER NOT NULL REFERENCES users(id)
      );
    `);
    console.log('✓ Created subscription_payment_methods table');

    // Insert sample subscription plans
    const plans = [
      {
        name: 'Basic',
        description: 'Perfect for small businesses just getting started',
        price: 29.99,
        billing_cycle: 'monthly',
        features: JSON.stringify([
          'Up to 1 branch',
          'Up to 5 users',
          'Up to 100 products',
          'Up to 1,000 orders/month',
          '1GB storage',
          'Basic support',
          'Standard reporting'
        ]),
        max_branches: 1,
        max_users: 5,
        max_products: 100,
        max_orders: 1000,
        storage_limit: 1024,
        support_level: 'basic'
      },
      {
        name: 'Professional',
        description: 'Ideal for growing businesses with multiple locations',
        price: 79.99,
        billing_cycle: 'monthly',
        features: JSON.stringify([
          'Up to 3 branches',
          'Up to 15 users',
          'Up to 500 products',
          'Up to 5,000 orders/month',
          '5GB storage',
          'Priority support',
          'Advanced reporting',
          'Inventory management',
          'Customer loyalty program'
        ]),
        max_branches: 3,
        max_users: 15,
        max_products: 500,
        max_orders: 5000,
        storage_limit: 5120,
        support_level: 'priority'
      },
      {
        name: 'Enterprise',
        description: 'For large businesses with complex requirements',
        price: 199.99,
        billing_cycle: 'monthly',
        features: JSON.stringify([
          'Unlimited branches',
          'Unlimited users',
          'Unlimited products',
          'Unlimited orders',
          '50GB storage',
          '24/7 premium support',
          'Custom reporting',
          'Advanced analytics',
          'Multi-location management',
          'API access',
          'Custom integrations'
        ]),
        max_branches: 999,
        max_users: 999,
        max_products: 999999,
        max_orders: 999999,
        storage_limit: 51200,
        support_level: 'premium'
      }
    ];

    for (const plan of plans) {
      await client.query(`
        INSERT INTO subscription_plans (
          name, description, price, billing_cycle, features,
          max_branches, max_users, max_products, max_orders,
          storage_limit, support_level
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        ON CONFLICT DO NOTHING
      `, [
        plan.name, plan.description, plan.price, plan.billing_cycle,
        plan.features, plan.max_branches, plan.max_users, plan.max_products,
        plan.max_orders, plan.storage_limit, plan.support_level
      ]);
    }
    console.log('✓ Inserted sample subscription plans');

    console.log('Subscription tables migration completed successfully!');
    
  } catch (error) {
    console.error('Error during subscription tables migration:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  addSubscriptionTables()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { addSubscriptionTables };
