import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useAuth } from "@/context/auth-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, logQueryCache, queryClient } from "@/lib/queryClient";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { FileInput } from "@/components/ui/file-input";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";

// Define types for API responses
interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  categoryId?: number;
  image?: string;
  barcode?: string;
  sku?: string;
  taxIncluded?: boolean;
  taxRate?: number;
  active: boolean;
}

interface Category {
  id: number;
  name: string;
  description?: string;
}

// Interface for paginated data
interface PaginatedData<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";

import {
  Plus,
  Search,
  Edit,
  Trash2,
  AlertCircle,
  Loader2,
  Image as ImageIcon,
  X
} from "lucide-react";

import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Product form schema
const productSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  price: z.string().min(1, "Price is required").refine(
    (value) => !isNaN(Number(value)) && Number(value) > 0,
    {
      message: "Price must be a positive number",
    }
  ),
  categoryId: z.number().optional(),
  image: z.string().optional(),
  barcode: z.string().optional(),
  sku: z.string().optional(),
  taxIncluded: z.boolean().default(false),
  taxRate: z.string().optional().refine(
    (value) => !value || (!isNaN(Number(value)) && Number(value) >= 0),
    {
      message: "Tax rate must be a positive number",
    }
  ),
  active: z.boolean().default(true),
});

// Category form schema
const categorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
});

type ProductFormValues = z.infer<typeof productSchema>;
type CategoryFormValues = z.infer<typeof categorySchema>;

export default function Products() {
  const { token } = useAuth();
  const { toast } = useToast();

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAddCategoryFormVisible, setIsAddCategoryFormVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [selectedProduct, setSelectedProduct] = useState<any | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // Fixed page size

  // Fetch all products and categories
  const { data: allProducts, isLoading: isLoadingProducts, refetch: refetchProducts } = useQuery<Product[]>({
    queryKey: ['/api/products'],
    enabled: !!token,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
    select: (data: any) => {
      // Handle the API response format: { products: [...], pagination: {...} }
      if (data && Array.isArray(data.products)) {
        return data.products;
      }
      // Fallback: if data is already an array
      if (Array.isArray(data)) {
        return data;
      }
      return [];
    }
  });









  const { data: categories, isLoading: isLoadingCategories } = useQuery<Category[]>({
    queryKey: ['/api/categories'],
    enabled: !!token,
    staleTime: 0, // Always consider data stale
    refetchOnMount: 'always', // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });

  // Client-side pagination implementation
  const paginateData = (data: Product[] | undefined, page: number, size: number): PaginatedData<Product> => {
    // Check if data exists and is an array
    if (!data || !Array.isArray(data)) {
      return { data: [], total: 0, page, pageSize: size, totalPages: 0 };
    }

    const startIndex = (page - 1) * size;
    const endIndex = startIndex + size;
    const paginatedData = data.slice(startIndex, endIndex);
    const total = data.length;
    const totalPages = Math.ceil(total / size);

    return {
      data: paginatedData,
      total,
      page,
      pageSize: size,
      totalPages
    };
  };

  // Create paginated data
  const productsData = paginateData(allProducts, currentPage, pageSize);
  const products = productsData.data;

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, categoryFilter]);

  // Filter products
  const getFilteredProducts = () => {
    // Check if allProducts exists and is an array
    if (!allProducts || !Array.isArray(allProducts)) {
      return [];
    }

    // First ensure we're only working with active products
    const activeProducts = allProducts.filter((product: Product) => product.active === true);

    // Then apply other filters
    return activeProducts.filter((product: Product) => {
      let matchesSearch = true;
      let matchesCategory = true;

      if (searchTerm) {
        matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
      }

      if (categoryFilter !== "all") {
        matchesCategory = product.categoryId === parseInt(categoryFilter);
      }

      return matchesSearch && matchesCategory;
    });
  };

  // Get filtered products and paginate them
  const filteredAllProducts = getFilteredProducts();
  const filteredProductsData = paginateData(filteredAllProducts, currentPage, pageSize);
  const filteredProducts = filteredProductsData.data;

  // State for file upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // State for image preview dialog
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  // Add product form
  const addForm = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      price: "",
      categoryId: undefined,
      image: "",
      barcode: "",
      sku: "",
      taxIncluded: false,
      taxRate: "",
      active: true,
    },
  });

  // Add category form
  const categoryForm = useForm<CategoryFormValues>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // Edit product form
  const editForm = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      price: "",
      categoryId: undefined,
      image: "",
      barcode: "",
      sku: "",
      taxIncluded: false,
      taxRate: "",
      active: true,
    },
  });

  // Reset add form
  const resetAddForm = () => {
    addForm.reset({
      name: "",
      description: "",
      price: "",
      categoryId: undefined,
      image: "",
      barcode: "",
      sku: "",
      taxIncluded: false,
      taxRate: "",
      active: true,
    });
    setSelectedFile(null);
  };

  // Reset category form
  const resetCategoryForm = () => {
    categoryForm.reset({
      name: "",
      description: "",
    });
  };

  // Set form values for editing
  const setFormForEdit = (product: any) => {
    editForm.reset({
      name: product.name,
      description: product.description || "",
      price: product.price.toString(),
      categoryId: product.categoryId,
      image: product.image || "",
      barcode: product.barcode || "",
      sku: product.sku || "",
      taxIncluded: product.taxIncluded || false,
      taxRate: product.taxRate ? product.taxRate.toString() : "",
      active: product.active,
    });
    setSelectedFile(null);
  };

  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: async (data: CategoryFormValues) => {
      const response = await apiRequest("POST", "/api/categories", data);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || "Failed to create category");
      }
      return response.json();
    },
    onSuccess: async (newCategory) => {
      console.log("Category created successfully:", newCategory);

      // Clear the cache completely for categories
      queryClient.removeQueries({ queryKey: ['/api/categories'] });

      // Directly refetch categories to get fresh data from server
      await queryClient.refetchQueries({ queryKey: ['/api/categories'] });

      // Set the newly created category as the selected category in the product form
      addForm.setValue("categoryId", newCategory.id);

      toast({
        title: "Category created",
        description: "Category has been created successfully",
      });

      // Hide the category form
      setIsAddCategoryFormVisible(false);
      resetCategoryForm();
    },
    onError: (error) => {
      console.error("Failed to create category:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create category",
      });
    },
  });

  // Create product mutation
  const createProductMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/products", data);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || `Failed to create product. Status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: async (newProduct) => {
      console.log("Product created successfully:", newProduct);

      // Clear the cache completely for products
      queryClient.removeQueries({ queryKey: ['/api/products'] });

      // Directly refetch products to get fresh data from server
      await refetchProducts();

      // Also refetch any related queries
      queryClient.refetchQueries({ queryKey: ['/api/dashboard/top-products'] });

      toast({
        title: "Product created",
        description: "Product has been created successfully",
      });

      setIsAddDialogOpen(false);
      setIsAddCategoryFormVisible(false);
      resetAddForm();
      resetCategoryForm();
    },
    onError: (error) => {
      console.error("Failed to create product:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create product",
      });
    },
  });

  // Update product mutation
  const updateProductMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await apiRequest("PATCH", `/api/products/${id}`, data);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || `Failed to update product. Status: ${response.status}`);
      }
      return response.json();
    },
    onSuccess: async (updatedProduct) => {
      console.log("Product updated successfully:", updatedProduct);

      // Clear the cache completely for products
      queryClient.removeQueries({ queryKey: ['/api/products'] });

      // Directly refetch products to get fresh data from server
      await refetchProducts();

      // Also refetch any related queries
      queryClient.refetchQueries({ queryKey: ['/api/dashboard/top-products'] });

      toast({
        title: "Product updated",
        description: "Product has been updated successfully",
      });

      setIsEditDialogOpen(false);
      setSelectedProduct(null);
    },
    onError: (error) => {
      console.error("Failed to update product:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update product",
      });
    },
  });

  // Delete product mutation
  const deleteProductMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/products/${id}`, undefined);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
        throw new Error(errorData.message || `Failed to delete product. Status: ${response.status}`);
      }

      // Parse the response to get the success message
      const result = await response.json();
      return { id, result };
    },
    onSuccess: async (data) => {
      console.log("Product deleted successfully:", data);

      // Clear the cache completely for products
      queryClient.removeQueries({ queryKey: ['/api/products'] });

      // Directly refetch products to get fresh data from server
      await refetchProducts();

      // Also refetch any related queries
      queryClient.refetchQueries({ queryKey: ['/api/dashboard/top-products'] });

      toast({
        title: "Product deleted",
        description: "Product has been deleted successfully",
      });

      setIsDeleteDialogOpen(false);
      setSelectedProduct(null);
    },
    onError: (error) => {
      console.error("Failed to delete product:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete product",
      });
    },
  });

  // File upload function
  const uploadFile = async (file: File): Promise<string> => {
    setIsUploading(true);
    try {
      console.log('Uploading file:', file.name, 'Size:', file.size, 'Type:', file.type);

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Only image files are allowed."
        });
        throw new Error('Only image files are allowed');
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "File too large. Maximum size is 5MB."
        });
        throw new Error('File too large');
      }

      const formData = new FormData();
      formData.append('image', file);

      // Get the auth token
      const authToken = localStorage.getItem('auth_token');
      if (!authToken) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Authentication token not found. Please log in again."
        });
        throw new Error('Authentication token not found');
      }

      console.log('Sending upload request to server');
      // Don't set Content-Type header, let the browser set it with the boundary
      const response = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        body: formData
      });

      console.log('Received response from server:', response.status);

      // Handle error responses
      if (!response.ok) {
        let errorMessage = 'Failed to upload image. Please try again.';

        try {
          const errorData = await response.json();
          console.error('Server error response:', errorData);
          errorMessage = errorData.message || errorMessage;
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
        }

        toast({
          variant: "destructive",
          title: "Error",
          description: errorMessage
        });

        throw new Error(errorMessage);
      }

      // Parse the response
      let data;
      try {
        data = await response.json();
        console.log('Upload successful, received data:', data);

        if (!data.url) {
          throw new Error('No URL in response');
        }

        return data.url;
      } catch (jsonError) {
        console.error('Error parsing success response:', jsonError);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Error processing server response. Please try again."
        });
        throw new Error('Error processing server response');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      // Show a generic error message if one hasn't been shown already
      if (error instanceof Error && error.message === 'Failed to fetch') {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Network error. Please check your connection and try again."
        });
      }
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Form submission handlers
  const handleCreateProduct = async (data: ProductFormValues) => {
    try {
      // If there's a file selected, upload it first
      if (selectedFile) {
        try {
          console.log('Uploading file for new product');
          const imageUrl = await uploadFile(selectedFile);
          data.image = imageUrl;
          console.log('File uploaded successfully, URL:', imageUrl);
        } catch (uploadError) {
          console.error('Error uploading file for new product:', uploadError);
          // Don't proceed with product creation if file upload failed
          // The error toast is already shown in the uploadFile function
          return;
        }
      }

      // Then create the product
      console.log('Creating product with data:', data);
      createProductMutation.mutate({
        ...data,
        price: parseFloat(data.price),
        taxRate: data.taxRate ? parseFloat(data.taxRate) : null,
      });
    } catch (error) {
      console.error('Error creating product:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create product. Please try again."
      });
    }
  };

  const handleCreateCategory = (data: CategoryFormValues) => {
    createCategoryMutation.mutate(data);
  };

  const handleUpdateProduct = async (data: ProductFormValues) => {
    if (!selectedProduct) return;

    try {
      // If there's a file selected, upload it first
      if (selectedFile) {
        try {
          console.log('Uploading file for product update');
          const imageUrl = await uploadFile(selectedFile);
          data.image = imageUrl;
          console.log('File uploaded successfully, URL:', imageUrl);
        } catch (uploadError) {
          console.error('Error uploading file for product update:', uploadError);
          // Don't proceed with product update if file upload failed
          // The error toast is already shown in the uploadFile function
          return;
        }
      }

      // Then update the product
      console.log('Updating product with data:', data);
      updateProductMutation.mutate({
        id: selectedProduct.id,
        data: {
          ...data,
          price: parseFloat(data.price),
          taxRate: data.taxRate ? parseFloat(data.taxRate) : null,
        },
      });
    } catch (error) {
      console.error('Error updating product:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update product. Please try again."
      });
    }
  };

  const handleDeleteProduct = () => {
    if (!selectedProduct) return;
    deleteProductMutation.mutate(selectedProduct.id);
  };

  // Loading state
  const isLoading =
    isLoadingProducts ||
    isLoadingCategories ||
    createProductMutation.isPending ||
    createCategoryMutation.isPending ||
    updateProductMutation.isPending ||
    deleteProductMutation.isPending ||
    isUploading;



  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold">Products</h1>
        <Sheet
          open={isAddDialogOpen}
          onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            if (!open) {
              setIsAddCategoryFormVisible(false);
              resetAddForm();
              resetCategoryForm();
            }
          }}
        >
          <SheetTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="max-h-[100vh] overflow-y-auto w-full sm:max-w-md">
            <SheetHeader className="pb-2">
              <SheetTitle>Add New Product</SheetTitle>
              <SheetDescription>
                Add a new product to your menu
              </SheetDescription>
            </SheetHeader>

            <Form {...addForm}>
              <form onSubmit={addForm.handleSubmit(handleCreateProduct)} className="space-y-3">
                <FormField
                  control={addForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>Product Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter product name" {...field} disabled={isLoading} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="categoryId"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <div className="flex justify-between items-center">
                        <FormLabel>Category</FormLabel>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsAddCategoryFormVisible(!isAddCategoryFormVisible)}
                          className="h-8 px-2 text-xs"
                        >
                          {isAddCategoryFormVisible ? "Cancel" : "Add New Category"}
                        </Button>
                      </div>

                      {isAddCategoryFormVisible ? (
                        <div className="border p-3 rounded-md space-y-3 bg-muted/20">
                          <h4 className="text-sm font-medium">Add New Category</h4>
                          <div className="space-y-2">
                            <div>
                              <FormLabel className="text-xs">Name</FormLabel>
                              <Input
                                placeholder="Enter category name"
                                value={categoryForm.watch("name")}
                                onChange={(e) => categoryForm.setValue("name", e.target.value)}
                                disabled={createCategoryMutation.isPending}
                              />
                              {categoryForm.formState.errors.name && (
                                <p className="text-xs text-destructive mt-1">
                                  {categoryForm.formState.errors.name.message}
                                </p>
                              )}
                            </div>
                            <div>
                              <FormLabel className="text-xs">Description (optional)</FormLabel>
                              <Input
                                placeholder="Enter category description"
                                value={categoryForm.watch("description") || ""}
                                onChange={(e) => categoryForm.setValue("description", e.target.value)}
                                disabled={createCategoryMutation.isPending}
                              />
                            </div>
                            <Button
                              type="button"
                              size="sm"
                              className="w-full"
                              disabled={createCategoryMutation.isPending || !categoryForm.watch("name")}
                              onClick={() => {
                                const data = categoryForm.getValues();
                                if (data.name) {
                                  handleCreateCategory(data);
                                }
                              }}
                            >
                              {createCategoryMutation.isPending ? (
                                <>
                                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                                  Creating...
                                </>
                              ) : (
                                "Save Category"
                              )}
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <Select
                          value={field.value?.toString()}
                          onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Array.isArray(categories) && categories.filter((category: Category) => category && category.id && category.name).map((category: Category) => (
                              <SelectItem
                                key={category.id}
                                value={category.id.toString()}
                              >
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <FormField
                    control={addForm.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel>Price</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="Enter price"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addForm.control}
                    name="image"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel>Product Image</FormLabel>
                        <FormControl>
                          <FileInput
                            imageUrl={field.value}
                            onFileChange={(file) => {
                              setSelectedFile(file);
                            }}
                            onUrlChange={(url) => {
                              field.onChange(url);
                            }}
                            disabled={isLoading || isUploading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <FormField
                    control={addForm.control}
                    name="barcode"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel>Barcode</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter barcode"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addForm.control}
                    name="sku"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel>SKU</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter SKU"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <FormField
                    control={addForm.control}
                    name="taxRate"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel>Tax Rate (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="Enter tax rate"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={addForm.control}
                    name="taxIncluded"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Tax Included in Price</FormLabel>
                          <FormDescription>
                            Whether the tax is included in the product price
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={addForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter product description"
                          className="resize-none h-20"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-2">
                      <div className="space-y-0">
                        <FormLabel className="mb-0">Active</FormLabel>
                        <FormDescription className="text-xs">
                          Make this product available for orders
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <SheetFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsAddDialogOpen(false);
                      setIsAddCategoryFormVisible(false);
                      resetAddForm();
                      resetCategoryForm();
                    }}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isUploading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Uploading Image...
                      </>
                    ) : createProductMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Product"
                    )}
                  </Button>
                </SheetFooter>
              </form>
            </Form>
          </SheetContent>
        </Sheet>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Product List</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search products..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select
              value={categoryFilter}
              onValueChange={setCategoryFilter}
            >
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories && categories.map((category: Category) => (
                  <SelectItem
                    key={category.id}
                    value={category.id.toString()}
                  >
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Products Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="hidden md:table-cell">Barcode</TableHead>
                  <TableHead className="hidden md:table-cell">Tax</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoadingProducts ? (
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-10 w-10 rounded-md" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-4 w-16 ml-auto" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-20 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredProducts && filteredProducts.length > 0 ? (
                  filteredProducts.map((product: Product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        {product.image ? (
                          <div
                            className="relative h-10 w-10 rounded-md overflow-hidden border bg-muted/20 cursor-pointer hover:opacity-80 transition-opacity"
                            onClick={() => setPreviewImage(product.image)}
                            title="Click to view full image"
                          >
                            <img
                              src={product.image}
                              alt={product.name}
                              className="h-full w-full object-cover"
                              onError={(e) => {
                                // Replace with placeholder on error
                                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/40?text=No+Image';
                              }}
                            />
                          </div>
                        ) : (
                          <div className="h-10 w-10 rounded-md border flex items-center justify-center bg-muted/20">
                            <ImageIcon className="h-5 w-5 text-muted-foreground" />
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{product.name}</TableCell>
                      <TableCell>
                        {categories && categories.find((c: Category) => c.id === product.categoryId)?.name || "Uncategorized"}
                      </TableCell>
                      <TableCell className="text-right">₹{product.price}</TableCell>
                      <TableCell className="hidden md:table-cell">
                        {product.barcode || <span className="text-gray-400">-</span>}
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {product.taxRate ? (
                          <span>
                            {product.taxRate}% {product.taxIncluded ? "(Incl.)" : "(Excl.)"}
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={product.active ? "outline" : "secondary"} className={
                          product.active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                        }>
                          {product.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedProduct(product);
                            setFormForEdit(product);
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedProduct(product);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-6 text-gray-500">
                      No products found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {!isLoadingProducts && filteredProductsData && filteredProductsData.totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {Array.from({ length: filteredProductsData.totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        isActive={currentPage === page}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(filteredProductsData.totalPages, currentPage + 1))}
                      className={currentPage === filteredProductsData.totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Product Sheet */}
      <Sheet open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <SheetContent side="right" className="max-h-[100vh] overflow-y-auto w-full sm:max-w-md">
          <SheetHeader className="pb-2">
            <SheetTitle>Edit Product</SheetTitle>
            <SheetDescription>
              Make changes to the product details
            </SheetDescription>
          </SheetHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdateProduct)} className="space-y-3">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel>Product Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter product name" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel>Category</FormLabel>
                    <Select
                      value={field.value?.toString()}
                      onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.isArray(categories) && categories.filter((category: Category) => category && category.id && category.name).map((category: Category) => (
                          <SelectItem
                            key={category.id}
                            value={category.id.toString()}
                          >
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <FormField
                  control={editForm.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="Enter price"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="image"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>Product Image</FormLabel>
                      <FormControl>
                        <FileInput
                          imageUrl={field.value}
                          onFileChange={(file) => {
                            setSelectedFile(file);
                          }}
                          onUrlChange={(url) => {
                            field.onChange(url);
                          }}
                          disabled={isLoading || isUploading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <FormField
                  control={editForm.control}
                  name="barcode"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>Barcode</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter barcode"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>SKU</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter SKU"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <FormField
                  control={editForm.control}
                  name="taxRate"
                  render={({ field }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel>Tax Rate (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="Enter tax rate"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="taxIncluded"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Tax Included in Price</FormLabel>
                        <FormDescription>
                          Whether the tax is included in the product price
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="space-y-1.5">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter product description"
                        className="resize-none h-20"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-2">
                    <div className="space-y-0">
                      <FormLabel className="mb-0">Active</FormLabel>
                      <FormDescription className="text-xs">
                        Make this product available for orders
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <SheetFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setIsEditDialogOpen(false);
                    setSelectedProduct(null);
                  }}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading Image...
                    </>
                  ) : updateProductMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Product"
                  )}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </SheetContent>
      </Sheet>

      {/* Delete Confirmation Sheet */}
      <Sheet open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <SheetContent side="right">
          <SheetHeader>
            <SheetTitle>Delete Product</SheetTitle>
            <SheetDescription>
              Are you sure you want to delete this product? This action cannot be undone.
            </SheetDescription>
          </SheetHeader>

          {selectedProduct && (
            <div className="py-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You are about to delete <strong>{selectedProduct.name}</strong>
                </AlertDescription>
              </Alert>
            </div>
          )}

          <SheetFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setSelectedProduct(null);
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteProduct}
              disabled={isLoading}
            >
              {deleteProductMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Product"
              )}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      {/* Image Preview Dialog */}
      <Dialog open={!!previewImage} onOpenChange={(open) => !open && setPreviewImage(null)}>
        <DialogContent className="sm:max-w-md p-0 overflow-hidden">
          {previewImage && (
            <div className="relative w-full h-full max-h-[80vh] flex items-center justify-center bg-black/50">
              <img
                src={previewImage}
                alt="Product image"
                className="max-w-full max-h-[80vh] object-contain"
                onError={(e) => {
                  // Replace with placeholder on error
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400?text=Image+Not+Available';
                }}
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 rounded-full bg-black/50 hover:bg-black/70"
                onClick={() => setPreviewImage(null)}
              >
                <X className="h-4 w-4 text-white" />
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
