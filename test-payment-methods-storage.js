// Test payment methods storage function directly
import { storage } from './server/storage.js';

async function testPaymentMethodsStorage() {
  try {
    console.log('Testing payment methods storage function...');
    
    const shopId = 12;
    console.log(`Testing with shopId: ${shopId}`);
    
    const paymentMethods = await storage.getSubscriptionPaymentMethodsByShop(shopId);
    console.log('Success! Payment methods:', paymentMethods);
    
  } catch (error) {
    console.error('Error testing payment methods storage:', error);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
  }
  
  process.exit(0);
}

testPaymentMethodsStorage();
