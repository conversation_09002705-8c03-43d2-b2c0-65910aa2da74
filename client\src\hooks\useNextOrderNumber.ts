import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useApp } from '@/context/app-context';

interface NextOrderNumberResponse {
  nextOrderNumber: string;
}

export function useNextOrderNumber() {
  const { currentShop, currentBranch } = useApp();

  return useQuery<NextOrderNumberResponse>({
    queryKey: ['nextOrderNumber', currentShop?.id, currentBranch?.id],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/settings/order-number/next');
      if (!response.ok) {
        throw new Error('Failed to fetch next order number');
      }
      return response.json();
    },
    enabled: !!currentShop?.id, // Only run query when we have a shop
    refetchInterval: 10000, // Refetch every 10 seconds to keep it updated
    staleTime: 0, // Always consider data stale to ensure fresh data
    gcTime: 0, // Don't cache to prevent stale data
  });
}
