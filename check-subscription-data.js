import pg from 'pg';
const { Client } = pg;

const client = new Client({
  connectionString: '*******************************************************************************'
});

async function checkSubscriptionData() {
  try {
    await client.connect();
    console.log('✅ Connected to database');
    
    // Check subscription plans
    console.log('\n📋 Checking Subscription Plans:');
    const plans = await client.query('SELECT id, name, price, active FROM subscription_plans ORDER BY id');
    console.log(`Found ${plans.rows.length} subscription plans:`);
    plans.rows.forEach(plan => {
      console.log(`  - ID: ${plan.id}, Name: "${plan.name}", Price: $${plan.price}, Active: ${plan.active}`);
    });
    
    // Check subscriptions with plan details
    console.log('\n📊 Checking Subscriptions with Plan Details:');
    const subscriptions = await client.query(`
      SELECT 
        s.id,
        s.shop_id,
        s.plan_id,
        s.status,
        s.total_amount,
        p.name as plan_name,
        p.price as plan_price
      FROM subscriptions s
      LEFT JOIN subscription_plans p ON s.plan_id = p.id
      ORDER BY s.id DESC
      LIMIT 10
    `);
    
    console.log(`Found ${subscriptions.rows.length} subscriptions:`);
    subscriptions.rows.forEach(sub => {
      console.log(`  - Sub ID: ${sub.id}, Shop: ${sub.shop_id}, Plan ID: ${sub.plan_id}, Plan Name: "${sub.plan_name || 'NULL'}", Status: ${sub.status}, Amount: $${sub.total_amount}`);
    });
    
    // Check if there are any subscriptions with missing plan references
    console.log('\n🔍 Checking for Orphaned Subscriptions:');
    const orphaned = await client.query(`
      SELECT s.id, s.plan_id, s.status
      FROM subscriptions s
      LEFT JOIN subscription_plans p ON s.plan_id = p.id
      WHERE p.id IS NULL
    `);
    
    if (orphaned.rows.length > 0) {
      console.log(`⚠️  Found ${orphaned.rows.length} subscriptions with missing plan references:`);
      orphaned.rows.forEach(sub => {
        console.log(`  - Subscription ID: ${sub.id}, Plan ID: ${sub.plan_id}, Status: ${sub.status}`);
      });
    } else {
      console.log('✅ No orphaned subscriptions found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

checkSubscriptionData();
