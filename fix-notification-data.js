import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function fixNotificationData() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing notification data for current shop...\n');

    // 1. First, let's see what shops exist
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    console.log('📋 Available shops:');
    shopsResult.rows.forEach(shop => {
      console.log(`   Shop ID: ${shop.id}, Name: ${shop.name}`);
    });

    // 2. Get users and their shops
    const usersResult = await client.query(`
      SELECT u.id, u.name, u.email, us.shop_id 
      FROM users u 
      LEFT JOIN user_shops us ON u.id = us.user_id 
      ORDER BY u.id
    `);
    console.log('\n👥 Users and their shops:');
    usersResult.rows.forEach(user => {
      console.log(`   User ID: ${user.id}, Name: ${user.name}, Shop ID: ${user.shop_id || 'None'}`);
    });

    // 3. Clear existing notifications
    await client.query('DELETE FROM notification_recipients');
    await client.query('DELETE FROM notifications');
    console.log('\n🗑️  Cleared existing notifications');

    // 4. Create notifications for each shop
    for (const shop of shopsResult.rows) {
      console.log(`\n🔔 Creating notifications for Shop: ${shop.name} (ID: ${shop.id})`);

      // Create sample notifications for this shop
      const notifications = [
        {
          title: 'Welcome to NembooBill',
          message: `Welcome to ${shop.name}! Thank you for using our POS system. Explore all features to get the most out of your experience.`,
          type: 'system',
          priority: 'normal'
        },
        {
          title: 'New Order Received',
          message: `Order #ORD-001 has been placed by customer John Doe at ${shop.name}`,
          type: 'order',
          priority: 'normal'
        },
        {
          title: 'Low Stock Alert',
          message: `Product Coffee Beans is running low (5 remaining) at ${shop.name}`,
          type: 'stock',
          priority: 'high'
        },
        {
          title: 'Daily Sales Summary',
          message: `${shop.name} daily sales: $1,250.00 from 25 orders. Great job team!`,
          type: 'system',
          priority: 'normal'
        },
        {
          title: 'Special Promotion',
          message: `New promotion available at ${shop.name}: 20% off all beverages this weekend!`,
          type: 'marketing',
          priority: 'normal'
        }
      ];

      // Insert notifications for this shop
      for (const notif of notifications) {
        const notificationResult = await client.query(`
          INSERT INTO notifications (title, message, type, priority, recipient_type, recipient_id, shop_id, created_by, created_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW() - INTERVAL '${Math.floor(Math.random() * 24)} hours')
          RETURNING id
        `, [
          notif.title,
          notif.message,
          notif.type,
          notif.priority,
          'shop',
          shop.id,
          shop.id,
          1 // created by user 1
        ]);

        const notificationId = notificationResult.rows[0].id;

        // Get users for this shop
        const shopUsers = usersResult.rows.filter(user => user.shop_id === shop.id);
        
        if (shopUsers.length === 0) {
          // If no users assigned to shop, assign to all users
          console.log(`   ⚠️  No users found for shop ${shop.id}, assigning to all users`);
          for (const user of usersResult.rows) {
            await client.query(`
              INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
              VALUES ($1, $2, $3, NOW() - INTERVAL '${Math.floor(Math.random() * 24)} hours')
            `, [notificationId, user.id, Math.random() > 0.3 ? 'unread' : 'read']);
          }
        } else {
          // Assign to shop users
          for (const user of shopUsers) {
            await client.query(`
              INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
              VALUES ($1, $2, $3, NOW() - INTERVAL '${Math.floor(Math.random() * 24)} hours')
            `, [notificationId, user.id, Math.random() > 0.3 ? 'unread' : 'read']);
          }
        }

        console.log(`   ✅ Created: ${notif.title}`);
      }
    }

    // 5. Create default notification settings for all users
    console.log('\n⚙️  Creating default notification settings...');
    for (const user of usersResult.rows) {
      for (const shop of shopsResult.rows) {
        const notificationTypes = ['order', 'stock', 'marketing', 'system'];
        for (const type of notificationTypes) {
          await client.query(`
            INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (user_id, shop_id, notification_type) DO NOTHING
          `, [user.id, shop.id, type, true, JSON.stringify(['in_app'])]);
        }
      }
    }

    // 6. Show summary
    const summaryResult = await client.query(`
      SELECT 
        s.name as shop_name,
        COUNT(DISTINCT n.id) as total_notifications,
        COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_notifications,
        COUNT(DISTINCT nr.user_id) as users_with_notifications
      FROM shops s
      LEFT JOIN notifications n ON s.id = n.shop_id
      LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
      GROUP BY s.id, s.name
      ORDER BY s.id
    `);

    console.log('\n📊 Summary by Shop:');
    summaryResult.rows.forEach(row => {
      console.log(`   ${row.shop_name}:`);
      console.log(`     - Total notifications: ${row.total_notifications}`);
      console.log(`     - Unread notifications: ${row.unread_notifications}`);
      console.log(`     - Users with notifications: ${row.users_with_notifications}`);
    });

    console.log('\n🎉 Notification data fixed successfully!');
    console.log('💡 Now refresh your browser and check the notification bell.');
    console.log('🔔 You should see notifications for your current shop.');
    
  } catch (error) {
    console.error('❌ Error fixing notification data:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
fixNotificationData();
