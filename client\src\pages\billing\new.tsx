import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest, queryClient, invalidateAndRefetch, createOrderTwoStep } from "@/lib/queryClient";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useToast } from "@/hooks/use-toast";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Plus, Trash2, Receipt } from "lucide-react";

// Form schemas
const orderFormSchema = z.object({
  orderType: z.enum(["dine_in", "takeaway", "online"]),
  tableId: z.number().optional(),
  customerId: z.number().optional(),
  platformId: z.number().optional(),
  paymentMethod: z.string().min(1, "Payment method is required"),
  notes: z.string().optional(),
});

const productSelectionSchema = z.object({
  productId: z.number(),
  quantity: z.number().min(1, "Quantity must be at least 1"),
});

type OrderFormValues = z.infer<typeof orderFormSchema>;
type ProductSelectionValues = z.infer<typeof productSelectionSchema>;

interface OrderItem {
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

export default function NewBilling() {
  const { token } = useAuth();
  const { currentShop } = useApp();
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  const [selectedTab, setSelectedTab] = useState("dine_in");
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [subtotal, setSubtotal] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Fetch products from API
  const { data: products, isLoading: isLoadingProducts } = useQuery({
    queryKey: ['/api/products'],
    enabled: !!token && !!currentShop,
  });

  // Fetch tables from API
  const { data: tables, isLoading: isLoadingTables } = useQuery({
    queryKey: ['/api/tables'],
    enabled: !!token && !!currentShop,
  });

  // Fetch customers from API
  const { data: customers, isLoading: isLoadingCustomers } = useQuery({
    queryKey: ['/api/customers'],
    enabled: !!token && !!currentShop,
  });

  // Fetch online platforms from API
  const { data: platforms, isLoading: isLoadingPlatforms } = useQuery({
    queryKey: ['/api/platforms'],
    enabled: !!token && !!currentShop,
  });

  // Fetch tax settings from API
  const { data: taxSettings, isLoading: isLoadingTaxSettings } = useQuery({
    queryKey: ['/api/settings/tax'],
    enabled: !!token && !!currentShop,
  });

  // Fetch discount settings from API
  const { data: discountSettings, isLoading: isLoadingDiscountSettings } = useQuery({
    queryKey: ['/api/settings/discount'],
    enabled: !!token && !!currentShop,
  });

  // Fetch payment methods from API
  const { data: paymentMethods, isLoading: isLoadingPaymentMethods } = useQuery({
    queryKey: ['/api/payment-methods'],
    enabled: !!token && !!currentShop,
  });

  // Order form
  const orderForm = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      orderType: "dine_in",
      paymentMethod: "cash",
      notes: "",
    },
  });

  // Product selection form
  const productForm = useForm<ProductSelectionValues>({
    resolver: zodResolver(productSelectionSchema),
    defaultValues: {
      productId: 0,
      quantity: 1,
    },
  });

  // Update form values when tab changes
  useEffect(() => {
    orderForm.setValue("orderType", selectedTab as any);

    // Reset conditional fields
    orderForm.setValue("tableId", undefined);
    orderForm.setValue("customerId", undefined);
    orderForm.setValue("platformId", undefined);
  }, [selectedTab, orderForm]);

  // Calculate totals when items change
  useEffect(() => {
    const subtotalValue = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);
    setSubtotal(subtotalValue);

    // Calculate tax based on individual product tax rates
    let taxValue = 0;
    // Handle both paginated response format {products: [...], pagination: {...}} and direct array format
    const productsArray = Array.isArray(products) ? products : (products?.products || []);

    orderItems.forEach(item => {
      // Get the product to access its tax rate
      const product = productsArray.find((p: any) => p.id === item.productId);
      let itemTaxRate = 0;

      if (product?.taxRate && product.taxRate > 0) {
        // Use product-specific tax rate
        itemTaxRate = product.taxRate;
      } else {
        // Fall back to global tax rate if product has no specific rate
        itemTaxRate = taxSettings?.length > 0 && taxSettings[0].active
          ? taxSettings[0].rate
          : 5;
      }

      // Calculate tax for this item
      const itemTax = item.totalPrice * (itemTaxRate / 100);
      taxValue += itemTax;
    });

    setTaxAmount(taxValue);

    // Default discount (0)
    setDiscountAmount(0);

    const totalValue = subtotalValue + taxValue - discountAmount;
    setTotalAmount(totalValue);
  }, [orderItems, taxSettings, discountAmount, products]);

  // Add product to order
  const handleAddProduct = (data: ProductSelectionValues) => {
    const selectedProduct = products?.find(p => p.id === data.productId);

    if (!selectedProduct) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Selected product not found",
      });
      return;
    }

    const unitPrice = selectedProduct.price;
    const totalPrice = unitPrice * data.quantity;

    const newItem: OrderItem = {
      productId: data.productId,
      productName: selectedProduct.name,
      quantity: data.quantity,
      unitPrice,
      totalPrice,
    };

    // Check if product already exists in order
    const existingItemIndex = orderItems.findIndex(item => item.productId === data.productId);

    if (existingItemIndex >= 0) {
      // Update quantity
      const updatedItems = [...orderItems];
      const existingItem = updatedItems[existingItemIndex];

      updatedItems[existingItemIndex] = {
        ...existingItem,
        quantity: existingItem.quantity + data.quantity,
        totalPrice: existingItem.unitPrice * (existingItem.quantity + data.quantity),
      };

      setOrderItems(updatedItems);
    } else {
      // Add new item
      setOrderItems([...orderItems, newItem]);
    }

    // Reset product form
    productForm.reset({ productId: 0, quantity: 1 });
  };

  // Remove item from order
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...orderItems];
    updatedItems.splice(index, 1);
    setOrderItems(updatedItems);
  };

  // Create order mutation using two-step process
  const createOrderMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log("Using two-step order creation process");
      console.log("Submitting order data:", data);

      try {
        // Extract order and items data
        const { order, items } = data;

        // Log the data being sent
        console.log("Order data for two-step process:", JSON.stringify(data, null, 2));

        // Use the two-step order creation process
        const result = await createOrderTwoStep(order, items);
        console.log("Two-step order creation result:", result);
        return result;
      } catch (error) {
        console.error("Error in two-step order creation:", error);
        throw error;
      }
    },
    onSuccess: async () => {
      // Immediately invalidate and refetch orders data and all related queries
      await invalidateAndRefetch([
        '/api/orders/recent',
        '/api/products',
        '/api/tables'
      ]);

      toast({
        title: "Success",
        description: "Order created successfully!",
      });

      setLocation("/billing/orders");
    },
    onError: (err: any) => {
      // Extract error details if available
      let errorMessage = "Failed to create order. Please try again.";
      if (err.response?.data?.errors) {
        const validationErrors = err.response.data.errors;
        errorMessage = `Validation errors: ${JSON.stringify(validationErrors)}`;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    },
  });

  // Submit order
  const handleSubmitOrder = (data: OrderFormValues) => {
    if (!currentShop) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "No shop selected. Please select a shop first.",
      });
      return;
    }

    if (orderItems.length === 0) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please add at least one item to the order",
      });
      return;
    }

    // Validate required fields based on order type
    if (data.orderType === "dine_in" && !data.tableId) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a table for dine-in orders",
      });
      return;
    }


    if (data.orderType === "online" && !data.platformId) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please select a platform for online orders",
      });
      return;
    }

    // Calculate effective tax rate (for future use)
    const effectiveTaxRate = subtotal > 0 ? (taxAmount / subtotal) * 100 : 0;

    // Prepare order data with all required fields
    const orderData = {
      order: {
        orderNumber: `ORD-${Math.floor(10000 + Math.random() * 90000)}`, // Generate a random order number
        orderType: data.orderType,
        status: "pending", // Initial status
        // Only include optional fields if they have values
        ...(data.tableId ? { tableId: data.tableId } : {}),
        ...(data.customerId ? { customerId: data.customerId } : {}),
        ...(data.platformId ? { platformId: data.platformId } : {}),
        subtotal: Number(subtotal),
        taxAmount: Number(taxAmount),
        taxRate: Number(effectiveTaxRate), // Store the effective tax rate
        discountAmount: Number(discountAmount),
        totalAmount: Number(totalAmount),
        paymentMethod: data.paymentMethod,
        paymentStatus: "paid", // Default to paid
        notes: data.notes || "",
        shopId: currentShop.id, // Add shop ID to ensure it's associated with the current shop
      },
      items: orderItems.map(item => ({
        // orderId will be set by the server
        productId: Number(item.productId),
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
      })),
    };

    // Log the final data being sent
    console.log("Final order data being sent:", JSON.stringify(orderData, null, 2));

    // Validate that all required fields are present and have the correct types
    console.log("Validating order data:");
    console.log("- orderType:", typeof orderData.order.orderType, orderData.order.orderType);
    console.log("- status:", typeof orderData.order.status, orderData.order.status);
    console.log("- subtotal:", typeof orderData.order.subtotal, orderData.order.subtotal);
    console.log("- taxAmount:", typeof orderData.order.taxAmount, orderData.order.taxAmount);
    console.log("- totalAmount:", typeof orderData.order.totalAmount, orderData.order.totalAmount);
    console.log("- paymentStatus:", typeof orderData.order.paymentStatus, orderData.order.paymentStatus);

    if (orderItems.length > 0) {
      console.log("First item validation:");
      console.log("- productId:", typeof orderData.items[0].productId, orderData.items[0].productId);
      console.log("- quantity:", typeof orderData.items[0].quantity, orderData.items[0].quantity);
      console.log("- unitPrice:", typeof orderData.items[0].unitPrice, orderData.items[0].unitPrice);
      console.log("- totalPrice:", typeof orderData.items[0].totalPrice, orderData.items[0].totalPrice);
    }

    createOrderMutation.mutate(orderData);
  };

  // Loading state
  const isLoading =
    isLoadingProducts ||
    isLoadingTables ||
    isLoadingCustomers ||
    isLoadingPlatforms ||
    isLoadingTaxSettings ||
    isLoadingDiscountSettings ||
    isLoadingPaymentMethods ||
    createOrderMutation.isPending;

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6">
        {/* Left side - Order Form */}
        <div className="w-full md:w-2/3 space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>New Order</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={selectedTab} onValueChange={setSelectedTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="dine_in">Dine In</TabsTrigger>
                  <TabsTrigger value="takeaway">Take Away</TabsTrigger>
                  <TabsTrigger value="online">Online Order</TabsTrigger>
                </TabsList>

                <Form {...orderForm}>
                  <form className="mt-6 space-y-4">
                    {/* Dine In - Table Selection */}
                    {selectedTab === "dine_in" && (
                      <FormField
                        control={orderForm.control}
                        name="tableId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Table</FormLabel>
                            <Select
                              value={field.value?.toString()}
                              onValueChange={(value) => field.onChange(parseInt(value))}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a table" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {tables?.map((table) => (
                                  <SelectItem
                                    key={table.id}
                                    value={table.id.toString()}
                                    disabled={table.status === 'occupied'}
                                  >
                                    Table {table.name} ({table.capacity} seats)
                                    {table.status !== 'available' && ` - ${table.status}`}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Takeaway - Customer Selection */}
                    {selectedTab === "takeaway" && (
                      <FormField
                        control={orderForm.control}
                        name="customerId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Customer</FormLabel>
                            <Select
                              value={field.value?.toString()}
                              onValueChange={(value) => field.onChange(parseInt(value))}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a customer" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {customers?.map((customer) => (
                                  <SelectItem
                                    key={customer.id}
                                    value={customer.id.toString()}
                                  >
                                    <div className="flex flex-col">
                                      <span className="font-medium">{customer.name}</span>
                                      <span className="text-xs text-muted-foreground">{customer.phone}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Online Order - Platform Selection */}
                    {selectedTab === "online" && (
                      <FormField
                        control={orderForm.control}
                        name="platformId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Platform</FormLabel>
                            <Select
                              value={field.value?.toString()}
                              onValueChange={(value) => field.onChange(parseInt(value))}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a platform" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {platforms?.map((platform) => (
                                  <SelectItem
                                    key={platform.id}
                                    value={platform.id.toString()}
                                  >
                                    {platform.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {/* Payment Method */}
                    <FormField
                      control={orderForm.control}
                      name="paymentMethod"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Payment Method</FormLabel>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            disabled={isLoading}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a payment method" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {paymentMethods && paymentMethods.length > 0 ? (
                                paymentMethods.filter(method => method.active).map((method) => (
                                  <SelectItem key={method.id} value={method.name}>
                                    {method.name}
                                  </SelectItem>
                                ))
                              ) : (
                                <>
                                  <SelectItem value="cash">Cash</SelectItem>
                                  <SelectItem value="card">Card</SelectItem>
                                  <SelectItem value="upi">UPI</SelectItem>
                                  <SelectItem value="other">Other</SelectItem>
                                </>
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Notes */}
                    <FormField
                      control={orderForm.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Add any special instructions"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Add Products</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...productForm}>
                <form
                  onSubmit={productForm.handleSubmit(handleAddProduct)}
                  className="flex flex-col md:flex-row items-end space-y-4 md:space-y-0 md:space-x-4"
                >
                  <FormField
                    control={productForm.control}
                    name="productId"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormLabel>Product</FormLabel>
                        <Select
                          value={field.value ? field.value.toString() : ""}
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a product" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {products?.map((product) => (
                              <SelectItem
                                key={product.id}
                                value={product.id.toString()}
                              >
                                {product.name} - ₹{product.price}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={productForm.control}
                    name="quantity"
                    render={({ field }) => (
                      <FormItem className="w-24">
                        <FormLabel>Quantity</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value))}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    size="sm"
                    className="md:ml-2"
                    disabled={isLoading || !productForm.getValues().productId}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Order Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Quantity</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orderItems.length > 0 ? (
                      orderItems.map((item, index) => (
                        <TableRow key={`${item.productId}-${index}`}>
                          <TableCell>{item.productName}</TableCell>
                          <TableCell className="text-right">₹{item.unitPrice.toFixed(2)}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">₹{item.totalPrice.toFixed(2)}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveItem(index)}
                              disabled={isLoading}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                          No items added to order
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right side - Order Summary */}
        <div className="w-full md:w-1/3">
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500">Subtotal</span>
                  <span>₹{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Tax</span>
                  <span>₹{taxAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Discount</span>
                  <span>₹{discountAmount.toFixed(2)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>₹{totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col">
              <Button
                className="w-full mb-2"
                onClick={orderForm.handleSubmit(handleSubmitOrder)}
                disabled={isLoading || orderItems.length === 0}
              >
                {createOrderMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Receipt className="mr-2 h-4 w-4" />
                    Complete Order
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setLocation("/billing/orders")}
                disabled={isLoading}
              >
                Cancel
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
