import { Pool } from 'pg';
import dotenv from 'dotenv';
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function fixTable3() {
  const client = await pool.connect();
  try {
    console.log('🔍 Current table data:');
    const before = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(before.rows);
    
    console.log('\n🔧 Fixing Table 3 mapping...');
    
    // Update table ID 3 to be "Table 3"
    await client.query("UPDATE tables SET name = 'Table 3' WHERE id = 3");
    console.log('✅ Set table ID 3 to "Table 3"');
    
    // Update table ID 5 to be "Table 5" (was previously "Table 3")
    await client.query("UPDATE tables SET name = 'Table 5' WHERE id = 5");
    console.log('✅ Set table ID 5 to "Table 5"');
    
    console.log('\n📋 Updated table data:');
    const after = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.table(after.rows);
    
    console.log('\n🎉 SUCCESS! Now "Table 3" will use ID 3 when selected!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixTable3();
