@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for landing page */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes grow {
  from {
    height: 0;
    transform: scaleY(0);
  }
  to {
    height: var(--final-height);
    transform: scaleY(1);
  }
}

@keyframes growUp {
  0% {
    transform: scaleY(0);
    transform-origin: bottom;
  }
  100% {
    transform: scaleY(1);
    transform-origin: bottom;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-grow {
  animation: grow 1s ease-out forwards;
}

.animate-grow-up {
  animation: growUp 1s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Gradient text animation */
.gradient-text-animated {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Particle animation */
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.6;
  animation: particle-float 6s infinite ease-in-out;
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@layer utilities {
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
  .animation-delay-2000 {
    animation-delay: 2000ms;
  }
  .animation-delay-3000 {
    animation-delay: 3000ms;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(2deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

@keyframes pulse-slow {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes scan {
  0% {
    top: 0;
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0.8;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-scan {
  animation: scan 2s ease-in-out infinite;
}

:root {
  --background: 210 30% 98%; /* #F5F9FF - Light blue tinted background */
  --foreground: 220 20% 20%; /* Darker text for better readability */
  --muted: 210 25% 95%; /* Slightly tinted background for muted elements */
  --muted-foreground: 215 20% 45%; /* Muted text color */
  --popover: 0 0% 100%; /* White popover background */
  --popover-foreground: 220 20% 20%; /* Same as foreground */
  --card: 0 0% 100%; /* White card background */
  --card-foreground: 220 20% 20%; /* Same as foreground */
  --border: 214 20% 90%; /* Subtle border color */
  --input: 214 20% 90%; /* Same as border */

  /* Vibrant primary colors */
  --primary: 210 100% 50%; /* #007BFF - Bright blue */
  --primary-foreground: 0 0% 100%; /* White text on primary */

  /* Secondary color with more saturation */
  --secondary: 250 30% 60%; /* #7B68EE - Soft purple */
  --secondary-foreground: 0 0% 100%; /* White text on secondary */

  /* Accent color with more pop */
  --accent: 35 100% 60%; /* #FFA726 - Warm orange */
  --accent-foreground: 0 0% 100%; /* White text on accent */

  /* Status colors with more vibrancy */
  --success: 142 70% 45%; /* #2ECC71 - Vibrant green */
  --success-foreground: 0 0% 100%; /* White text on success */
  --info: 200 100% 50%; /* #03A9F4 - Bright cyan */
  --info-foreground: 0 0% 100%; /* White text on info */
  --warning: 35 100% 50%; /* #FF9800 - Bright orange */
  --warning-foreground: 0 0% 100%; /* White text on warning */
  --destructive: 354 85% 55%; /* #FF3B30 - Bright red */
  --destructive-foreground: 0 0% 100%; /* White text on destructive */

  --ring: 210 100% 50%; /* Same as primary */
  --radius: 0.75rem; /* Rounded corners */

  /* Chart colors - more vibrant */
  --chart-1: 210 100% 55%; /* Bright blue */
  --chart-2: 250 70% 65%; /* Vibrant purple */
  --chart-3: 142 70% 45%; /* Vibrant green */
  --chart-4: 354 85% 55%; /* Bright red */
  --chart-5: 35 100% 60%; /* Warm orange */
  --chart-6: 180 100% 45%; /* Turquoise */
}
  .dark {
  --background: 220 30% 10%; /* Dark background with blue-purple tint */
  --foreground: 210 40% 98%; /* Light text for dark mode */
  --muted: 220 25% 15%; /* Slightly lighter than background */
  --muted-foreground: 215 25% 70%; /* Muted text color with better contrast */
  --popover: 220 30% 10%; /* Same as background */
  --popover-foreground: 210 40% 98%; /* Same as foreground */
  --card: 220 30% 13%; /* Slightly lighter than background */
  --card-foreground: 210 40% 98%; /* Same as foreground */
  --border: 220 25% 20%; /* Subtle border color */
  --input: 220 25% 20%; /* Same as border */

  /* Vibrant primary colors for dark mode */
  --primary: 210 100% 65%; /* Brighter blue for dark mode */
  --primary-foreground: 0 0% 100%; /* White text on primary */

  /* Secondary color with more saturation for dark mode */
  --secondary: 250 50% 65%; /* Vibrant purple for dark mode */
  --secondary-foreground: 0 0% 100%; /* White text on secondary */

  /* Accent color with more pop for dark mode */
  --accent: 35 100% 65%; /* Bright orange for dark mode */
  --accent-foreground: 0 0% 100%; /* White text on accent */

  /* Status colors with more vibrancy for dark mode */
  --success: 142 70% 50%; /* Brighter green for dark mode */
  --success-foreground: 0 0% 100%; /* White text on success */
  --info: 200 100% 60%; /* Brighter cyan for dark mode */
  --info-foreground: 0 0% 100%; /* White text on info */
  --warning: 35 100% 60%; /* Brighter orange for dark mode */
  --warning-foreground: 0 0% 100%; /* White text on warning */
  --destructive: 354 85% 60%; /* Brighter red for dark mode */
  --destructive-foreground: 0 0% 100%; /* White text on destructive */

  --ring: 210 100% 65%; /* Same as primary */
  --radius: 0.75rem; /* Same as light mode */

  /* Chart colors - more vibrant for dark mode */
  --chart-1: 210 100% 65%; /* Bright blue */
  --chart-2: 250 70% 70%; /* Vibrant purple */
  --chart-3: 142 70% 50%; /* Vibrant green */
  --chart-4: 354 85% 60%; /* Bright red */
  --chart-5: 35 100% 65%; /* Warm orange */
  --chart-6: 180 100% 50%; /* Turquoise */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  :root {
    /* Professional gradient sidebar background */
    --sidebar-background: 230 50% 25%; /* #1E3A70 - Rich indigo blue for professional look */
    --sidebar-foreground: 0 0% 100%; /* White text on sidebar */
    --sidebar-primary: 0 0% 100%; /* White for primary elements */
    --sidebar-primary-foreground: 210 100% 60%; /* Bright blue */
    --sidebar-accent: 230 45% 35%; /* Slightly lighter than background for hover */
    --sidebar-accent-foreground: 0 0% 100%; /* White text on accent */
    --sidebar-active: 210 100% 50%; /* Bright blue for active items */
    --sidebar-active-foreground: 0 0% 100%; /* White text on active */
    --sidebar-border: 230 40% 40%; /* Enhanced border color for better visibility */
    --sidebar-ring: 210 100% 60%; /* Bright blue for focus ring */
  }

  .dark {
    /* Professional gradient sidebar background for dark mode */
    --sidebar-background: 235 60% 18%; /* #151E3D - Deep indigo blue for dark mode */
    --sidebar-foreground: 210 40% 98%; /* Light text for dark mode */
    --sidebar-primary: 210 40% 98%; /* Light color for primary elements */
    --sidebar-primary-foreground: 210 100% 65%; /* Brighter blue */
    --sidebar-accent: 235 50% 28%; /* Slightly lighter than background for hover */
    --sidebar-accent-foreground: 210 40% 98%; /* Light text on accent */
    --sidebar-active: 210 100% 65%; /* Bright blue for active items */
    --sidebar-active-foreground: 0 0% 100%; /* White text on active */
    --sidebar-border: 235 40% 35%; /* Enhanced border color for better visibility */
    --sidebar-ring: 210 100% 65%; /* Brighter blue for focus ring */
  }

  /* Optimized scrolling performance */
  .sidebar-scroll-optimized {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
    contain: layout style paint;
  }

  /* Enhanced sidebar scrollbar styling similar to TrackFina */
  .sidebar-scroll-area [data-radix-scroll-area-scrollbar] {
    width: 8px !important;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 2px;
    transition: all 0.2s ease;
  }

  .sidebar-scroll-area [data-radix-scroll-area-scrollbar]:hover {
    width: 12px !important;
    background: rgba(255, 255, 255, 0.15);
  }

  .sidebar-scroll-area [data-radix-scroll-area-thumb] {
    background: rgba(255, 255, 255, 0.4) !important;
    border-radius: 4px !important;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .sidebar-scroll-area [data-radix-scroll-area-thumb]:hover {
    background: rgba(255, 255, 255, 0.6) !important;
    border-color: rgba(255, 255, 255, 0.2);
  }

  .sidebar-scroll-area [data-radix-scroll-area-thumb]:active {
    background: rgba(255, 255, 255, 0.8) !important;
  }

  /* Custom scrollbar for webkit browsers as fallback */
  .sidebar-scroll-area::-webkit-scrollbar {
    width: 8px;
  }

  .sidebar-scroll-area::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  .sidebar-scroll-area::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.4);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
  }

  .sidebar-scroll-area::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .sidebar-scroll-area::-webkit-scrollbar-thumb:active {
    background: rgba(255, 255, 255, 0.8);
  }

  /* Dark mode scrollbar adjustments */
  .dark .sidebar-scroll-area [data-radix-scroll-area-scrollbar] {
    background: rgba(255, 255, 255, 0.05);
  }

  .dark .sidebar-scroll-area [data-radix-scroll-area-scrollbar]:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .dark .sidebar-scroll-area [data-radix-scroll-area-thumb] {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.05);
  }

  .dark .sidebar-scroll-area [data-radix-scroll-area-thumb]:hover {
    background: rgba(255, 255, 255, 0.5) !important;
    border-color: rgba(255, 255, 255, 0.1);
  }

  .dark .sidebar-scroll-area::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }

  .dark .sidebar-scroll-area::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.05);
  }

  .dark .sidebar-scroll-area::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
  }

  /* Smooth scroll behavior for sidebar navigation */
  .sidebar-scroll-area [data-radix-scroll-area-viewport] {
    scroll-behavior: smooth;
  }

  /* Reduce paint operations during scroll */
  .sidebar-nav-item {
    contain: layout style;
    transform: translateZ(0); /* Force hardware acceleration */
  }

  /* Optimize transitions for better performance */
  .sidebar-transition-optimized {
    transition-property: background-color, color;
    transition-duration: 200ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    will-change: background-color, color;
  }
}