import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function createSubscriptionPaymentMethodsTable() {
  const client = await pool.connect();
  try {
    console.log('Creating subscription_payment_methods table...');
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS subscription_payment_methods (
        id SERIAL PRIMARY KEY,
        shop_id INTEGER NOT NULL REFERENCES shops(id),
        method_type TEXT NOT NULL,
        card_number TEXT,
        card_holder_name TEXT,
        expiry_month INTEGER,
        expiry_year INTEGER,
        billing_address TEXT,
        is_default BOOLEAN DEFAULT false NOT NULL,
        active BOOLEAN DEFAULT true NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
        created_by INTEGER NOT NULL REFERENCES users(id)
      );
    `);
    
    console.log('✅ subscription_payment_methods table created successfully');
    
    // Check if table exists
    const result = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'subscription_payment_methods'
      );
    `);
    
    console.log('Table exists:', result.rows[0].exists);
    
  } catch (error) {
    console.error('Error creating table:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

createSubscriptionPaymentMethodsTable();
