import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Pizza, Wheat, GlassWater, IceCream, RefreshCw } from "lucide-react";
import { useAuth } from "@/context/auth-context";
import { useApp } from "@/context/app-context";
import { useSocketEvent } from "@/context/socket-context";
import { DashboardEvents } from "@/lib/socket";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Icons based on category
const categoryIcons: Record<string, React.ReactNode> = {
  "Main Course": <Pizza className="text-gray-500" />,
  "Breads": <Wheat className="text-gray-500" />,
  "Beverages": <GlassWater className="text-gray-500" />,
  "Desserts": <IceCream className="text-gray-500" />
};

interface TopProductsProps {
  refreshInterval?: number;
  socketEnabled?: boolean;
}

export default function TopProducts({
  refreshInterval = 30000,
  socketEnabled = false
}: TopProductsProps) {
  const { token } = useAuth();
  const { currentShop, currentBranch } = useApp();
  const [period, setPeriod] = useState<"today" | "week" | "month">("today");
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch top products from API
  const {
    data: products,
    isLoading,
    error,
    refetch: refetchProducts
  } = useQuery({
    queryKey: ['/api/dashboard/top-products', { period }, currentShop?.id, currentBranch?.id],
    queryFn: async ({ queryKey }) => {
      console.log('Fetching top products with query key:', queryKey);

      // Extract the period from the query key
      const [url, params] = queryKey;
      const periodParam = params?.period || 'today';

      // Make the API request with the period parameter
      const apiUrl = `${url}?period=${periodParam}`;
      console.log('Making request to:', apiUrl);

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': currentShop?.id?.toString() || '',
          'X-Branch-ID': currentBranch?.id?.toString() || ''
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error fetching top products:', errorText);
        throw new Error(`Failed to fetch top products: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Received top products data:', data);
      return data;
    },
    enabled: !!token && !!currentShop,
    refetchInterval: refreshInterval,
    retry: 2,
    staleTime: 30000, // Consider data fresh for 30 seconds
  });

  // Set up socket event listener for real-time updates
  useSocketEvent(DashboardEvents.TOP_PRODUCTS_UPDATED, (data) => {
    console.log('Received real-time top products update:', data);
    if (data.period === period) {
      // The query client will automatically update the cache with the new data
      refetchProducts();
    }
  }, [period]);

  // Handle manual refresh
  const handleRefresh = () => {
    if (!isRefreshing) {
      setIsRefreshing(true);
      refetchProducts().then(() => {
        setLastUpdated(new Date());
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      });
    }
  };

  // Update last updated time when products change
  useEffect(() => {
    if (products) {
      setLastUpdated(new Date());
    }
  }, [products]);

  // Force a refetch when period changes
  useEffect(() => {
    refetchProducts();
  }, [period, refetchProducts]);

  // Format the last updated time
  const formatLastUpdated = () => {
    const now = new Date();
    const diffMs = now.getTime() - lastUpdated.getTime();
    const diffSec = Math.floor(diffMs / 1000);

    if (diffSec < 60) {
      return `${diffSec}s ago`;
    } else if (diffSec < 3600) {
      return `${Math.floor(diffSec / 60)}m ago`;
    } else {
      return lastUpdated.toLocaleTimeString();
    }
  };

  if (error) {
    return (
      <Card className="border-t-4 border-t-accent/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center">
            <CardTitle>Top Selling Products</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              className="ml-2 text-muted-foreground hover:text-primary"
              onClick={() => refetchProducts()}
              title="Retry loading"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="p-6 text-center">
            <p className="text-red-500 mb-2">Failed to load top products.</p>
            <p className="text-sm text-muted-foreground">
              {error instanceof Error ? error.message : 'Unknown error occurred'}
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-4"
              onClick={() => refetchProducts()}
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-t-4 border-t-accent/50">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center">
          <CardTitle>Top Selling Products</CardTitle>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "ml-2 text-muted-foreground hover:text-primary",
              isRefreshing && "animate-spin text-primary"
            )}
            onClick={handleRefresh}
            disabled={isRefreshing}
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <span className="text-xs text-muted-foreground ml-2">
            Updated {formatLastUpdated()}
          </span>
        </div>
        <div className="text-sm">
          <Select
            value={period}
            onValueChange={(value) => setPeriod(value as "today" | "week" | "month")}
          >
            <SelectTrigger className="bg-muted/50 border-border/50 shadow-sm rounded-md px-3 py-1 text-sm h-auto w-32">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent className="mt-3">
        <div className="overflow-x-auto rounded-md border shadow-sm">
          <Table>
            <TableHeader className="bg-muted/50">
              <TableRow className="hover:bg-muted/70">
                <TableHead className="font-semibold">Product</TableHead>
                <TableHead className="font-semibold">Category</TableHead>
                <TableHead className="font-semibold">Quantity Sold</TableHead>
                <TableHead className="font-semibold">Revenue</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array(4).fill(0).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <div className="flex items-center">
                        <Skeleton className="h-10 w-10 rounded-md mr-4" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </TableCell>
                    <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  </TableRow>
                ))
              ) : products?.length > 0 ? (
                products.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-primary/10 rounded-md flex items-center justify-center text-primary shadow-sm">
                          {categoryIcons[product.category] || <Pizza className="text-primary" />}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-foreground">{product.name}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm font-medium text-muted-foreground">{product.category || "Uncategorized"}</TableCell>
                    <TableCell className="text-sm font-medium text-muted-foreground">{product.quantity_sold}</TableCell>
                    <TableCell className="text-sm font-semibold text-foreground">₹{product.revenue}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8">
                    <div className="flex flex-col items-center justify-center">
                      <p className="text-muted-foreground mb-2">
                        No products data available for the selected period
                      </p>
                      <p className="text-xs text-muted-foreground mb-4">
                        Try selecting a different time period or create some orders to see data here
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleRefresh}
                        className="flex items-center gap-2"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Refresh Data
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
