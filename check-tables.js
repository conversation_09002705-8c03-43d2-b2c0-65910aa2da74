import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkTables() {
  const client = await pool.connect();
  try {
    const result = await client.query('SELECT id, name, capacity, status FROM tables ORDER BY id');
    console.log('Current table data:');
    console.table(result.rows);
    
    // Check if there's a table with name "table3" but different ID
    const table3 = result.rows.find(row => row.name.toLowerCase().includes('3'));
    if (table3) {
      console.log('\nTable with "3" in name:', table3);
    }
    
    // Check if there's a table with ID 3
    const tableId3 = result.rows.find(row => row.id === 3);
    if (tableId3) {
      console.log('Table with ID 3:', tableId3);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

checkTables();
