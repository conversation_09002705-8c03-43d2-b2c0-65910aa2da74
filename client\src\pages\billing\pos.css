/* POS Page Specific Styles */

.pos-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Prevent horizontal overflow and ensure no scrolling on main container */
.pos-container * {
  box-sizing: border-box;
}

/* Ensure main POS layout doesn't scroll */
.pos-main-layout {
  height: 100vh;
  overflow: hidden;
}

/* Prevent middle section from scrolling */
.pos-products-section {
  overflow: hidden;
}

/* Quick search integration styles */
.pos-quick-search-trigger {
  flex-shrink: 0;
  min-width: 40px;
  height: 40px; /* Match input height */
}

/* Search bar container alignment */
.pos-search-container {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

/* Right sidebar layout fixes - ONLY cart area should scroll */
.pos-right-sidebar {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto; /* Prevent sidebar itself from scrolling */
}

.pos-right-sidebar-header {
  flex-shrink: 0;
  background: white;
  border-left: 1px solid #e5e7eb;
  max-height: 50vh; /* Limit header height to 50% of viewport */
  overflow: hidden; /* Prevent header from scrolling */
}

.pos-right-sidebar-content {
  flex: 1;
  overflow: hidden; /* Prevent content wrapper from scrolling */
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex child to shrink */
}

/* Ensure cart sidebar takes full height and manages scrolling properly */
.pos-cart-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ensure action buttons are always visible */
.pos-cart-actions {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
}

/* Left sidebar - Categories (15% width) */
.pos-sidebar {
  width: 15%;
  background-color: #343A40;
  color: white;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.pos-sidebar-header {
  padding: 0.75rem;
  text-align: center;
  font-weight: bold;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #ffffff;
  border-bottom: 1px solid #495057;
}

.pos-category-button {
  width: 100%;
  padding: 0.75rem;
  text-align: center;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  background-color: transparent;
  color: #ffffff;
  border-bottom: 1px solid #495057;
  transition: all 0.2s;
}

.pos-category-button:hover {
  background-color: #007BFF;
  color: white;
}

.pos-category-button.active {
  background-color: #007BFF;
  color: white;
}

.pos-category-icon {
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.5rem;
}

/* Middle section - Products (50% width) */
.pos-products-section {
  width: 50%;
  display: flex;
  flex-direction: column;
  background-color: #F8F9FA;
}

.pos-search {
  padding: 0.75rem;
  background-color: white;
  border-bottom: 1px solid #dee2e6;
}

.pos-search-input {
  position: relative;
}

.pos-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 0.75rem;
  height: 1rem;
  width: 1rem;
  color: #6c757d;
}

.pos-products {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

/* Enhanced scrollbar for better touch interaction */
.pos-products::-webkit-scrollbar {
  width: 8px;
}

.pos-products::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pos-products::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.pos-products::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.pos-product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

/* Responsive grid adjustments */
@media (min-width: 768px) {
  .pos-product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1200px) {
  .pos-product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.pos-product-card {
  background-color: white;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #f0f0f0;
  cursor: pointer;
}

.pos-product-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: #007BFF;
}

.pos-product-card:active {
  transform: scale(0.98);
}

.pos-product-badge {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #007BFF;
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0 0 0.75rem 0;
  font-weight: 500;
}

.pos-product-name {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pos-product-price {
  font-size: 1.125rem;
  font-weight: 600;
  color: #007BFF;
}

/* Right section - Order Summary (35% width) */
.pos-order {
  width: 35%;
  background-color: white;
  border-left: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
}

.pos-table-header {
  padding: 0.75rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pos-table-info {
  display: flex;
  gap: 1rem;
}

.pos-table-field {
  display: flex;
  flex-direction: column;
}

.pos-table-label {
  font-size: 0.75rem;
  color: #6c757d;
}

.pos-table-value {
  font-weight: 500;
}

.pos-customer-info {
  padding: 0.75rem;
  border-bottom: 1px solid #dee2e6;
}

.pos-customer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.pos-order-items {
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem;
}

.pos-order-header {
  display: flex;
  justify-content: space-between;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
  font-weight: 500;
  color: #6c757d;
  font-size: 0.875rem;
}

.pos-order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.pos-item-name {
  display: flex;
  align-items: center;
}

.pos-item-remove {
  margin-right: 0.5rem;
  cursor: pointer;
}

.pos-quantity-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pos-quantity-button {
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #dee2e6;
  background-color: white;
  cursor: pointer;
}

.pos-quantity-value {
  width: 1.5rem;
  text-align: center;
}

.pos-order-summary {
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
}

.pos-summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.pos-discount-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pos-discount-type {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pos-discount-input {
  width: 4rem;
  height: 2rem;
  text-align: right;
}

.pos-total-row {
  display: flex;
  justify-content: space-between;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
  font-weight: bold;
  font-size: 1.125rem;
}

.pos-total-value {
  color: #28A745;
}

.pos-actions {
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  gap: 0.5rem;
}

.pos-action-button {
  flex: 1;
  padding: 0.75rem;
  border-radius: 0.25rem;
  font-weight: 500;
  text-align: center;
  color: white;
  cursor: pointer;
}

.pos-action-save {
  background-color: #28A745;
}

.pos-action-save:hover {
  background-color: #218838;
}

.pos-action-print {
  background-color: #007BFF;
}

.pos-action-print:hover {
  background-color: #0069d9;
}

.pos-action-kot {
  background-color: #6C757D;
}

.pos-action-kot:hover {
  background-color: #5a6268;
}

/* Responsive layout adjustments for POS screen */
@media (max-width: 1280px) {
  /* Adjust category sidebar width */
  .w-[15\%] {
    width: 20%;
  }

  /* Adjust product section width */
  .w-[50\%] {
    width: 45%;
  }

  /* Adjust order summary width */
  .w-[35\%] {
    width: 35%;
  }

  /* Reduce grid columns for smaller screens */
  .grid-cols-2.md\:grid-cols-3.lg\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1024px) {
  /* Further adjust for smaller screens */
  .grid-cols-2.md\:grid-cols-3.lg\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Increase padding for better touch */
  .p-4 {
    padding: 1.25rem;
  }

  /* Make product cards taller */
  .min-h-\[120px\] {
    min-height: 140px;
  }
}
