// Test script to call the cleanup endpoint
import fetch from 'node-fetch';

async function testCleanup() {
  try {
    // First login to get a token
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);

    if (!loginData.token) {
      console.error('No token received');
      return;
    }

    // Get the first shop and branch
    const shopId = loginData.shops[0]?.id;
    const branchId = loginData.branches[0]?.id;

    console.log(`Using shop ${shopId}, branch ${branchId}`);

    // Call cleanup endpoint
    const cleanupResponse = await fetch('http://localhost:5000/api/debug/cleanup-database', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${loginData.token}`,
        'x-shop-id': shopId.toString(),
        'x-branch-id': branchId.toString()
      }
    });

    const cleanupData = await cleanupResponse.json();
    console.log('Cleanup response:', cleanupData);

  } catch (error) {
    console.error('Error:', error);
  }
}

testCleanup();
