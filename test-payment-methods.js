// Test payment methods API
const fetch = require('node-fetch');

async function testPaymentMethods() {
  try {
    console.log('Testing payment methods API...');
    
    // Test GET payment methods
    const response = await fetch('http://localhost:5000/api/payment-methods/subscription', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Payment methods data:', JSON.stringify(data, null, 2));
    } else {
      const error = await response.text();
      console.log('Error response:', error);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testPaymentMethods();
