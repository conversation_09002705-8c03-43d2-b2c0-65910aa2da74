import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function createNotificationsForAllShops() {
  const client = await pool.connect();
  try {
    console.log('🔔 Creating notifications for ALL shops so you can see them regardless of which shop you select...\n');

    // 1. Get all shops and users
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    const usersResult = await client.query(`
      SELECT DISTINCT u.id, u.name, u.email
      FROM users u
      ORDER BY u.id
    `);

    console.log('🏪 Found shops:');
    shopsResult.rows.forEach(shop => {
      console.log(`   Shop ${shop.id}: ${shop.name}`);
    });

    console.log('\n👥 Found users:');
    usersResult.rows.forEach(user => {
      console.log(`   User ${user.id}: ${user.name} (${user.email})`);
    });

    if (shopsResult.rows.length === 0 || usersResult.rows.length === 0) {
      console.log('❌ No shops or users found.');
      return;
    }

    // 2. Clear ALL existing notifications
    await client.query('DELETE FROM notification_recipients');
    await client.query('DELETE FROM notifications');
    console.log('\n🗑️  Cleared all existing notifications');

    // 3. Create notifications for EACH shop
    let totalNotifications = 0;

    for (const shop of shopsResult.rows) {
      console.log(`\n🔔 Creating notifications for ${shop.name} (Shop ID: ${shop.id})...`);

      // Sample notifications for this shop
      const notifications = [
        {
          title: `${shop.name} - Stock Alert!`,
          message: `Dosa mix is running low at ${shop.name} (0 pieces remaining). Please reorder immediately!`,
          type: 'stock',
          priority: 'urgent'
        },
        {
          title: `${shop.name} - Low Stock Warning`,
          message: `Ice cream stock is low at ${shop.name} (1 piece remaining, minimum: 3)`,
          type: 'stock',
          priority: 'high'
        },
        {
          title: `${shop.name} - New Order`,
          message: `Order #ORD-${String(shop.id).padStart(3, '0')} received at ${shop.name}`,
          type: 'order',
          priority: 'normal'
        },
        {
          title: `Welcome to ${shop.name}!`,
          message: `Your notification system is active for ${shop.name}. You'll receive alerts for orders, stock, and more!`,
          type: 'system',
          priority: 'normal'
        },
        {
          title: `${shop.name} - Daily Summary`,
          message: `Great day at ${shop.name}! You made ₹1,250 from 15 orders today.`,
          type: 'system',
          priority: 'normal'
        }
      ];

      // Create each notification for this shop
      for (const notif of notifications) {
        const notificationResult = await client.query(`
          INSERT INTO notifications (
            title, message, type, priority, recipient_type, recipient_id, shop_id, created_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, NOW() - INTERVAL '${Math.floor(Math.random() * 24)} hours')
          RETURNING id
        `, [
          notif.title,
          notif.message,
          notif.type,
          notif.priority,
          'shop',
          shop.id,
          shop.id
        ]);

        const notificationId = notificationResult.rows[0].id;

        // Create recipient records for ALL users (so any user can see notifications for any shop)
        for (const user of usersResult.rows) {
          const status = Math.random() > 0.3 ? 'unread' : 'read'; // 70% unread, 30% read
          
          await client.query(`
            INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
            VALUES ($1, $2, $3, NOW() - INTERVAL '${Math.floor(Math.random() * 24)} hours')
          `, [notificationId, user.id, status]);
        }

        totalNotifications++;
        console.log(`   ✅ ${notif.title} (${notif.priority})`);
      }

      // Create notification settings for all users for this shop
      for (const user of usersResult.rows) {
        const types = ['stock', 'order', 'system', 'marketing'];
        for (const type of types) {
          await client.query(`
            INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
            enabled = $4, delivery_methods = $5
          `, [user.id, shop.id, type, true, JSON.stringify(['in_app'])]);
        }
      }
    }

    // 4. Show summary by shop
    console.log('\n📊 Summary by Shop:');
    for (const shop of shopsResult.rows) {
      const shopSummary = await client.query(`
        SELECT 
          COUNT(DISTINCT n.id) as total_notifications,
          COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_notifications
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE n.shop_id = $1
      `, [shop.id]);

      const summary = shopSummary.rows[0];
      console.log(`   ${shop.name} (ID: ${shop.id}):`);
      console.log(`     - Total notifications: ${summary.total_notifications}`);
      console.log(`     - Unread notifications: ${summary.unread_notifications}`);
    }

    // 5. Show summary by user
    console.log('\n📊 Summary by User:');
    for (const user of usersResult.rows) {
      const userSummary = await client.query(`
        SELECT COUNT(*) as unread_count
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE nr.user_id = $1 AND nr.status = 'unread'
      `, [user.id]);

      console.log(`   ${user.name}: ${userSummary.rows[0].unread_count} unread notifications`);
    }

    console.log(`\n🎉 SUCCESS! Created ${totalNotifications} notifications across ${shopsResult.rows.length} shops`);
    
    console.log('\n🎯 What to do now:');
    console.log('   1. Refresh your browser (Ctrl+Shift+R)');
    console.log('   2. Login with any user account');
    console.log('   3. Select ANY shop from the dropdown');
    console.log('   4. Check the notification bell - should show notifications for that shop');
    console.log('   5. Switch to different shops to see different notifications');

    console.log('\n💡 Now notifications will appear for:');
    console.log('   - Whatever shop you currently have selected');
    console.log('   - Whatever user you are logged in as');
    console.log('   - All combinations of users and shops have notifications');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

createNotificationsForAllShops();
