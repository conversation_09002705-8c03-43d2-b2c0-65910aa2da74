import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

async function fixSubscriptionPaymentMethodsTable() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing subscription_payment_methods table structure...\n');
    
    // Drop the existing table and recreate with correct structure
    console.log('1. Dropping existing table...');
    await client.query('DROP TABLE IF EXISTS subscription_payment_methods CASCADE;');
    console.log('   ✅ Table dropped');
    
    // Create the table with correct structure matching the schema
    console.log('\n2. Creating table with correct structure...');
    await client.query(`
      CREATE TABLE subscription_payment_methods (
        id SERIAL PRIMARY KEY,
        shop_id INTEGER NOT NULL REFERENCES shops(id),
        method_type TEXT NOT NULL DEFAULT 'card',
        card_number TEXT,
        card_holder_name TEXT,
        expiry_month INTEGER,
        expiry_year INTEGER,
        billing_address TEXT,
        is_default BOOLEAN DEFAULT false NOT NULL,
        active BOOLEAN DEFAULT true NOT NULL,
        created_at TIMESTAMP DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
        created_by INTEGER NOT NULL REFERENCES users(id)
      );
    `);
    console.log('   ✅ Table created with correct structure');
    
    // Verify the new structure
    console.log('\n3. Verifying new table structure...');
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'subscription_payment_methods' 
      ORDER BY ordinal_position;
    `);
    
    console.log('   ✅ New table columns:');
    result.rows.forEach(col => {
      console.log(`      ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    console.log('\n🎉 Table structure fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing table:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

fixSubscriptionPaymentMethodsTable();
