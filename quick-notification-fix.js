import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function createNotificationsForCurrentShop() {
  const client = await pool.connect();
  try {
    console.log('🔔 Creating notifications for your current shop...\n');

    // Get the first shop (assuming it's your main shop)
    const shopResult = await client.query('SELECT id, name FROM shops ORDER BY id LIMIT 1');
    if (shopResult.rows.length === 0) {
      console.log('❌ No shops found. Please create a shop first.');
      return;
    }

    const shop = shopResult.rows[0];
    console.log(`📍 Using shop: ${shop.name} (ID: ${shop.id})`);

    // Get the first user (assuming it's you)
    const userResult = await client.query('SELECT id, name FROM users ORDER BY id LIMIT 1');
    if (userResult.rows.length === 0) {
      console.log('❌ No users found. Please create a user first.');
      return;
    }

    const user = userResult.rows[0];
    console.log(`👤 Using user: ${user.name} (ID: ${user.id})`);

    // Clear existing notifications for this shop
    await client.query(`
      DELETE FROM notification_recipients 
      WHERE notification_id IN (
        SELECT id FROM notifications WHERE shop_id = $1
      )
    `, [shop.id]);
    
    await client.query('DELETE FROM notifications WHERE shop_id = $1', [shop.id]);
    console.log('🗑️  Cleared existing notifications for this shop');

    // Create sample notifications
    const notifications = [
      {
        title: 'Welcome to NembooBill!',
        message: 'Thank you for using our POS system. Check out all the amazing features!',
        type: 'system',
        priority: 'normal'
      },
      {
        title: 'New Order Received',
        message: 'Order #ORD-001 has been placed by customer Sarah Johnson',
        type: 'order',
        priority: 'normal'
      },
      {
        title: 'Low Stock Alert',
        message: 'Product "Espresso Beans" is running low (3 remaining, minimum: 10)',
        type: 'stock',
        priority: 'high'
      },
      {
        title: 'Daily Sales Update',
        message: 'Great day! You made $850.00 from 18 orders today.',
        type: 'system',
        priority: 'normal'
      },
      {
        title: 'Weekend Special!',
        message: '🎉 Weekend promotion: Buy 2 coffees, get 1 free! Valid until Sunday.',
        type: 'marketing',
        priority: 'normal'
      }
    ];

    console.log('\n📝 Creating notifications...');

    for (let i = 0; i < notifications.length; i++) {
      const notif = notifications[i];
      
      // Create notification
      const notificationResult = await client.query(`
        INSERT INTO notifications (title, message, type, priority, recipient_type, recipient_id, shop_id, created_by, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW() - INTERVAL '${i * 2} hours')
        RETURNING id
      `, [
        notif.title,
        notif.message,
        notif.type,
        notif.priority,
        'shop',
        shop.id,
        shop.id,
        user.id
      ]);

      const notificationId = notificationResult.rows[0].id;

      // Create recipient record (mark some as read, some as unread)
      const status = i < 3 ? 'unread' : 'read'; // First 3 are unread
      await client.query(`
        INSERT INTO notification_recipients (notification_id, user_id, status, delivered_at)
        VALUES ($1, $2, $3, NOW() - INTERVAL '${i * 2} hours')
      `, [notificationId, user.id, status]);

      console.log(`   ✅ ${notif.title} (${status})`);
    }

    // Create notification settings
    console.log('\n⚙️  Setting up notification preferences...');
    const notificationTypes = ['order', 'stock', 'marketing', 'system'];
    for (const type of notificationTypes) {
      await client.query(`
        INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
        enabled = $4, delivery_methods = $5
      `, [user.id, shop.id, type, true, JSON.stringify(['in_app'])]);
    }

    // Show final count
    const countResult = await client.query(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE n.shop_id = $1 AND nr.user_id = $2
    `, [shop.id, user.id]);

    const counts = countResult.rows[0];
    console.log(`\n🎉 Success! Created ${counts.total} notifications (${counts.unread} unread)`);
    console.log('\n💡 Now refresh your browser and check the notification bell!');
    console.log(`🔔 You should see a red badge with "${counts.unread}" on the bell icon.`);
    
  } catch (error) {
    console.error('❌ Error creating notifications:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
createNotificationsForCurrentShop();
