import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, invalidateAndRefetch } from "@/lib/queryClient";
import { Loader2 } from "lucide-react";

// Customer form schema
const customerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string()
    .length(10, "Phone number must be exactly 10 digits")
    .regex(/^\d{10}$/, "Phone number must contain only digits"),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  address: z.string().optional(),
  gstNumber: z.string().optional(),
  campaignOptIn: z.boolean().default(true),
});

type CustomerFormValues = z.infer<typeof customerSchema>;

interface Customer {
  id: number;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  gstNumber?: string;
  campaignOptIn?: boolean;
}

interface CustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer | null;
  onCustomerSaved?: (customer: Customer) => void;
}

export function CustomerModal({ 
  isOpen, 
  onClose, 
  customer = null, 
  onCustomerSaved 
}: CustomerModalProps) {
  const { toast } = useToast();
  const isEditing = !!customer;

  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: customer?.name || "",
      phone: customer?.phone || "",
      email: customer?.email || "",
      address: customer?.address || "",
      gstNumber: customer?.gstNumber || "",
      campaignOptIn: customer?.campaignOptIn ?? true,
    },
  });

  // Reset form when customer changes
  React.useEffect(() => {
    if (isOpen) {
      form.reset({
        name: customer?.name || "",
        phone: customer?.phone || "",
        email: customer?.email || "",
        address: customer?.address || "",
        gstNumber: customer?.gstNumber || "",
        campaignOptIn: customer?.campaignOptIn ?? true,
      });
    }
  }, [customer, isOpen, form]);

  // Create customer mutation
  const createCustomerMutation = useMutation({
    mutationFn: async (data: CustomerFormValues) => {
      const response = await apiRequest("POST", "/api/customers", data);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }
      return response.json();
    },
    onSuccess: (newCustomer) => {
      toast({
        title: "Customer created",
        description: "Customer has been created successfully",
      });
      // Let the parent component handle data refresh
      onCustomerSaved?.(newCustomer);
      onClose();
      form.reset();
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create customer",
      });
    },
  });

  // Update customer mutation
  const updateCustomerMutation = useMutation({
    mutationFn: async (data: CustomerFormValues) => {
      if (!customer) throw new Error("No customer to update");
      const response = await apiRequest("PATCH", `/api/customers/${customer.id}`, data);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText);
      }
      return response.json();
    },
    onSuccess: (updatedCustomer) => {
      toast({
        title: "Customer updated",
        description: "Customer has been updated successfully",
      });
      // Let the parent component handle data refresh
      onCustomerSaved?.(updatedCustomer);
      onClose();
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update customer",
      });
    },
  });

  const onSubmit = (data: CustomerFormValues) => {
    if (isEditing) {
      updateCustomerMutation.mutate(data);
    } else {
      createCustomerMutation.mutate(data);
    }
  };

  const isLoading = createCustomerMutation.isPending || updateCustomerMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Customer" : "Add New Customer"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="Customer name"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone *</Label>
              <Input
                id="phone"
                {...form.register("phone")}
                placeholder="Phone number (10 digits)"
                type="tel"
                maxLength={10}
                onInput={(e) => {
                  // Allow only numeric input
                  const target = e.target as HTMLInputElement;
                  target.value = target.value.replace(/[^0-9]/g, '');
                }}
              />
              {form.formState.errors.phone && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.phone.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...form.register("email")}
              placeholder="Email address"
            />
            {form.formState.errors.email && (
              <p className="text-sm text-red-500">
                {form.formState.errors.email.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              {...form.register("address")}
              placeholder="Customer address"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="gstNumber">GST Number</Label>
            <Input
              id="gstNumber"
              {...form.register("gstNumber")}
              placeholder="GST number"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="campaignOptIn"
              checked={form.watch("campaignOptIn")}
              onCheckedChange={(checked) => 
                form.setValue("campaignOptIn", checked as boolean)
              }
            />
            <Label htmlFor="campaignOptIn" className="text-sm">
              Opt-in for marketing campaigns
            </Label>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update Customer" : "Create Customer"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
