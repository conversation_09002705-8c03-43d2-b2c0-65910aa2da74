import pg from 'pg';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function runNotificationsMigration() {
  const client = await pool.connect();
  try {
    console.log('🚀 Running notifications migration...\n');

    // Read the migration file
    const migrationSQL = fs.readFileSync('migrations/notifications.sql', 'utf8');
    
    // Execute the migration
    await client.query(migrationSQL);
    
    console.log('✅ Notifications migration completed successfully!');
    console.log('📋 Created tables:');
    console.log('   - notifications (main notification data)');
    console.log('   - notification_recipients (user-specific notification tracking)');
    console.log('   - notification_settings (user notification preferences)');
    console.log('📊 Added indexes for performance optimization');
    console.log('🎯 Inserted sample notifications for testing');
    console.log('\n🔔 Notification system is now ready to use!');
    
  } catch (error) {
    console.error('❌ Error running notifications migration:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runNotificationsMigration()
  .then(() => {
    console.log('\n✨ Migration process completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  });
