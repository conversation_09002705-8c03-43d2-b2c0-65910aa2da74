// Test if route ordering fix worked
console.log('🧪 Testing Route Ordering Fix...\n');

const testRouteOrdering = async () => {
  const baseURL = 'http://localhost:5000';
  
  try {
    // Test the specific routes that were failing
    const routes = [
      '/api/subscriptions/stats',
      '/api/subscriptions/usage', 
      '/api/subscriptions/billing-history'
    ];
    
    for (const route of routes) {
      console.log(`Testing ${route}...`);
      
      const response = await fetch(`${baseURL}${route}`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-token',
          'X-Shop-ID': '12'
        }
      });
      
      console.log(`  Status: ${response.status}`);
      
      if (response.status === 403) {
        console.log('  ✅ Route found (authentication required)');
      } else if (response.status === 400) {
        const error = await response.text();
        if (error.includes('Invalid subscription ID')) {
          console.log('  ❌ Route ordering issue still exists');
        } else {
          console.log('  ✅ Route found (different validation error)');
        }
      } else {
        console.log(`  ✅ Route found (status: ${response.status})`);
      }
    }
    
    console.log('\n🎉 Route ordering test completed!');
    
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
};

testRouteOrdering();
