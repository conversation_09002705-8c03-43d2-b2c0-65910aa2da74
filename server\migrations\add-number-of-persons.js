import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Database connection using DATABASE_URL
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration: Adding numberOfPersons column to orders table');
    
    // Check if column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'orders' AND column_name = 'number_of_persons'
    `;
    
    const columnExists = await client.query(checkColumnQuery);
    
    if (columnExists.rows.length > 0) {
      console.log('Column number_of_persons already exists in orders table, skipping');
      return;
    }
    
    // Add the column
    const addColumnQuery = `
      ALTER TABLE "orders" ADD COLUMN "number_of_persons" integer;
    `;
    
    await client.query(addColumnQuery);
    console.log('Successfully added number_of_persons column to orders table');
    
    // Add comment
    const addCommentQuery = `
      COMMENT ON COLUMN "orders"."number_of_persons" IS 'Number of persons for dine-in orders';
    `;
    
    await client.query(addCommentQuery);
    console.log('Successfully added comment to number_of_persons column');
    
    console.log('Migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runMigration().catch(console.error);
