// Test only the payment methods API to see detailed error
console.log('🧪 Testing Payment Methods API Only...\n');

const testPaymentMethods = async () => {
  try {
    console.log('Making request to payment methods API...');

    const response = await fetch('http://localhost:5000/api/payment-methods/subscription', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token',
        'X-Shop-ID': '12',
        'Content-Type': 'application/json'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      console.log('✅ Success!');
    } else {
      console.log('❌ Error response received');
    }

  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
};

testPaymentMethods();