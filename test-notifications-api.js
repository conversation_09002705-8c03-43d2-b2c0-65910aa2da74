import fetch from 'node-fetch';

const API_BASE = 'http://localhost:5000';

async function testNotificationsAPI() {
  console.log('🧪 Testing Notifications API...\n');

  try {
    // Test 1: Check notifications endpoint without auth (should get 401)
    console.log('1. Testing notifications endpoint...');
    
    const response1 = await fetch(`${API_BASE}/api/notifications`, {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    
    console.log(`   Status: ${response1.status} ${response1.statusText}`);
    
    if (response1.status === 401) {
      console.log('   ✅ API is working (requires authentication)');
    } else {
      console.log('   ⚠️  Unexpected response');
    }

    // Test 2: Check unread count endpoint
    console.log('\n2. Testing unread count endpoint...');
    
    const response2 = await fetch(`${API_BASE}/api/notifications/unread-count`, {
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    
    console.log(`   Status: ${response2.status} ${response2.statusText}`);

    // Test 3: Direct database check
    console.log('\n3. Checking database directly...');
    
    // Import database connection
    const { Pool } = await import('pg');
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
    });

    const client = await pool.connect();
    
    try {
      // Check notifications in database
      const notificationsResult = await client.query(`
        SELECT 
          n.id,
          n.title,
          n.message,
          n.type,
          n.priority,
          n.shop_id,
          nr.user_id,
          nr.status,
          n.created_at
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE n.type = 'stock'
        ORDER BY n.created_at DESC
        LIMIT 10
      `);

      console.log(`   📋 Found ${notificationsResult.rows.length} stock notifications in database:`);
      
      notificationsResult.rows.forEach((notif, index) => {
        console.log(`     ${index + 1}. ${notif.title} (Shop: ${notif.shop_id}, User: ${notif.user_id}, Status: ${notif.status})`);
      });

      // Check unread count by user and shop
      const unreadCountResult = await client.query(`
        SELECT 
          nr.user_id,
          n.shop_id,
          COUNT(*) as unread_count
        FROM notifications n
        JOIN notification_recipients nr ON n.id = nr.notification_id
        WHERE nr.status = 'unread'
        GROUP BY nr.user_id, n.shop_id
        ORDER BY nr.user_id, n.shop_id
      `);

      console.log(`\n   📊 Unread notifications by user/shop:`);
      unreadCountResult.rows.forEach(row => {
        console.log(`     User ${row.user_id}, Shop ${row.shop_id}: ${row.unread_count} unread`);
      });

      // Check users and their current shop
      const usersResult = await client.query(`
        SELECT u.id, u.name, u.email, us.shop_id
        FROM users u
        LEFT JOIN user_shops us ON u.id = us.user_id
        ORDER BY u.id
      `);

      console.log(`\n   👥 Users and their shops:`);
      usersResult.rows.forEach(user => {
        console.log(`     User ${user.id} (${user.name}): Shop ${user.shop_id || 'None'}`);
      });

    } finally {
      client.release();
      await pool.end();
    }

    console.log('\n🎯 Troubleshooting Steps:');
    console.log('   1. Make sure you are logged in to the application');
    console.log('   2. Check that you have the correct shop selected');
    console.log('   3. Try hard refresh (Ctrl+Shift+R)');
    console.log('   4. Check browser console for any errors');
    console.log('   5. Verify your user ID matches the notification recipients');

  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

// Run the test
testNotificationsAPI();
