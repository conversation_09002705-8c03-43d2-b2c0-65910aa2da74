import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useNotifications } from "@/hooks/use-notifications";
import { Bell, Package, Megaphone, Settings, Mail, Smartphone } from "lucide-react";

interface NotificationSetting {
  id: number;
  userId: number;
  shopId: number;
  notificationType: string;
  enabled: boolean;
  deliveryMethods: string[];
  settings: Record<string, any>;
}

const notificationTypes = [
  {
    type: 'order',
    title: 'Order Notifications',
    description: 'Get notified about new orders, status changes, and payments',
    icon: Bell,
    color: 'text-blue-600'
  },
  {
    type: 'stock',
    title: 'Stock Alerts',
    description: 'Receive alerts for low stock, out of stock, and reorder notifications',
    icon: Package,
    color: 'text-orange-600'
  },
  {
    type: 'marketing',
    title: 'Marketing Messages',
    description: 'Stay updated with promotional campaigns and marketing announcements',
    icon: Megaphone,
    color: 'text-green-600'
  },
  {
    type: 'system',
    title: 'System Notifications',
    description: 'Important system updates, maintenance alerts, and security notices',
    icon: Settings,
    color: 'text-gray-600'
  }
];

const deliveryMethods = [
  {
    method: 'in_app',
    title: 'In-App Notifications',
    description: 'Show notifications within the application',
    icon: Bell
  },
  {
    method: 'email',
    title: 'Email Notifications',
    description: 'Send notifications to your email address',
    icon: Mail
  },
  {
    method: 'sms',
    title: 'SMS Notifications',
    description: 'Send notifications to your mobile phone',
    icon: Smartphone
  }
];

export default function NotificationSettings() {
  const {
    settings,
    settingsLoading: isLoading,
    updateSetting,
    getSettingForType,
    isUpdatingSetting
  } = useNotifications();

  const handleToggleNotification = (type: string, enabled: boolean) => {
    const currentSetting = getSettingForType(type);
    updateSetting(type, enabled, currentSetting?.deliveryMethods || ['in_app']);
  };

  const handleToggleDeliveryMethod = (type: string, method: string, enabled: boolean) => {
    const currentSetting = getSettingForType(type);
    if (!currentSetting) return;

    let newMethods = [...currentSetting.deliveryMethods];

    if (enabled) {
      if (!newMethods.includes(method)) {
        newMethods.push(method);
      }
    } else {
      newMethods = newMethods.filter(m => m !== method);
    }

    // Ensure at least one delivery method is selected
    if (newMethods.length === 0) {
      newMethods = ['in_app'];
    }

    updateSetting(type, currentSetting.enabled, newMethods);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Notification Settings</h2>
        <p className="text-muted-foreground">
          Manage your notification preferences and delivery methods.
        </p>
      </div>

      <div className="grid gap-6">
        {notificationTypes.map((notificationType) => {
          const setting = getSettingForType(notificationType.type);
          const isEnabled = setting?.enabled ?? true;
          const Icon = notificationType.icon;

          return (
            <Card key={notificationType.type}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Icon className={`h-5 w-5 ${notificationType.color}`} />
                    <div>
                      <CardTitle className="text-lg">{notificationType.title}</CardTitle>
                      <CardDescription>{notificationType.description}</CardDescription>
                    </div>
                  </div>
                  <Switch
                    checked={isEnabled}
                    onCheckedChange={(checked) =>
                      handleToggleNotification(notificationType.type, checked)
                    }
                    disabled={isUpdatingSetting}
                  />
                </div>
              </CardHeader>
              
              {isEnabled && (
                <CardContent className="pt-0">
                  <Separator className="mb-4" />
                  <div>
                    <Label className="text-sm font-medium mb-3 block">
                      Delivery Methods
                    </Label>
                    <div className="space-y-3">
                      {deliveryMethods.map((method) => {
                        const isMethodEnabled = setting?.deliveryMethods?.includes(method.method) ?? false;
                        const MethodIcon = method.icon;

                        return (
                          <div key={method.method} className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <MethodIcon className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <div className="text-sm font-medium">{method.title}</div>
                                <div className="text-xs text-muted-foreground">
                                  {method.description}
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {method.method === 'in_app' && (
                                <Badge variant="secondary" className="text-xs">
                                  Required
                                </Badge>
                              )}
                              <Switch
                                checked={isMethodEnabled}
                                onCheckedChange={(checked) =>
                                  handleToggleDeliveryMethod(notificationType.type, method.method, checked)
                                }
                                disabled={
                                  isUpdatingSetting ||
                                  (method.method === 'in_app' && isMethodEnabled)
                                }
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage all notification settings at once
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Button
              variant="outline"
              onClick={() => {
                notificationTypes.forEach(type => {
                  handleToggleNotification(type.type, true);
                });
              }}
              disabled={isUpdatingSetting}
            >
              Enable All
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                notificationTypes.forEach(type => {
                  handleToggleNotification(type.type, false);
                });
              }}
              disabled={isUpdatingSetting}
            >
              Disable All
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
