import pg from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const { Pool } = pg;

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function createTestNotificationForCurrentUser() {
  const client = await pool.connect();
  try {
    console.log('🔔 Creating test notification for your current user...\n');

    // 1. Check what users and shops exist
    const usersResult = await client.query(`
      SELECT u.id, u.name, u.email, us.shop_id
      FROM users u
      LEFT JOIN user_shops us ON u.id = us.user_id
      ORDER BY u.id
    `);

    console.log('👥 Available users:');
    usersResult.rows.forEach(user => {
      console.log(`   User ${user.id}: ${user.name} (${user.email}) - Shop: ${user.shop_id || 'None'}`);
    });

    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    console.log('\n🏪 Available shops:');
    shopsResult.rows.forEach(shop => {
      console.log(`   Shop ${shop.id}: ${shop.name}`);
    });

    if (usersResult.rows.length === 0) {
      console.log('❌ No users found. Please create a user first.');
      return;
    }

    if (shopsResult.rows.length === 0) {
      console.log('❌ No shops found. Please create a shop first.');
      return;
    }

    // 2. Use the first user and first shop
    const user = usersResult.rows[0];
    const shop = shopsResult.rows[0];

    console.log(`\n🎯 Creating notification for:`);
    console.log(`   User: ${user.name} (ID: ${user.id})`);
    console.log(`   Shop: ${shop.name} (ID: ${shop.id})`);

    // 3. Clear existing notifications for this user
    await client.query(`
      DELETE FROM notification_recipients 
      WHERE user_id = $1
    `, [user.id]);
    
    await client.query(`
      DELETE FROM notifications 
      WHERE shop_id = $1
    `, [shop.id]);

    console.log('\n🗑️  Cleared existing notifications');

    // 4. Create a simple test notification
    const notificationResult = await client.query(`
      INSERT INTO notifications (
        title, 
        message, 
        type, 
        priority, 
        recipient_type, 
        recipient_id, 
        shop_id, 
        data,
        created_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      RETURNING id
    `, [
      'Test Notification - Stock Alert',
      'This is a test notification to verify the system is working. Your dosa stock is low (0 pieces remaining).',
      'stock',
      'urgent',
      'shop',
      shop.id,
      shop.id,
      JSON.stringify({
        productName: 'dosa',
        currentStock: 0,
        minStock: 5
      })
    ]);

    const notificationId = notificationResult.rows[0].id;
    console.log(`✅ Created notification with ID: ${notificationId}`);

    // 5. Create recipient record
    await client.query(`
      INSERT INTO notification_recipients (
        notification_id, 
        user_id, 
        status, 
        delivered_at
      )
      VALUES ($1, $2, $3, NOW())
    `, [notificationId, user.id, 'unread']);

    console.log(`✅ Created recipient record for user ${user.id}`);

    // 6. Create another notification for good measure
    const notification2Result = await client.query(`
      INSERT INTO notifications (
        title, 
        message, 
        type, 
        priority, 
        recipient_type, 
        recipient_id, 
        shop_id, 
        data,
        created_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      RETURNING id
    `, [
      'Welcome to NembooBill!',
      'Thank you for using our POS system. This notification confirms that your notification system is working perfectly!',
      'system',
      'normal',
      'shop',
      shop.id,
      shop.id,
      JSON.stringify({
        type: 'welcome'
      })
    ]);

    const notification2Id = notification2Result.rows[0].id;

    await client.query(`
      INSERT INTO notification_recipients (
        notification_id, 
        user_id, 
        status, 
        delivered_at
      )
      VALUES ($1, $2, $3, NOW())
    `, [notification2Id, user.id, 'unread']);

    console.log(`✅ Created second notification with ID: ${notification2Id}`);

    // 7. Verify the notifications were created
    const verifyResult = await client.query(`
      SELECT 
        n.id,
        n.title,
        n.message,
        n.type,
        n.priority,
        nr.status,
        nr.user_id,
        n.shop_id
      FROM notifications n
      JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE nr.user_id = $1 AND n.shop_id = $2
      ORDER BY n.created_at DESC
    `, [user.id, shop.id]);

    console.log(`\n📋 Verification - Found ${verifyResult.rows.length} notifications:`);
    verifyResult.rows.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.title} (${notif.type}, ${notif.priority}, ${notif.status})`);
    });

    // 8. Create notification settings
    await client.query(`
      INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
      enabled = $4, delivery_methods = $5
    `, [user.id, shop.id, 'stock', true, JSON.stringify(['in_app'])]);

    await client.query(`
      INSERT INTO notification_settings (user_id, shop_id, notification_type, enabled, delivery_methods)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (user_id, shop_id, notification_type) DO UPDATE SET
      enabled = $4, delivery_methods = $5
    `, [user.id, shop.id, 'system', true, JSON.stringify(['in_app'])]);

    console.log(`✅ Created notification settings`);

    console.log('\n🎉 SUCCESS! Test notifications created.');
    console.log('\n🎯 What to do now:');
    console.log('   1. Make sure you are logged in as the user shown above');
    console.log('   2. Make sure you have the shop shown above selected');
    console.log('   3. Hard refresh your browser (Ctrl+Shift+R)');
    console.log('   4. Check the notification bell - should show red badge "2"');
    console.log('   5. Click the bell to see the notifications');

    console.log('\n💡 If still not working:');
    console.log('   - Check browser console for errors');
    console.log('   - Verify you are logged in as the correct user');
    console.log('   - Try opening in incognito/private window');

  } catch (error) {
    console.error('❌ Error creating test notification:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the script
createTestNotificationForCurrentUser();
