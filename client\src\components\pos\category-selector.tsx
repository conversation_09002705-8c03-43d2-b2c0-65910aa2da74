import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ShoppingBag } from "lucide-react";

interface Category {
  id: number;
  name: string;
  description?: string;
}

interface CategorySelectorProps {
  categories: Category[];
  selectedCategoryId: number | null;
  onSelectCategory: (categoryId: number | null) => void;
  className?: string;
  variant?: "horizontal" | "vertical";
}

export function CategorySelector({
  categories,
  selectedCategoryId,
  onSelectCategory,
  className,
  variant = "horizontal"
}: CategorySelectorProps) {
  if (variant === "vertical") {
    return (
      <div className={cn("flex flex-col", className)}>
        <Button
          className={cn(
            "w-full p-3 text-left justify-start text-sm border-b border-gray-700",
            selectedCategoryId === null ? "bg-primary text-primary-foreground" : "bg-transparent"
          )}
          variant={selectedCategoryId === null ? "default" : "ghost"}
          onClick={() => onSelectCategory(null)}
        >
          <span>All Items</span>
        </Button>

        <ScrollArea className="flex-1">
          <div className="py-2">
            {categories?.map((category) => (
              <Button
                key={category.id}
                className={cn(
                  "w-full p-3 text-left justify-start text-sm border-b border-gray-700",
                  selectedCategoryId === category.id ? "bg-primary text-primary-foreground" : "bg-transparent"
                )}
                variant={selectedCategoryId === category.id ? "default" : "ghost"}
                onClick={() => onSelectCategory(category.id)}
              >
                <span>{category.name}</span>
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>
    );
  }

  // Horizontal layout (default)
  return (
    <ScrollArea className={cn("w-full", className)}>
      <div className="flex space-x-2 p-2">
        <Button
          variant={selectedCategoryId === null ? "default" : "outline"}
          size="sm"
          className="rounded-full whitespace-nowrap"
          onClick={() => onSelectCategory(null)}
        >
          All Items
        </Button>
        
        {categories?.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategoryId === category.id ? "default" : "outline"}
            size="sm"
            className="rounded-full whitespace-nowrap"
            onClick={() => onSelectCategory(category.id)}
          >
            {category.name}
          </Button>
        ))}
      </div>
    </ScrollArea>
  );
}
