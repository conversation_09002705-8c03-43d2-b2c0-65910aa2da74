import pg from 'pg';
const { Pool } = pg;

const pool = new Pool({
  connectionString: '*******************************************************************************'
});

async function insertSampleData() {
  const client = await pool.connect();
  
  try {
    console.log('Inserting sample subscription data...');
    
    // Get the first shop and user IDs
    const shopResult = await client.query('SELECT id FROM shops LIMIT 1');
    const userResult = await client.query('SELECT id FROM users LIMIT 1');
    const planResult = await client.query('SELECT id, price FROM subscription_plans ORDER BY price LIMIT 3');
    
    if (shopResult.rows.length === 0 || userResult.rows.length === 0 || planResult.rows.length === 0) {
      console.log('Missing required data (shops, users, or plans). Cannot create subscriptions.');
      
      // Create a basic user and shop if they don't exist
      if (userResult.rows.length === 0) {
        await client.query(`
          INSERT INTO users (name, username, email, password, role, active) 
          VALUES ('Sample Admin', 'sampleadmin', '<EMAIL>', 'hashed_password', 'admin', true)
        `);
        console.log('Created sample user');
      }
      
      if (shopResult.rows.length === 0) {
        const newUserResult = await client.query('SELECT id FROM users LIMIT 1');
        await client.query(`
          INSERT INTO shops (name, address, phone, shop_type, access_code, created_by) 
          VALUES ('Sample Shop', '123 Sample St', '************', 'restaurant', 'SAMPLE001', $1)
        `, [newUserResult.rows[0].id]);
        console.log('Created sample shop');
      }
      
      // Get the IDs again
      const newShopResult = await client.query('SELECT id FROM shops LIMIT 1');
      const newUserResult = await client.query('SELECT id FROM users LIMIT 1');
      
      if (newShopResult.rows.length === 0 || newUserResult.rows.length === 0) {
        console.log('Still missing required data. Exiting.');
        return;
      }
    }
    
    // Get fresh data
    const shop = await client.query('SELECT id FROM shops LIMIT 1');
    const user = await client.query('SELECT id FROM users LIMIT 1');
    const plans = await client.query('SELECT id, price FROM subscription_plans ORDER BY price');
    
    const shopId = shop.rows[0].id;
    const userId = user.rows[0].id;
    
    console.log(`Using shop ID: ${shopId}, user ID: ${userId}`);
    console.log(`Available plans: ${plans.rows.length}`);
    
    // Check if subscriptions already exist
    const existingCount = await client.query('SELECT COUNT(*) FROM subscriptions');
    if (parseInt(existingCount.rows[0].count) > 0) {
      console.log(`Already have ${existingCount.rows[0].count} subscriptions. Skipping insertion.`);
    } else {
      // Insert sample subscriptions
      const subscriptions = [
        {
          planId: plans.rows[0].id,
          status: 'active',
          totalAmount: plans.rows[0].price
        },
        {
          planId: plans.rows[1]?.id || plans.rows[0].id,
          status: 'active', 
          totalAmount: plans.rows[1]?.price || plans.rows[0].price
        },
        {
          planId: plans.rows[2]?.id || plans.rows[0].id,
          status: 'expired',
          totalAmount: plans.rows[2]?.price || plans.rows[0].price
        }
      ];
      
      for (let i = 0; i < subscriptions.length; i++) {
        const sub = subscriptions[i];
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - (i * 2)); // Stagger start dates
        
        const endDate = new Date(startDate);
        endDate.setFullYear(endDate.getFullYear() + 1); // 1 year subscription
        
        await client.query(`
          INSERT INTO subscriptions (
            shop_id, plan_id, status, start_date, end_date, 
            auto_renew, discount_percent, discount_amount, total_amount, created_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        `, [
          shopId, sub.planId, sub.status, startDate, endDate,
          true, 0, 0, sub.totalAmount, userId
        ]);
        
        console.log(`✓ Created subscription ${i + 1} with plan ${sub.planId}`);
      }
    }
    
    // Show final stats
    const stats = await client.query(`
      SELECT 
        COUNT(*) as total,
        COUNT(*) FILTER (WHERE status = 'active') as active,
        COALESCE(SUM(total_amount), 0) as revenue
      FROM subscriptions
    `);
    
    console.log('Final stats:', stats.rows[0]);
    console.log('Sample data insertion completed!');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

insertSampleData();
