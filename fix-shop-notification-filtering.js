import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || '****************************************************/nemboobill?sslmode=disable'
});

async function fixShopNotificationFiltering() {
  const client = await pool.connect();
  try {
    console.log('🔧 Fixing shop notification filtering issue...\n');

    // Step 1: Check current notification distribution
    console.log('📊 Current notification distribution by shop:');
    
    const distributionResult = await client.query(`
      SELECT 
        s.id as shop_id,
        s.name as shop_name,
        COUNT(n.id) as notification_count,
        COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_count
      FROM shops s
      LEFT JOIN notifications n ON s.id = n.shop_id
      LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
      GROUP BY s.id, s.name
      ORDER BY s.id
    `);

    distributionResult.rows.forEach(row => {
      console.log(`   ${row.shop_name} (ID: ${row.shop_id}): ${row.notification_count} notifications, ${row.unread_count} unread`);
    });

    // Step 2: Check for any notifications with wrong shop_id
    console.log('\n🔍 Checking for notifications with incorrect shop assignments...');
    
    const wrongAssignments = await client.query(`
      SELECT 
        n.id,
        n.title,
        n.shop_id,
        s.name as assigned_shop,
        CASE 
          WHEN n.title LIKE '%Sathya Cafe%' AND s.name != 'Sathya Cafe' THEN 'WRONG'
          WHEN n.title LIKE '%likitha%' AND s.name != 'likitha' THEN 'WRONG'
          WHEN n.title LIKE '%gokul%' AND s.name != 'gokul' THEN 'WRONG'
          WHEN n.title LIKE '%Deva%' AND s.name != 'Deva' THEN 'WRONG'
          WHEN n.title LIKE '%mohn%' AND s.name != 'mohn' THEN 'WRONG'
          ELSE 'CORRECT'
        END as assignment_status
      FROM notifications n
      JOIN shops s ON n.shop_id = s.id
      WHERE n.title LIKE '%Sathya Cafe%' 
         OR n.title LIKE '%likitha%' 
         OR n.title LIKE '%gokul%' 
         OR n.title LIKE '%Deva%' 
         OR n.title LIKE '%mohn%'
      ORDER BY n.shop_id, n.title
    `);

    const wrongNotifications = wrongAssignments.rows.filter(row => row.assignment_status === 'WRONG');
    
    if (wrongNotifications.length > 0) {
      console.log(`   ❌ Found ${wrongNotifications.length} notifications with wrong shop assignments:`);
      wrongNotifications.forEach(notif => {
        console.log(`      - "${notif.title}" assigned to "${notif.assigned_shop}" (should be different shop)`);
      });
    } else {
      console.log('   ✅ All notifications are correctly assigned to their shops');
    }

    // Step 3: Fix any wrong assignments by correcting shop_id
    console.log('\n🔧 Fixing notification shop assignments...');
    
    // Get all shops for reference
    const shopsResult = await client.query('SELECT id, name FROM shops ORDER BY id');
    const shopMap = {};
    shopsResult.rows.forEach(shop => {
      shopMap[shop.name.toLowerCase()] = shop.id;
    });

    console.log('Available shops:', Object.keys(shopMap));

    // Fix notifications that have wrong shop assignments
    let fixedCount = 0;
    
    for (const notif of wrongAssignments.rows) {
      let correctShopId = null;
      
      // Determine correct shop based on notification title
      if (notif.title.includes('Sathya Cafe')) {
        correctShopId = shopMap['sathya cafe'];
      } else if (notif.title.includes('likitha')) {
        correctShopId = shopMap['likitha'];
      } else if (notif.title.includes('gokul')) {
        correctShopId = shopMap['gokul'];
      } else if (notif.title.includes('Deva')) {
        correctShopId = shopMap['deva'];
      } else if (notif.title.includes('mohn')) {
        correctShopId = shopMap['mohn'];
      }
      
      if (correctShopId && correctShopId !== notif.shop_id) {
        await client.query(`
          UPDATE notifications 
          SET shop_id = $1 
          WHERE id = $2
        `, [correctShopId, notif.id]);
        
        console.log(`   ✅ Fixed: "${notif.title}" moved from shop ${notif.shop_id} to shop ${correctShopId}`);
        fixedCount++;
      }
    }

    console.log(`\n📊 Fixed ${fixedCount} notification assignments`);

    // Step 4: Verify the fix
    console.log('\n✅ Verification - Updated notification distribution:');
    
    const verificationResult = await client.query(`
      SELECT 
        s.id as shop_id,
        s.name as shop_name,
        COUNT(n.id) as notification_count,
        COUNT(CASE WHEN nr.status = 'unread' THEN 1 END) as unread_count
      FROM shops s
      LEFT JOIN notifications n ON s.id = n.shop_id
      LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
      GROUP BY s.id, s.name
      ORDER BY s.id
    `);

    verificationResult.rows.forEach(row => {
      console.log(`   ${row.shop_name} (ID: ${row.shop_id}): ${row.notification_count} notifications, ${row.unread_count} unread`);
    });

    // Step 5: Test specific shop (likitha)
    console.log('\n🧪 Testing likitha shop notifications:');
    
    const likithaTest = await client.query(`
      SELECT 
        n.id,
        n.title,
        n.type,
        n.priority,
        COUNT(nr.id) as recipient_count
      FROM notifications n
      LEFT JOIN notification_recipients nr ON n.id = nr.notification_id
      WHERE n.shop_id = 12
      GROUP BY n.id, n.title, n.type, n.priority
      ORDER BY n.created_at DESC
    `);

    console.log(`   Found ${likithaTest.rows.length} notifications for likitha shop:`);
    likithaTest.rows.forEach((notif, index) => {
      console.log(`   ${index + 1}. ${notif.title} (${notif.type}, ${notif.priority}) - ${notif.recipient_count} recipients`);
    });

    console.log('\n🎉 SUCCESS! Shop notification filtering has been fixed');
    
    console.log('\n🎯 What to do now:');
    console.log('   1. Hard refresh your browser (Ctrl+Shift+R)');
    console.log('   2. Switch between different shops');
    console.log('   3. Each shop should now show only its own notifications');
    console.log('   4. "likitha" shop should show only likitha notifications');
    console.log('   5. "Sathya Cafe" should show only Sathya Cafe notifications');

  } catch (error) {
    console.error('❌ Error fixing shop notification filtering:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixShopNotificationFiltering();
