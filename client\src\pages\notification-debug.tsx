import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "@/hooks/use-notifications";
import { Check, Trash2, RefreshCw } from "lucide-react";

export default function NotificationDebugPage() {
  const {
    notifications,
    unreadCount,
    notificationsLoading,
    markAsRead,
    deleteNotification,
    refreshNotifications,
    isMarkingAsRead,
    isDeleting
  } = useNotifications();

  const [debugInfo, setDebugInfo] = useState<string>("");

  const handleMarkAsRead = async (notificationId: number) => {
    setDebugInfo(`Marking notification ${notificationId} as read...`);
    try {
      await markAsRead(notificationId);
      setDebugInfo(`Successfully marked notification ${notificationId} as read!`);
    } catch (error) {
      setDebugInfo(`Error marking notification ${notificationId} as read: ${error}`);
    }
  };

  const handleRefresh = () => {
    setDebugInfo("Refreshing notifications...");
    refreshNotifications();
    setTimeout(() => {
      setDebugInfo("Notifications refreshed!");
    }, 1000);
  };

  const testApiDirectly = async () => {
    setDebugInfo("Testing API directly...");
    try {
      const token = localStorage.getItem('auth_token');
      const shopId = JSON.parse(localStorage.getItem('current_shop') || '{}').id;
      
      // Test unread count API
      const countResponse = await fetch('/api/notifications/unread-count', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': shopId,
          'Cache-Control': 'no-cache'
        }
      });
      
      const countData = await countResponse.json();
      
      // Test notifications API
      const notificationsResponse = await fetch('/api/notifications', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Shop-ID': shopId,
          'Cache-Control': 'no-cache'
        }
      });
      
      const notificationsData = await notificationsResponse.json();
      
      setDebugInfo(`API Test Results:
- Unread Count: ${countData}
- Notifications Count: ${notificationsData.length}
- First notification: ${notificationsData[0]?.title || 'None'}
- Auth Token: ${token ? 'Present' : 'Missing'}
- Shop ID: ${shopId || 'Missing'}`);
      
    } catch (error) {
      setDebugInfo(`API Test Error: ${error}`);
    }
  };

  return (
    <div className="container mx-auto max-w-6xl py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Notification Debug Page</h1>
          <p className="text-muted-foreground">Debug and test notification functionality</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={testApiDirectly} variant="outline">
            Test API
          </Button>
        </div>
      </div>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>Unread Count:</strong> {unreadCount}</p>
            <p><strong>Total Notifications:</strong> {notifications.length}</p>
            <p><strong>Loading:</strong> {notificationsLoading ? 'Yes' : 'No'}</p>
            <p><strong>Is Marking as Read:</strong> {isMarkingAsRead ? 'Yes' : 'No'}</p>
            <p><strong>Is Deleting:</strong> {isDeleting ? 'Yes' : 'No'}</p>
          </div>
          {debugInfo && (
            <div className="mt-4 p-3 bg-gray-100 rounded-md">
              <pre className="text-sm whitespace-pre-wrap">{debugInfo}</pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Notifications ({notifications.length})</CardTitle>
          <CardDescription>
            Click the check mark to mark as read, or trash to delete
          </CardDescription>
        </CardHeader>
        <CardContent>
          {notificationsLoading ? (
            <div className="text-center py-8">Loading notifications...</div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No notifications found
            </div>
          ) : (
            <div className="space-y-3">
              {notifications.map((notification: any) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${
                    notification.status === "unread"
                      ? "bg-blue-50 border-blue-200"
                      : "bg-gray-50 border-gray-200"
                  }`}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{notification.title}</h4>
                        <Badge 
                          variant={notification.status === "unread" ? "default" : "secondary"}
                        >
                          {notification.status}
                        </Badge>
                        <Badge variant="outline">
                          {notification.type}
                        </Badge>
                        <Badge variant="outline">
                          {notification.priority}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500">
                        ID: {notification.id} | Created: {new Date(notification.created_at).toLocaleString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      {notification.status === "unread" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMarkAsRead(notification.id)}
                          disabled={isMarkingAsRead}
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Mark Read
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteNotification(notification.id)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            <Button
              onClick={() => {
                const unreadNotifications = notifications.filter(n => n.status === 'unread');
                if (unreadNotifications.length > 0) {
                  handleMarkAsRead(unreadNotifications[0].id);
                } else {
                  setDebugInfo("No unread notifications to mark as read");
                }
              }}
              disabled={isMarkingAsRead || notifications.filter(n => n.status === 'unread').length === 0}
            >
              Mark First Unread as Read
            </Button>
            
            <Button
              onClick={() => {
                const unreadNotifications = notifications.filter(n => n.status === 'unread');
                unreadNotifications.forEach(notification => {
                  markAsRead(notification.id);
                });
                setDebugInfo(`Marking ${unreadNotifications.length} notifications as read...`);
              }}
              disabled={isMarkingAsRead || notifications.filter(n => n.status === 'unread').length === 0}
              variant="outline"
            >
              Mark All as Read ({notifications.filter(n => n.status === 'unread').length})
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
