// Simple test script to check subscription API
import fetch from 'node-fetch';

async function testSubscriptionAPI() {
  try {
    console.log('Testing subscription API...');
    
    // Test subscription plans endpoint
    const response = await fetch('http://localhost:5000/api/subscription-plans', {
      headers: {
        'Authorization': 'Bearer test-token' // You'll need a real token
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Subscription plans:', data);
    } else {
      const error = await response.text();
      console.log('Error response:', error);
    }
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testSubscriptionAPI();
